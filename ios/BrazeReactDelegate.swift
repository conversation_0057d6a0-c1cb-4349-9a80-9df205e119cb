//
//  BrazeReactDelegate.swift
//  ichangiFe
//
//  Created by <PERSON><PERSON><PERSON> on 4/6/25.
//

import Foundation
import UIKit
import BrazeKit // Required for Braze and Braze.URLContext types
 
class BrazeReactDelegate: NSObject, BrazeDelegate {
  func braze(_ braze: Braze, shouldOpenURL context: Braze.URLContext) -> Bool {
    let host = context.url.host?.lowercased() ?? ""
    if (host == "cagapp3.page.link") || (host == "ichangi3.page.link") {
      let application = UIApplication.shared
      let userActivity = NSUserActivity(activityType: NSUserActivityTypeBrowsingWeb)
      userActivity.webpageURL = context.url
      // Routes to the `continue.userActivity` method, which should be handled in your `AppDelegate`.
      _ = application.delegate?.application?(application, continue: userActivity, restorationHandler: { restorableObjects in
          // Handle restorable objects here if needed
      })
      
      // Return `false` to indicate that the app has handled this URL,
      // so <PERSON><PERSON> should not open it.
      return false
    }
    return true
  }
}
