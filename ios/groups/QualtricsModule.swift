//
//  QualtricsModule.swift
//  ichangiFe
//
//  Created by <PERSON><PERSON><PERSON> on 12/5/25.
//

import Foundation
import Qualtrics

@objc(QualtricsModule)
class QualtricsModule: NSObject {
  @objc static func updateUI() {
    let blackColor = UIColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1)
    let whiteColor = UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1)
    let mainColor = UIColor(
        red: CGFloat((Float((0x7a35b0 & 0xff0000) >> 16)) / 255.0),
        green: CGFloat((Float((0x7a35b0 & 0x00ff00) >> 8)) / 255.0),
        blue: CGFloat((Float((0x7a35b0 & 0x0000ff) >> 0)) / 255.0),
        alpha: 1.0)
    let buttonOne = ButtonTheme.init(
        textColor: mainColor,
        linkColor: whiteColor,
        font: UIFont(name: "Lato-Bold", size: 16)!,
        backgroundColor: whiteColor,
        borderColor: whiteColor
    )
    let buttonTwo = ButtonTheme.init(
        textColor: whiteColor,
        linkColor: mainColor,
        font: UIFont(name: "Lato-Bold", size: 16)!,
        backgroundColor: mainColor,
        borderColor: mainColor
    )
    let mobileAppPromptTheme = MobileAppPromptTheme.init(
        backgroundColor: whiteColor,
        headlineTextColor: blackColor,
        headlineFont: UIFont(name: "Lato-Bold", size: 16)!,
        descriptionTextColor: blackColor,
        descriptionFont: UIFont(name: "Lato", size: 16)!,
        closeButtonColor: blackColor,
        buttonOneTheme: buttonOne,
        buttonTwoTheme: buttonTwo
    )
    let embeddedAppFeedbackTheme = EmbeddedAppFeedbackTheme.init()
    let theme = QualtricsTheme.init(
        mobileAppPromptTheme: mobileAppPromptTheme,
        embeddedAppFeedbackTheme: embeddedAppFeedbackTheme
    )
    Qualtrics.shared.setCreativeTheme(to: theme)
  }
}
