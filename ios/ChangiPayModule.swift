//
//  ChangiPayModule.swift
//  ichangiFe
//
//  Created by <PERSON><PERSON><PERSON> on 29/08/2022.
//

import Foundation
import UIKit
import LiquidPayWidget

@objc(ChangiPayModule)
class ChangiPayModule: NSObject {
  @objc(changiPayPopup:profile:)
  func changiPayPopup(_ config: NSDictionary, profile: NSDictionary) -> Void {
    DispatchQueue.main.async {
      guard let viewController = self.getViewController() else {
        print("Failed to get top view controller")
        return
      }
      
      let vc = CoverLiquidViewController(config: config, profile: profile)
      vc.modalPresentationStyle = .overFullScreen
      vc.modalTransitionStyle = .coverVertical
      viewController.present(vc, animated: false)
    }
    
    print("Did tap open Liqid")
  }
  
  @objc
  func closeChangiPay() -> Void {
    DispatchQueue.main.async {
      guard let viewController = self.getViewController() else {
        print("Failed to get top view controller")
        return
      }
      viewController.dismiss(animated: false)
    }
  }
  
  private func getViewController() -> UIViewController? {
    let window = UIApplication.shared.windows.first
    return window?.rootViewController as? UIViewController
  }
}



class CoverLiquidViewController: UIViewController, LPWidgetDelegate {
  
  private var isFirstAppear: Bool = false
  
  private let widgetConfig = LPWidgetConfig()
  private let widgetProfile = LPWidgetProfile()
  
  init(config: NSDictionary, profile: NSDictionary) {
    super.init(nibName: nil, bundle: nil)
    
    widgetConfig.appId = "com.changiairport.cagapp3.uat"
    widgetConfig.apiKey = config["apiKey"] as? String
    widgetConfig.apiSecret = config["apiSecret"] as? String
    widgetConfig.deviceToken = config["deviceToken"] as? String
    
    widgetProfile.firstName = profile["firstName"] as? String
    widgetProfile.lastName = profile["lastName"] as? String
    widgetProfile.mobileNo = profile["mobileNumber"] as? String
    widgetProfile.mobileDialingCode = profile["mobileDialingCode"] as? String
    widgetProfile.email = profile["email"] as? String
    widgetProfile.memberId = profile["memberId"] as? String
    widgetProfile.ciamUid = profile["memberId"] as? String
  }
  
  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  override func viewDidLoad() {
    super.viewDidLoad()
    view.backgroundColor = .clear
  }
  
  override func viewWillAppear(_ animated: Bool) {
    super.viewWillAppear(animated)
    
    // Present, dismiss Liquid
    if !isFirstAppear {
      openLiquidScreen()
      isFirstAppear = true
    } else {
      self.dismiss(animated: false)
    }
  }
  
  private func openLiquidScreen() {
    let widget = LPWidget(widgetType: .Home, widgetConfig: widgetConfig, widgetProfile: widgetProfile, delegate: self)
    widget.show()
  }
  
  func widgetDidClose() {
    self.dismiss(animated: false)
    DispatchQueue.main.async {
      if let emitter = RNEventEmitter.sharedInstance() {
        emitter.sendEvent(withName: "onCloseChangiPay", body: nil)
      } else {
        print("My RNEventEmitter not initialized yet")
      }
    }
//    RNEventEmitter.emitter.sendEvent(withName: "onCloseChangiPay", body: nil);
  }
  
  func widgetOnError(widgetStatus: LPWidgetStatus) {
    let errorCode = widgetStatus.getCode()
    let errorMessage = widgetStatus.getMessage()
//    print(errorCode)
//    print(errorMessage)
    DispatchQueue.main.async {
      if let emitter = RNEventEmitter.sharedInstance() {
        emitter.sendEvent(withName: "onChange", body: ["message": errorMessage, "code": errorCode])
      } else {
        print("My RNEventEmitter not initialized yet")
      }
    }
  }
}
