# CalendarModule.swift - iOS Calendar Implementation

This Swift implementation replaces the previous Objective-C version and provides native iOS calendar functionality for React Native.

## Architecture

The `CalendarModule` class provides:
- **React Native Bridge**: Exposes methods to JavaScript/TypeScript
- **EventKit Integration**: Uses iOS EventKit and EventKitUI frameworks
- **Native UI**: Presents iOS native calendar event edit controller

## Files Structure

### Core Implementation
- **`CalendarModule.swift`**: Main Swift implementation
- **`CalendarModuleBridge.m`**: React Native bridge registration
- **`CalendarModule-Bridging-Header.h`**: Swift-Objective-C bridging header

## Key Features

### React Native Methods (Accessible from JavaScript/TypeScript)

#### `openCalendarEventPopup(eventData, resolver, rejecter)`
- Opens iOS native calendar event edit controller
- Accepts event data dictionary with: title, description, location, startTime, endTime
- Returns promise with success/error status
- Automatically handles calendar permissions

#### `openSimpleCalendarEvent(title, description, location, startTime, endTime, resolver, rejecter)`
- Simplified method with direct parameters
- More convenient for basic calendar event creation
- Same native UI as the comprehensive method

#### `isCalendarAvailable(resolver, rejecter)`
- Checks calendar authorization status
- Returns boolean indicating if calendar access is available
- Useful for conditional calendar functionality

## Implementation Details

### Permission Handling
- Automatically requests calendar access when needed
- Uses `EKEventStore.requestAccess(to: .event)`
- Handles both granted and denied permission states
- Provides clear error messages for permission issues

### Event Creation Process
1. **Permission Check**: Requests calendar access if needed
2. **Event Setup**: Creates `EKEvent` with provided details
3. **UI Presentation**: Shows native `EKEventEditViewController`
4. **User Interaction**: User can save, cancel, or delete the event
5. **Callback**: Returns result via promise resolution

### Native UI Integration
- Uses `EKEventEditViewController` for native iOS experience
- Integrates with iOS calendar app and system calendars
- Supports all iOS calendar features (reminders, alerts, etc.)
- Follows iOS Human Interface Guidelines

### Error Handling
- **Permission Denied**: Clear error message with rejection code
- **No Root View**: Handles cases where UI cannot be presented
- **EventKit Errors**: Passes through system errors with context
- **Promise-Based**: All errors returned via promise rejection

## Swift Advantages

### Over Objective-C Implementation
1. **Type Safety**: Swift's strong type system prevents runtime errors
2. **Null Safety**: Optional types eliminate null pointer exceptions
3. **Modern Syntax**: More readable and maintainable code
4. **Memory Management**: Automatic reference counting with weak references
5. **Error Handling**: Built-in error handling with do-catch blocks
6. **Closures**: More elegant callback handling

### Code Quality Improvements
- **Weak Self**: Prevents retain cycles in closures
- **Guard Statements**: Early returns for cleaner error handling
- **Optional Binding**: Safe unwrapping of optional values
- **Switch Expressions**: Exhaustive pattern matching
- **Extensions**: Clean separation of concerns

## Usage Examples

### From React Native (JavaScript/TypeScript)
```javascript
import { NativeModules } from 'react-native'
const { CalendarModule } = NativeModules

// Method 1: Comprehensive event data
const eventData = {
  title: "Team Meeting",
  description: "Weekly sync meeting",
  location: "Conference Room A",
  startTime: Date.now(),
  endTime: Date.now() + (60 * 60 * 1000) // 1 hour later
}

try {
  const result = await CalendarModule.openCalendarEventPopup(eventData)
  console.log(result) // "Calendar event saved successfully"
} catch (error) {
  console.error('Calendar error:', error)
}

// Method 2: Simple parameters
try {
  const result = await CalendarModule.openSimpleCalendarEvent(
    "Doctor Appointment",
    "Annual checkup",
    "Medical Center",
    Date.now(),
    Date.now() + (30 * 60 * 1000) // 30 minutes
  )
  console.log(result)
} catch (error) {
  console.error('Calendar error:', error)
}

// Check availability
const isAvailable = await CalendarModule.isCalendarAvailable()
if (isAvailable) {
  // Proceed with calendar operations
}
```

### Integration with TypeScript Service
```typescript
// Works seamlessly with existing calendar service
import calendarService from 'app/services/calendar/calendar-service'

const eventData = calendarService.createEventDataFromDates(
  "Flight Departure",
  new Date(),
  new Date(Date.now() + 2 * 60 * 60 * 1000),
  "Flight SQ123 to Tokyo",
  "Changi Airport Terminal 3"
)

await calendarService.openCalendarEventPopup(eventData)
```

## Migration Notes

### From Objective-C Implementation
- **API Compatibility**: All method signatures remain the same
- **Behavior**: Identical functionality with improved reliability
- **Performance**: Better memory management and performance
- **Maintenance**: Easier to read, debug, and extend

### Xcode Project Setup
1. **Add Swift Files**: Ensure Swift files are included in target
2. **Bridging Header**: Configure bridging header if needed
3. **Swift Version**: Set appropriate Swift language version
4. **Build Settings**: Enable Swift compilation

### No JavaScript Changes Required
- React Native interface remains identical
- TypeScript service layer requires no changes
- Existing calendar components work without modification

## Dependencies

### iOS Frameworks
- **EventKit**: Core calendar functionality
- **EventKitUI**: Native calendar UI components
- **Foundation**: Basic Swift/iOS functionality

### React Native
- **React/RCTBridgeModule**: Bridge functionality
- **React/RCTUtils**: Utility functions for UI presentation

## Error Codes

- **`PERMISSION_DENIED`**: Calendar access permission denied
- **`NO_ROOT_VIEW`**: No root view controller available for presentation
- **`CALENDAR_ERROR`**: General calendar operation error

The Swift implementation provides a more robust, maintainable, and modern approach to iOS calendar integration while maintaining full compatibility with the existing React Native interface.
