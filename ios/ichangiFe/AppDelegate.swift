//
//  AppDelegate.swift
//  ichangiFe
//
//  Created by <PERSON><PERSON><PERSON> on 12/5/25.
//

import UIKit
import React
import React_RCTAppDelegate
import ReactAppDependencyProvider
import GoogleMaps
import FBSDKCoreKit
import FirebaseCore

import BrazeKit

import RNBootSplash

import LiquidPayWidget


private func ClearKeychainIfNecessary() {
    // Checks wether or not this is the first time the app is run
    if !UserDefaults.standard.bool(forKey: "HAS_RUN_BEFORE") {
        // Set the appropriate value so we don't clear next time the app is launched
        UserDefaults.standard.set(true, forKey: "HAS_RUN_BEFORE")

        let secItemClasses = [
            kSecClassGenericPassword,
            kSecClassInternetPassword,
            kSecClassCertificate,
            kSecClassKey,
            kSecClassIdentity
        ]

        // Maps through all Keychain classes and deletes all items that match
        for secItemClass in secItemClasses {
            var spec: [AnyHashable? : AnyHashable]?
            if let kSecClass = kSecClass as? AnyHashable {
                spec = [
                    kSecClass: secItemClass
                ]
            }
            if let spec = spec as? CFDictionary? {
              SecItemDelete(spec!)
            }
        }
    }
}

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
  var window: UIWindow?
  var reactNativeDelegate: ReactNativeDelegate?
  var reactNativeFactory: RCTReactNativeFactory?
  public var brazeDelegate: BrazeReactDelegate?
  
  func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
    // Firebase
    FirebaseApp.configure()
    
    // FBSDKCoreKit
    ApplicationDelegate.shared.application(application, didFinishLaunchingWithOptions: launchOptions)
    
    ClearKeychainIfNecessary()
    
    ACPCore.setLogLevel(.debug)
    ACPCore.setWrapperType(.reactNative)
    ACPCore.configure(withAppId: "669a23884c44/a4d46dc0e7a2/launch-7be9e717566b-development")
    ACPAnalytics.registerExtension()
    ACPUserProfile.registerExtension()
    ACPIdentity.registerExtension()
    ACPLifecycle.registerExtension()
    ACPSignal.registerExtension()
    ACPMobileServices.registerExtension()
    ACPCampaign.registerExtension()
    ACPTarget.registerExtension()
    
    let appState = UIApplication.shared.applicationState
    
    ACPCore.start {
      // only start lifecycle if the application is not in the background
      if appState != .background {
          ACPCore.lifecycleStart(nil)
      }
    }
    
    // React Native Maps - Google API Key
    let apiKey = Bundle.main.object(forInfoDictionaryKey: "GoogleMapsAPIKey") as? String
    GoogleMaps.GMSServices.provideAPIKey(apiKey!)
    
    // update UI Quatrics
    QualtricsModule.updateUI()
    
    // Setup Braze
    let configuration = Braze.Configuration(apiKey: "227837b8-7d3a-443a-9bb8-2108b3d833d7", endpoint: "sdk.iad-05.braze.com")
    // Enable logging and customize the configuration here.
    configuration.forwardUniversalLinks = true
    configuration.logger.level = .info
    configuration.triggerMinimumTimeInterval = 1
    // Enable all automations and disable the automatic notification authorization request at launch.
    configuration.push.automation = true
    configuration.push.automation.requestAuthorizationAtLaunch = false
    let braze = BrazeReactBridge.perform(
          #selector(BrazeReactBridge.initBraze(_:)),
          with: configuration
        ).takeUnretainedValue() as! Braze
    self.brazeDelegate = BrazeReactDelegate()
    braze.delegate = self.brazeDelegate
    AppDelegate.braze = braze
    BrazeReactUtils.sharedInstance().populateInitialPayload(fromLaunchOptions: launchOptions)


    let delegate = ReactNativeDelegate()
    let factory = RCTReactNativeFactory(delegate: delegate)
    delegate.dependencyProvider = RCTAppDependencyProvider()

    reactNativeDelegate = delegate
    reactNativeFactory = factory

    window = UIWindow(frame: UIScreen.main.bounds)

    factory.startReactNative(
      withModuleName: "ichangiFe",
      in: window,
      launchOptions: launchOptions
    )

    return true
  }

  // Deep Linking
  func application(_ application: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
    AppsFlyerAttribution.shared().handleOpen(url, options: options)
    // Pass the URL to the CPay Widget.
    if url.absoluteString.hasPrefix("com.changiairport.cagapp.liquidwidget://") {
      let nc = NotificationCenter.default
      nc.post(name: Notification.Name(NSNotification.LPWidgetReceivedUrlSchemeNotification as String), object: url)
      return true
    }
    
    return ApplicationDelegate.shared.application(application, open: url, options: options) || RCTLinkingManager.application(application, open: url, options: options)
  }
  
  // Universal links
  func application(
    _ application: UIApplication,
    continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
      AppsFlyerAttribution.shared().continue(userActivity, restorationHandler: nil)
      return RCTLinkingManager.application(
        application,
        continue: userActivity,
        restorationHandler: restorationHandler
      )
  }
  
  // This method is called when the app moves to the background.
  func applicationDidEnterBackground(_ application: UIApplication) {
    // Request the widget to refresh with the new data
    WidgetKitHelper.reloadAllTimelines()
  }
  
  // MARK: - AppDelegate.braze

  static var braze: Braze? = nil
}

class ReactNativeDelegate: RCTDefaultReactNativeFactoryDelegate {
  override func sourceURL(for bridge: RCTBridge) -> URL? {
    self.bundleURL()
  }

  override func bundleURL() -> URL? {
#if DEBUG
    RCTBundleURLProvider.sharedSettings().jsBundleURL(forBundleRoot: "index")
#else
    Bundle.main.url(forResource: "main", withExtension: "jsbundle")
#endif
  }

  // ⬇️ override this method
  override func customize(_ rootView: RCTRootView) {
    super.customize(rootView)
    RNBootSplash.initWithStoryboard("ChangiLaunchScreen", rootView: rootView) // ⬅️ initialize the splash screen
  }
}
