import Foundation
import EventKit
import Event<PERSON><PERSON><PERSON>
import React

@objc(CalendarModule)
class CalendarModule: NSObject, RCTBridgeModule, EKEventEditViewControllerDelegate {
    
    static func moduleName() -> String! {
        return "CalendarModule"
    }
    
    static func requiresMainQueueSetup() -> Bool {
        return true
    }
    
    // Promise handlers for current operation
    private var currentResolve: RCTPromiseResolveBlock?
    private var currentReject: RCTPromiseRejectBlock?
    
    // MARK: - React Native Methods
    
    @objc func openCalendarEventPopup(_ eventData: [String: Any], 
                                     resolver resolve: @escaping RCTPromiseResolveBlock,
                                     rejecter reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            self.openCalendarEvent(with: eventData, resolver: resolve, rejecter: reject)
        }
    }
    
    @objc func openSimpleCalendarEvent(_ title: String,
                                      description: String,
                                      location: String,
                                      startTime: Double,
                                      endTime: Double,
                                      resolver resolve: @escaping RCTPromiseResolveBlock,
                                      rejecter reject: @escaping RCTPromiseRejectBlock) {
        DispatchQueue.main.async {
            let eventData: [String: Any] = [
                "title": title,
                "description": description,
                "location": location,
                "startTime": startTime,
                "endTime": endTime
            ]
            self.openCalendarEvent(with: eventData, resolver: resolve, rejecter: reject)
        }
    }
    
    @objc func isCalendarAvailable(_ resolve: @escaping RCTPromiseResolveBlock,
                                  rejecter reject: @escaping RCTPromiseRejectBlock) {
        let status = EKEventStore.authorizationStatus(for: .event)
        let isAvailable = status == .authorized || status == .notDetermined
        resolve(isAvailable)
    }
    
    // MARK: - Private Helper Methods
    
    private func openCalendarEvent(with eventData: [String: Any],
                                  resolver resolve: @escaping RCTPromiseResolveBlock,
                                  rejecter reject: @escaping RCTPromiseRejectBlock) {
        
        self.currentResolve = resolve
        self.currentReject = reject
        
        let eventStore = EKEventStore()
        
        eventStore.requestAccess(to: .event) { [weak self] granted, error in
            DispatchQueue.main.async {
                guard let self = self else { return }
                
                if !granted {
                    reject("PERMISSION_DENIED", "Calendar access permission denied", error)
                    return
                }
                
                let event = EKEvent(eventStore: eventStore)
                event.title = eventData["title"] as? String ?? ""
                event.notes = eventData["description"] as? String ?? ""
                event.location = eventData["location"] as? String ?? ""
                
                // Convert from milliseconds to seconds
                let startTime = (eventData["startTime"] as? Double ?? 0) / 1000.0
                let endTime = (eventData["endTime"] as? Double ?? 0) / 1000.0
                
                event.startDate = Date(timeIntervalSince1970: startTime)
                event.endDate = Date(timeIntervalSince1970: endTime)
                event.calendar = eventStore.defaultCalendarForNewEvents
                
                let eventEditViewController = EKEventEditViewController()
                eventEditViewController.event = event
                eventEditViewController.eventStore = eventStore
                eventEditViewController.editViewDelegate = self
                
                guard let rootViewController = RCTPresentedViewController() else {
                    reject("NO_ROOT_VIEW", "No root view controller available", nil)
                    return
                }
                
                rootViewController.present(eventEditViewController, animated: true, completion: nil)
            }
        }
    }
    
    // MARK: - EKEventEditViewControllerDelegate
    
    func eventEditViewController(_ controller: EKEventEditViewController, 
                               didCompleteWith action: EKEventEditViewAction) {
        controller.dismiss(animated: true) { [weak self] in
            guard let self = self, let resolve = self.currentResolve else { return }
            
            let message: String
            switch action {
            case .saved:
                message = "Calendar event saved successfully"
            case .canceled:
                message = "Calendar event creation canceled"
            case .deleted:
                message = "Calendar event deleted"
            @unknown default:
                message = "Calendar event action completed"
            }
            
            resolve(message)
            self.currentResolve = nil
            self.currentReject = nil
        }
    }
}

// MARK: - React Native Bridge Registration

extension CalendarModule {
    @objc static func requiresMainQueueSetup() -> Bool {
        return true
    }
}
