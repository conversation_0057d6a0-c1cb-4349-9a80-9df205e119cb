//
//  CalendarModuleBridge.m
//  ichangiFe
//
//  React Native bridge for CalendarModule Swift implementation
//

#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(CalendarModule, NSObject)

RCT_EXTERN_METHOD(openCalendarEventPopup:(NSDictionary *)eventData
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(openSimpleCalendarEvent:(NSString *)title
                  description:(NSString *)description
                  location:(NSString *)location
                  startTime:(double)startTime
                  endTime:(double)endTime
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

RCT_EXTERN_METHOD(isCalendarAvailable:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)

@end
