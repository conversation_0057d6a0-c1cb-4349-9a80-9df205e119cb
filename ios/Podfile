ENV['RCT_NEW_ARCH_ENABLED'] = "0"
source 'https://github.com/CocoaPods/Specs.git'
# Resolve react_native_pods.rb with node to allow for hoisting
def node_require(script)
  # Resolve script with node to allow for hoisting
  require Pod::Executable.execute_command('node', ['-p',
    "require.resolve(
      '#{script}',
      {paths: [process.argv[1]]},
    )", __dir__]).strip
end

node_require('react-native/scripts/react_native_pods.rb')
node_require('react-native-permissions/scripts/setup.rb')

platform :ios, '15.5'
prepare_react_native_project!

setup_permissions([
  # 'AppTrackingTransparency',
  # 'Bluetooth',
  'Calendars',
  # 'CalendarsWriteOnly',
  'Camera',
  # 'Contacts',
  # 'FaceID',
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  # 'MediaLibrary',
  'Microphone',
  # 'Motion',
  'Notifications',
  'PhotoLibrary',
  # 'PhotoLibraryAddOnly',
  # 'Reminders',
  # 'Siri',
  # 'SpeechRecognition',
  # 'StoreKit',
])

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

#Variant Releases
project 'ichangiFe',
  'Uat' => :debug,
  'Sit' => :debug,
  'Production'=> :release

target 'ichangiFe' do
  # use_unimodules!
  config = use_native_modules!

  pod 'Firebase', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCrashlytics', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseCoreExtension', :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  pod 'GoogleDataTransport', :modular_headers => true
  pod 'nanopb', :modular_headers => true
  pod 'FirebaseRemoteConfig', :modular_headers => true
  pod 'FirebaseSessions', :modular_headers => true
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseABTesting', :modular_headers => true

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  rn_maps_path = '../node_modules/react-native-maps'
  pod 'react-native-maps', path: rn_maps_path
  pod 'react-native-google-maps', path: rn_maps_path
  pod 'GoogleMaps'

  pod 'ACPCore', '~> 2.0'
  pod 'ACPUserProfile', '~> 2.0'
  pod 'ACPMobileServices', '~> 1.1'

  target 'ichangiFeTests' do
    inherit! :complete
    # Pods for testing
  end
  
  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
    $MLRN.post_install(installer)
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.5'
        config.build_settings["ONLY_ACTIVE_ARCH"] = "NO"
      end
    end
    # installer.pods_project.targets.each do |target|
    #   target.build_configurations.each do |config|
    #       if  target.name == 'FirebaseAnalytics'
    #           config.build_settings['MACH_O_TYPE'] = 'staticlib'
    #       end
    #   end
    # end
  end
end
