#import "RNEventEmitter.h"

@implementation RNEventEmitter {
  bool hasListeners;
}

static RNEventEmitter *_sharedInstance = nil;

RCT_EXPORT_MODULE(RNEventEmitter);

+ (instancetype)sharedInstance {
  return _sharedInstance;
}

- (instancetype)init {
  if (self = [super init]) {
    _sharedInstance = self;
  }
  return self;
}

- (NSArray<NSString *> *)supportedEvents {
  return @[
    @"onChange",
    @"onCloseChangiPay",
  ];
}
- (void)startObserving {
  hasListeners = YES;
}

- (void)stopObserving {
  hasListeners = NO;
}
@end
