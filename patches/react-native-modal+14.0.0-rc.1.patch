diff --git a/node_modules/react-native-modal/dist/modal.js b/node_modules/react-native-modal/dist/modal.js
index 74edee4..ea8ddd8 100644
--- a/node_modules/react-native-modal/dist/modal.js
+++ b/node_modules/react-native-modal/dist/modal.js
@@ -501,12 +501,13 @@ export class ReactNativeModal extends React.Component {
         // TouchableWithoutFeedback
         return (React.createElement(TouchableWithoutFeedback, { onPress: onBackdropPress }, backdropWrapper));
     };
+    stableAnimatedValueZero = new Animated.Value(0);
     render() {
         /* eslint-disable @typescript-eslint/no-unused-vars */
         const { animationIn, animationInTiming, animationOut, animationOutTiming, avoidKeyboard, coverScreen, hasBackdrop, backdropColor, backdropOpacity, backdropTransitionInTiming, backdropTransitionOutTiming, customBackdrop, children, isVisible, onModalShow, onBackButtonPress, useNativeDriver, propagateSwipe, style, ...otherProps } = this.props;
         const { testID, ...containerProps } = otherProps;
         const computedStyle = [
-            { margin: this.getDeviceWidth() * 0.05, transform: [{ translateY: 0 }] },
+            { margin: this.getDeviceWidth() * 0.05, transform: [{ translateY: this.stableAnimatedValueZero }] },
             styles.content,
             style,
         ];
