diff --git a/node_modules/@notifee/react-native/ios/NotifeeCore/NotifeeCoreUtil.m b/node_modules/@notifee/react-native/ios/NotifeeCore/NotifeeCoreUtil.m
index e489445..d27fad2 100644
--- a/node_modules/@notifee/react-native/ios/NotifeeCore/NotifeeCoreUtil.m
+++ b/node_modules/@notifee/react-native/ios/NotifeeCore/NotifeeCoreUtil.m
@@ -571,6 +571,8 @@ + (NSNumber *)convertToTimestamp:(NSDate *)date {
   return [NSNumber numberWithDouble:([date timeIntervalSince1970] * 1000)];
 }
 
+static NSString *const AppboyAPNSDictionaryKey = @"ab";
+
 /**
  * Parse UNNotificationRequest to NSDictionary
  *
@@ -584,6 +586,10 @@ + (NSMutableDictionary *)parseUNNotificationRequest:(UNNotificationRequest *)req
 
   NSDictionary *userInfo = request.content.userInfo;
 
+  if (userInfo != nil && userInfo[AppboyAPNSDictionaryKey] != nil) {
+    return nil;
+  }
+
   // Check for remote details
   if ([request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
     NSMutableDictionary *remote = [NSMutableDictionary dictionary];
