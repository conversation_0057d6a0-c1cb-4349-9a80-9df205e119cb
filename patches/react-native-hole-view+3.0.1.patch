diff --git a/node_modules/react-native-hole-view/android/src/main/java/com/ibitcy/react_native_hole_view/RNHoleView.kt b/node_modules/react-native-hole-view/android/src/main/java/com/ibitcy/react_native_hole_view/RNHoleView.kt
index ec18da3..b8fc3ca 100644
--- a/node_modules/react-native-hole-view/android/src/main/java/com/ibitcy/react_native_hole_view/RNHoleView.kt
+++ b/node_modules/react-native-hole-view/android/src/main/java/com/ibitcy/react_native_hole_view/RNHoleView.kt
@@ -11,6 +11,7 @@ import android.view.View
 import android.view.ViewGroup
 import android.view.animation.*
 import android.view.animation.Interpolator
+import androidx.core.graphics.ColorUtils
 import com.facebook.react.bridge.ReactContext
 import com.facebook.react.uimanager.UIManagerHelper
 import com.facebook.react.uimanager.UIManagerModule
@@ -176,18 +177,29 @@ class RNHoleView(context: Context) : ReactViewGroup(context) {
         mHoles.addAll(holes)
     }
 
-    override fun onDraw(canvas: Canvas) {
-        super.onDraw(canvas)
-        if (mHolesPath != null) {
-            canvas?.drawPath(mHolesPath!!, mHolesPaint)
-        }
-    }
+    private val customBackgroundColor = ColorUtils.setAlphaComponent(
+        Color.parseColor("#000000"),
+        (0.85 * 255).toInt()
+    )
+
+    // override fun onDraw(canvas: Canvas) {
+    //     super.onDraw(canvas)
+    //     if (mHolesPath != null) {
+    //         canvas?.drawPath(mHolesPath!!, mHolesPaint)
+    //     }
+    // }
 
     override fun dispatchDraw(canvas: Canvas) {
-        super.dispatchDraw(canvas)
         if (mHolesPath != null) {
-            canvas?.drawPath(mHolesPath!!, mHolesPaint)
+            val layerId = canvas.saveLayer(
+                0F, 0F,
+                canvas.width.toFloat(), canvas.height.toFloat(), null, Canvas.ALL_SAVE_FLAG
+            )
+            canvas.drawColor(customBackgroundColor)
+            canvas.drawPath(mHolesPath!!, mHolesPaint)
+            canvas.restoreToCount(layerId);
         }
+        super.dispatchDraw(canvas)
     }
 
     private fun isTouchInsideHole(touchX: Int, touchY: Int): Boolean {
