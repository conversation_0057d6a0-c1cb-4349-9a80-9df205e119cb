diff --git a/node_modules/react-native-qualtrics/android/src/main/java/com/qualtrics/reactdigital/RNQualtricsDigitalModule.kt b/node_modules/react-native-qualtrics/android/src/main/java/com/qualtrics/reactdigital/RNQualtricsDigitalModule.kt
index b837660..b8268f9 100644
--- a/node_modules/react-native-qualtrics/android/src/main/java/com/qualtrics/reactdigital/RNQualtricsDigitalModule.kt
+++ b/node_modules/react-native-qualtrics/android/src/main/java/com/qualtrics/reactdigital/RNQualtricsDigitalModule.kt
@@ -5,6 +5,10 @@ import com.facebook.react.bridge.*
 import com.facebook.react.modules.core.DeviceEventManagerModule.RCTDeviceEventEmitter
 import com.qualtrics.reactdigital.RNQualtricsDigitalSpec
 import com.qualtrics.digital.*
+import com.qualtrics.digital.theming.fonts.FontTheme
+import com.qualtrics.digital.theming.prompt.MobileAppPromptTheme
+import com.qualtrics.digital.theming.prompt.ButtonTheme
+import com.qualtrics.digital.QualtricsTheme
 
 class RNQualtricsDigitalModule internal constructor(context: ReactApplicationContext) :
   RNQualtricsDigitalSpec(context) {
@@ -50,6 +54,33 @@ class RNQualtricsDigitalModule internal constructor(context: ReactApplicationCon
       reactApplicationContext.getJSModule(RCTDeviceEventEmitter::class.java)
         .emit(INITIALIZE_PROJECT_EVENT, initializeProjectResult)
     }
+    val appPromptCreativeTheme = MobileAppPromptTheme(
+      backgroundColor = R.color.colorSurface,
+      headlineFont = FontTheme(R.font.lato_bold, 20),
+      headlineTextColor = R.color.colorOnSurface,
+      descriptionFont = FontTheme(R.font.lato_regular,16),
+      descriptionTextColor = R.color.colorOnSurface,
+      closeButtonColor = R.color.colorOnPrimary,
+      closeButtonBackgroundColor = R.color.colorPrimary,
+      // dismiss button
+      buttonOneTheme = ButtonTheme(
+          labelColor = R.color.colorPurple,
+          backgroundColor = R.color.colorOnPrimary,
+          borderColor = R.color.colorOnPrimary,
+          font = FontTheme(R.font.lato_bold,16),
+      ),
+      // feedback button
+      buttonTwoTheme = ButtonTheme(
+          labelColor = R.color.colorOnPrimary,
+          backgroundColor = R.color.colorPurple,
+          borderColor = R.color.colorPurple,
+          font = FontTheme(R.font.lato_bold,16),
+      )
+    )
+    val theme = QualtricsTheme.Builder()
+            .setMobileAppPromptTheme(appPromptCreativeTheme)
+            .build()
+    Qualtrics.instance().setCreativeTheme(theme)
   }
   //endregion
 
diff --git a/node_modules/react-native-qualtrics/android/src/main/res/values/colors.xml b/node_modules/react-native-qualtrics/android/src/main/res/values/colors.xml
new file mode 100644
index 0000000..ecd05e1
--- /dev/null
+++ b/node_modules/react-native-qualtrics/android/src/main/res/values/colors.xml
@@ -0,0 +1,10 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <color name="colorSurface">@color/white</color>
+    <color name="colorOnSurface">#000000</color>
+    <color name="colorPrimary">#7517FF</color>
+    <color name="colorOnPrimary">#FFFFFF</color>
+    <color name="colorSecondary">#F4FF89</color>
+    <color name="colorOnSecondary">#000000</color>
+    <color name="colorPurple">#7A35B0</color>
+</resources>
\ No newline at end of file
