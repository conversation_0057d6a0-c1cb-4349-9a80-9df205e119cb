import {
  LatestHappeningOrientation,
  LatestHappeningType,
} from "app/components/latest-happening/latest-happening.props"
import { ExploreBaseClass } from "../base/explore-base-class"
import { isArray } from "lodash"
import { NavigationTypeEnum } from "./navigation-type"
import { NavigationValueDeepLink } from "app/utils/navigation-helper"

export const HORIZONTAL_TILES_COUNT = 2

class LatestHappeningDetail {
  type: string
  gradientInfo: LatestHappeningType
  orientation: LatestHappeningOrientation
  imageUrl: string
  title: string
  subtitle: string
  campaignCode: string
  packageCode: string
  navigationType: NavigationTypeEnum
  navigationValue: string
  redirect: any
  offerId: string
  sequenceNumber: string
  redirectToShowOnListing: boolean
  tabName: string
  fragmentTitle: string

  constructor(data?: any) {
    this.gradientInfo = data.gradientInfo
    this.orientation = data.orientation
    this.imageUrl = data.imageUrl
    this.title = data.title
    this.subtitle = data.subtitle
    this.campaignCode = data.campaignCode
    this.packageCode = data.packageCode
    this.navigationType = data.navigationType
    this.navigationValue = data.navigationValue
    this.offerId = data.offerId
    this.sequenceNumber = data.sequenceNumber
    this.redirectToShowOnListing = data.redirectToShowOnListing
    this.tabName = data.tabName
    this.fragmentTitle = data.fragmentTitle
  }
}

class LatestHappenings extends ExploreBaseClass {
  data: LatestHappeningDetail[] | []
  hasError: boolean

  constructor(latestHappenings?: LatestHappeningDetail[], hasError?: boolean) {
    super()
    this.data = latestHappenings
    this.hasError = hasError
  }

  public static getLoadingData() {
    return {
      data: [
        new LatestHappeningDetail({
          gradientInfo: LatestHappeningType.loading,
          orientation: LatestHappeningOrientation.tall,
        }),
        [...Array(HORIZONTAL_TILES_COUNT).keys()].map(
          () =>
            new LatestHappeningDetail({
              gradientInfo: LatestHappeningType.loading,
              orientation: LatestHappeningOrientation.short,
            }),
        ),
      ],
    }
  }

  public request() {
    return LatestHappenings.getLoadingData()
  }

  public success(action: any) {
    const latestHappeningsPayload = action.payload
    const { isFeatureFlagON } = action
    const latestHappeningsData = { data: [] }

    isArray(latestHappeningsPayload) &&
      latestHappeningsPayload?.forEach((latestHappening: LatestHappeningDetail) => {
        if (latestHappening.navigationValue === NavigationValueDeepLink.appscapade && !isFeatureFlagON) {
          return
        }
        if (latestHappening.orientation === LatestHappeningOrientation.tall) {
          latestHappeningsData.data.push(latestHappening)
        } else {
          const latestHappeningsLastDataIndex = latestHappeningsData.data.length - 1
          const latestHappeningsLastData = latestHappeningsData.data[latestHappeningsLastDataIndex]
          const isLatestHappenningsDataArray = isArray(latestHappeningsLastData)

          if (!isLatestHappenningsDataArray) {
            latestHappeningsData.data.push([latestHappening])
          } else {
            if (latestHappeningsLastData.length < HORIZONTAL_TILES_COUNT) {
              latestHappeningsLastData.push(latestHappening)
            } else {
              latestHappeningsData.data.push([latestHappening])
            }
          }
        }
      })

    return latestHappeningsData
  }

  public failure() {
    return { data: [], hasError: true }
  }
}

export { LatestHappeningDetail, LatestHappenings }
