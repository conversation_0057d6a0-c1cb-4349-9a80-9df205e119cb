import { Image } from "react-native"
// undocumented but part of react-native; see
// https://github.com/facebook/react-native/issues/5603#issuecomment-297959695

const cache = new Map()

const getImageSizeFromCache = (image) => {
  if (typeof image === "number") {
    return cache.get(image)
  } else {
    return cache.get(image.uri)
  }
}

export const getImageSizeFitWidthFromCache = (image, toWidth, maxHeight) => {
  const size = getImageSizeFromCache(image)
  if (size) {
    const { width, height } = size
    if (!width || !height) return { width: 0, height: 0 }
    const scaledHeight = (toWidth * height) / width
    return {
      width: toWidth,
      height: scaledHeight > maxHeight ? maxHeight : scaledHeight,
    }
  }
  return {}
}

const getImageSize = (image) =>
  new Promise((resolve) => {
    Image.getSize(image.uri, (width, height) => {
      resolve({ width, height })
    })
  })

const getImageSizeMaybeFromCache = async (image) => {
  let size = getImageSizeFromCache(image)
  if (!size) {
    size = await getImageSize(image)
    if (typeof image === "number") {
      cache.set(image, size)
    } else {
      cache.set(image.uri, size)
    }
  }
  return size
}

export const getImageSizeFitWidth = async (image, toWidth, maxHeight) => {
  const { width, height } = await getImageSizeMaybeFromCache(image)
  if (!width || !height) return { width: 0, height: 0 }
  const scaledHeight = (toWidth * height) / width
  return {
    width: toWidth,
    height: scaledHeight > maxHeight ? maxHeight : scaledHeight,
  }
}
