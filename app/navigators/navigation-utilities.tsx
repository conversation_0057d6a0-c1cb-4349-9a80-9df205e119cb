import { NavigationContainerRef, NavigationState, PartialState } from "@react-navigation/native"
import { Text } from "app/elements/text/text"
import React, { useCallback, useContext, useEffect, useRef, useState } from "react"
import {
  BackHandler,
  LayoutAnimation,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TouchableOpacity,
  View,
  ScrollView,
  Animated,
  Pressable,
  Keyboard,
} from "react-native"
import * as styles from "./top-tabs/top-tabs-styles"
import { TopTabProps } from "./top-tabs/top-tabs-props"
import { color } from "app/theme"
import { BottomNavContext } from "./bottom-navigator"

const focusTabIconColor: any = { color: color.palette.purple8F58BE }
const unfocusTabIconColor: any = { color: color.palette.midGrey }

let timeout = null

export const RootNavigation = {
  navigate(name: string) {
    return name // eslint-disable-line no-unused-expressions
  },
  goBack() {
    return true
  }, // eslint-disable-line @typescript-eslint/no-empty-function
  resetRoot(state?: PartialState<NavigationState> | NavigationState) {
    return state
  }, // eslint-disable-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  getRootState(): NavigationState {
    return {} as any
  },
}

export const setRootNavigation = (ref: React.RefObject<NavigationContainerRef>) => {
  for (const method in RootNavigation) {
    RootNavigation[method] = (...args: any) => {
      if (ref.current) {
        return ref.current[method](...args)
      }
    }
  }
}

/**
 * Gets the current screen from any navigation state.
 */
export function getActiveRouteName(state: NavigationState | PartialState<NavigationState>) {
  const route = state.routes[state.index]

  // Found the active route -- return the name
  if (!route?.state) return route?.name

  // Recursive call to deal with nested routers
  return getActiveRouteName(route.state)
}

/**
 * Hook that handles Android back button presses and forwards those on to
 * the navigation or allows exiting the app.
 */
export function useBackButtonHandler(
  ref: React.RefObject<NavigationContainerRef>,
  canExit: (routeName: string) => boolean,
) {
  const canExitRef = useRef(canExit)

  useEffect(() => {
    canExitRef.current = canExit
  }, [canExit])

  useEffect(() => {
    // We'll fire this when the back button is pressed on Android.
    const onBackPress = () => {
      const navigation = ref.current

      if (navigation == null) {
        return false
      }

      // grab the current route
      const routeName = getActiveRouteName(navigation.getRootState())

      // are we allowed to exit?
      if (canExitRef.current(routeName)) {
        // let the system know we've not handled this event
        return false
      }

      // we can't exit, so let's turn this into a back action
      if (navigation.canGoBack()) {
        navigation.goBack()

        return true
      }

      return false
    }

    // Subscribe when we come to life
    const backHandler = BackHandler.addEventListener("hardwareBackPress", onBackPress)

    // Unsubscribe when we're done
    return () => backHandler.remove()
  }, [ref])
}

/**
 * Custom hook for persisting navigation state.
 */
export function useNavigationPersistence(storage: any, persistenceKey: string) {
  const [initialNavigationState, setInitialNavigationState] = useState()
  const [isRestoringNavigationState, setIsRestoringNavigationState] = useState(true)

  const routeNameRef = useRef()
  const onNavigationStateChange = (state) => {
    const previousRouteName = routeNameRef.current
    const currentRouteName = getActiveRouteName(state)

    if (previousRouteName !== currentRouteName) {
      // track screens.
      __DEV__ && console.tron.log(currentRouteName)
    }

    // Save the current route name for later comparision
    routeNameRef.current = currentRouteName

    // Persist state to storage
    storage.save(persistenceKey, state)
  }

  const restoreState = async () => {
    try {
      const state = await storage.load(persistenceKey)
      if (state) setInitialNavigationState(state)
    } finally {
      setIsRestoringNavigationState(false)
    }
  }

  useEffect(() => {
    if (!isRestoringNavigationState) restoreState()
  }, [isRestoringNavigationState])

  return { onNavigationStateChange, restoreState, initialNavigationState }
}

export const useHandleScroll = () => {
  const isTabVisible = useRef(true)
  const scrollOffset = useRef(0)
  const scrollDirection = useRef<"up" | "down">()

  const {hideBottombar, showBottombar} = useContext(BottomNavContext)

  const handleScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const CustomLayoutLinear = {
      duration: 100,
      create: {
        type: LayoutAnimation.Types.linear,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.linear,
        property: LayoutAnimation.Properties.opacity,
      },
      delete: {
        type: LayoutAnimation.Types.linear,
        property: LayoutAnimation.Properties.opacity,
      },
    }
    // Check if the user is scrolling up or down by confronting the new scroll position with your own one
    const currentOffset = event.nativeEvent.contentOffset.y
    const direction = currentOffset > 0 && currentOffset > scrollOffset.current ? "down" : "up"
    // If the user is scrolling down (and the action-button is still visible) hide it
    const isActionButtonVisible = direction === "up"
    if (isActionButtonVisible !== isTabVisible.current) {
      LayoutAnimation.configureNext(CustomLayoutLinear)
      isTabVisible.current = isActionButtonVisible
    }
    // Update your scroll position
    scrollOffset.current = currentOffset
    scrollDirection.current = direction

    if(isTabVisible.current){
      showBottombar()
    } else{
      hideBottombar()
    }
  }, [hideBottombar, showBottombar])
  return { handleScroll, isTabVisible, scrollDirection }
}

export function TopTabNavBar(topTabProps: TopTabProps) {
  const { state, descriptors, navigation } = topTabProps.props
  const scrollTabRef = useRef(null)
  const {
    topTabParentStyle,
    topTabTouchableOpacityStyle,
    topTabLabelsPresets = "tabsSmall",
    topTabActiveLabelStyle = styles.topTabActiveLabelStyle,
    topTabInActiveLabelStyle = styles.topTabInActiveLabelStyle,
    topTabActiveIndicatorStyle = styles.topTabActiveIndicatorStyle,
    isCenter = false,
    scrollEnabled = true,
    shouldKeyboardBeDismissed,
    scrollToEndIndex = 5,
  } = topTabProps

  const onPressTabBarContainer = () => {
    if (shouldKeyboardBeDismissed) {
      Keyboard.dismiss()
    }
  }

  useEffect(() => {
    // hard code condition. will fix flexible next code
    if (state?.index > scrollToEndIndex && state?.routeNames?.length <= 8) {
      timeout = setTimeout(() => {
        scrollTabRef?.current?.scrollToEnd({ animated: true })
      }, 500)
    }
  }, [state])

  useEffect(() => {
    return () => clearTimeout(timeout)
  }, [])

  return (
    <Pressable onPress={onPressTabBarContainer} style={topTabParentStyle || styles.topTabParentStyle}>
      <ScrollView
        ref={scrollTabRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        testID={`NavigationUtilities_ScrollView`}
        accessibilityLabel={`NavigationUtilities_ScrollView`}
        contentContainerStyle={isCenter ? styles.centerElement : {}}
        scrollEnabled={scrollEnabled}
      >
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key]
          let label: string
          if (options.tabBarLabel) {
            label = options.tabBarLabel
          } else {
            if (options.title !== undefined) {
              label = options.title
            } else {
              label = route.name
            }
          }
          const isFocused = state.index === index

          const onPress = () => {
            tabItemOnPress(navigation, route, isFocused)
          }

          const onLongPress = () => {
            tabItemOnLongPress(navigation, route)
          }

          return (
            <TouchableOpacity
              accessibilityRole="button"
              // accessibilityTraits={isFocused ? ["selected"] : []}
              accessibilityLabel={options.tabBarAccessibilityLabel || `${label}__BackButton`}
              testID={options.tabBarTestID || `${label}__BackButton`}
              onPress={onPress}
              onLongPress={onLongPress}
              style={topTabTouchableOpacityStyle || styles.topTabTouchableOpacityStyle}
              key={index}
            >
              <View style={styles.topTabLabelTabIndicatorContainerStyle}>
                <Text
                  style={isFocused ? topTabActiveLabelStyle : topTabInActiveLabelStyle}
                  preset={topTabLabelsPresets}
                >
                  {label}
                </Text>
                <View style={isFocused ? topTabActiveIndicatorStyle : null}></View>
              </View>
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </Pressable>
  )
}

function tabItemOnPress(navigation, route, isFocused) {
  const event = navigation?.emit({
    type: "tabPress",
    target: route?.key,
  })

  if (!isFocused && !event.defaultPrevented) {
    navigation?.navigate(route?.name)
  }
}

function tabItemOnLongPress(navigation, route) {
  navigation?.emit({
    type: "tabLongPress",
    target: route?.key,
  })
}

export function TabNavBarCustom(tabProps) {
  const { state, descriptors } = tabProps.props
  const {
    topTabParentStyle,
    topTabTouchableOpacityStyle,
    topTabLabelsPresets = "tabsSmall",
    topTabActiveLabelStyle = styles.topTabActiveLabelStyle,
    topTabInActiveLabelStyle = styles.topTabInActiveLabelStyle,
    topTabActiveIndicatorStyle = styles.topTabActiveIndicatorStyle,
    isCenter = false,
    scrollEnabled = true,
    onPress,
    onLongPress = () => {
      return
    },
    tabIndexInit = 0,
  } = tabProps

  const [tabIndex, tabClick] = useState(null)

  React.useEffect(() => {
    tabClick(tabIndexInit)
  }, [tabIndexInit])

  return (
    <View style={topTabParentStyle || styles.topTabParentStyle}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        testID={`NavigationUtilities_ScrollView`}
        accessibilityLabel={`NavigationUtilities_ScrollView`}
        contentContainerStyle={isCenter ? styles.centerElement : {}}
        scrollEnabled={scrollEnabled}
      >
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key]
          let label: string
          if (options.tabBarLabel) {
            label = options.tabBarLabel
          } else {
            if (options.title !== undefined) {
              label = options.title
            } else {
              label = route.name
            }
          }
          const isFocused =
            (tabIndex !== null && tabIndex === index) ||
            (tabIndex === null && state.index === index)

          let LeftIcon
          if (route?.icon) {
            if (route?.icon?.active?.leftSVGIcon && isFocused) {
              LeftIcon = route?.icon?.active?.leftSVGIcon
            }
            if (route?.icon?.inActive?.leftSVGIcon && !isFocused) {
              LeftIcon = route?.icon?.inActive?.leftSVGIcon
            }
          }
          return (
            <TouchableOpacity
              accessibilityRole="button"
              // accessibilityTraits={isFocused ? ["selected"] : []}
              accessibilityLabel={options.tabBarAccessibilityLabel || `${label}__BackButton`}
              testID={options.tabBarTestID || `${label}__BackButton`}
              onPress={() => {
                tabClick(index)
                onPress({ route, index })
              }}
              onLongPress={() => onLongPress({ route, index })}
              style={topTabTouchableOpacityStyle || styles.topTabTouchableOpacityStyle}
              key={index}
            >
              <View style={styles.topTabLabelTabIndicatorContainerStyle}>
                <View style={styles.topTabContentView}>
                  {LeftIcon && <LeftIcon height={20} width={20} style={styles.leftIconStyles} />}
                  <Text
                    style={isFocused ? topTabActiveLabelStyle : topTabInActiveLabelStyle}
                    preset={topTabLabelsPresets}
                  >
                    {label}
                  </Text>
                </View>
                <View style={isFocused ? topTabActiveIndicatorStyle : null}></View>
              </View>
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </View>
  )
}

export function TabNavBarWithTopIcon(topTabProps: TopTabProps) {
  const { state, descriptors, navigation } = topTabProps.props
  const scrollTabRef = useRef(null)
  const {
    topTabParentStyle,
    topTabTouchableOpacityStyle,
    topTabLabelsPresets = "tabsSmall",
    topTabActiveLabelStyle = styles.topTabActiveLabelStyle,
    topTabInActiveLabelStyle = styles.topTabInActiveLabelStyle,
    topTabActiveIndicatorStyle = styles.topTabActiveIndicatorStyle,
    topIconActiveStyle = focusTabIconColor,
    topIconInActiveStyle = unfocusTabIconColor,
    topTabInActiveIndicatorStyle,
    isCenter = false,
    scrollEnabled = true,
  } = topTabProps
  return (
    <View style={topTabParentStyle || styles.topTabParentStyle}>
      <ScrollView
        ref={scrollTabRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        testID={`NavigationUtilities_ScrollView`}
        accessibilityLabel={`NavigationUtilities_ScrollView`}
        contentContainerStyle={isCenter ? styles.centerElement : {}}
        scrollEnabled={scrollEnabled}
      >
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key]
          let label: string
          let TabIcon
          if (options.tabBarLabel) {
            label = options.tabBarLabel
          } else {
            if (options.title !== undefined) {
              label = options.title
            } else {
              label = route.name
            }
          }
          if (options.tabBarIcon) {
            TabIcon = options.tabBarIcon
          }
          const isFocused = state.index === index

          const onPress = () => {
            tabItemOnPress(navigation, route, isFocused)
          }

          const onLongPress = () => {
            tabItemOnLongPress(navigation, route)
          }

          return (
            <TouchableOpacity
              accessibilityRole="button"
              accessibilityLabel={options.tabBarAccessibilityLabel || `${label}__TabButton`}
              testID={options.tabBarTestID || `${label}__TabButton`}
              onPress={onPress}
              onLongPress={onLongPress}
              style={topTabTouchableOpacityStyle || styles.topTabTouchableOpacityStyle}
              key={index}
            >
              <View
                style={styles.tabContentWrap}
              >
                <View style={styles.iConAndTextContainer}>
                  <TabIcon
                    fill="currentColor"
                    style={isFocused ? topIconActiveStyle : topIconInActiveStyle}
                  />
                  <Text
                    style={isFocused ? topTabActiveLabelStyle : topTabInActiveLabelStyle}
                    preset={topTabLabelsPresets}
                    text={label}
                  />
                </View>

                <View
                  style={isFocused ? topTabActiveIndicatorStyle : topTabInActiveIndicatorStyle}
                />
              </View>
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </View>
  )
}

export function TopTabNavBarFlightListing(topTabProps: TopTabProps) {
  const { state, descriptors, navigation } = topTabProps.props
  const scrollTabRef = useRef(null)
  const {
    topTabParentStyle,
    topTabTouchableOpacityStyle,
    topTabLabelsPresets = "tabsSmall",
    topTabActiveLabelStyle = styles.topTabActiveLabelStyle,
    topTabInActiveLabelStyle = styles.topTabInActiveLabelStyle,
    topTabActiveIndicatorStyle = styles.topTabActiveIndicatorStyle,
    isCenter = false,
    scrollEnabled = true,
  } = topTabProps

  return (
    <View style={topTabParentStyle || styles.topTabParentStyle}>
      <ScrollView
        ref={scrollTabRef}
        horizontal
        showsHorizontalScrollIndicator={false}
        testID={`NavigationUtilities_ScrollView`}
        accessibilityLabel={`NavigationUtilities_ScrollView`}
        contentContainerStyle={isCenter ? styles.centerElement : {}}
        scrollEnabled={scrollEnabled}
      >
        {state.routes.map((route, index) => {
          const { options } = descriptors[route.key]
          let label: string
          if (options.tabBarLabel) {
            label = options.tabBarLabel
          } else {
            if (options.title !== undefined) {
              label = options.title
            } else {
              label = route.name
            }
          }
          const isFocused = state.index === index

          let LeftIcon
          if (options?.tabBarIcon) {
            LeftIcon = options?.tabBarIcon
          }

          const onPress = () => {
            tabItemOnPress(navigation, route, isFocused)
          }

          const onLongPress = () => {
            tabItemOnLongPress(navigation, route)
          }

          return (
            <TouchableOpacity
              accessibilityRole="button"
              accessibilityLabel={options.tabBarAccessibilityLabel || `${label}__BackButton`}
              testID={options.tabBarTestID || `${label}__BackButton`}
              onPress={onPress}
              onLongPress={onLongPress}
              style={topTabTouchableOpacityStyle || styles.topTabTouchableOpacityStyle}
              key={index}
            >
              <View style={styles.topTabLabelTabIndicatorContainerStyle}>
                <View style={styles.topTabFlightListingContentView}>
                  {LeftIcon && (
                    <LeftIcon
                      height={20}
                      width={20}
                      fill="currentColor"
                      style={
                        isFocused
                          ? styles.leftFlightIconActiveStyles
                          : styles.leftFlightIconInActiveStyles
                      }
                    />
                  )}
                  <Text
                    style={isFocused ? topTabActiveLabelStyle : topTabInActiveLabelStyle}
                    preset={topTabLabelsPresets}
                  >
                    {label}
                  </Text>
                </View>
                <View style={isFocused ? topTabActiveIndicatorStyle : null}></View>
              </View>
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </View>
  )
}

export const TabNavBarWithTopIconAnimation = (topTabProps: TopTabProps) => {
  const { state, descriptors, navigation } = topTabProps.props
  const {
    topTabParentStyle,
    topTabTouchableOpacityStyle,
    topTabLabelsPresets = "tabsSmall",
    topTabActiveLabelStyle = styles.topTabActiveLabelStyle,
    topTabInActiveLabelStyle = styles.topTabInActiveLabelStyle,
    topTabActiveIndicatorStyle = styles.topTabActiveIndicatorStyle,
    topIconActiveStyle = focusTabIconColor,
    topIconInActiveStyle = unfocusTabIconColor,
    topTabInActiveIndicatorStyle,
    topTabLabelStyleAnimation,
    topIconAnimationStyle,
    callBackPress
  } = topTabProps

  return (
    <Animated.View style={topTabParentStyle || styles.topTabParentStyle}>
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key]
        let label: string
        let TabIcon
        if (options.tabBarLabel) {
          label = options.tabBarLabel
        } else {
          if (options.title !== undefined) {
            label = options.title
          } else {
            label = route.name
          }
        }
        if (options.tabBarIcon) {
          TabIcon = options.tabBarIcon
        }
        const isFocused = state.index === index

        const onPress = () => {
          tabItemOnPress(navigation, route, isFocused)
          if(callBackPress){
            callBackPress(route?.name, isFocused)
          }
        }

        const onLongPress = () => {
          tabItemOnLongPress(navigation, route)
          if(callBackPress){
            callBackPress(route?.name, isFocused)
          }
        }

        return (
          <TouchableOpacity
            accessibilityRole="button"
            accessibilityLabel={options.tabBarAccessibilityLabel || `${label}__TabButton`}
            testID={options.tabBarTestID || `${label}__TabButton`}
            onPress={onPress}
            onLongPress={onLongPress}
            style={topTabTouchableOpacityStyle || styles.topTabTouchableOpacityStyle}
            key={index}
          >
            <Animated.View style={styles.tabContentWrap}>
              <Animated.View style={styles.iConAndTextContainer}>
                <Animated.View style={topIconAnimationStyle}>
                  <TabIcon
                    fill="currentColor"
                    style={isFocused ? topIconActiveStyle : topIconInActiveStyle}
                  />
                </Animated.View>
                <Animated.View style={topTabLabelStyleAnimation}>
                  <Text
                    style={isFocused ? topTabActiveLabelStyle : topTabInActiveLabelStyle}
                    preset={topTabLabelsPresets}
                    text={label}
                  />
                </Animated.View>
              </Animated.View>
              <Animated.View
                style={isFocused ? topTabActiveIndicatorStyle : topTabInActiveIndicatorStyle}
              />
            </Animated.View>
          </TouchableOpacity>
        )
      })}
    </Animated.View>
  )
}
