import { createStackNavigator, StackNavigationOptions, TransitionPresets } from "@react-navigation/stack"
import React, { useEffect, useState, useRef, useContext } from "react"
import { useDispatch, useSelector } from "react-redux"
import WelcomeScreen from "../screens/welcome/welcome-screen"
import SearchScreen from "../screens/search/search-screen"
import SearchScreenV2 from "../screens/search-v2/search-screen"
import { WebViewComponent } from "../components/webview/web-view"
import { PlayPassWebView } from "../screens/explore-screen/playpass-webview/playpass-webview"
import { TouchableOpacity, ViewStyle, AppState, Linking, View, Dimensions, Platform, BackHandler, InteractionManager } from "react-native"
import dynamicLinks from "@react-native-firebase/dynamic-links"
import { get, isEmpty } from "lodash"
import { color, shadow } from "app/theme"
import { presets } from "app/elements/text/text.presets"
import {
  <PERSON>e<PERSON><PERSON><PERSON>,
  ShopFilter,
  RestaurantDetailsScreen,
  ShopDetailsScreen,
  FlightResultLandingScreen,
  DineShopOfferDetailsScreen,
  EventDetailsScreen,
  FlightListing,
} from "app/screens"
import { ArrowLeft, ClosePopupPurple, CrossPurple, CrossBlue, ArrowLeftGray } from "ichangi-fe/assets/icons"
import { translate } from "app/i18n"
import NativeAuthReducer, { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import {
  AppConfigPermissionTypes,
  API_FAILURE_KIND,
  NavigationConstants,
  ScreenListShowPopupRating,
  TrackingScreenName,
  SOURCE_SYSTEM,
  APP_DEEPLINKS,
  StateCode
} from "app/utils/constants"
import MobileGSTRefund from "app/screens/mobile-gst-refund"
import AirportInfoL2 from "app/screens/airport-Info-L2-page"
import BottomTabNavigator from "./bottom-navigator"
import { PaymentConfirmationScreen } from "app/screens/explore-screen/payment-confirmation/payment-confirmation"
import ScanCodeScreen from "app/screens/scan-code"
import { generateScreenOptions } from "./foryou-navigator"
import { ChangiRewardsDetailScreen } from "app/screens/changi-rewards-detail/changi-rewards-detail"
import OrderDetailPage from "app/screens/order-detail/order-detail"
import DetailPerkScreen from "app/screens/wallet/detail-perk/detail-perk"
import { RedemptionCatalogueScreen } from "app/screens/redemption-catalogue-screen/redemption-catalogue-screen-wrapper"
import CarParkScreen from "app/screens/car-park/car-park"
import {
  getGameCodeFromDeepLink,
  getParamsCarPassFromDeepLink,
  getTargetFromModeOfDeepLink,
  MODE_CODE,
  onCollectUtmTracking,
  triggerAdobeCampaignTrackingCodeUTM,
} from "app/helpers/deeplink/deeplink-parameter"
import AirportLandingCreator, { AirportLandingSelectors } from "app/redux/airportLandingRedux"
import { ProfileScreen } from "app/screens/profile/profile-screen"
import { MapScreen } from "app/screens/map"
import { AttractionPageView } from "app/screens/explore-screen/explore-container/attraction-page-view"
import { load, save } from "app/utils/storage"
import { StorageKey } from "app/utils/storage/storage-key"
import VersionInfo from "react-native-version-info"
import {
  useGeneratePlayPassUrl,
  usePrevious,
  usePreviousRouteName,
  useHandleFDL,
  getCurrentScreenActive,
  getPreviousScreen,
} from "app/utils/screen-hook"
import AdvisoryNotificationPopup from "app/sections/notification-sections/advisory-notification-popup"
import { handleCondition, ifAllTrue, ifOneTrue, simpleCondition } from "app/utils"
import { AdobeTagName, AdobeValueByTagName, getExperienceCloudId, trackAction } from "app/services/adobe"
import NetInfo from "@react-native-community/netinfo"
import { ProfileSelectors } from "app/redux/profileRedux"
import { closeChangiPay, useCPay } from "app/helpers/changipay"
import { getDeepLink, getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import Toilets from "app/screens/toilets"
import { BAGGAGE_TRACKER_CONTEXT } from "app/services/context/baggage-tracker"
import { ChangiMillionaireWebView } from "app/screens/changi-millionaire/changi-millionaire"
import { handleModeCarPassCsmFDL } from "app/utils/navigation-helper"
import { LoadingOverlay } from "app/components/loading-modal"
import { useGetConfigurationPermissionHelper } from "app/utils/get-configuration-permission"
import { ExploreSelectors } from "app/redux/exploreRedux"
import { ForcedUpdateDialog } from "app/components/forced-update-dialog"
import DineFilterResults from "app/screens/dine-shop/filter-results/dine-filter-results"
import ShopFilterResults from "app/screens/dine-shop/filter-results/shop-filter-results"
import StaffPerkListing from "app/screens/staff-perk-listing/staff-perk-listing"
import YourRewardScreen from "app/screens/your-reward/your-reward"
import OfferDetailScreen from "app/screens/offer-detail/offer-detail"
import { store } from "app/redux/store"
import { handleShowPopupRating, resetInactivityTimeout, trackingShowRatingPopup, trackingShowRatingPopupExploreScreen } from "app/utils/screen-helper"
import { PageConfigSelectors } from "app/redux/pageConfigRedux"
import AtomMap from "app/screens/changi-maps"
import AppscapadeScanBoadingPassScreen from "app/screens/appscapade-scan-boarding-pass"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import SystemActions from "app/redux/systemRedux"
import FlightDetailsContext from "app/screens/fly/flights/flight-details/flight-details-context"
import YourRewardRedeemSuccessScreen from "app/screens/your-reward-redeem-success"
import { RedeemRewardSuccessScreen } from "app/screens/redeem-reward-success"
import {
  childReservation,
  filterState,
  itemReservation,
  resultFilterTitles,
} from "app/sections/dine-reservation/dine-reservation"
import DineCreators from "app/redux/dineRedux"
import MonarchPrivilegesScreen from "app/screens/monarch-privileges/monarch-privileges"
// import FlySubscription from "app/screens/fly/fly-subscription"
import { getFeatureFlagInit, isFlagOnCondition, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import DeviceInfo from "react-native-device-info"
import { RedeemRewardDetailScreenV2 } from "app/screens/redeem-reward-detail/redeem-reward-detail-v2"
import { AccountContext } from "app/services/context/account"
import ForYouActions from "app/redux/forYouRedux"
import NativeLoginScreen from "app/screens/login/native-login-screen"
import SessionExpiredPopup from "app/components/session-expired-popup/session-expired-popup"
import { getActiveRouteName } from "./navigation-utilities"
import VouchersPrizesRedemptionsPage from "app/screens/vouchers-prizes-redemptions/vouchers-prizes-redemptions"
import playPassBookingDetail from "app/screens/play-pass-booking-detail"
import SaveFlightsScreen from "app/screens/saved-flights"
import BookingsOrdersScreen from "app/screens/bookings-orders/bookings-orders"
import OrderDetailsScreen from "app/screens/bookings-orders/order-details/order-details"
import ChangiRewardsPrivilegesScreen from "app/screens/changi-rewards-privileges"
import { AppRatingSession, useAppRatingOnSaveFlight, useTriggerAppRating } from "app/hooks/useAppRating"
import { PanResponderContext } from "app/services/context/pan-responder"
import { checkLoginState } from "app/utils/authentication"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import _get from "lodash/get"
import RetroClaimsTakePhotoScreen from "app/screens/retro-claims-take-photo-screen/retro-claims-take-photo-screen"
import { setIsShowingSessionPopup } from "app/utils/storage/mmkv-storage"
import Transaction from "app/screens/transactions/transactions"
import { goToRetroClaims } from "app/hooks/useRetroClaims"
import { checkSurvey } from "app/services/survey/qualtrics"
import { env } from "app/config/env-params"
import L2AnnouncementDetails from "app/screens/notifications/l2-announcement-details"
import retroClaimsNotificationsScreen from "app/screens/retro-claims-notifications-screen"
import { NotificationDetailScreen, NotificationScreen } from "app/screens/notifications"
import ChangiEcardController from "app/components/changi-ecard/changi-ecard-controler"
import ChangiRewardsActions from "app/redux/changiRewardsRedux"
import { StackActions } from "@react-navigation/native"
import { MytravelSelectors } from "app/redux/mytravelRedux"
import PasswordBiometricsScreen from "app/screens/password-biometrics/password-biometrics"
import useL3BaggagePrediction from "app/hooks/useL3BaggagePrediction"
import L1AdvisoryDetails from "app/screens/notifications/l1-advisory-details"
import L1AdvisoryOverlay from "app/components/l1-advisory/l1-advisory"
import parse from "url-parse"
import SearchResultScreen from "app/screens/search-v2/search-result/search-result-screen"
import CreditsScreen from "app/screens/credits/credits"
import SearchFlightsV2ResultScreen from "app/screens/search-v2/search-result/search-flights-result"
import { dtManualActionEvent, FE_LOG_PREFIX } from "app/services/firebase"
import { ForcedUpdateDialogCpay } from "app/components/forced-update-dialog-cpay"
import { FlightSearchModal } from "app/screens/search-v2/search-result/search-flights-result/flight-search-modal"
import ParkingLandingScreen from "app/screens/parking-landing/parking-landing"
import FastImage from "react-native-fast-image"
import ParkingLandingBenefitScreen from "app/screens/parking-landing-benefit/parking-landing-benefit"
import { SubmitSuggestionFeedbackScreen } from "app/screens/submit-suggestion-feedback/submit-suggestion-feedback-screen"
import { putUserDeviceTokenRequest } from "app/services/api/profile"
import { useAppsFlyerStore } from "app/zustand/appsflyers"
import ParkingBenefitsMonarchScreen from "app/screens/parking-benefits-monarch/parking-benefits-monarch"
import { actionLogout } from "app/screens/login/login-helper"
import FAQLanding from "app/screens/FAQ-landing"
import DriveOnboardingScreen from "app/screens/drive-onboarding"
import PromoCodesScreen from "app/screens/promo-codes/promo-codes"
import DineShopDirectory from "app/screens/dine-shop-directory"
import DealsPromosListing from "app/screens/deals-promos-listing/deals-promos-listing"
import { FacilitiesServices } from "app/screens/fly-v2/facilities-services"
import PDFView from "app/screens/pdfview"

export type PrimaryParamList = {
  welcome: undefined
  webview: undefined
  playpassWebview: undefined
  restaurantDetailScreen: undefined
  dineShopOfferDetailsScreen: undefined
  dineFilter: undefined
  shopDetailsScreen: undefined
  shopFilter: undefined
  bottomNavigation: undefined
  dineFilterResults: undefined
  shopFilterResults: undefined
  changiRewardsDetailScreen: undefined
  changiRewardsEcardScreen: undefined
  flightResultLandingScreen: undefined
  settingScreen: undefined
  profileScreen: undefined
  redemptionCatalogueScreen: undefined
  redeemRewardDetailScreen: undefined
  eventDetailsScreen: undefined
  flightDetails: undefined
  search: undefined
  searchResult: undefined
  paymentConfirmationScreen: undefined
  mobileGSTRefund: undefined
  airportInfoL2: undefined
  scanCode: undefined
  orderDetailPage: undefined
  mapScreen: undefined
  toilets: undefined
  searchFlightsV2Result: undefined
  facilitiesServices: undefined
}

const AppStateType = {
  active: "active",
  inactive: "inactive",
  background: "background",
}

const cardStyle: ViewStyle = {
  backgroundColor: color.palette.whiteGrey,
}

const headerRight: ViewStyle = {
  marginRight: 24,
}
const headerLeft: ViewStyle = {
  marginLeft: 16,
}
const renderLeft = (navigation) => {
  return (
    <TouchableOpacity style={headerLeft} onPress={() => navigation.goBack()}>
      <ArrowLeft />
    </TouchableOpacity>
  )
}

const renderRight = (navigation) => {
  return (
    <TouchableOpacity style={headerRight} onPress={() => navigation.goBack()}>
      <ClosePopupPurple />
    </TouchableOpacity>
  )
}

const stackHeaderStyle: ViewStyle = {
  ...shadow.filterHeaderShadow,
}

export enum InstallAppStatus {
  NEW_INSTALL = "NEW_INSTALL",
  INSTALL_FROM_UPGRADE = "INSTALL_FROM_UPGRADE",
  INSTALLED = "INSTALLED",
  NONE = "NONE",
}

const redemptionCatalogueOptions = generateScreenOptions({
  headerTitleTx: "changiRewardsDetail.rewardCatalogue",
})

const profileOptions = generateScreenOptions({
  headerTitleTx: "profile.header",
})

const mapOptions = generateScreenOptions({
  headerShown: true,
})

const toiletsOptions = generateScreenOptions({
  headerTitleTx: "toilets.header",
})

const YourRewardRedeemSuccessOptions = generateScreenOptions({
  headerTitleTx: "yourRewardRedeemSuccess.header",
  headerLeftShown: false,
  headerRightShown: true,
  onHeaderRightPress: (navigation) => navigation.pop(2),
  rightIcon: CrossPurple,
  gestureEnabled: false,
})
const OfferDetailOptions = generateScreenOptions({
  headerTitleTx: "offerDetail.header",
})

const changiMillionaireOptions = generateScreenOptions({
  headerTitleTx: "changiMillionaire.header",
  headerShown: false,
})
const staffPerkListingOptions = generateScreenOptions({
  headerTitleTx: "staffPerkListing.header",
  headerShown: false,
  gestureEnabled: false,
})
const atomMapOptions = generateScreenOptions({
  headerTitleTx: "atomMap.header",
  headerShown: false,
})

const playPassBookingDetailOptions = generateScreenOptions({
  headerShown: false,
})

const saveFlightsScreenOptions = generateScreenOptions({
  headerShown: false,
})

const searchV2Options = generateScreenOptions({
  headerTitleTx: "searchV2.header",
  headerStyle: {
    ...shadow.noShadow,
    backgroundColor: color.palette.almostWhiteGrey
  },
  headerShadowVisible: false,
})

const creditsScreenOptions = generateScreenOptions({
  headerTitleTx: "creditsScreen.header",
  headerStyle: {
    ...shadow.noShadow,
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
  },
  headerShadowVisible: false,
  leftIcon: ArrowLeftGray,
})

const ParkingLandingBenefitOptions = generateScreenOptions({
  headerTitleTx: "parkingLandingBenefit.headerTitle",
  headerStyle: {
    ...shadow.noShadow,
  },
  leftIcon: ArrowLeftGray,
})

const FAQLandingOptions = generateScreenOptions({
  headerTitleTx: "faqLanding.titleHeader",
  headerStyle: {
    ...shadow.noShadow,
  },
  leftIcon: ArrowLeftGray,
})

const submitSuggestionFeedbackOptions = generateScreenOptions({
  headerTitleTx: "submitSuggestionFeedback.header",
})

const redeemRewardDetailOptions: StackNavigationOptions = {
  headerShown: false,
  gestureEnabled: false,
}

const DriveOnboardingOptions: StackNavigationOptions = {
  headerShown: false,
}

const promoCodesOptions = generateScreenOptions({
  headerTitleTx: "promoCodes.title",
  headerStyle: {
    ...shadow.noShadow,
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
  },
  leftIcon: ArrowLeftGray,
  onHeaderLeftPress(navigation, route) {
    if(route?.params?.backPressToVouchersPrizesRedemptionsScreen){
      navigation?.popToTop()
      navigation?.navigate(NavigationConstants.redemptionCatalogueScreen)
    } else {
      navigation?.goBack()
    }
  },
})

const DineShopDirectoryOptions = generateScreenOptions({
  headerShown: false,
})

const facilitiesServicesOptions = generateScreenOptions({
  headerShown: false,
})


const pdfViewOptions = generateScreenOptions({
  headerTitle: "PDF",
  headerStyle: {
    ...shadow.noShadow,
    borderBottomColor: color.palette.lighterGrey,
    borderBottomWidth: 1,
  },
  leftIcon: ArrowLeftGray,
})


const LIST_APP_STATUS_NEED_SHOW_WELCOME = [
  InstallAppStatus.NEW_INSTALL,
  InstallAppStatus.INSTALL_FROM_UPGRADE,
]

const compareVersion = (a, b) =>
  a.localeCompare(b, undefined, { numeric: true, sensitivity: "base" })

const handleGetGameChangi = (deepLinkTrigger, myTravelFlightDetails, profilePayload, dispatch) => {
  const gameCode = get(deepLinkTrigger, "bingoGame.gameCode", "")
  const qrCodeId = get(deepLinkTrigger, "bingoGame.qrCodeId", "")

  const flightNo = myTravelFlightDetails?.length
    ? myTravelFlightDetails[myTravelFlightDetails.length - 1]?.flightNumber
    : ""
  dispatch(
    AirportLandingCreator.getChangiGameUrlRequest(
      qrCodeId,
      flightNo,
      gameCode,
      profilePayload?.cardNo,
    ),
  )
}

const redeemRewardSuccessHandleRightButtonPress = (previousRouteName, newNavigation) => {
  if (previousRouteName === NavigationConstants.redemptionCatalogueScreen) {
    newNavigation.goBack()
  } else {
    newNavigation.replace(NavigationConstants.redemptionCatalogueScreen)
  }
}

const handleValidateToken = ({ dispatch, isLoggedIn, isTokenInvalid }) => {
  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      isLoggedIn && isTokenInvalid && dispatch(NativeAuthReducer.nativeAuthTokenVerifyRequest())
    }
  }
  checkInternet()
}

const resetUserAuthData = async({ dispatch }) => {
  await actionLogout()
  putUserDeviceTokenRequest()
}

const getValueSendAA = async () => {
  const isFirstOpenApp = await load(StorageKey.isFirstOpenApp)
  return isFirstOpenApp
}

const getValueTracking = async (deepLink: boolean) => {
  if (deepLink) {
    return "Deep Links"
  }
  const isFirstOpenApp = await getValueSendAA()
  if (!isFirstOpenApp) {
    trackAction(AdobeTagName.cappFirstTimeVisit, {
      [AdobeTagName.cappFirstTimeVisit]: "1",
      [AdobeTagName.cappVisitSource]: "Store Organic",
    })
    save(StorageKey.isFirstOpenApp, true)
    return ""
  }
  return "Organic"
}

const trackActionWithCondition = (action, contextData, condition) => {
  if (condition) {
    trackAction(action, contextData)
  }
}

const webViewOptionsRender = () => ({
  headerShown: false,
  headerStyle: {
    height: 0,
  },
  headerLeft: () => null,
  headerRight: () => null,
})

const filterOptionsRender = (navigation) => ({
  headerShown: true,
  headerStyle: stackHeaderStyle,
  headerLeft: () => null,
  headerTitle: "Filters",
  cardStyle: cardStyle,
  headerTitleStyle: {
    ...presets.subTitleBold,
  },
  headerRight: () => renderRight(navigation),
})

const flightResultLandingScreenOptionsRender = (filterOptions, navigation) => ({
  ...filterOptions,
  headerStyle: {
    ...shadow.noShadow,
  },
  headerShadowVisible: false,
  headerTitle: translate("flightResultScreenLanding.flightResults"),
  headerLeft: () => renderLeft(navigation),
  headerRight: null,
})

const changiRewardOptionsRender = (filterOptions, navigation) => ({
  ...filterOptions,
  headerStyle: {
    ...shadow.noShadow,
  },
  headerShadowVisible: false,
  headerTitle: translate("changiRewardsDetail.header"),
  headerLeft: () => renderLeft(navigation),
  headerRight: null,
})

const useHandleOneLink = (
  { isDeeplink, isLoggedIn },
  {
    navigation,
    openChangiPayByDeepLink,
    handleDynamicLinkNotLoggedInFromCarPassAndBingo,
    handleDeeplinkFromCarpassAndBingo,
    BAGGAGE_HANDLERS,
    setHandleChangiEventFromFDLs,
    dispatch,
    fromInactive,
    handleOpenL3BaggagePrediction,
  },
) => {
  const { deepLinkData, setDeepLinkData } = useAppsFlyerStore()
  const isHandledRef = useRef(null)

  const handleOneLink = () => {
    isHandledRef.current = true
    setTimeout(() => {
      __handleDynamicLink(
        {
          link: {
            url: deepLinkData?.af_dp,
          },
          isDeeplink,
          isLoggedIn,
        },
        {
          navigation,
          openChangiPayByDeepLink,
          handleDynamicLinkNotLoggedInFromCarPassAndBingo,
          handleDeeplinkFromCarpassAndBingo,
          BAGGAGE_HANDLERS,
          setHandleChangiEventFromFDLs,
          dispatch,
          fromInactive,
          handleOpenL3BaggagePrediction,
        },
      )
    }, 500)
    
    setTimeout(() => {
      isHandledRef.current = false
      setDeepLinkData(null)
    }, 3000)
  }

  useEffect(() => {
    if (!isEmpty(deepLinkData?.af_dp) && !isHandledRef.current) {
      handleOneLink()
    }
  }, [deepLinkData?.af_dp, isHandledRef.current])
}

const useHandleDeepLink = ({
  isDeeplink,
  isLoggedIn,
}, {
  navigation,
  openChangiPayByDeepLink,
  setHandleChangiEventFromFDLs,
  handleDynamicLinkNotLoggedInFromCarPassAndBingo,
  handleDeeplinkFromCarpassAndBingo,
  BAGGAGE_HANDLERS,
  fromInactive,
  handleOpenL3BaggagePrediction,
  dispatch,
}) => {
  const handleDeepLink = async ({ url }) => {
    if (url?.startsWith(APP_DEEPLINKS.DEEP_MODE)) {
      setTimeout(() => {
        __handleDynamicLink(
          {
            link: {
              url,
            },
            isDeeplink,
            isLoggedIn,
          },
          {
            navigation,
            openChangiPayByDeepLink,
            handleDynamicLinkNotLoggedInFromCarPassAndBingo,
            handleDeeplinkFromCarpassAndBingo,
            BAGGAGE_HANDLERS,
            setHandleChangiEventFromFDLs,
            dispatch,
            fromInactive,
            handleOpenL3BaggagePrediction,
          },
        )
      }, 500)
    } else if (url?.startsWith(APP_DEEPLINKS.PARKING)) {
      const enableDriveParking = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.DRIVE_PARKING)
      if (enableDriveParking) {
        setTimeout(() => {
          navigation.navigate(NavigationConstants.mainStack, {
            screen: NavigationConstants.parkingLanding,
          })
        }, 1500)
      } else {
        const parsedUrl = parse(url, true)
        const query = get(parsedUrl, "query")
        triggerAdobeCampaignTrackingCodeUTM(query)
        if (!isLoggedIn) {
          handleDynamicLinkNotLoggedInFromCarPassAndBingo({ stateCode: StateCode.CARPASS, bingoGame: null })
          return
        }
        handleDeeplinkFromCarpassAndBingo({ stateCode: StateCode.CARPASS, bingoGame: null })
      }

    }
  }

  useEffect(() => {
    const linkingEvent: any = Linking.addEventListener("url", handleDeepLink)
    Linking.getInitialURL().then((url) => {
      handleDeepLink({ url })
    })
    return () => {
      linkingEvent?.remove()
    }
  }, [isLoggedIn])
}

const changiRewardsPrivilegesScreenOptions = generateScreenOptions({})

const retroClaimsTakePhotoScreenOptions = generateScreenOptions({
  headerShown: false,
  gestureEnabled: false,
})

const l2AnnouncementDetailsOptions = generateScreenOptions({
  gestureEnabled: false,
  leftIcon: ArrowLeftGray,
  headerTitleStyle: {
    maxWidth: Dimensions.get("window").width - (77 + 78)
  },
})

const l1AnnouncementDetailsOptions = generateScreenOptions({
  gestureEnabled: false,
  leftIcon: ArrowLeftGray,
  headerTitleStyle: {
    maxWidth: Dimensions.get("window").width - (77 + 78)
  },
})

const retroClaimsNotificationsScreenOptions = generateScreenOptions({
  leftIcon: ArrowLeftGray,
})

const Stack = createStackNavigator<PrimaryParamList>()

export function MainNavigator({ navigation, route }) {
  const dispatch = useDispatch()
  const previousRouteName = usePreviousRouteName()
  const timeOutTracking = useRef(null)
  const isDeeplink = useRef(null)
  const [deepLinkTrigger, setDeepLinkTrigger] = useState<any>({})
  const [isGetGameChangi, setGetGameChangi] = useState<any>(false)
  const [appStatus, setAppStatus] = useState(InstallAppStatus.NONE)
  const isTokenInvalid = useSelector(AuthSelectors.isTokenInvalid)
  const authTokenVerifyError = useSelector(AuthSelectors.authTokenVerifyError)
  const { getCommonLoginModule } = useGeneratePlayPassUrl("viewCarPass")
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const getChangiGameUrlPathData = useSelector(AirportLandingSelectors.getChangiGameUrlPathData)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const profileFetching = useSelector(ProfileSelectors.profileFetching)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const [state, setState] = useState(AppState.currentState)
  const [willOpenChangiPayByDeeplink, openChangiPayByDeepLink] = useState(false)
  const prevAppState = usePrevious(state)
  const BAGGAGE_HANDLERS = useContext(BAGGAGE_TRACKER_CONTEXT).Handlers
  const { conditionTimeRef, idleTimeRef, triggerShowAppRatingRef } = useContext(PanResponderContext)
  const isGetRatingSuccess = useSelector(PageConfigSelectors.getRatingSuccess)
  const {
    loadingGetConfig,
    getConfigApp,
    notifyDisableChangiPay,
  } = useGetConfigurationPermissionHelper()
  const { handleChangiEventFromFDLs } = useHandleFDL()
  const exploreCategoriesPayload = useSelector(ExploreSelectors.exploreCategoriesData)
  const [valueHandleChangiEventFromFDLs, setHandleChangiEventFromFDLs] = useState(null)
  const sessionExpiredRef = useRef(null)
  const isShowingExpirePopup = useRef(false)
  const { enableSearchV2, nativeOnboardingScreen, flyLandingFeatureFlag } = useContext(AccountContext)
  const isNativeOnboardingScreen = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.NATIVE_ONBOARDING_SCREEN, nativeOnboardingScreen)
  const getScreenOptions = () => {
    return Platform.select({
      ios: { presentation: "modal", gestureEnabled: false },
      android: {
        ...TransitionPresets.ModalSlideFromBottomIOS,
        gestureEnabled: false,
        presentation: "transparentModal",
        cardStyle: {
          marginTop: insets.top,
          borderTopStartRadius: 20,
          borderTopEndRadius: 20,
        },
      },
    })
  }

  let fromInactive = true
  const navigationState = navigation?.getState?.()
  const exploreCategories = simpleCondition({
    condition: !!exploreCategoriesPayload?.data,
    ifValue: exploreCategoriesPayload?.data,
    elseValue: [],
  })
  const vouchersPrizesRedemptionsPageOptions = generateScreenOptions({
    headerTitleTx: "vouchersPrizesRedemptionsScreen.title",
    headerStyle: {
      ...shadow.noShadow,
      borderBottomColor: color.palette.lighterGrey,
      borderBottomWidth: 1,
      ...Platform.select({
        ios: {
          height: 116,
        },
      }),
    },
    headerShadowVisible: false,
    headerTitleContainerStyle: {
      maxWidth: Dimensions.get("window").width - 61 * 2, // Desired left/right margin (77) - default margin (16)
    },
    leftIcon: ArrowLeftGray,
    onHeaderLeftPress(navigation, route) {
      if(route?.params?.backPressToVouchersPrizesRedemptionsScreen){
        navigation?.popToTop()
        navigation?.navigate(NavigationConstants.redemptionCatalogueScreen)
        trackAction(AdobeTagName.CAppVPR, {
          [AdobeTagName.CAppVPR]: AdobeValueByTagName.CAppVPRBack,
        })
      } else {
        navigation?.goBack?.()
        trackAction(AdobeTagName.CAppVPR, {
          [AdobeTagName.CAppVPR]: AdobeValueByTagName.CAppVPRBack,
        })
      }
    },
  })
  const {openChangiPay} = useCPay()

  const { handleOpenL3BaggagePrediction } = useL3BaggagePrediction()

  const clearStatusLinkFromInactive = () => {
    fromInactive = false
  }

  useTriggerAppRating(AppRatingSession.MainNavigator)

  useEffect(() => {
    dispatch(AirportLandingCreator.setPlayPassEntryPoint("")) //reset playpass entry
    const storedBanner = store.getState().imageManagerReducer?.dineShopV2Banner?.uri
    const storedBg = store.getState().imageManagerReducer?.dineShopV2Background?.uri
    if (storedBanner || storedBg) {
      FastImage.preload([
        { uri: storedBanner, priority: 'high', cache: 'immutable' },
        { uri: storedBg, priority: 'high', cache: 'immutable'},
      ])
    }
  }, [])

  const webViewOptions: StackNavigationOptions = webViewOptionsRender()

  const filterOptions: StackNavigationOptions = filterOptionsRender(navigation)

  const flightResultLandingScreenOptions = ({ navigation }) => flightResultLandingScreenOptionsRender(
    filterOptions,
    navigation,
  )

  const changiRewardOptions = ({ navigation }) =>  changiRewardOptionsRender(
    filterOptions,
    navigation,
  )

  const redeemRewardSuccessScreenOptions = generateScreenOptions({
    headerTitleTx: "redeemRewardSuccess.header",
    headerLeftShown: false,
    headerRightShown: true,
    rightIcon: CrossBlue,
    onHeaderRightPress: (newNavigation) =>
      redeemRewardSuccessHandleRightButtonPress(previousRouteName, newNavigation),
  })

  const monarchPrivilegesScreenOptions = generateScreenOptions({
    headerTitleTx: "monarchPrivilegesScreen.header",
    headerLeftShown: true,
    leftIcon: ArrowLeftGray,
  })

  const handleDeeplinkFromCarpassAndBingo = ({ stateCode, bingoGame }) => {
    if (stateCode) {
      getCommonLoginModule(stateCode)
      return
    }

    if (bingoGame) {
      dispatch(NativeAuthReducer.setLoadingRequest())
      setGetGameChangi(true)
      setDeepLinkTrigger({
        isTrigger: true,
        bingoGame,
      })
    }
  }

  const handleDynamicLinkNotLoggedInFromCarPassAndBingo = ({ stateCode, bingoGame }) => {
    navigation.navigate(NavigationConstants.mainStack, {
      screen: NavigationConstants.authScreen,
      params: {
        callBackAfterLoginSuccess: () => {
          handleDeeplinkFromCarpassAndBingo({ stateCode, bingoGame })
        },
        callBackAfterLoginCancel: () => {
          setGetGameChangi(false)
          setDeepLinkTrigger({})
        },
      },
    })
  }

  // Foreground events - Handle dynamic link inside your own application
  const handleDynamicLink = (link) =>
    __handleDynamicLink(
      { link, isDeeplink, isLoggedIn },
      {
        navigation,
        openChangiPayByDeepLink,
        handleDynamicLinkNotLoggedInFromCarPassAndBingo,
        handleDeeplinkFromCarpassAndBingo,
        BAGGAGE_HANDLERS,
        setHandleChangiEventFromFDLs,
        dispatch,
        fromInactive,
        handleOpenL3BaggagePrediction,
      },
    )

  const handleDynamicLinkQuitState = (link) =>
    __handleDynamicLink(
      { link, isDeeplink, isLoggedIn },
      {
        navigation,
        openChangiPayByDeepLink,
        handleDynamicLinkNotLoggedInFromCarPassAndBingo,
        handleDeeplinkFromCarpassAndBingo,
        BAGGAGE_HANDLERS,
        timeOut: 1500,
        setHandleChangiEventFromFDLs,
        dispatch,
        fromInactive,
        handleOpenL3BaggagePrediction,
      },
    )
  
  useHandleDeepLink({
    isDeeplink,
    isLoggedIn,
  }, {
    navigation,
    openChangiPayByDeepLink,
    setHandleChangiEventFromFDLs,
    handleDynamicLinkNotLoggedInFromCarPassAndBingo,
    handleDeeplinkFromCarpassAndBingo,
    BAGGAGE_HANDLERS,
    dispatch,
    fromInactive,
    handleOpenL3BaggagePrediction,
  })
  useHandleOneLink({
    isDeeplink, isLoggedIn
  }, {
    navigation,
    openChangiPayByDeepLink,
    handleDynamicLinkNotLoggedInFromCarPassAndBingo,
    handleDeeplinkFromCarpassAndBingo,
    BAGGAGE_HANDLERS,
    setHandleChangiEventFromFDLs,
    dispatch,
    fromInactive,
    handleOpenL3BaggagePrediction,
  })

  const handTrackingVisitSource = async () => {
    const value = await getValueTracking(isDeeplink.current)
    trackActionWithCondition(
      AdobeTagName.cappVisitSource,
      {
        [AdobeTagName.cappVisitSource]: value,
      },
      value,
    )
  }
  const handleStateBackground = () => {
    if (state === AppStateType.background) {
      // send AA visit end page
      clearTimeout(timeOutTracking.current)
      isDeeplink.current = null
    }
  }

  const handleCompareVersion = (currentVersionInstalled) => {
    const appVersion = VersionInfo.appVersion
    if (!currentVersionInstalled) {
      setAppStatus(InstallAppStatus.NEW_INSTALL)
      save(StorageKey.currentVersionInstalled, appVersion)
      return
    }
    if (compareVersion(currentVersionInstalled, appVersion) === -1) {
      setAppStatus(InstallAppStatus.INSTALL_FROM_UPGRADE)
      save(StorageKey.currentVersionInstalled, appVersion)
      return
    }
    setAppStatus(InstallAppStatus.INSTALLED)
    save(StorageKey.currentVersionInstalled, appVersion)
  }

  const handleSessionExpiredLogin = () => {
    isShowingExpirePopup.current = false
    sessionExpiredRef?.current?.close?.()
    setIsShowingSessionPopup(false)
    resetUserAuthData({ dispatch })
    navigation.navigate(NavigationConstants.mainStack, {
      screen: NavigationConstants.authScreen,
    })
  }

  const handleCloseSessionExpiredPopup = () => {
    isShowingExpirePopup.current = false
    resetUserAuthData({ dispatch })
    sessionExpiredRef?.current?.close?.()
    navigation.dispatch(StackActions.popToTop())
    setIsShowingSessionPopup(false)
    setTimeout(() => {
      checkSurvey(env())
    }, 2000)
  }

  useEffect(() => {
    if (
      ifAllTrue([
        state === AppStateType.active,
        ifOneTrue([prevAppState === AppStateType.background, !prevAppState]),
      ])
    ) {
      trackingActionVisitID()
      timeOutTracking.current = setTimeout(handTrackingVisitSource, 10)
      return
    }
    handleStateBackground()
  }, [state, prevAppState])

  const trackingActionVisitID = () => {
    const deviceId = DeviceInfo.getUniqueIdSync();
    const uid = profilePayload?.id;
    const dataTracking = isLoggedIn ? `${deviceId} | ${uid}` : `${deviceId}`
    trackAction(AdobeTagName.cappVisitId, {
      [AdobeTagName.cappVisitId]: dataTracking,
    })
  }

  useEffect(() => {
    AppState.addEventListener("change", (changedState) => {
      setState(changedState)
    })
    const loadDataFormLocal = async () => {
      const currentVersionInstalled = await load(StorageKey.currentVersionInstalled)
      handleCompareVersion(currentVersionInstalled)
    }
    loadDataFormLocal()
    dynamicLinks().getInitialLink().then(handleDynamicLinkQuitState)
  }, [])

  useEffect(() => {
    const timer = setTimeout(() => {
      clearStatusLinkFromInactive()
    }, 2000)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    const myTravelFlightDetails = myTravelFlightsPayload?.getMyTravelFlightDetails
    if (ifAllTrue([isGetGameChangi, deepLinkTrigger?.bingoGame, !profileFetching])) {
      handleGetGameChangi(deepLinkTrigger, myTravelFlightDetails, profilePayload, dispatch)
    }
  }, [deepLinkTrigger, isGetGameChangi, myTravelFlightsPayload, profileFetching])

  useEffect(() => {
    if (
      ifAllTrue([deepLinkTrigger?.isTrigger, getChangiGameUrlPathData?.data?.getChangiGames?.url])
    ) {
      const url = getChangiGameUrlPathData?.data?.getChangiGames?.url
      setDeepLinkTrigger({})
      setGetGameChangi(false)
      dispatch(NativeAuthReducer.setLoadingReset())
      navigation.navigate(NavigationConstants.mainStack, {
        screen: NavigationConstants.webview,
        params: {
          uri: url,
        },
      })
    }
  }, [deepLinkTrigger, getChangiGameUrlPathData])

  useEffect(() => {
    const unsubscribe = dynamicLinks().onLink(handleDynamicLink)
    // When the component is unmounted, remove the listener
    return () => {
      unsubscribe()
    }
  }, [isLoggedIn])

  const checkCmWebviewShowing = (routes: any) => {
    return routes?.some((item: any) => item.name === "playpassWebview")
  }

  const checkListTrigger = async () => {
    const isParticipatedCmWebview = await load(StorageKey.isParticipatedCmWebview)
    const isParticipatedInAppFolio = await load(StorageKey.isParticipatedInAppFolio)
    return ifOneTrue([isParticipatedCmWebview, isParticipatedInAppFolio])
  }

  const handleTrackingShowRatingPopup = async () => {
    const listTrigger = await checkListTrigger()
    const {
      authTokenAgeHours = "48",
      ratingPopupDays = "120",
    } = store.getState()?.pageConfigReducer
    const checkAuthTokenAndCadence = ifAllTrue([
      !isEmpty(authTokenAgeHours),
      !isEmpty(ratingPopupDays),
      isGetRatingSuccess,
    ])
    if (
      ifAllTrue([
        listTrigger,
        !checkCmWebviewShowing(route?.state?.routes),
        ScreenListShowPopupRating.includes(getCurrentScreenActive()),
        checkLoginState(),
        checkAuthTokenAndCadence,
      ])
    ) {
      handleShowPopupRating(Number(authTokenAgeHours), Number(ratingPopupDays))
    }
  }

  useEffect(() => {
    if (triggerShowAppRatingRef?.current) {
      triggerShowAppRatingRef.current[AppRatingSession.OldLogic] = handleTrackingShowRatingPopup
    } else {
      triggerShowAppRatingRef.current = {
        [AppRatingSession.OldLogic]: handleTrackingShowRatingPopup,
      }
    }
  }, [handleTrackingShowRatingPopup])

  // Handle app rating popup logic
  useAppRatingOnSaveFlight({ route, triggerShowAppRatingRef })

  useEffect(() => {
    handleValidateToken({ dispatch, isLoggedIn, isTokenInvalid })
  }, [isTokenInvalid, route])

  useEffect(() => {
    const activeScreen = getActiveRouteName(navigationState)
    if (
      activeScreen !== NavigationConstants.authScreen &&
      !isEmpty(authTokenVerifyError) &&
      authTokenVerifyError.isError &&
      authTokenVerifyError.kind !== API_FAILURE_KIND.CANNOT_CONNECT
    ) {
      const dtAction = dtManualActionEvent(`${FE_LOG_PREFIX}Popup_force_logout_trigger`)
      dtAction.reportStringValue("start_open", String(isShowingExpirePopup.current))
      if (isShowingExpirePopup.current) {
        dtAction.leaveAction()
        return
      }
      dtAction.reportStringValue("about_opening", "true")
      isShowingExpirePopup.current = true
      ChangiEcardController.hideModal()
      closeChangiPay()
      dispatch(ChangiRewardsActions.setChangiECardModalOpenning(false))
      setIsShowingSessionPopup(true)
      if (activeScreen === NavigationConstants.redeemRewardDetailScreen) {
        dtAction.reportStringValue("open_from", activeScreen)
        sessionExpiredRef?.current?.open?.()
      } else {
        InteractionManager.runAfterInteractions(() => {
          dtAction.reportStringValue("open_from", activeScreen)
          sessionExpiredRef?.current?.open?.()
        })
      }
      dtAction.leaveAction()
    }
  }, [authTokenVerifyError, authTokenVerifyError?.isError, authTokenVerifyError?.kind, navigationState])

  useEffect(() => {
    if (
      ifAllTrue([
        valueHandleChangiEventFromFDLs,
        exploreCategoriesPayload,
        !exploreCategoriesPayload?.isLoading,
      ])
    ) {
      handleChangiEventFromFDLs(valueHandleChangiEventFromFDLs, {
        isLoggedIn,
        exploreCategories,
        profilePayload,
      })
      setHandleChangiEventFromFDLs(null)
    }
  }, [valueHandleChangiEventFromFDLs, exploreCategoriesPayload])

  useEffect(() => {
    if (ifAllTrue([willOpenChangiPayByDeeplink, profilePayload])) {
      getConfigApp({
        configKey: AppConfigPermissionTypes.changiappWalletEnabled,
        callbackSuccess: () => openChangiPay(),
        callbackFailure: () => notifyDisableChangiPay(),
      })
      openChangiPayByDeepLink(false)
    }
  }, [willOpenChangiPayByDeeplink, profilePayload])

  useEffect(() => {
    const onHardwareBack = () => {
      resetInactivityTimeout({
        conditionTimeRef,
        idleTimeRef,
        callback: () =>
          getPreviousScreen() === TrackingScreenName.Explore
            ? trackingShowRatingPopupExploreScreen({ route })
            : trackingShowRatingPopup({ route }),
      })
      // Return false to keep default behavior of back button
      return false
    }

    const backHandler = BackHandler.addEventListener("hardwareBackPress", onHardwareBack)
    return () => backHandler.remove()
  }, [])

  const handleLeftButtonPressYourReward = (newNavigation, newRoute) => {
    const { needReload, onGoBack } = newRoute?.params || {}
    if (onGoBack && !needReload) {
      onGoBack?.()
    }
    newNavigation.goBack()
  }

  const YourRewardOptions = generateScreenOptions({
    headerTitleTx: "yourReward.header",
    onHeaderLeftPress: (newNavigation, newRoute) => handleLeftButtonPressYourReward(newNavigation, newRoute),
  })

  const bookingsOrdersOptions = generateScreenOptions({
    headerTitleTx: "bookingsOrdersScreen.title",
    headerStyle: {
      ...shadow.noShadow,
      borderBottomColor: color.palette.lighterGrey,
      borderBottomWidth: 1,
    },
    leftIcon: ArrowLeftGray,
  })

  const handleLeftHeaderPress = (newNavigation, route) => {
    if (previousRouteName === NavigationConstants.bookingsOrdersScreen) {
      dispatch(ForYouActions.dataBookingAndOrderCacheData(true))
    }
    newNavigation.goBack()
  }

  const orderDetailsOrdersOptions = generateScreenOptions({
    headerStyle: {
      ...shadow.noShadow,
      borderBottomColor: color.palette.lighterGrey,
    },
    leftIcon: ArrowLeftGray,
    onHeaderLeftPress: handleLeftHeaderPress,
  })

  const notificationScreenOptions = generateScreenOptions({
    headerShown: false,
  })

  const insets = useSafeAreaInsets()

  return appStatus !== InstallAppStatus.NONE ? (
    <View style={{ flex: 1 }}>
      <Stack.Navigator
        initialRouteName={isNativeOnboardingScreen ? handleCondition(
          LIST_APP_STATUS_NEED_SHOW_WELCOME.includes(appStatus),
          "welcome",
          "bottomNavigation",
        ) : "bottomNavigation"}
        screenOptions={{
          headerShown: false,
          headerTitleAlign: "center",
        }}
      >
        <Stack.Screen name="bottomNavigation" component={BottomTabNavigator} />
        <Stack.Screen
          name={NavigationConstants.welcome}
          component={WelcomeScreen}
          initialParams={{ status: appStatus }}
        />
        <Stack.Screen name="webview" component={WebViewComponent} options={webViewOptions} />
        <Stack.Screen
          name={NavigationConstants.attractionPageView}
          component={AttractionPageView}
          options={{ headerShown: false }}
        />
        <Stack.Screen name="playpassWebview" component={PlayPassWebView} />
        <Stack.Screen name="dineFilter" component={DineFilter} options={filterOptions} />
        <Stack.Screen name="shopFilter" component={ShopFilter} options={filterOptions} />
        <Stack.Screen
          name="dineFilterResults"
          component={DineFilterResults}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="shopFilterResults"
          component={ShopFilterResults}
          options={{ headerShown: false }}
        />
        <Stack.Screen name="restaurantDetailScreen" component={RestaurantDetailsScreen} />
        <Stack.Screen name="eventDetailsScreen" component={EventDetailsScreen} />
        <Stack.Screen name="paymentConfirmationScreen" component={PaymentConfirmationScreen} />
        <Stack.Screen name="dineShopOfferDetailsScreen" component={DineShopOfferDetailsScreen} />
        <Stack.Screen name="shopDetailsScreen" component={ShopDetailsScreen} />
        <Stack.Screen
          name="flightResultLandingScreen"
          component={
            getFeatureFlagInit(REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag)
              ? FlightListing
              : FlightResultLandingScreen
          }
          options={
            getFeatureFlagInit(REMOTE_CONFIG_FLAGS.FLY_LANDING, flyLandingFeatureFlag)
              ? {
                  headerShown: false,
                }
              : flightResultLandingScreenOptions
          }
        />
        <Stack.Screen
          name="search"
          component={isFlagOnCondition(enableSearchV2) ? SearchScreenV2 : SearchScreen}
          options={isFlagOnCondition(enableSearchV2) ? searchV2Options : {}} />
        <Stack.Screen
          name="searchResult"
          component={SearchResultScreen} />
        <Stack.Screen
          name="searchFlightsV2Result"
          component={SearchFlightsV2ResultScreen} />
        <Stack.Screen
          name={"changiRewardsDetailScreen"}
          component={ChangiRewardsDetailScreen}
          options={changiRewardOptions}
        />
        <Stack.Screen
          name="mobileGSTRefund"
          component={MobileGSTRefund}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="airportInfoL2"
          component={AirportInfoL2}
          options={{ headerShown: false }}
        />
        <Stack.Screen name="scanCode" component={ScanCodeScreen} options={{ headerShown: false }} />
        <Stack.Screen
          name="detailPerkScreen"
          component={DetailPerkScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="orderDetailPage"
          component={OrderDetailPage}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name={"redemptionCatalogueScreen"}
          component={RedemptionCatalogueScreen}
          options={redemptionCatalogueOptions}
        />
        <Stack.Screen
          name={"carPark"}
          component={CarParkScreen}
          options={{ headerShown: false, gestureEnabled: false }}
        />
        <Stack.Screen name={"profileScreen"} component={ProfileScreen} options={profileOptions} />
        <Stack.Screen name={"mapScreen"} component={MapScreen} options={mapOptions} />
        <Stack.Screen
          name={NavigationConstants.redeemRewardDetailScreen}
          component={RedeemRewardDetailScreenV2}
          options={redeemRewardDetailOptions}
        />
        <Stack.Screen name="toilets" component={Toilets} options={toiletsOptions} />
        <Stack.Screen
          name={NavigationConstants.changiMap}
          component={AtomMap}
          options={atomMapOptions}
        />
        <Stack.Screen
          name={NavigationConstants.changiMillionaire}
          component={ChangiMillionaireWebView}
          options={changiMillionaireOptions}
        />
        <Stack.Screen
          name={NavigationConstants.staffPerkListing}
          component={StaffPerkListing}
          options={staffPerkListingOptions}
        />
        <Stack.Screen
          name={NavigationConstants.yourReward}
          component={YourRewardScreen}
          options={YourRewardOptions}
        />
        <Stack.Screen
          name={NavigationConstants.yourRewardRedeemSuccess}
          component={YourRewardRedeemSuccessScreen}
          options={YourRewardRedeemSuccessOptions}
        />
        <Stack.Screen
          name={NavigationConstants.offerDetail}
          component={OfferDetailScreen}
          options={OfferDetailOptions}
        />
        <Stack.Screen
          name={NavigationConstants.flightDetails}
          component={FlightDetailsContext}
          options={{
            headerShown: false,
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name={NavigationConstants.appscapadeScanBoadingPass}
          component={AppscapadeScanBoadingPassScreen}
          options={{
            headerShown: false,
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name={NavigationConstants.redeemRewardSuccessScreen}
          component={RedeemRewardSuccessScreen}
          options={redeemRewardSuccessScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.monarchPrivileges}
          component={MonarchPrivilegesScreen}
          options={monarchPrivilegesScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.vouchersPrizesRedemptionsScreen}
          component={VouchersPrizesRedemptionsPage}
          options={vouchersPrizesRedemptionsPageOptions}
        />
        <Stack.Screen
          name={NavigationConstants.playPassBookingDetail}
          component={playPassBookingDetail}
          options={playPassBookingDetailOptions}
        />
        <Stack.Screen
          name={NavigationConstants.saveFlightsScreen}
          component={SaveFlightsScreen}
          options={saveFlightsScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.bookingsOrdersScreen}
          component={BookingsOrdersScreen}
          options={bookingsOrdersOptions}
        />
        <Stack.Screen
          name={NavigationConstants.orderDetailsScreen}
          component={OrderDetailsScreen}
          options={orderDetailsOrdersOptions}
        />
        <Stack.Screen
          name={NavigationConstants.changiRewardsPrivilegesScreen}
          component={ChangiRewardsPrivilegesScreen}
          options={changiRewardsPrivilegesScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.retroClaimsTakePhotoScreen}
          component={RetroClaimsTakePhotoScreen}
          options={retroClaimsTakePhotoScreenOptions}
        />
        <Stack.Screen
          name={"transactions"}
          component={Transaction}
          options={{
            headerShown: false,
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name={NavigationConstants.l2AnnouncementDetails}
          component={L2AnnouncementDetails}
          options={l2AnnouncementDetailsOptions}
        />
        <Stack.Screen
          name={NavigationConstants.retroClaimsNotifications}
          component={retroClaimsNotificationsScreen}
          options={retroClaimsNotificationsScreenOptions}
        />
        <Stack.Group screenOptions={getScreenOptions}>
          <Stack.Screen name={NavigationConstants.authScreen} component={NativeLoginScreen} />
          <Stack.Screen name={NavigationConstants.passwordBiometrics} component={PasswordBiometricsScreen} />
        </Stack.Group>
        <Stack.Group screenOptions={{
            ...TransitionPresets.ModalSlideFromBottomIOS,
            gestureEnabled: false,
            presentation: "transparentModal",
            cardStyle: {
              backgroundColor: "transparent",
            },
        }}>
          <Stack.Screen name={NavigationConstants.searchFlightsV2Modal} component={FlightSearchModal} />
        </Stack.Group>
        <Stack.Screen
          name={NavigationConstants.notificationsScreen}
          component={NotificationScreen}
          options={notificationScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.notificationDetailScreen}
          component={NotificationDetailScreen}
          options={notificationScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.l1AdvisoryDetails}
          component={L1AdvisoryDetails}
          options={l1AnnouncementDetailsOptions}
        />
        <Stack.Screen
          name={NavigationConstants.creditsScreen}
          component={CreditsScreen}
          options={creditsScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.parkingLanding}
          component={ParkingLandingScreen}
        />
        <Stack.Screen 
          name={NavigationConstants.ParkingLandingBenefit}
          component={ParkingLandingBenefitScreen}
          options={ParkingLandingBenefitOptions}
        />
        <Stack.Screen 
          name={NavigationConstants.parkingBenefitsMonarch}
          component={ParkingBenefitsMonarchScreen}
        />
        <Stack.Screen
          name={NavigationConstants.submitSuggestionsAndFeedBack}
          component={SubmitSuggestionFeedbackScreen}
          options={submitSuggestionFeedbackOptions}
        />
        <Stack.Screen
          name={NavigationConstants.FAQLanding}
          component={FAQLanding}
          options={FAQLandingOptions}
        />
        <Stack.Screen
          name={NavigationConstants.DriveOnboarding}
          component={DriveOnboardingScreen}
          options={DriveOnboardingOptions}
        />
        <Stack.Screen
          name={NavigationConstants.promoCodes}
          component={PromoCodesScreen}
          options={promoCodesOptions}
        />
        <Stack.Screen
          name={NavigationConstants.DineShopDirectory}
          component={DineShopDirectory}
          options={DineShopDirectoryOptions}
        />
        <Stack.Screen
          name={NavigationConstants.dealsPromosListing}
          component={DealsPromosListing}
          options={staffPerkListingOptions}
        />
        <Stack.Screen
          name={NavigationConstants.facilitiesServices}
          component={FacilitiesServices}
          options={facilitiesServicesOptions}
        />
        <Stack.Screen
          name={NavigationConstants.pdfView}
          component={PDFView}
          options={pdfViewOptions}
        />
      </Stack.Navigator>
      <ForcedUpdateDialog />
      <ForcedUpdateDialogCpay />
      <AdvisoryNotificationPopup />
      {/* <FlySubscription dispatch={dispatch} /> */}
      <LoadingOverlay visible={loadingGetConfig} />
      <SessionExpiredPopup
        onClose={handleCloseSessionExpiredPopup}
        onPressLogin={handleSessionExpiredLogin}
        ref={sessionExpiredRef}
      />
      <L1AdvisoryOverlay />
    </View>
  ) : (
    <View style={{ flex: 1 }}></View>
  )
}

const handleFunctionTypeDeeplink = async ({
  BAGGAGE_HANDLERS,
  navigation,
  openChangiPayByDeepLink,
  setHandleChangiEventFromFDLs,
  target,
  timeOut,
  handleOpenL3BaggagePrediction,
}) => {
  const { name, value } = _get(target, 'navigation', {})
  switch (name) {
    case MODE_CODE.pay:
      openChangiPayByDeepLink(true)
      break
    case MODE_CODE.baggage_tracking:
      BAGGAGE_HANDLERS.baggage_tracker_request({ navigation })
      break
    case MODE_CODE.changi_event:
      setTimeout(() => {
        setHandleChangiEventFromFDLs(value)
      }, timeOut)
      break
    case MODE_CODE.retro_claims:
      goToRetroClaims({ navigation })
      break;
    case MODE_CODE.baggage_prediction:
      handleOpenL3BaggagePrediction({ queryData: value })
      break
    default:
      break
  }
}

async function handleDL(
  target,
  navigation,
  { openChangiPayByDeepLink, setHandleChangiEventFromFDLs, dispatch, isLoggedIn },
  BAGGAGE_HANDLERS,
  timeOut,
  handleOpenL3BaggagePrediction?,
) {
  if (target) {
    const {
      navigation: {
        type,
        name,
        value,
        sourceSystem,
        params,
        authCallbackParams,
        proceedAfterCancelLogin,
      },
      mode,
    } = target
    let navigationCallback
    if (authCallbackParams) {
      navigationCallback = {
        callBackAfterLoginSuccess: () =>
          handleDL(
            { mode, navigation: authCallbackParams },
            navigation,
            { openChangiPayByDeepLink, setHandleChangiEventFromFDLs, dispatch, isLoggedIn },
            BAGGAGE_HANDLERS,
            0,
          ),
        callBackAfterLoginCancel: () => {
          if (proceedAfterCancelLogin) {
            handleDL(
              { mode, navigation: authCallbackParams },
              navigation,
              { openChangiPayByDeepLink, setHandleChangiEventFromFDLs, dispatch, isLoggedIn },
              BAGGAGE_HANDLERS,
              0,
            )
          } else {
            value?.callBackAfterLoginCancel?.()
          }
        },
        triggerCallbackAfterRedirection: true,
      }
    }
    switch (type) {
      case "in-app":
        setTimeout(() => {
          navigation.navigate(name, {
            sourceSystem,
            ...value,
            ...navigationCallback,
          })
        }, timeOut)
        break
      case "function":
        handleFunctionTypeDeeplink({
          BAGGAGE_HANDLERS,
          navigation,
          openChangiPayByDeepLink,
          setHandleChangiEventFromFDLs,
          target,
          timeOut,
          handleOpenL3BaggagePrediction,
        })
        break
      case "deeplink":
        if (mode === MODE_CODE.iSCsessionid) {
          const ecid = await getExperienceCloudId()
          GlobalLoadingController.showLoading(true)
          let getLink = null
          const payload = {
            stateCode: params?.stateCode,
            params: params?.params,
            input: {
              ...params?.input,
              ecid,
            },
          }
          getLink = await getDeepLinkV2(payload)
          GlobalLoadingController.hideLoading()
          navigation.navigate(NavigationConstants.mainStack, {
            screen: name,
            params: {
              uri: getLink?.redirectUri,
              basicAuthCredential: getLink?.basicAuth,
              ...value,
            },
          })
        } else if ([MODE_CODE.changi_cm, MODE_CODE.changi_cm_login].some((val) => val === mode)) {
          GlobalLoadingController.showLoading(true)
          const isCM24 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.CSM_CM24)
          const reqBody = {
            stateCode: params?.stateCode,
            params: params?.params,
            input: params?.input,
          }
          let getLink = null
          if (isCM24 || mode === MODE_CODE.changi_cm_login) {
            try {
              getLink = await getDeepLinkV2(reqBody, true)
            } catch {
              GlobalLoadingController.hideLoading()
              navigation.popTo(NavigationConstants.mainStack, {
                screen: NavigationConstants.bottomNavigation,
                params: {
                  screen: NavigationConstants.explore,
                  params: {
                    bottomSheetErrorHash: new Date().getTime(),
                  }
                }
              })
              return
            }
          } else {
            getLink = await getDeepLink(reqBody)
          }
          GlobalLoadingController.hideLoading()
          navigation.navigate(NavigationConstants.mainStack, {
            screen: name,
            params: {
              uri: getLink?.redirectUri,
              basicAuthCredential: getLink?.basicAuth,
              ...value,
            },
          })
        } else if (mode === MODE_CODE.game) {
          const airportStaff = store.getState()?.profileReducer?.profilePayload?.airportStaff
          GlobalLoadingController.showLoading(true)
          const navigationInput = params?.input ?? {}
          navigationInput.ecid = await getExperienceCloudId()
          navigationInput.staff = airportStaff ? 1 : 0
          let getLink = null
          try {
            getLink = await getDeepLinkV2({
              stateCode: params?.stateCode,
              params: params?.params,
              input: navigationInput,
            }, true)
          } catch {
            GlobalLoadingController.hideLoading()
            dispatch(SystemActions.setBottomSheetErrorData({ visible: true }))
            return
          }
          GlobalLoadingController.hideLoading()
          if (getLink?.redirectUri) {
            navigation.navigate(NavigationConstants.mainStack, {
              screen: name,
              params: {
                uri: getLink?.redirectUri,
                basicAuthCredential: getLink?.basicAuth,
                ...value,
              },
            })
          }
        } else if (mode === MODE_CODE.carpass_csm) {
          const navigationInput = params?.input ?? {}
          if (isLoggedIn) {
            handleModeCarPassCsmFDL(navigation, dispatch, navigationInput)
          } else {
            navigation.navigate(NavigationConstants.mainStack, {
              screen: NavigationConstants.authScreen,
              params: {
                sourceSystem: SOURCE_SYSTEM.OTHERS,
                callBackAfterLoginSuccess: () => {
                  handleModeCarPassCsmFDL(navigation, dispatch, navigationInput)
                },
                callBackAfterLoginCancel: undefined
              },
            })
          }
        } else {
          GlobalLoadingController.showLoading(true)
          const getLink = await getDeepLink({
            stateCode: params?.stateCode,
            params: params?.params,
            input: params?.input,
          })
          GlobalLoadingController.hideLoading()
          navigation.navigate(NavigationConstants.mainStack, {
            screen: name,
            params: {
              uri: getLink?.redirectUri,
              basicAuthCredential: getLink?.basicAuth,
              ...value,
            },
          })
        }
        break
      default:
        break
    }
  }
}

const __handleDynamicLink = async (
  { link, isDeeplink, isLoggedIn },
  {
    navigation,
    openChangiPayByDeepLink,
    handleDynamicLinkNotLoggedInFromCarPassAndBingo,
    handleDeeplinkFromCarpassAndBingo,
    BAGGAGE_HANDLERS,
    timeOut = 0,
    setHandleChangiEventFromFDLs,
    dispatch,
    fromInactive,
    handleOpenL3BaggagePrediction,
  },
) => {
  closeChangiPay()
  if (!link) {
    return
  }

  dispatch(SystemActions.setIsOpenAppFromDeepLink(true))
  isDeeplink.current = true
  // CarPass
  const stateCode = getParamsCarPassFromDeepLink(link)
  // Changi Bingo
  const bingoGame = getGameCodeFromDeepLink(link)
  // check firebase dynamic link mode keyword
  const target = await getTargetFromModeOfDeepLink({
    link,
    isLoggedIn,
    ecid: await getExperienceCloudId(),
    navigation,
  })
  // handle collect UTM tracking codes CAPP-1774
  onCollectUtmTracking(link)

  if (ifAllTrue([!stateCode, !bingoGame, !target])) {
    return
  }

  if (target?.mode === MODE_CODE.dine_reservation) {
    dispatch(DineCreators.dineHandleFilterDetails(itemReservation, childReservation))
    dispatch(DineCreators.dineSetFilterTitles(resultFilterTitles))
    dispatch(DineCreators.startRequestFilter(true))
  }

  if (stateCode) {
    if (!isLoggedIn) {
      handleDynamicLinkNotLoggedInFromCarPassAndBingo({ stateCode, bingoGame })
      return
    }
    handleDeeplinkFromCarpassAndBingo({ stateCode, bingoGame })
  } else if (bingoGame) {
    if (bingoGame?.interstitialMode === "true") {
      handleDeeplinkFromCarpassAndBingo({ stateCode, bingoGame })
    } else {
      if (!isLoggedIn) {
        handleDynamicLinkNotLoggedInFromCarPassAndBingo({ stateCode, bingoGame })
        return
      }
      handleDeeplinkFromCarpassAndBingo({ stateCode, bingoGame })
    }
  } else if (target?.mode === MODE_CODE.myaccount && !isLoggedIn) {
    const redirectToAccountLP = () => {
      navigation.navigate(NavigationConstants.mainStack, {
        screen: NavigationConstants.bottomNavigation,
        params: {
          screen: NavigationConstants.account,
        }
      })
    }
    navigation.navigate(NavigationConstants.mainStack, {
      screen: NavigationConstants.authScreen,
      params: {
        sourceSystem: SOURCE_SYSTEM.ANYTHING_IN_APP,
        callBackAfterLoginCancel: fromInactive ? () => redirectToAccountLP() : null,
        callBackAfterLoginSuccess: () => redirectToAccountLP()
      }
    })
  } else if (target) {
    handleDL(
      target,
      navigation,
      { openChangiPayByDeepLink, setHandleChangiEventFromFDLs, dispatch, isLoggedIn },
      BAGGAGE_HANDLERS,
      timeOut,
      handleOpenL3BaggagePrediction,
    )
  }
}

const exitRoutes = ["welcome"]
export const canExit = (routeName: string) => exitRoutes.includes(routeName)
