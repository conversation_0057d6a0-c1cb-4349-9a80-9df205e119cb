/**
 * The root navigator is used to switch between major navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow (which is contained in your MainNavigator) which the user
 * will use once logged in.
 */
import {
  NavigationContainer,
  NavigationContainerRef,
  LinkingOptions,
} from "@react-navigation/native"
import {
  createStackNavigator
} from "@react-navigation/stack"
import { color } from "app/theme"
import { Amplify, API } from "aws-amplify"
import React, { useEffect, useRef, useState, useContext, useLayoutEffect, useCallback } from "react"
import {
  ViewStyle,
  AppStateStatus,
  AppState,
  Linking,
  View,
  Animated,
  NativeModules,
  PanResponder,
  Keyboard,
  Platform,
} from "react-native"
import { useDispatch, useSelector } from "react-redux"
import NativeAuthReducer, { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import StaffPerkCreators, { StaffPerkSelectors } from "app/redux/staffPerkRedux"
import { MainNavigator } from "./main-navigator"
import { env } from "app/config/env-params"
import PageConfigCreators, { PageConfigSelectors } from "app/redux/pageConfigRedux"
import { FlyCreators } from "app/redux/flyRedux"
import ProfileAction, { ProfileSelectors } from "app/redux/profileRedux"
import NetInfo from "@react-native-community/netinfo"
import { LoadingOverlay } from "app/components/loading-modal"
import Braze from "@braze/react-native-sdk"
import DeviceInfo from "react-native-device-info"
import notificationAction, { NotificationSelectors } from "app/redux/notificationRedux"
import { onNotificationPublished } from "app/models/queries"
import { TickerBandStatus } from "app/components/ticker-band/ticker-band.props"
import { NOTIFICATION_TYPES, NavigationConstants, SOCIAL_PROVIDER, WIDGET_QUERY_STRING } from "app/utils/constants"
import { handleDataEventAndPromotion } from "app/screens/notifications/tab-screen/helper"
import { loadFromEncryptedStorage, StorageKey, saveToEncryptedStorage } from "app/utils/storage"
import { AdobeTagName, trackAction, adobeUpdateConfiguration, AdobeValueByTagName, adobeSyncIdentifier, AdobeAuthenticationState, AdobeIdentifierType } from "app/services/adobe"
import { usePersonaTags, usePrevious } from "app/utils/screen-hook"
import { BAGGAGE_TRACKER_CONTEXT } from "app/services/context/baggage-tracker"
import { getDeepLink } from "app/sagas/pageConfigSaga"
import { AppLink, Settings } from "react-native-fbsdk-next"
import { analyticsLogEvent, ANALYTICS_LOG_EVENT_NAME } from "app/services/firebase/analytics"
import ChangiEcardModal from "app/components/changi-ecard/changi-ecard-modal"
import ChangiEcardController from "app/components/changi-ecard/changi-ecard-controler"
import ChangiRewardsActions, { ChangiRewardsSelectors } from "app/redux/changiRewardsRedux"
import { WebViewHeaderTypes } from "app/models/enum"
import GlobalLoading from "app/components/global-loading/global-loading"
import {
  getCurrentTimeInSingapore,
  ifAllTrue,
  ifOneTrue,
  // mappingUrlAem, 
  // simpleCondition,
} from "app/utils"
import StaffPerkPromotionDetailModal from "app/components/staff-perk-promotion-detail/staff-perk-promotion-detail-modal"
import FlightDetailsContext from "app/screens/fly/flights/flight-details/flight-details-context"
import { getEnvSetting } from "app/utils/env-settings"
// import moment from "moment"
import JewelPrivilegesDetailModal from "app/components/jewel-privileges-detail/jewel-privileges-detail-modal"
import { PrivilegesSelectors } from "app/redux/privilegesRedux"
import GlobalRetryBottom from "app/components/global-retry-bottom"
import { Dynatrace } from "@dynatrace/react-native-plugin"
import CommonErrorBottomSheet from "app/components/error-overlay/error-bottom-sheet/error-bottom-sheet"
import { ForYouSelectors } from "app/redux/forYouRedux"
import { getViewerUID, getWidgetTrackingValue, queryAccountInfoData, setCadenceTimeout } from "app/utils/screen-helper"
import { ENUM_STORAGE_MMKV, setIsShowModalCheckRatingPopup, setMMKVdata } from "app/utils/storage/mmkv-storage"
import { PanResponderContext } from "app/services/context/pan-responder"
import { AppRatingSession } from "app/hooks/useAppRating"
import VersionInfo from "react-native-version-info"
import { useFetchTickerbandMaintanance } from "app/hooks/useFetchTickerbandMaintanance"
import { useL2AnnouncementBanner, useL2AnnouncementEffects } from "app/hooks/useL2Announcement"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import BottomSheetVcea from "app/sections/bottom-sheet-vcea"
import sha256 from "crypto-js/sha256"

const appVersion = VersionInfo.appVersion
const buildVersion = VersionInfo.buildVersion

import { MytravelCreators } from "../redux/mytravelRedux"
import { AccountContext } from "app/services/context/account"
import { store } from "app/redux/store"
import moment from "moment"
import { SOCIAL_CHANNEL_VALUES } from "app/screens/login/components/social-provider"
import { rnBiometrics } from "app/screens/login/login-helper"
import { BiometryTypes } from "react-native-biometrics"
import { getAuthTokenPayload } from "app/utils/storage/mmkv-encryption-storage"
import { updateLocationPreference } from "app/services/api/profile"

// const { DataModule } = NativeModules

export type RootParamList = {
  mainStack: undefined
  auth: undefined
  transactions: undefined
  nativeLoginScreen: undefined
  flightDetails: undefined
}

const Stack = createStackNavigator<RootParamList>()

// Facebook App link - Deep linking from Facebook Ad
const fbSdkInit = () => {
  Settings.initializeSDK()
  AppLink.fetchDeferredAppLink().then((link) => {
    // console.log("AppLink.fetchDeferredAppLink", link)
    if (link) {
      Linking.openURL(link)
    }
  })
}

const trackLinkedSocialLogin = async (socialProvider: string) => {
  const viewerUID = await getViewerUID()
  if (viewerUID) {
    const socialChannelValue = SOCIAL_CHANNEL_VALUES[socialProvider]
    const dataToBeSent = `${AdobeValueByTagName.AppOpenStateLinkedSocialAccounts} | ${socialChannelValue} | ${AdobeValueByTagName.AppOpenStateLinked} | ${viewerUID}`
    trackAction(AdobeTagName.CAppAppOpenState, {
      [AdobeTagName.CAppAppOpenState]: dataToBeSent,
    })
  }
}

const trackBiometricLoginStatus = async (statusValue: string) => {
  const viewerUID = await getViewerUID()
  if (viewerUID) {
    const biometricTypeValue =
    Platform.OS === "ios"
      ? AdobeValueByTagName.AppOpenStateEnableFaceID
      : AdobeValueByTagName.AppOpenStateEnableFingerprint
    trackAction(AdobeTagName.CAppAppOpenState, {
      [AdobeTagName.CAppAppOpenState]: `${AdobeValueByTagName.AppOpenStateBiometricAuthentication} | ${biometricTypeValue} | ${statusValue} | ${viewerUID}`,
    })
  }
}

// DeepLink Init
const prefixes = ["cagichangi://"]

const deepLinking: LinkingOptions<any> = {
  prefixes,
  config: {
    screens: {
      [NavigationConstants.mainStack]: {
        screens: {
          [NavigationConstants.bottomNavigation]: {
            screens: {
              [NavigationConstants.explore]: {
                path: "explore",
              },
              dineShop: {
                path: "dineShop",
              },
              fly: {
                path: "fly",
              },
            },
          },
        },
      },
    },
  },
  async getInitialURL() {
    const url = await Linking.getInitialURL()
    return url
  },
}

const RootStack = () => {
  const dispatch = useDispatch()

  const appState = useRef(AppState.currentState)
  const personaTagInterval = useRef(null)
  const [baggageInfo, gettingBaggageWithInfo] = useState(null)

  const loadingPage = useSelector(AuthSelectors.loadingPage)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const prevProfilePayload = usePrevious(profilePayload)
  const profileError = useSelector(ProfileSelectors.profileError)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const ratingPopupDays = useSelector(PageConfigSelectors.ratingPopupDays)
  const { updatePersonaTags } = usePersonaTags()
  const BAGGAGE_CONTEXT = useContext(BAGGAGE_TRACKER_CONTEXT).Handlers

  const { l2AnnouncementFeatureFlag, notificationPreferencesV2FeatureFlag, l1AnnouncementFeatureFlag } = useContext(AccountContext)
  const showL2Announcement = isFlagOnCondition(l2AnnouncementFeatureFlag)
  const isNotificationPreferencesV2 = isFlagOnCondition(notificationPreferencesV2FeatureFlag)
  const showL1Advisory = isFlagOnCondition(l1AnnouncementFeatureFlag)

  const [isMoreSubscribe, setMoreSubscribe] = useState(0)
  const isBrazeEventRef = useRef(false)
  const eventAndPromotionNotificationPayload = useSelector(NotificationSelectors.eventAndPromotionNotificationPayload)
  const eventAndPromotionNotificationCount = useSelector(NotificationSelectors.eventAndPromotionNotificationCount)
  const rewardsData = useSelector(ForYouSelectors.rewardsData)
  const widgetInfoRef = useRef(null)
  const currentSGPTimeRef = useRef<any>(null)
  const { UpdateWidgetModule } = NativeModules
  const { onAdd, onDismiss } = useL2AnnouncementBanner()
  const {forceUpdateHandling} = useContext(PanResponderContext)

  useEffect(() => {
    // Reset staff perk tab selected
    dispatch(FlyCreators.setInitFilterPillFlight())
    dispatch(StaffPerkCreators.setCurrentStaffPerkTab(null))
    dispatch(StaffPerkCreators.resetStaffPerkListing())
    const flightFilterOptionSelected = {
      terminal: [],
      airline: "all",
      cityAirport: "all",
    }
    dispatch(FlyCreators.setFlightFilterOption(flightFilterOptionSelected))
    dispatch(notificationAction.toggleL1Advisory(true))
    dispatch(notificationAction.getUserNotificationFirstOpenApp())
  }, [])

  useEffect(() => {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_FIRST_APP, true)
    setIsShowModalCheckRatingPopup(false)

    const deviceId = DeviceInfo.getUniqueIdSync()
    trackAction(AdobeTagName.cappAppId, {
      [AdobeTagName.cappAppId]: deviceId,
    })
    fbSdkInit()
    adobeUpdateConfiguration({
      "target.propertyToken": getEnvSetting()?.ADOBE_TARGET_PROPERTY_TOKEN,
    })
  }, [])

  // const callbackSplashScreenSetting = async (content) => {
  //   const foregroundImagePath = content?.foregroundImageUrl
  //   const backgroundImagePath = content?.backgroundImageUrl
  //   const foregroundURL = simpleCondition({
  //     condition: foregroundImagePath,
  //     ifValue: mappingUrlAem(foregroundImagePath),
  //     elseValue: "",
  //   })
  //   const backgroundURL = simpleCondition({
  //     condition: backgroundImagePath,
  //     ifValue: mappingUrlAem(backgroundImagePath),
  //     elseValue: "",
  //   })
  //   const currentUtcDate = moment().utc().format()
  //   const savedData = {
  //     date: currentUtcDate,
  //     foregroundURL: foregroundURL,
  //     backgroundURL: backgroundURL,
  //   }
  //   DataModule.saveObjectToLocalStorage("splashScreenSetting", savedData)
  // }

  useLayoutEffect(() => {
    dispatch(ChangiRewardsActions.setChangiECardModalOpenning(false))
    // dispatch(
    //   AemActions.getAemConfigData({
    //     name: AEM_PAGE_NAME.SPLASH_SCREEN_SETTING,
    //     pathName: "getSplashScreenSetting",
    //     callBackAfterSuccess: callbackSplashScreenSetting,
    //   }),
    // )
  }, [])

  useEffect(() => {
    const deviceId = DeviceInfo.getUniqueIdSync()
    const getCookies = () => {
      const authTokenPayload = getAuthTokenPayload()
      return {
        ocid_login_token: authTokenPayload?.postMethod?.access_token || "",
        "ocid-login-token": authTokenPayload?.postMethod?.access_token || "",
        uid: authTokenPayload?.postMethod?.member_id_cr || deviceId,
        app_version: `${appVersion}_${buildVersion}`
      }
    }
    Amplify.configure({
      API: {
        graphql_headers: () => (isLoggedIn ? getCookies() : null),
      },
    })
  }, [isLoggedIn])

  const getPageConfiguration = () => {
    dispatch(PageConfigCreators.getRatingConfigurationRequest())
    dispatch(FlyCreators.flyCityCodeRequest())
    dispatch(MytravelCreators.flyClearInsertFlightPayload())
    dispatch(FlyCreators.setFlyShowTickerBand(TickerBandStatus.NONE))
    dispatch(FlyCreators.setFilterDateArrival(null))
    dispatch(FlyCreators.setFilterDateDeparture(null))
    dispatch(FlyCreators.flyClearFlightDetailsPayload())
  }

  useEffect(() => {
    getPageConfiguration()
    forceUpdateHandling("CAPP")
  }, [])

  useL2AnnouncementEffects()

  useEffect(() => {
    if (!isLoggedIn) {
      clearInterval(personaTagInterval.current)
      dispatch(NativeAuthReducer.nativeUpdateAuthState({ isJustCancelLogIn: false, isJustLoggedIn: false }))
    }
  }, [isLoggedIn])

  useEffect(() => {
    Linking.getInitialURL().then((url) => {
      const widgetTypeQueryString = `${WIDGET_QUERY_STRING.TYPES.TYPE}=${WIDGET_QUERY_STRING.VALUES.WIDGET}`
      const isFromLoginWidget = url?.includes(widgetTypeQueryString)
      if (isFromLoginWidget) {
        const trackingStringValue = getWidgetTrackingValue(url)
        trackAction(AdobeTagName.CAppWidget, {
          [AdobeTagName.CAppWidget]: trackingStringValue,
        })
      }
    })
    
    return () => {
      clearInterval(personaTagInterval.current)
    }
  }, [])

  useEffect(() => {
    if (
      ifAllTrue([
        isLoggedIn,
        profilePayload?.email,
        prevProfilePayload?.email !== profilePayload?.email,
        !profileError,
      ])
    ) {
      clearInterval(personaTagInterval.current)
      const personaTag = updatePersonaTags(profilePayload?.email)
      personaTagInterval.current = setInterval(() => {
        personaTag()
      }, env()?.PERSONA_REFRESH_INTERVAL)
    }
  }, [isLoggedIn, profilePayload, prevProfilePayload, profileError])

  useEffect(() => {
    const subscription = async (nextAppState: AppStateStatus) => {
      if (ifAllTrue([appState.current.match(/inactive|background/), nextAppState === "active"])) {
        // If user is logged in -> verify saved token is valid or not
        const { isConnected } = await NetInfo.fetch()
        if (ifAllTrue([isConnected, isLoggedIn, profilePayload])) {
          dispatch(NativeAuthReducer.nativeAuthTokenVerifyRequest())
        }
      }

      appState.current = nextAppState
    }

    const listener = AppState.addEventListener("change", subscription)

    return () => {
      listener.remove()
      //   AppState.removeEventListener("change", subscription)
    }
  }, [isLoggedIn])

  useEffect(() => {
    const eventAndPromotionInit = async () => {
      const LAST_LOGGED_IN_USER = await loadFromEncryptedStorage(StorageKey.lastLoggedInUser)
      const listUserNotification = await loadFromEncryptedStorage(StorageKey.lastNotificationPreferenceAccount) || []
      if (ifAllTrue([isLoggedIn, profilePayload])) {
        const userExists = listUserNotification.some(user => user.uid === profilePayload?.id)
        // login
        if (!LAST_LOGGED_IN_USER || !userExists) {
          // if login first time - CA30-4191
          dispatch(
            ProfileAction.changeSettingNotificationRequest(
              [],
              { last_login_id: null },
              { isNotificationPreferencesV2 },
            ),
          )
        }
        Dynatrace.identifyUser(profilePayload?.id)
        if (profilePayload?.id !== LAST_LOGGED_IN_USER?.uid) {
          // save last login user in local storage when the user is changed
          saveToEncryptedStorage(StorageKey.lastLoggedInUser, {
            uid: profilePayload?.id,
            email: profilePayload?.email,
          })
          saveToEncryptedStorage(StorageKey.lastNotificationPreferenceAccount, [
            ...listUserNotification,
            {
              uid: profilePayload?.id,
              email: profilePayload?.email,
            }
          ])
          // set Braze User External ID when the user is changed
          Braze.changeUser(profilePayload?.id)
        }
      } else {
        Dynatrace.identifyUser(DeviceInfo.getUniqueIdSync())
      }
      if (!isBrazeEventRef?.current) {
        Braze.addListener(Braze.Events.CONTENT_CARDS_UPDATED, async ({ cards }) => {
          handleDataEventAndPromotion(cards, dispatch, {
            previousData: {
              eventAndPromotionNotificationPayload,
              eventAndPromotionNotificationCount
            }
          })
        })
        isBrazeEventRef.current = true
      }
    }
    eventAndPromotionInit()
    Braze.requestContentCardsRefresh()
  }, [isLoggedIn, profilePayload])

  const showPopup = (data) => {
    if (data?.category === NOTIFICATION_TYPES.ADVISORIES) {
      dispatch(notificationAction.closePopupNewAdvisoriesNotification())
      setTimeout(() => {
        dispatch(notificationAction.resetNewAdvisoriesNotification())
        dispatch(notificationAction.isDisplayPopupNewAdvisoriesNotification(true))
        dispatch(notificationAction.newAdvisoriesNotification(data))
      }, 500)
    }
    if (data?.category === NOTIFICATION_TYPES.FLIGHTS) {
      dispatch(notificationAction.newFlightNotification(data))
    }
  }
  const removeNotification = (data) => {
    if (data?.category === NOTIFICATION_TYPES.ADVISORIES) {
      dispatch(notificationAction.removeAdvisoriesNotification(data?.id))
    }
    if (data?.category === NOTIFICATION_TYPES.FLIGHTS) {
      dispatch(notificationAction.removeFlightNotification(data?.id))
    }
  }

  useEffect(() => {
    const deviceId = DeviceInfo.getUniqueIdSync()
    let sub
    if (ifOneTrue([!isLoggedIn, ifAllTrue([isLoggedIn, profilePayload?.id])])) {
      let listIn = deviceId

      if (ifAllTrue([isLoggedIn, profilePayload?.id])) {
        listIn = profilePayload?.id
      }
      // console.log("Advisory_Subscribe_listIn", listIn)
      const objFilter = {
        target_id: {
          in: [listIn, "GLOBAL"],
        },
      }
      sub = (
        API.graphql({
          query: onNotificationPublished,
          variables: {
            filter: JSON.stringify(objFilter),
          },
        }) as any
      ).subscribe({
        next: (res) => {
          // console.log("Advisory_Subscribe_res", res)
          const data = res?.value?.data?.onNotificationPublished
          dispatch(notificationAction.notificationsCountRequest(deviceId))
          if (data?.category === NOTIFICATION_TYPES.L2_ANNOUNCEMENT && showL2Announcement) {
            if (data?.type !== "REMOVE") {
              onAdd(data)
            } else {
              onDismiss(data)
              dispatch(notificationAction.removeAnnouncementBySubscription(data))
            }
          } else if (data?.category === NOTIFICATION_TYPES.L1_ADVISORIES && showL1Advisory) {
            if (data?.type !== "REMOVE") {
              // Publish new L1 Advisory
              dispatch(notificationAction.l1AdvisoryPush(data))
            } else {
              // Remove deleted L1 Advisory
              dispatch(notificationAction.l1AdvisoryRemove(data))
              dispatch(notificationAction.removeL1AdvisoryBySubscription(data))
            }
          } else if (data?.type !== "REMOVE") {
            showPopup(data)
          } else {
            removeNotification(data)
          }
        },
        error: (err) => {
          console.log("Advisory_Subscribe_error", err)
          dispatch(notificationAction.notificationsCountRequest(deviceId))
          if (isMoreSubscribe < 2) {
            const wait = new Promise((resolve) => setTimeout(resolve, 500))
            wait.then(() => {
              setMoreSubscribe(isMoreSubscribe + 1)
            })
          }
        },
      })
      dispatch(notificationAction.notificationsCountRequest(deviceId))
    }
    return () => {
      sub?.unsubscribe()
    }
  }, [isLoggedIn, profilePayload?.id, isMoreSubscribe, showL2Announcement])

  useEffect(() => {
    if (baggageInfo && profilePayload) {
      baggageTracker(baggageInfo)
      gettingBaggageWithInfo(null)
    }
  }, [isLoggedIn, profilePayload, baggageInfo])

  useEffect(() => {
    if (rewardsData) {
      const arrayTier = rewardsData?.reward?.currentTierInfo?.split?.(" ")
      const isStaff = arrayTier[0] === "Staff"
      const widgetInfo = {
        reward: {
          point: rewardsData?.reward?.point || "0",
          originalCurrentTier: rewardsData?.reward?.currentTierInfo,
          currentTierInfo: isStaff ? arrayTier[1] : arrayTier[0],
          loading: false,
          isStaff,
        },
      }
      const previousWidgetReward = widgetInfoRef.current?.reward
      const widgetReward = widgetInfo?.reward
      if (
        previousWidgetReward?.originalCurrentTier !== widgetReward?.originalCurrentTier ||
        previousWidgetReward?.point !== widgetReward?.point
      ) {
        UpdateWidgetModule.updateWidgetInfo(widgetInfo)
        widgetInfoRef.current = widgetInfo
      }
    } else if (!isLoggedIn) {
      UpdateWidgetModule.updateWidgetInfo({})
      widgetInfoRef.current = null
    }
  }, [rewardsData, isLoggedIn])

  // App rating login:
  // Reset qualified parameter when display cadence is satisfied
  useEffect(() => {
    if (ratingPopupDays) {
      setCadenceTimeout()
    }
  }, [ratingPopupDays])

  useEffect(() => {
    if (isLoggedIn && profilePayload?.id) {
      const emailSha256 = sha256(profilePayload?.email?.toLowerCase())?.toString()
      trackAction(AdobeTagName.common, {
        [AdobeTagName.cagLoginmethod]: "Email",
        [AdobeTagName.cagUsername]: emailSha256,
        [AdobeTagName.cappUID]: profilePayload?.id,
        [AdobeTagName.cagLoginstatus]: "yes",
      })
    }
  }, [isLoggedIn, profilePayload?.id])

  BAGGAGE_CONTEXT.baggage_tracker_request = async ({
    navigation,
    sourceSystem,
    flight = null,
    messageID = undefined,
    requiredLogin = true,
  }) => {
    if (ifAllTrue([requiredLogin, ifOneTrue([!isLoggedIn, !profilePayload])])) {
      navigation.navigate(NavigationConstants.mainStack, {
        screen: NavigationConstants.authScreen,
        params: {
          sourceSystem,
          callBackAfterLoginSuccess: () => {
            setTimeout(() => gettingBaggageWithInfo({ navigation, flight, messageID }), 1)
          },
          callBackAfterLoginCancel: () => null,
        },
      })
      return null
    }
    baggageTracker({ navigation, flight, messageID })
  }

  const baggageTracker = async ({ navigation, flight = null, messageID = undefined }) => {
    dispatch(NativeAuthReducer.setLoadingRequest())
    let input: any = {
      username: profilePayload?.email,
      uid: profilePayload?.id,
      messageID,
    }
    if (flight) {
      input = {
        ...input,
        terminal: flight?.flightDetailsData?.terminal,
        flightNo: flight?.flightDetailsData?.flightNumber,
        flightDirection: flight?.flyItem?.direction,
        flightDt:
          flight?.flightDetailsData?.scheduledDate + " " + flight?.flightDetailsData?.scheduledTime,
      }
    }
    const getLink = await getDeepLink({
      stateCode: "BAGGAGE_TRACKING",
      input,
    })
    dispatch(NativeAuthReducer.setLoadingReset())
    analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.PBE_LANDING)
    navigation.navigate(NavigationConstants.mainStack, {
      screen: NavigationConstants.playpassWebview,
      params: {
        uri: getLink?.redirectUri,
        basicAuthCredential: getLink?.basicAuth,
        needCloseButton: true,
        needBackButton: true,
        headerType: WebViewHeaderTypes.default,
      },
    })
  }

  useFetchTickerbandMaintanance()

  useEffect(() => {
    // === Re-establish yesterday dismissed L1 Advisories when it reaches 12:00AM SGT ===
    const handleTimeout = () => {
      currentSGPTimeRef.current = getCurrentTimeInSingapore()
      const dismissedL1Advisories =
        store.getState()?.notificationsReducer?.dismissedL1Announcements
      const dismissedItemsToUpdate = dismissedL1Advisories?.filter?.((item) => {
        const dismissTime = moment(item?.dismissTime)
        const yesterday = currentSGPTimeRef.current?.clone?.()?.subtract?.(1, "day")?.startOf("day")
        const today = currentSGPTimeRef.current?.clone?.()?.startOf("day")

        return dismissTime.isBetween(yesterday, today, null, "[]")
      })
      if (dismissedItemsToUpdate?.length) {
        dispatch(notificationAction.setL1AdvisoryDismissList(dismissedItemsToUpdate, "remove"))
      }
    }
    let resetTimeout
    const startResetTimeout = () => {
      clearTimeout(resetTimeout)
      currentSGPTimeRef.current = getCurrentTimeInSingapore()
      resetTimeout = setTimeout(
        handleTimeout,
        Number(
          currentSGPTimeRef.current
            ?.clone()
            ?.add(1, "day")
            ?.startOf("day")
            ?.toDate?.()
            ?.getTime?.() - currentSGPTimeRef.current?.toDate?.().getTime?.(),
        ) + 1000,
      )
    }
    const trackByAuthPayload = () => {
      const deviceId = DeviceInfo.getUniqueIdSync()
      const authTokenPayload = getAuthTokenPayload()
      if (!authTokenPayload) {
        trackAction(AdobeTagName.MobileDeviceID, {
          [AdobeTagName.MobileDeviceID]: deviceId
        })
        trackAction(AdobeTagName.CAppRewardsMembershipTier, {
          [AdobeTagName.CAppRewardsMembershipTier]: AdobeValueByTagName.CAppRewardsNonLoggedIn,
        })
      }
    }
    const trackForBiometricLoginStatus = async () => {
      const { available, biometryType } = await rnBiometrics.isSensorAvailable()
      if (available && biometryType !== BiometryTypes.TouchID) {
        const isBiometricsOn = store.getState()?.pageConfigReducer?.isBiometricsOn
        const biometricsStatusValue = isBiometricsOn
          ? AdobeValueByTagName.AppOpenStateOn
          : AdobeValueByTagName.AppOpenStateOff
        trackBiometricLoginStatus(biometricsStatusValue)
      } else {
        trackBiometricLoginStatus(AdobeValueByTagName.AppOpenStateNotAvailable)
      }
    }
    const trackingForLoginManagement = async () => {
      const accountInfoData = await queryAccountInfoData() 
      const socialProviders = accountInfoData?.message?.socialProviders?.split(",")
      if (socialProviders?.includes?.(SOCIAL_PROVIDER.GOOGLE)) {
        trackLinkedSocialLogin(SOCIAL_PROVIDER.GOOGLE)
      }
      if (socialProviders?.includes?.(SOCIAL_PROVIDER.APPLE)) {
        trackLinkedSocialLogin(SOCIAL_PROVIDER.APPLE)
      }
      if (socialProviders?.includes?.(SOCIAL_PROVIDER.FACEBOOK)) {
        trackLinkedSocialLogin(SOCIAL_PROVIDER.FACEBOOK)
      }
    }
    const syncIdentifierForAA = () => {
      const persistedProfilePayload = store.getState()?.profileReducer?.profilePayload
      if (persistedProfilePayload?.id) {
        adobeSyncIdentifier({
          identifierType: AdobeIdentifierType.uid,
          identifierValue: `${persistedProfilePayload.id}`,
          authenticationState: AdobeAuthenticationState.authenticated,
        })
      } else {
        adobeSyncIdentifier({authenticationState: AdobeAuthenticationState.loggedOut})
      }
    }

    handleTimeout()
    startResetTimeout()
    trackByAuthPayload()
    trackForBiometricLoginStatus()
    trackingForLoginManagement()
    syncIdentifierForAA()
    const dismissInterval = setInterval(startResetTimeout, 86400000) // re-check everyday

    return () => {
      clearInterval(dismissInterval)
      clearTimeout(resetTimeout)
    }
  }, [])

  useEffect(() => {
    const dismissInterval = setInterval(updateLocationPreference, 1000 * 60 * 5) // 5 minutes
    return () => {
      clearInterval(dismissInterval)
    }
  }, [])

  return (
    <>
      <LoadingOverlay visible={loadingPage} />
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          headerTitleAlign: "center",
        }}
      >
        <Stack.Screen
          name="mainStack"
          component={MainNavigator}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="flightDetails"
          component={FlightDetailsContext}
          options={{
            headerShown: false,
            gestureEnabled: false,
          }}
        />
      </Stack.Navigator>
      <LoadingOverlay />
    </>
  )
}

export const RootNavigator = React.forwardRef<
  NavigationContainerRef<any>,
  Partial<React.ComponentProps<typeof NavigationContainer>>
>((props, ref) => {
  ChangiEcardController.setAppRef(ref)
  const isChangiRewardModalOpenning = useSelector(ChangiRewardsSelectors.isChangiECardModalOpenning)
  const isStaffPerkPromotionDetailModalOpenning = useSelector(
    StaffPerkSelectors.isStaffPerkPromotionDetailModalOpenning,
  )
  const isJewelPrivilegesDetailModalOpenning = useSelector(PrivilegesSelectors.isJewelPrivilegesDetailModalOpenning)
  const animatedPaddingValue = useRef(new Animated.Value(0)).current
  const onCapturePanResponseRef = useRef({})
  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        onCapturePanResponseRef?.current?.[AppRatingSession.ExploreScreen]?.()
        onCapturePanResponseRef?.current?.[AppRatingSession.MainNavigator]?.()
        return false
      },
    }),
  ).current
  // App rating timer refs
  const idleTimeRef = useRef(null)
  const conditionTimeRef = useRef(null)
  const isPageLoadingRef = useRef(null)
  const triggerShowAppRatingRef = useRef({})
  const currentForceUpdateFlow = useRef<"CAPP" | "CPAY" | null>(null)
  const isPendingCpayUpdateCheck = useRef(false)

  const handleSetCapturePanResponseRef = useCallback((name, handler) => {
    if (onCapturePanResponseRef.current) {
      onCapturePanResponseRef.current[name] = handler
    } else {
      onCapturePanResponseRef.current = {
        [name]: handler,
      }
    }
  }, [])

  const forceUpdateHandling = (flowType: "CAPP" | "CPAY" | null) => {
    currentForceUpdateFlow.current = flowType
  }

  const setPendingCpayUpdate = (pending: boolean) => {
    isPendingCpayUpdateCheck.current = pending
  }
  useEffect(() => {
    if (isChangiRewardModalOpenning || isStaffPerkPromotionDetailModalOpenning || isJewelPrivilegesDetailModalOpenning) {
      Animated.timing(animatedPaddingValue, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start()
    } else {
      Animated.timing(animatedPaddingValue, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }).start()
    }
  }, [isChangiRewardModalOpenning, isStaffPerkPromotionDetailModalOpenning, isJewelPrivilegesDetailModalOpenning])

  useEffect(() => {
    const willShowSubscription = Keyboard.addListener("keyboardWillShow", () => {
      setIsShowModalCheckRatingPopup(true)
    })
    const didShowSubscription = Keyboard.addListener("keyboardDidShow", () => {
      setIsShowModalCheckRatingPopup(true)
    })
    const hideSubscription = Keyboard.addListener("keyboardDidHide", () => {
      setIsShowModalCheckRatingPopup(false)
    })

    return () => {
      willShowSubscription.remove()
      didShowSubscription.remove()
      hideSubscription.remove()
    }
  }, [])

  return (
    <NavigationContainer {...props} linking={deepLinking} ref={ref}>
      <PanResponderContext.Provider
        value={{
          conditionTimeRef,
          handleSetCapturePanResponseRef,
          idleTimeRef,
          isPageLoadingRef,
          triggerShowAppRatingRef,
          forceUpdateHandling,
          currentForceUpdateFlow,
          isPendingCpayUpdateCheck,
          setPendingCpayUpdate,
        }}
      >
        <View style={containerApp} {...panResponder.panHandlers}>
          <Animated.View
            style={[
              containerApp,
              {
                transform: [
                  {
                    scale: animatedPaddingValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [1, 0.9],
                    }),
                  },
                ],
                borderRadius: animatedPaddingValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 8],
                }),
              },
            ]}
          >
            <RootStack />
          </Animated.View>
          <JewelPrivilegesDetailModal navigationRef={ref} />
          <StaffPerkPromotionDetailModal navigationRef={ref}/>
          <ChangiEcardModal />
          <GlobalLoading />
          <GlobalRetryBottom />
          <CommonErrorBottomSheet />
          <BottomSheetVcea />
        </View>
      </PanResponderContext.Provider>
    </NavigationContainer>
  )
})

const containerApp: ViewStyle = {
  flex: 1,
  backgroundColor: color.palette.black,
  overflow: "hidden",
}
RootNavigator.displayName = "RootNavigator"
