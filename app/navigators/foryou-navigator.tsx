import React, { use<PERSON>ontext, useEffect } from "react"
import { createStackNavigator, StackNavigationOptions, TransitionPresets } from "@react-navigation/stack"
import { presets } from "app/elements/text/text.presets"
import { color, shadow } from "app/theme"
import { Dimensions, Platform, TextStyle, TouchableOpacity, ViewStyle, InteractionManager } from "react-native"
import { ArrowLeft, ArrowLeftGray, Cross, CrossBlue, BackArrow } from "ichangi-fe/assets/icons"
import { translate } from "app/i18n"
import { usePreviousRouteName } from "app/utils/screen-hook"
import { SettingScreen } from "app/screens/setting/setting-screen"
import { ProfileScreen } from "app/screens/profile/profile-screen"
import PrivacyPolicyScreen from "app/screens/privacy-policy"
import TermsOfUsePolicy from "app/screens/terms-of-use-policy"
import { ChangiRewardsDetailScreen } from "app/screens/changi-rewards-detail/changi-rewards-detail"
import { LinkedMembershipsScreen } from "app/screens/linked-memberships/linked-memberships"
import { RedemptionCatalogueScreen } from "app/screens/redemption-catalogue-screen/redemption-catalogue-screen-wrapper"
import { LoginAndSecurityScreen } from "app/screens/login-and-security/login-and-security"
import { AboutChangiLifeScreen } from "app/screens/about-changi-life/about-changi-life-screen"
import { NavigationConstants } from "app/utils/constants"
import { SettingNotificationsScreen } from "app/screens/setting-notifications"
import { RedeemRewardDetailScreen } from "app/screens/redeem-reward-detail/redeem-reward-detail"
import { RateAirportExperienceScreen } from "app/screens/rate-airport-experience/rate-airport-experience-screen"
import { RedeemRewardSuccessScreen } from "app/screens/redeem-reward-success"
import { PrivilegesScreen } from "app/screens/privileges/privileges"
import { SubmitSuggestionFeedbackScreen } from "app/screens/submit-suggestion-feedback/submit-suggestion-feedback-screen"
import AppInformation from "app/screens/app-information/app-information"
import { accessibility } from "app/utils"
import Toilets from "app/screens/toilets"
import AskMaxView from "app/screens/ask-max/ask-max"
import ForYouScreen from "app/screens/for-you/for-you-screen.wrapper"
import { RedeemRewardDetailScreenV2 } from "app/screens/redeem-reward-detail/redeem-reward-detail-v2"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"
import CreditsScreen from "app/screens/credits/credits"
import ChangiRewardsPrivilegesScreen from "app/screens/changi-rewards-privileges"
import VouchersPrizesRedemptionsPage from "app/screens/vouchers-prizes-redemptions/vouchers-prizes-redemptions"
import { ENUM_STORAGE_MMKV, setMMKVdata } from "app/utils/storage/mmkv-storage"
import throttle from 'lodash/throttle';
import { trackAction, AdobeTagName, AdobeValueByTagName } from "app/services/adobe"
import LoginManagement from "app/screens/login-management/login-management"

const Stack = createStackNavigator()

const headerLeft: ViewStyle = {
  marginLeft: 16,
}

const headerRight: ViewStyle = {
  marginRight: 16,
}

interface GenerateScreenOptionsProps {
  headerShown?: boolean
  headerTitle?: string
  headerTitleTx?: string
  headerTitleStyle?: TextStyle
  headerStyle?: ViewStyle
  headerLeftShown?: boolean
  headerRightShown?: boolean
  rightIcon?: any
  onHeaderLeftPress?: (navigation: any, route: any) => void
  onHeaderRightPress?: (navigation: any, route: any) => void
  gestureEnabled?: any
  leftIcon?: any
  headerTitleContainerStyle?: ViewStyle
  headerShadowVisible?: boolean
  presentation?: "screen" | "modal"
  insets?: any
  rightIconStyle?: any
  headerRightStyle?: ViewStyle
}

export const generateScreenOptions = (
  props: GenerateScreenOptionsProps,
): (({ navigation, route }: { navigation: any; route: any }) => StackNavigationOptions) => {
  const {
    headerTitle,
    headerTitleTx,
    headerShown = true,
    headerTitleStyle,
    headerStyle,
    headerLeftShown = true,
    headerRightShown,
    rightIcon,
    onHeaderLeftPress,
    onHeaderRightPress,
    gestureEnabled = true,
    leftIcon,
    headerTitleContainerStyle,
    presentation,
    insets,
    rightIconStyle,
    headerRightStyle,
  } = props

  const RightIcon = rightIcon

  const headerRightIcon = () => {
    return rightIcon ? (
      <RightIcon width="24" height="24" />
    ) : (
      <Cross color={color.palette.darkGrey} width="24" height="24" {...rightIconStyle} />
    )
  }

  const LeftIcon = leftIcon
  const headerLeftIcon = leftIcon ? (
    <LeftIcon width="24" height="24" />
  ) : (
    <ArrowLeft width="24" height="24" />
  )

  const headerLeftPress = throttle((navigation, route) => {
    onHeaderLeftPress ? onHeaderLeftPress(navigation, route) : navigation.goBack()
  }, 2000, { leading: true, trailing: false })

  const headerRightPress = throttle((navigation, route) => {
    onHeaderRightPress ? onHeaderRightPress(navigation, route) : navigation.goBack()
  }, 2000, { leading: true, trailing: false })

  const presentationConfig =
    presentation === "modal"
      ? {
          ...Platform.select({
            ios: { presentation: "modal", gestureEnabled: false },
            android: {
              ...TransitionPresets.ModalSlideFromBottomIOS,
              gestureEnabled: false,
              presentation: "transparentModal",
              cardStyle: {
                marginTop: insets?.top,
                borderTopStartRadius: 20,
                borderTopEndRadius: 20,
              },
            },
          }),
        }
      : {}

  return ({ navigation, route }) => ({
    headerShown: headerShown,
    headerTitle: headerTitleTx ? translate(headerTitleTx) : headerTitle,
    headerTitleStyle: {
      ...presets.subTitleBold,
      color: color.palette.almostBlackGrey,
      textAlign: "center",
      allowFontScaling: false,
      ...headerTitleStyle,
    },
    headerStyle: {
      ...shadow.filterHeaderShadow,
      backgroundColor: color.palette.whiteGrey,
      ...headerStyle,
    },
    gestureEnabled,
    headerLeft() {
      return headerLeftShown ? (
        <TouchableOpacity
          style={headerLeft}
          onPress={() => headerLeftPress(navigation, route)}
          {...accessibility({
            testID: `Header__Touchable__BackButton`,
            accessibilityLabel: `Header__Touchable__BackButton`,
            OS: Platform.OS,
          })}
        >
          {headerLeftIcon}
        </TouchableOpacity>
      ) : null
    },
    headerRight() {
      return headerRightShown ? (
        <TouchableOpacity
          style={[headerRight, headerRightStyle]}
          onPress={() =>
            headerRightPress(navigation, route)
          }
          {...accessibility({
            testID: `Header__Touchable__CloseButton`,
            accessibilityLabel: `Header__Touchable__CloseButton`,
            OS: Platform.OS,
          })}
        >
          {headerRightIcon()}
        </TouchableOpacity>
      ) : null
    },
    headerTitleContainerStyle,
    ...presentationConfig,
  })
}

export const ForYouNavigator = ({ navigation }) => {
  const previousRouteName = usePreviousRouteName()

  useEffect(() => {
    const unsubscribeFocus = navigation.addListener("focus", () => {
      InteractionManager.runAfterInteractions(() => {
        setMMKVdata(ENUM_STORAGE_MMKV.IS_FIRST_APP, true)
      })
    })
    return unsubscribeFocus
  }, [navigation])
  const forYouOptions: StackNavigationOptions = {
    headerShown: false,
  }
  const redeemRewardDetailOptions: StackNavigationOptions = {
    headerShown: false,
    gestureEnabled: false,
  }

  const changiRewardOptions = generateScreenOptions({
    headerTitleTx: "changiRewardsDetail.header",
  })

  const settingOptions = generateScreenOptions({
    headerTitleTx: "setting.header",
    headerStyle: {
      ...Platform.select({
        ios: {
          shadowRadius: 6,
          shadowOpacity: 0.08,
          shadowOffset: { width: 0, height: 2 },
          backgroundColor: color.palette.whiteGrey,
          width: 288,
          height: 106,
        },
        android: {
          elevation: 3,
          backgroundColor: color.palette.whiteGrey,
          width: 288,
          height: 106,
        },
      }),
    },
    leftIcon: BackArrow,
  })

  const loginManagementOptions = generateScreenOptions({
    headerTitleTx: "loginManagement.header",
    headerStyle: {
      ...Platform.select({
        ios: {
          shadowRadius: 6,
          shadowOpacity: 0.08,
          shadowOffset: { width: 0, height: 2 },
          backgroundColor: color.palette.whiteGrey,
          width: 288,
          height: 106,
        },
        android: {
          elevation: 3,
          backgroundColor: color.palette.whiteGrey,
          width: 288,
          height: 106,
        },
      }),
    },
    leftIcon: BackArrow,
    onHeaderLeftPress: (navigation) => {
      navigation.popTo(NavigationConstants.settingScreen)
    },
  })

  const profileOptions = generateScreenOptions({
    headerTitleTx: "profile.header",
  })

  const linkedOptions = generateScreenOptions({
    headerTitleTx: "linkedMember.header",
  })

  const redemptionCatalogueOptions = generateScreenOptions({
    headerTitleTx: "changiRewardsDetail.rewardCatalogue",
  })

  const privacyPolicyScreenOptions = generateScreenOptions({
    headerTitleTx: "privacyPolicy.header",
  })

  const termsOfUsePolicyScreenOptions = generateScreenOptions({
    headerTitleTx: "termsOfUsePolicy.header",
  })

  const loginAndSecurityOptions = generateScreenOptions({
    headerTitleTx: "loginAndSecurity.header",
  })

  const aboutChangiLifeOptions = generateScreenOptions({
    headerTitleTx: "aboutChangiLife.header",
  })

  const toiletsOptions = generateScreenOptions({
    headerTitleTx: "toilets.header",
  })

  const askMaxViewOptions = generateScreenOptions({
    headerTitleTx: "askMax.header",
  })

  const settingNotificationsOptions = generateScreenOptions({
    headerTitleTx: "subscription.settingHeader",
    headerStyle: {
      ...shadow.noShadow,
      backgroundColor: color.palette.whiteGrey,
    },
    headerShadowVisible: false,
    leftIcon: ArrowLeftGray,
    onHeaderLeftPress(navigation, route) {
      if (route?.params?.isFromInbox) {
        navigation?.navigate(NavigationConstants.mainStack, {
          screen: NavigationConstants.notificationsScreen,
        })
      } else {
        navigation?.goBack?.()
      }
    },
  })

  const rateAirportExperienceOptions = generateScreenOptions({
    headerTitleTx: "rateAirportExperience.header",
  })

  const redeemRewardSuccessScreenOptions = generateScreenOptions({
    headerTitleTx: "redeemRewardSuccess.header",
    headerLeftShown: false,
    headerRightShown: true,
    rightIcon: CrossBlue,
    onHeaderRightPress: (newNavigation) => {
      if (previousRouteName === NavigationConstants.redemptionCatalogueScreen) {
        newNavigation.goBack()
      } else {
        newNavigation.replace(NavigationConstants.redemptionCatalogueScreen)
      }
    },
  })

  const privilegesScreenOptions = generateScreenOptions({
    headerTitleTx: "privilegesScreen.header",
    headerLeftShown: true,
    headerRightShown: false,
    headerStyle: {
      ...shadow.noShadow,
      backgroundColor: color.palette.whiteGrey,
    },
    headerShadowVisible: false,
  })

  const appInformationOptions = generateScreenOptions({
    headerTitle: "App Information",
  })

  const vouchersPrizesRedemptionsPageOptions = generateScreenOptions({
    headerTitleTx: "vouchersPrizesRedemptionsScreen.title",
    headerStyle: {
      ...shadow.noShadow,
      borderBottomColor: color.palette.lighterGrey,
      borderBottomWidth: 1,
      ...Platform.select({
        ios: {
          height: 116,
        },
      }),
    },
    headerShadowVisible: false,
    headerTitleContainerStyle: {
      maxWidth: Dimensions.get("window").width - 61 * 2, // Desired left/right margin (77) - default margin (16)
    },
    leftIcon: ArrowLeftGray,
    onHeaderLeftPress(navigation, route) {
      navigation?.goBack?.()
      trackAction(AdobeTagName.CAppVPR, {
        [AdobeTagName.CAppVPR]: AdobeValueByTagName.CAppVPRBack,
      })
    },
  })

  const changiRewardsPrivilegesScreenOptions = generateScreenOptions({})

  return (
    <>
      <Stack.Navigator
        initialRouteName={NavigationConstants.forYouScreen}
        screenOptions={{ headerTitleAlign: "center" }}
      >
        <Stack.Screen
          name={NavigationConstants.forYouScreen}
          component={ForYouScreen}
          options={forYouOptions}
        />
        <Stack.Screen
          name={NavigationConstants.changiRewardsDetailScreen}
          component={ChangiRewardsDetailScreen}
          options={changiRewardOptions}
        />
        <Stack.Screen
          name={NavigationConstants.settingScreen}
          component={SettingScreen}
          options={settingOptions}
        />
        <Stack.Screen
          name={NavigationConstants.profileScreen}
          component={ProfileScreen}
          options={profileOptions}
        />
        <Stack.Screen
          name={NavigationConstants.loginAndSecurityScreen}
          component={LoginAndSecurityScreen}
          options={loginAndSecurityOptions}
        />
        <Stack.Screen
          name={NavigationConstants.loginManagement}
          component={LoginManagement}
          options={loginManagementOptions}
        />
        <Stack.Screen
          name={NavigationConstants.linkedMembershipsScreen}
          component={LinkedMembershipsScreen}
          options={linkedOptions}
        />
        <Stack.Screen
          name={NavigationConstants.redemptionCatalogueScreen}
          component={RedemptionCatalogueScreen}
          options={redemptionCatalogueOptions}
        />
        <Stack.Screen
          name={NavigationConstants.privacyPolicy}
          component={PrivacyPolicyScreen}
          options={privacyPolicyScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.termsOfUsePolicy}
          component={TermsOfUsePolicy}
          options={termsOfUsePolicyScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.redeemRewardDetailScreen}
          component={RedeemRewardDetailScreenV2}
          options={redeemRewardDetailOptions}
        />
        <Stack.Screen
          name={NavigationConstants.aboutChangiLifeScreen}
          component={AboutChangiLifeScreen}
          options={aboutChangiLifeOptions}
        />
        <Stack.Screen
          name={NavigationConstants.settingNotificationsScreen}
          component={SettingNotificationsScreen}
          options={settingNotificationsOptions}
        />
        <Stack.Screen
          name={NavigationConstants.rateAirportExperienceScreen}
          component={RateAirportExperienceScreen}
          options={rateAirportExperienceOptions}
        />
        <Stack.Screen
          name={NavigationConstants.redeemRewardSuccessScreen}
          component={RedeemRewardSuccessScreen}
          options={redeemRewardSuccessScreenOptions}
        />
        <Stack.Screen
          name={NavigationConstants.vouchersPrizesRedemptionsScreen}
          component={VouchersPrizesRedemptionsPage}
          options={vouchersPrizesRedemptionsPageOptions}
        />
        <Stack.Screen
          name={NavigationConstants.privilegesScreen}
          component={PrivilegesScreen}
          options={privilegesScreenOptions}
        />
        <Stack.Screen
          name={"appInformation"}
          component={AppInformation}
          options={appInformationOptions}
        />
        <Stack.Screen
          name={NavigationConstants.toilets}
          component={Toilets}
          options={toiletsOptions}
        />
        <Stack.Screen
          name={NavigationConstants.askMax}
          component={AskMaxView}
          options={askMaxViewOptions}
        />

        <Stack.Screen
          name={NavigationConstants.changiRewardsPrivilegesScreen}
          component={ChangiRewardsPrivilegesScreen}
          options={changiRewardsPrivilegesScreenOptions}
        />
      </Stack.Navigator>
    </>

  )
}
