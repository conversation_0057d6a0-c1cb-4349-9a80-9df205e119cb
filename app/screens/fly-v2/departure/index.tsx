import NetInfo from "@react-native-community/netinfo"
import { AlertApp } from "app/components/alert-app/alert-app"
import { AlertTypes } from "app/components/alert-app/alert-app.props"
import { FeedBackToast, FeedBackToastType } from "app/components/feedback-toast"
import { FlightListingProps } from "app/components/flight-listing-card/flight-listing-card.props"
import { Text } from "app/elements/text"
import { useModal } from "app/hooks/useModal"
import { translate } from "app/i18n"
import AemActions, { AEM_PAGE_NAME, AemSelectors } from "app/redux/aemRedux"
import { FlightListingCreators, FlightListingSelectors } from "app/redux/flightListingRedux"
import { FlySelectors } from "app/redux/flyRedux"
import { MytravelCreators } from "app/redux/mytravelRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { FlightDirection } from "app/screens/fly/flights/flight-props"
import { commonComponentStyles } from "app/screens/fly/flights/flight-results/flight-result-styles"
import { FlyContext } from "app/screens/fly/flights/flight-results/flight-results"
import { SearchFlightsOptions } from "app/screens/search-v2/search-result/search-flights-result"
import Category from "app/screens/search-v2/search-result/search-flights-result/category"
import { FlightItem } from "app/screens/search-v2/search-result/search-flights-result/flight-item"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { simpleCondition } from "app/utils"
import {
  FlightDirection as BottomSheetFlightDirection,
  NavigationConstants,
  SOURCE_SYSTEM,
  TOAST_MESSAGE_DURATION,
} from "app/utils/constants"
import { trackActionOldFormat } from "app/utils/screen-helper"
import { isEmpty } from "lodash"
import moment from "moment"
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from "react"
import { Alert, SectionList, View } from "react-native"
import Animated, {
  Extrapolation,
  interpolate,
  runOnJS,
  useAnimatedScrollHandler,
  useSharedValue,
  withTiming,
} from "react-native-reanimated"
import { useDispatch, useSelector } from "react-redux"
import { LoadingSkeleton } from "../components"
import { EmptyState } from "../components/empty-state"
import FlightListToolbar from "../components/flight-list-toolbar"
import LoadingIndicator from "../components/loading-indicator"
import { useFlightListingContext } from "../contexts/flight-listing-context"
import { tabScreenStyles } from "../flight-listing.styles"
import { useDepartureFlightV2 } from "../hooks"
import { Gesture, GestureDetector } from "react-native-gesture-handler"
import { GESTURE_THRESHOLD } from "../flight-listing"
import FlightListingSubscription from "../subscriptors/flight-listing-subscription"

const COMPONENT_NAME = "DepartureListingScreen__"

const AnimatedSectionList = Animated.createAnimatedComponent(SectionList)

function DepartureScreen({ navigation }, ref) {
  // state
  const dispatch = useDispatch()

  const [isNoConnection, setNoConnection] = useState(false)

  const {
    openSearchBottomSheet,
    setTerminal,
    setAirline,
    setAirport,
    handleSelectTravelOptionAndOpenModal,
    selectedTab,
    currentScrollPosition,
    sharedRefreshing,
    contentPosition,
    setIsRefreshing,
  } = useFlightListingContext()

  const flightListingFilter: SearchFlightsOptions = useSelector(
    FlightListingSelectors.flightListingFilter,
  )
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const flightFilterOptions = useSelector(FlySelectors.flightFilterOptions)
  const dataCommonAEM = useSelector(AemSelectors.getMessagesCommon)
  const errorCommonAEM = useSelector(AemSelectors.getErrorsCommon)
  const { isLoadFlightAfter24h } = React.useContext(FlyContext)
  const { setLastUpdated, scrollY, isRefreshing } = useFlightListingContext()

  const {
    sectionList,
    isLoading,
    isEndLoadMore,
    isFocusedRef,
    lastUpdatedTime,
    startLoopApiCall,
    getFlyDepartureList,
    removeSavedFlight,
    cancelLoopApiJob,
    getEarlierFlights,
    isLoadingEarlierFlights,
    isEndEarlierFlights,
    networkFailedAttempt,
    isNetworkError,
    canLoadMore,
    setCanLoadMore,
    setNetworkError,
    setPreviousToken,
    isFirstLoadRef,
  } = useDepartureFlightV2()
  const { isModalVisible: isFilterModalVisible } = useModal("flightResultCategoryFilter")

  const willBeRefreshed = useSharedValue(true)
  const positionStartRefresh = useSharedValue(0)
  const scrollRef = useRef<SectionList>(null)
  const alertApp = useRef(null)
  const isAfterLogin = useRef(false)
  const filterRef = useRef({
    date: flightListingFilter?.date,
    keyword: flightListingFilter?.keyword,
    terminal: flightListingFilter?.terminal,
    airline: flightListingFilter?.airline,
    airport: flightListingFilter?.airport,
  })
  const toastForRemoveFlight = useRef(null)
  const toastForRefresh = useRef(null)
  const categoryRef = useRef(null)

  const terminalList = flightFilterOptions?.terminal || []
  const msg58 = dataCommonAEM?.find((e) => e?.code === "MSG58")
  const msg48 = dataCommonAEM?.find((e) => e?.code === "MSG48")
  const msg50 = dataCommonAEM?.find((e) => e?.code === "MSG50")
  const ehr44 = errorCommonAEM?.find((e) => e?.code === "EHR44")

  const listNotEmpty = useMemo(() => sectionList?.some((el) => el?.data?.length > 0), [sectionList])

  // func
  const handleInternetConnection = async ({
    onSuccess,
    onFail,
  }: {
    onSuccess?: () => void
    onFail?: () => void
  }) => {
    const { isConnected } = await NetInfo.fetch()

    if (isConnected) {
      setNoConnection(false)

      if (onSuccess && typeof onSuccess === "function") {
        onSuccess()
      }
    } else {
      setLastUpdated("")
      setIsRefreshing(false)
      contentPosition.value = withTiming(0)
      if (onFail && typeof onFail === "function") {
        onFail()
      }

      if (!isFirstLoadRef.current) {
        toastForRefresh?.current?.closeNow()
        toastForRefresh?.current?.show(TOAST_MESSAGE_DURATION)
      } else {
        setNoConnection(true)
      }
    }
  }

  const loadDepartureResults = ({ terminal, airline, airport }: Partial<SearchFlightsOptions>) => {
    getFlyDepartureList({
      direction: FlightDirection.departure,
      filterDate: moment(),
      filters: simpleCondition({
        condition: terminal?.length === terminalList.length - 1 || isEmpty(terminal),
        ifValue: [],
        elseValue: terminal,
      }),
      isFilter: false,
      isLoadFlightAfter24h: isLoadFlightAfter24h,
      filterAirline: airline ?? "",
      filterCityAirport: airport ?? "",
    })
    startLoopApiCall(handleRefresh)
  }

  const refreshDepartureFlights = ({
    terminal,
    airline,
    airport,
  }: Partial<SearchFlightsOptions>) => {
    // prevent refresh data while user typing search keyword
    // if (userTyping.current) {
    //   return
    // }
    loadDepartureResults({ terminal, airline, airport })
    dispatch(
      AemActions.getAemConfigData({
        name: AEM_PAGE_NAME.TICKER_BAND_FLIGHT,
        pathName: "getTickerbandFly",
        forceRequest: true,
      }),
    )
  }

  const handleRefresh = async () => {
    handleInternetConnection({
      onSuccess: () => {
        if (willBeRefreshed.value) {
          setNetworkError(false)
          refreshDepartureFlights({
            terminal: filterRef.current.terminal,
            airline: filterRef.current.airline,
            airport: filterRef.current.airport,
          })
          dispatch(MytravelCreators.flyClearInsertFlightPayload())
        }
      },
    })
  }

  const scrollListToTop = () => {
    willBeRefreshed.value = true
    if (sectionList.length > 0) {
      scrollRef.current?.scrollToLocation({
        animated: true,
        sectionIndex: 0,
        itemIndex: 0,
        viewPosition: 0,
        viewOffset: 120,
      })
    }
  }

  const onFilter = async ({
    date,
    keyword,
    terminal,
    airline,
    airport,
  }: Partial<SearchFlightsOptions>) => {
    handleInternetConnection({
      onSuccess: () => {
        // if (date) {
        //   trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
        //     [AdobeTagName.CAppFlightListingFlightListFilter]: moment(date).format("YYYY-MM-DD"),
        //   })
        // }
        // if (keyword) {
        //   trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
        //     [AdobeTagName.CAppFlightListingFlightListFilter]: `${keyword}`,
        //   })
        // }
        if (terminal) {
          filterRef.current.terminal = terminal
          trackAction(AdobeTagName.CAppFlightListingFlightListFilter, {
            [AdobeTagName.CAppFlightListingFlightListFilter]: `${terminal.join("|")}`,
          })
        }
        if (airline) {
          filterRef.current.airline = airline
        }
        if (airport) {
          filterRef.current.airport = airport
        }
        scrollListToTop()
        setTimeout(() => {
          scrollY.value = withTiming(0, {
            duration: 300,
          })
        }, 300)
        refreshDepartureFlights({ terminal, airline, airport })

        // Update the context values so that the search bottom sheet can navigate to SearchResult screen with the correct filters.
        setTerminal(terminal)
        setAirline(airline)
        setAirport(airport)
      },
    })
  }

  const onFlightPress = (item) => {
    const flightDate = item.flightDate
    const flightNumber = item.flightNumber
    const direction = item.direction

    trackAction(AdobeTagName.CAppFlightListingFlightCardClicked, {
      [AdobeTagName.CAppFlightListingFlightCardClicked]: "1",
    })
    trackAction(AdobeTagName.CAppFlightListingViewFlightDetails, {
      [AdobeTagName.CAppFlightListingViewFlightDetails]: `${direction}|${flightDate}|${flightNumber}`,
    })
    //@ts-ignore
    navigation.navigate("flightDetails", {
      payload: {
        item: item,
      },
      direction: FlightDirection.departure,
    })
  }

  const handleMessage58 = (message, flyItem) => {
    if (message) {
      let status = flyItem?.flightStatus?.toLowerCase()
      if (status?.includes("cancelled")) {
        status = `been ${status}`
      }
      return message
        .replace("<Flight No.>", flyItem?.flightNumber)
        .replace("<departed/landed/been cancelled>", status)
    }
    return message
  }

  const handleMessage48 = (message, number, place) => {
    if (message) {
      return message.replace("<Flight No.>", number).replace("<country>", place)
    }
    return message
  }

  const notAbleToSaveAlert = (flyItem) => {
    const temp = flyItem?.flightStatus?.split(" ")
    const status = temp?.length > 0 ? temp[0] : ""
    const message =
      handleMessage58(msg58?.message, flyItem) ||
      `${translate("flightLanding.flight")} ${flyItem?.flightNumber} ${translate(
        "flightLanding.has",
      )} ${status} ${translate("flightLanding.notSaveMessage")}`
    alertApp?.current?.show({
      title: msg58?.title || translate("flightLanding.alert"),
      description: message,
      labelAccept: msg58?.firstButton || translate("flightLanding.okay"),
      onAccept: () => null,
      type: AlertTypes.ALERT,
    })
  }

  const onRemoveFlight = (payload) => {
    const item = payload?.item
    trackAction(AdobeTagName.CAppFlightListingRemoveFlight, {
      [AdobeTagName.CAppFlightListingRemoveFlight]: "1",
    })
    Alert.alert(
      msg48?.title || translate("flightLanding.areYouSure"),
      msg48?.message
        ? handleMessage48(msg48?.message, item?.flightNumber, item?.destinationPlace)
        : `${translate("flightLanding.removeMessage1")} ${item?.flightNumber} ${translate(
            "flightLanding.to",
          )} ${item?.destinationPlace} ${translate("flightLanding.removeMessage2")}`,
      [
        {
          text: msg48?.firstButton || translate("flightLanding.cancel"),
        },
        {
          text: msg48?.secondButton || translate("flightLanding.remove"),
          style: "cancel",
          onPress: () => {
            removeSavedFlight(
              payload,
              () => {
                const action = "Unsave"
                const flyProfile = "flying"
                const flightStatus = "Successful"
                trackActionOldFormat(AdobeTagName.CAppFlightListingSaveFlight, {
                  pageName: AdobeTagName.CAppFlightListing,
                  flightNumber: item?.flightNumber,
                  flightDirection: FlightDirection.arrival,
                  flightDate: item?.flightDate,
                  flyProfile: flyProfile,
                  action: action,
                  flightStatus: flightStatus,
                })
                toastForRemoveFlight?.current?.show(TOAST_MESSAGE_DURATION)
                dispatch(MytravelCreators.flyClearInsertFlightPayload())
              },
              () => {
                const action = "Unsave"
                const flyProfile = "flying"
                const flightStatus = "Failed"
                trackActionOldFormat(AdobeTagName.CAppFlightListingSaveFlight, {
                  pageName: AdobeTagName.CAppFlightListing,
                  flightNumber: item?.flightNumber,
                  flightDirection: FlightDirection.arrival,
                  flightDate: item?.flightDate,
                  flyProfile: flyProfile,
                  action: action,
                  flightStatus: flightStatus,
                })
              },
            )
          },
        },
      ],
    )
  }

  const onSaveFlight = ({ flight, isSaved, canSaveFlight }) => {
    if (isLoggedIn && isSaved) {
      onRemoveFlight({ item: flight })
    } else {
      if (!canSaveFlight) {
        notAbleToSaveAlert(flight)
      } else {
        const saveFlight = flight as FlightListingProps
        if (isLoggedIn) {
          handleSelectTravelOptionAndOpenModal(saveFlight)
        } else {
          isAfterLogin.current = true
          navigation.navigate(NavigationConstants.authScreen, {
            sourceSystem: SOURCE_SYSTEM.FLIGHTS,
            callBackAfterLoginSuccess: () => {
              handleSelectTravelOptionAndOpenModal(saveFlight)
            },
            callBackAfterLoginCancel: () => null,
          })
        }
      }
    }
  }

  const onRenderItem = ({ item, index }) => {
    return (
      <View style={commonComponentStyles.flatListItemStyle}>
        <FlightItem
          flight={item}
          isLoggedIn={isLoggedIn}
          onPressed={onFlightPress}
          onSaveFlight={onSaveFlight}
          isFirstFlight={index === 0}
        />
      </View>
    )
  }

  const renderSectionHeader = ({ section: { title, data } }) => {
    return (
      <View>
        <Text
          preset="caption2Regular"
          text={title?.toUpperCase()}
          style={[tabScreenStyles.sectionContainer, data?.length === 0 && { marginBottom: 8 }]}
        />
        {data?.length === 0 && (
          <Text
            preset={"caption2Bold"}
            tx={"flightListingV2.noFlightsForTheDay"}
            style={tabScreenStyles.noFlightsText}
          />
        )}
      </View>
    )
  }

  const handleLoadMore = () => {
    if (isEndLoadMore || isLoading || !canLoadMore) {
      return
    }
    handleInternetConnection({
      onSuccess: () => {
        getFlyDepartureList({
          direction: FlightDirection.departure,
          filterDate: filterRef.current?.date ? moment(filterRef.current?.date) : moment(),
          filters: simpleCondition({
            condition:
              filterRef.current?.terminal?.length === terminalList.length - 1 ||
              isEmpty(filterRef.current?.terminal),
            ifValue: [],
            elseValue: filterRef.current?.terminal,
          }),
          isFilter: false,
          isLoadFlightAfter24h: isLoadFlightAfter24h,
          isLoadMore: true,
          filterAirline: filterRef.current?.airline ?? "",
          filterCityAirport: filterRef.current?.airport ?? "",
        })
      },
      onFail: () => {
        setCanLoadMore(false)
      },
    })
  }

  const handleSearchPress = () => {
    openSearchBottomSheet(BottomSheetFlightDirection.Departure)
  }

  const footerListFlight = () => {
    const isEndPage = isEndLoadMore
    if (isLoading) {
      return <LoadingIndicator />
    }

    if (isEndPage && listNotEmpty) {
      return (
        <Text
          tx={"flightLanding.noMoreFlights"}
          preset={"caption1Regular"}
          style={commonComponentStyles.noFlightListingStyle}
        />
      )
    }
    return null
  }

  const onFacetStateChange = (val: boolean) => {
    if (val) {
      cancelLoopApiJob()
      willBeRefreshed.value = false
    } else {
      startLoopApiCall(handleRefresh)
      willBeRefreshed.value = true
    }
  }

  const onGetEarlierFlights = () => {
    if (isLoadingEarlierFlights) {
      return
    }
    handleInternetConnection({
      onSuccess: () => {
        getEarlierFlights({
          direction: FlightDirection.departure,
          filterDate: filterRef.current?.date ? moment(filterRef.current?.date) : moment(),
          filters: simpleCondition({
            condition:
              filterRef.current?.terminal?.length === terminalList.length - 1 ||
              isEmpty(filterRef.current?.terminal),
            ifValue: [],
            elseValue: filterRef.current?.terminal,
          }),
          isFilter: false,
          isLoadFlightAfter24h: isLoadFlightAfter24h,
          filterAirline: filterRef.current?.airline ?? "",
          filterCityAirport: filterRef.current?.airport ?? "",
        })
      },
    })
  }

  const onRefresh = async () => {
    handleInternetConnection({
      onSuccess: () => {
        toastForRefresh?.current?.closeNow()
        willBeRefreshed.value = true
        handleRefresh()
      },
    })
  }

  const enableLoadMore = () => {
    setCanLoadMore(true)
  }

  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      const { contentOffset, contentSize, layoutMeasurement } = event

      scrollY.value = event.contentOffset.y
      currentScrollPosition.value = event.contentOffset.y

      if (event.contentOffset.y <= 20) {
        willBeRefreshed.value = true
      } else {
        willBeRefreshed.value = false
      }

      if (layoutMeasurement.height + contentOffset.y <= contentSize.height - 100 && !canLoadMore) {
        runOnJS(enableLoadMore)()
      }
    },
  })

  const onRenderListHeader = () => {
    return (
      <>
        <FlightListToolbar
          onGetEarlierFlights={onGetEarlierFlights}
          componentName={COMPONENT_NAME}
        />
        {isLoadingEarlierFlights && !isEndEarlierFlights && <LoadingIndicator />}
        {isEndEarlierFlights && (
          <Text
            tx={"flightLanding.noEarlierFlights"}
            preset={"caption2Bold"}
            style={tabScreenStyles.noEarlierFlights}
          ></Text>
        )}
      </>
    )
  }

  const renderList = () => {
    if (isNoConnection) {
      return (
        <EmptyState.NoInternet
          containerStyle={tabScreenStyles.errorContainer}
          onPressReload={handleRefresh}
        />
      )
    }
    if (isNetworkError) {
      return (
        <EmptyState.General
          containerStyle={tabScreenStyles.errorContainer}
          textTitle={ehr44?.header ?? translate("flightListingV2.currentlyUnavailable")}
          textDescription={
            ehr44?.subHeader ?? translate("flightListingV2.responseErrorDescription")
          }
          textButtonTitle={ehr44?.buttonLabel ?? translate("errorOverlay.variant3.retry")}
          callback={handleRefresh}
        />
      )
    }
    if (isRefreshing) {
      return <LoadingSkeleton />
    }
    return (
      <GestureDetector gesture={Gesture.Simultaneous(gesture, Gesture.Native())}>
        <AnimatedSectionList
          sections={sectionList}
          keyExtractor={(_item, index) =>
            `${(_item as any)?.flightNumber} ${(_item as any)?.scheduledDate} ${index.toString()}`
          }
          // @ts-ignore
          renderSectionHeader={renderSectionHeader}
          stickySectionHeadersEnabled={false}
          renderItem={onRenderItem}
          ListFooterComponent={footerListFlight}
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={16}
          onEndReachedThreshold={0.4}
          onEndReached={handleLoadMore}
          maxToRenderPerBatch={15}
          style={tabScreenStyles.listStyle}
          contentContainerStyle={tabScreenStyles.listContainer}
          windowSize={15}
          ref={scrollRef}
          onScroll={scrollHandler}
          // refreshControl={<RefreshControl refreshing={false} onRefresh={onRefresh} />}
          ListHeaderComponent={onRenderListHeader}
          bounces={false}
          scrollEnabled={!isRefreshing}
        />
      </GestureDetector>
    )
  }

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onRefresh()
    },
  }))

  useEffect(() => {
    filterRef.current = {
      date: flightListingFilter?.date,
      keyword: flightListingFilter?.keyword,
      terminal: flightListingFilter?.terminal,
      airline: flightListingFilter?.airline,
      airport: flightListingFilter?.airport,
    }
  }, [flightListingFilter])

  useEffect(() => {
    if (isFilterModalVisible) {
      cancelLoopApiJob()
      willBeRefreshed.value = false
    } else {
      startLoopApiCall(handleRefresh)
      willBeRefreshed.value = true
    }
  }, [isFilterModalVisible])

  useEffect(() => {
    if (networkFailedAttempt > 0) {
      toastForRefresh?.current?.closeNow()
      toastForRefresh?.current?.show(TOAST_MESSAGE_DURATION)
    }
  }, [networkFailedAttempt])

  useEffect(() => {
    if (selectedTab === FlightDirection.departure) {
      setPreviousToken('');
      setNetworkError(false);
      if (!isAfterLogin.current) {
        onFilter({
          terminal: filterRef.current.terminal,
          airline: filterRef.current.airline,
          airport: filterRef.current.airport,
        })
      } else {
        isAfterLogin.current = false
      }
      isFocusedRef.current = true
    } else {
      categoryRef.current?.unselectTab();
    }
    return () => {
      isFocusedRef.current = false
    }
  }, [selectedTab])

  const gesture = Gesture.Pan()
    .onBegin((e) => {
      if (currentScrollPosition.value === 0) {
        positionStartRefresh.value = e.y
      }
    })
    .onChange((e) => {
      if (currentScrollPosition.value === 0) {
        if (positionStartRefresh.value > 0) {
          if (positionStartRefresh.value + GESTURE_THRESHOLD <= e.y) {
            contentPosition.value = interpolate(
              e.translationY,
              [0, GESTURE_THRESHOLD * 3],
              [0, GESTURE_THRESHOLD],
              Extrapolation.CLAMP,
            )
            sharedRefreshing.value = 1
          } else {
            sharedRefreshing.value = 0
          }
        } else {
          positionStartRefresh.value = e.y
          sharedRefreshing.value = 0
          contentPosition.value = 0
        }
      } else {
        sharedRefreshing.value = 0
        contentPosition.value = 0
      }
    })
    .onEnd(() => {
      if (sharedRefreshing.value === 1 && selectedTab === FlightDirection.departure) {
        contentPosition.value = withTiming(Math.max(contentPosition.value, GESTURE_THRESHOLD))
        runOnJS(onRefresh)()
      } else {
        contentPosition.value = withTiming(0)
      }
    })
    .onFinalize(() => {
      sharedRefreshing.value = 0
    })

  return (
    <View style={tabScreenStyles.container}>
      <Category
        ref={categoryRef}
        componentName={COMPONENT_NAME}
        onSearchPress={handleSearchPress}
        filter={{
          terminal: flightListingFilter.terminal,
          direction: flightListingFilter.direction,
          airline: flightListingFilter.airline,
          cityAirport: flightListingFilter.airport,
        }}
        onFilterFlight={(options) => {
          dispatch(
            FlightListingCreators.setFlightListingFilter({
              ...flightListingFilter,
              terminal: options.terminal,
              airline: options.airline,
              airport: options.cityAirport,
            }),
          )
          onFilter({
            terminal: options.terminal,
            airline: options.airline,
            airport: options.cityAirport,
          })
        }}
        isShowDirection={false}
        isPaneAbsoluteToRoot={false}
        containerStyle={tabScreenStyles.filterContainer}
        onFacetStateChange={onFacetStateChange}
        disabledCategory={isLoading}
      />
      {renderList()}
      <FeedBackToast
        ref={toastForRemoveFlight}
        style={tabScreenStyles.feedBackToastStyle}
        textButtonStyle={tabScreenStyles.toastButtonStyle}
        position={"custom"}
        positionValue={{ bottom: 8 }}
        textStyle={tabScreenStyles.toastTextStyle}
        type={FeedBackToastType.smallFeedBack}
        text={msg50?.message || translate("flyLanding.removeFlight")}
      />
      <FeedBackToast
        ref={toastForRefresh}
        style={tabScreenStyles.feedBackToastStyle}
        textButtonStyle={tabScreenStyles.toastButtonStyle}
        position={"custom"}
        textStyle={tabScreenStyles.toastTextStyle}
        type={FeedBackToastType.fullWidthFeedBack}
        text={translate("flightLanding.feedBackToastErrorMessage") + lastUpdatedTime}
      />
      <AlertApp ref={alertApp} />
      <FlightListingSubscription direction={FlightDirection.departure} />
    </View>
  )
}

export default forwardRef(DepartureScreen)
