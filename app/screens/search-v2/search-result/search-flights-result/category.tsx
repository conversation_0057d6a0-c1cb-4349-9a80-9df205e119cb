import { forwardRef, useImperativeHandle, useMemo, useRef, useState } from "react"
import { FlatList, StyleSheet, TouchableOpacity, View, ViewStyle } from "react-native"
import {
  ArrowDown,
  AccordionUp as ArrowUp,
  FilterV2,
  LocationOutline,
  PlaneOutlineArrival,
  PlaneOutlineIcon,
  PlaneOutlineDeparture,
  PublicAreaIconV2,
  SearchIconV2,
} from "assets/icons"
import { color } from "app/theme"
import { Chip as Chip, ChipState } from "./chip"
import { ExpandablePanel } from "./ExpandablePanel"
import { Direction } from "./sections/direction"
import { FlightDirection } from "app/utils/constants"
import { Location } from "./sections/location"
import FlightFilter from "./flight-filter/flight-filter"
import { NavigationType } from "app/screens/fly/flights/flight-props"
import { AirLine } from "./sections/airline"
import { AirportCity } from "./sections/airport-city"
import { defaultListLocation, FILTER_ALL } from "./consts"
import { useSelector } from "react-redux"
import { FlySelectors } from "app/redux/flyRedux"
import { useModal } from "app/hooks/useModal"
import { isEmpty } from "lodash"

const COMPONENT_NAME = "SearchV2_FlightFilter_Facets"

const iconStyle = {
  width: 16,
  height: 16,
  color: color.palette.darkestGrey,
}

const iconAppliedStyle = {
  color: color.palette.lightPurple,
}

enum FacetID {
  Direction = "Direction",
  Terminal = "Terminal",
  Airline = "Airline",
  AirportCity = "AirportCity",
}

type CategoryFilter = {
  terminal?: string[]
  direction?: FlightDirection
  airline?: string
  cityAirport?: string
}

type CategoryProps = {
  filter: CategoryFilter
  onFilterFlight: (data: CategoryFilter) => void
  isShowDirection?: boolean;
  isPaneAbsoluteToRoot?: boolean;
  componentName?: string;
  containerStyle?: ViewStyle;
  onSearchPress?: () => void;
  onFacetStateChange?: (value: boolean) => void;
  disabledCategory?: boolean;
}

const Category = (props: CategoryProps, ref) => {
  const {
    filter,
    isShowDirection = true,
    isPaneAbsoluteToRoot = true,
    componentName = COMPONENT_NAME,
    containerStyle,
    onSearchPress,
    onFacetStateChange,
    disabledCategory,
  } = props
  const flightFilterOptions = useSelector(FlySelectors.flightFilterOptions)
  const {isModalVisible, openModal, closeModal} = useModal("flightResultCategoryFilter")
  const [selectedTab, setSelectedTab] = useState(undefined)

  const locations = useMemo(() => {
    return !filter.terminal || filter.terminal.length === defaultListLocation.length - 1
      ? defaultListLocation.map((item) => ({ ...item, selected: true }))
      : defaultListLocation.map((item) => ({
        ...item,
        selected: filter.terminal?.some((v) => v === item.tagName),
      }))
  }, [filter.terminal])

  const anchorRef = useRef(null)
  const facetList = useMemo(() => {
    return [
      isShowDirection && {
        id: FacetID.Direction,
        title: "Direction",
        iconLeft:
          filter?.direction === FlightDirection.Arrival ? (
            <PlaneOutlineArrival />
          ) : (
            <PlaneOutlineDeparture />
          ),
        values: [filter?.direction === FlightDirection.Arrival ? "Arrival" : "Departure"],
      },
      {
        id: FacetID.Terminal,
        title: "Terminal",
        iconLeft: <LocationOutline />,
        values: locations[0].selected
          ? []
          : locations.filter((item) => item.selected).map((item) => item.tagCode),
      },
      {
        id: FacetID.Airline,
        title: "Airline",
        iconLeft: <PlaneOutlineIcon />,
        values: filter?.airline && [
          flightFilterOptions?.airline?.find(
            (airlineElement) => airlineElement?.value === filter.airline,
          ).name,
        ],
      },
      {
        id: FacetID.AirportCity,
        title: "Airport/City",
        iconLeft: <PublicAreaIconV2 />,
        values: filter?.cityAirport && [
          flightFilterOptions?.cityAirport?.find((cityAirportElement) => {
            return cityAirportElement?.value === filter.cityAirport
          }).name,
        ],
      },
    ].filter(Boolean)
  }, [filter?.direction, locations, filter?.airline, filter.cityAirport])

  const onRemoveSelectedTab = () => {
    setSelectedTab(undefined);
    if (onFacetStateChange && typeof onFacetStateChange === 'function') {
      onFacetStateChange(false);
    }
  }

  const renderHeader = () => {
    const hasFilterTerminals = !locations[0].selected && filter?.terminal
    const isFilterActive = !!hasFilterTerminals || !!filter?.airline || !!filter.cityAirport
    const iconProps = { ...iconStyle, ...(isFilterActive ? iconAppliedStyle : {}) }

    return (
      <View style={{ flexDirection: "row", gap: 4 }}>
        {!!onSearchPress && (
          <TouchableOpacity
            disabled={disabledCategory}
            onPress={onSearchPress}
            testID={`${componentName}__Search`}
            accessibilityLabel={`${componentName}__Search`}
            style={[
              styles.item,
              { height: 30 },
            ]}
          >
            <SearchIconV2 {...iconStyle} />
          </TouchableOpacity>
        )}

        <TouchableOpacity
          disabled={disabledCategory}
          onPress={onPressFilter}
          testID={`${componentName}__FilterAll`}
          accessibilityLabel={`${componentName}__FilterAll`}
          style={[
            styles.item,
            isFilterActive && { ...styles.itemActive, ...styles.paddingRight10 },
            { height: 30 },
          ]}
        >
          <FilterV2 {...iconProps} />
          {isFilterActive && <View style={styles.dotActive} />}
        </TouchableOpacity>
      </View>

    )
  }

  const handleFilterFlight = (newFilter) => {
    const terminalFilter = isEmpty(newFilter.terminal)
      ? flightFilterOptions?.terminal?.filter((el) => !isEmpty(el?.value))?.map((el) => el?.value)
      : newFilter.terminal

    props.onFilterFlight({
      terminal: terminalFilter,
      direction: newFilter.direction,
      airline: newFilter.airline === FILTER_ALL.toLowerCase() ? undefined : newFilter.airline,
      cityAirport:
        newFilter.cityAirport === FILTER_ALL.toLowerCase() ? undefined : newFilter.cityAirport,
    })
    onRemoveSelectedTab()
  }

  const onPressFilter = () => {
    openModal()
  }

  const onPressItem = (item) => {
    if (selectedTab === item.id) {
      onRemoveSelectedTab();
      return;
    } 
    setSelectedTab(item.id)
    if (onFacetStateChange && typeof onFacetStateChange === "function") {
      onFacetStateChange(true)
    }
  }

  const renderItem = ({ item, index }) => {
    if (!item) return null;
    const isActive = !!item?.values?.length
    let state: ChipState = undefined
    if (!!item?.values?.length) state = "applied"
    if (selectedTab && selectedTab == item.id) state = "active"
    const iconProps = { ...iconStyle, ...(isActive ? iconAppliedStyle : {}) }
    let icon = <ArrowDown {...iconProps} />
    if (state === "active") {
      icon = <ArrowUp {...iconProps} />
    }
    return (
      <Chip
        title={item?.values?.join(", ") || item.title}
        state={state}
        onPress={() => onPressItem(item)}
        iconLeft={item.iconLeft}
        iconRight={icon}
        disabled={disabledCategory}
      />
    )
  }

  useImperativeHandle(ref, () => ({
    unselectTab: () => {
      setSelectedTab(undefined)
    }
  }))

  return (
    <>
      <View onLayout={() => {}} ref={anchorRef} style={[styles.container, containerStyle]}>
        <FlatList
          horizontal={true}
          contentContainerStyle={styles.contentContainer}
          data={facetList}
          renderItem={renderItem}
          showsHorizontalScrollIndicator={false}
          ListHeaderComponent={renderHeader}
          keyExtractor={(item, index) => `${item.title}-${index}`}
        />

        <FlightFilter
          visible={isModalVisible}
          direction={filter.direction}
          airline={filter.airline}
          airport={filter.cityAirport}
          terminal={filter.terminal}
          navigationType={NavigationType.FlightsResult}
          onClosedSheet={closeModal}
          handleApplyFilter={(filterOption) => {
            handleFilterFlight({
              terminal: filterOption.terminal,
              direction: filterOption.direction,
              airline: filterOption.airline,
              cityAirport: filterOption.cityAirport,
            })
            closeModal()
          }}
          testID={`${componentName}__FlightFilterModal`}
          accessibilityLabel={`${componentName}__FlightFilterModal`}
        />
      </View>
      {!!selectedTab && (
        <ExpandablePanel
          anchor={anchorRef}
          isExpanded={!!selectedTab}
          onDismiss={onRemoveSelectedTab}
          isPaneAbsoluteToRoot={isPaneAbsoluteToRoot}
        >
          <View style={styles.expandableContent}>
            {selectedTab === FacetID.Direction && (
              <Direction
                onSelect={(direction) => {
                  handleFilterFlight({
                    terminal: filter.terminal,
                    direction: direction,
                    airline: filter.airline,
                    cityAirport: filter.cityAirport,
                  })
                }}
                direction={filter.direction}
                onClosedSheet={onRemoveSelectedTab}
              />
            )}
            {selectedTab === FacetID.Terminal && (
              <Location
                items={locations}
                onApplyFilter={(data) => {
                  handleFilterFlight({
                    terminal: data
                      .slice(1)
                      .filter((t) => t.selected)
                      .map((t) => t.tagName),
                    direction: filter.direction,
                    airline: filter.airline,
                    cityAirport: filter.cityAirport,
                  })
                }}
                onClosedSheet={onRemoveSelectedTab}
              />
            )}
            {selectedTab === FacetID.Airline && (
              <AirLine
                airline={filter.airline}
                onClosedSheet={onRemoveSelectedTab}
                onSelect={(airline) =>
                  handleFilterFlight({
                    terminal: filter.terminal,
                    direction: filter.direction,
                    airline: airline,
                    cityAirport: filter.cityAirport,
                  })
                }
              />
            )}
            {selectedTab === FacetID.AirportCity && (
              <AirportCity
                airport={filter.cityAirport}
                onClosedSheet={onRemoveSelectedTab}
                onSelect={(airport) =>
                  handleFilterFlight({
                    terminal: filter.terminal,
                    direction: filter.direction,
                    airline: filter.airline,
                    cityAirport: airport,
                  })
                }
              />
            )}
          </View>
        </ExpandablePanel>
      )}
    </>
  )
}

export default forwardRef(Category)

const styles = StyleSheet.create({
  container: {
    paddingTop: 10,
  },
  contentContainer: {
    gap: 4,
    paddingHorizontal: 16,
  },
  item: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 99,
    borderWidth: 1,
    height: 28,
    borderColor: color.palette.lighterGrey,
  },
  itemActive: {
    backgroundColor: color.palette.lightestPurple,
    borderColor: color.palette.purpleD5BBEA,
    paddingRight: 6,
  },
  paddingRight10: {
    paddingRight: 10,
  },
  text: {
    color: color.palette.darkestGrey,
  },
  textActive: {
    color: color.palette.lightPurple,
  },
  dotActive: {
    width: 7,
    height: 7,
    borderWidth: 1,
    borderColor: color.palette.whiteGrey,
    backgroundColor: color.palette.lightPurple,
    borderRadius: 10,
    position: "absolute",
    top: 0,
    right: 0,
  },
  expandableContent: {
    marginTop: 10,
    borderTopWidth: 1,
    borderStyle: "solid",
    borderColor: color.palette.lighterGrey,
    backgroundColor: color.palette.whiteGrey
  },
})
