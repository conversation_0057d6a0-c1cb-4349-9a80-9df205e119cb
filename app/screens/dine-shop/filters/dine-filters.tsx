import { Button } from "app/elements/button/button"
import { Checkbox } from "app/elements/checkbox/checkbox"
import { Chip } from "app/elements/chip/chip"
import { ChipType } from "app/elements/chip/chip.props"
import { Text } from "app/elements/text/text"
import { translate } from "app/i18n"
import Creators, { Dine, DineSelectors } from "app/redux/dineRedux"
import { RootState } from "app/redux/store"
import { color } from "app/theme"
import { NavigationConstants } from "app/utils/constants"
import React, { useEffect, useMemo } from "react"
import { ScrollView, View, TouchableOpacity, InteractionManager } from "react-native"
import LinearGradient from "react-native-linear-gradient"
import { useDispatch, useSelector } from "react-redux"
import { ErrorScreen } from "../dine-shop-offer-details/error"
import * as styles from "./dine-shop-filter.styles"
import { isEmpty } from "lodash"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { handleCondition } from "app/utils"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { useFocusEffect, useNavigation } from "@react-navigation/native"
import { CloseCross } from "ichangi-fe/assets/icons"
import { presets } from "app/elements/text"
import { FilterValueDine } from "app/redux/types/dine-shop/dine-shop-filter"
import PageConfigCreators from "app/redux/pageConfigRedux"
import { setIsShowModalCheckRatingPopup } from "app/utils/storage/mmkv-storage"
const closeIconStyles: any = { color: color.palette.lightPurple }

const myParams = ["locations", "areas"]
const COMPONENT_NAME = "DineFilter"

const DineFilter = (props) => {
  const { showFilterModal, setShowFilterModal } = props
  const dispatch = useDispatch()
  const navigation = useNavigation()
  let filterState = useSelector((state) => DineSelectors.filterItems(state))
  const filterTitles: any = useSelector<RootState, Dine>((state) =>
    DineSelectors.filterTitles(state),
  )

  if (!filterState) {
    filterState = []
  }

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      dispatch(Creators.dineFilterParametersRequest())
    })
  }, [])

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        InteractionManager.runAfterInteractions(() => {
          setIsShowModalCheckRatingPopup(false)
          setShowFilterModal(false)
        })
      }
    }, []),
  )

  const handleClearAllFilters = () => {
    dispatch(Creators.dineResetFilterItems())
  }

  const dineFilterParameters = useSelector((state) => DineSelectors.filtersParametersData(state))
  const filterPillsPayload = useSelector((state) => DineSelectors.filterPillsPayload(state))

  const isNotFilterable = !dineFilterParameters || dineFilterParameters?.length === 0
  const filterableStyles = useMemo(() => {
    return isNotFilterable
    ? {
        label: { color: color.palette.darkGrey },
        wrapper: { backgroundColor: color.palette.lightGrey },
        closeIcon: { color: color.palette.darkestGrey },
      }
    : {}
  }, [isNotFilterable])

  const hasError = filterPillsPayload?.errorFlag
  const handleAllToggle = (item: any, childTags: any) => {
    dispatch(Creators.dineHandleCheckboxFilterItems(item, childTags))
  }
  const handleState = (item: string, child: any) => {
    dispatch(Creators.dineHandleFilterDetails(item, child))
  }

  const getContainerDineFilterStyle = (item) => {
    if (item?.tagName === myParams[0]) {
      return styles.flexColumn
    }

    if (myParams.includes("area")) {
      return styles.firstStyle
    }

    return styles.secondStyle
  }

  const getContainerCheckBoxStyle = (item, index) => {
    const firstStyle =
      item?.tagName === myParams[1]
        ? styles.areaCheckboxContainer
        : styles.locationCheckboxContainer
    const secondStyle = index > 0 && item?.tagName === myParams[1] ? styles.marginForArea : null

    return [firstStyle, secondStyle]
  }

  const getValueCheckBox = (item, childTag, checkifExists) => {
    const isActiveCheckBox = item.childTags.length === checkifExists?.child.length ? item : null

    return checkifExists?.child.indexOf(childTag.tagName) >= 0 || isActiveCheckBox
  }

  const renderCheckBoxAll = (item, index, checkifExists) => {
    if (item?.tagName === myParams[0] && index === 0) {
      return (
        <>
          <View style={styles.allCheckContainer}>
            <Checkbox
              onToggle={() => handleAllToggle(item.tagName, item.childTags)}
              value={item.childTags.length === checkifExists?.child.length}
              testID={`${COMPONENT_NAME}__CheckBoxAllToggle__${index}`}
              accessibilityLabel={`${COMPONENT_NAME}__CheckBoxAllToggle__${index}`}
              outlineStyle={{
                borderColor: color.palette.darkGrey,
              }}
              text={translate("dineShopFilter.all")}
              textStyle={{ ...presets.bodyTextRegular, ...styles.checkboxTitle }}
            />
          </View>
          <View style={styles.lineGrey} />
        </>
      )
    }
    return null
  }

  const renderCheckBoxList = (item, ind, checkifExists, childTag) => {
    const isColumnCheckBox = item?.tagName?.includes("area")
    return (
      <View
        key={ind}
        style={isColumnCheckBox ? styles.checkboxColumnContainer : styles.checkboxContainer}
      >
        {renderCheckBoxAll(item, ind, checkifExists)}
        <View key={ind} style={getContainerCheckBoxStyle(item, ind)}>
          <Checkbox
            value={getValueCheckBox(item, childTag, checkifExists)}
            onToggle={() => handleState(item.tagName, childTag)}
            testID={`${COMPONENT_NAME}__CheckBoxHandleState__${ind}`}
            accessibilityLabel={`${COMPONENT_NAME}__CheckBoxHandleState__${ind}`}
            outlineStyle={{
              borderColor: color.palette.darkGrey,
            }}
            text={childTag.tagTitle}
            textStyle={{ ...presets.bodyTextRegular, ...styles.checkboxTitle }}
          />
        </View>
        {item?.tagName !== myParams[1] && <View style={styles.lineGrey} />}
      </View>
    )
  }

  const renderChip = (item, ind, checkifExists, childTag) => {
    return (
      <View key={ind} style={styles.chipsContainer}>
        <Chip
          onPressed={() => handleState(item.tagName, childTag)}
          text={childTag.tagTitle}
          type={
            checkifExists?.child.indexOf(childTag.tagName) >= 0
              ? ChipType.selected
              : ChipType.unSelected
          }
          testID={`${COMPONENT_NAME}__ChipHandleState__${ind}`}
          accessibilityLabel={`${COMPONENT_NAME}__ChipHandleState__${ind}`}
        />
      </View>
    )
  }

  const renderDineFilter = (item) => {
    if (!item.childTags) {
      return null
    }

    return item.childTags?.map((childTag: any, ind: number) => {
      const stringMatch = myParams.includes(item.tagName)
      const checkifExists = filterState.find((i) => i?.main === item.tagName)
      return stringMatch
        ? renderCheckBoxList(item, ind, checkifExists, childTag)
        : renderChip(item, ind, checkifExists, childTag)
    })
  }

  const getMarginSection = (index: number) => {
    if (index !== 0) {
      return {
        marginTop: 50,
      }
    }
  }

  const renderContent = () => {
    return (
      <ScrollView
        scrollEnabled={true}
        contentContainerStyle={styles.contentContainer}
        style={styles.scrollContainer}
        testID={`${COMPONENT_NAME}__ScrollViewDineFilter`}
        accessibilityLabel={`${COMPONENT_NAME}__ScrollViewDineFilter`}
      >
        {isNotFilterable && (
          <Text
            preset="caption1Regular"
            style={styles.unableToDisplayStyle}
            tx="dineScreen.unableToDisplayFilters"
          />
        )}

        {!isEmpty(dineFilterParameters) && !isNotFilterable
          ? dineFilterParameters?.map((item: any, index: number) => {
            return (
              <View key={index} style={[styles.viewContent, getMarginSection(index)]}>
                <Text preset={"subTitleBold"} text={item.tagTitle} style={styles.titleStyle} />
                <View style={getContainerDineFilterStyle(item)}>{renderDineFilter(item)}</View>
              </View>
            )
          })
          : null}
      </ScrollView>
    )
  }

  const onClosedFilterModal = () => {
    setIsShowModalCheckRatingPopup(false)
    setShowFilterModal(false)
  }

  const setPreviousData = () => {
    const previousValue = FilterValueDine.getFilterValueDine()
    try {
      const filterItemValues = JSON.parse(previousValue?.filterValueItems)
      const filterItemTitles = JSON.parse(previousValue?.filterValueTitles)
      dispatch(
        Creators.setCurrentValueFilter({
          filterValueItems: !isEmpty(filterItemValues) ? filterItemValues : [],
          filterValueTitles: !isEmpty(filterItemTitles) ? filterItemTitles : [],
        }),
      )
    } catch (err) {
      dispatch(
        Creators.setCurrentValueFilter({
          filterValueItems: [],
          filterValueTitles: [],
        }),
      )
    }
  }

  const onApplyFilter = React.useCallback(() => {
    trackAction(AdobeTagName.CAppDineFilterOptions, {
      [AdobeTagName.CAppDineFilterOptions]: handleCondition(
        filterTitles && filterTitles?.length > 0,
        filterTitles.map((item) => item?.tagTitle).join("|"),
        "1",
      ),
    })
    dispatch(Creators.dineSetFilterTitles(filterTitles))
    setIsShowModalCheckRatingPopup(false)
    setShowFilterModal(false)
    dispatch(Creators.startRequestFilter(true))
    //@ts-ignore
    InteractionManager.runAfterInteractions(() => {
      navigation.navigate(NavigationConstants.dineFilterResultsScreen as never)
    })
  }, [filterTitles, filterState])

  return (
    <BottomSheet
      isModalVisible={showFilterModal}
      containerStyle={styles.bottomSheetContainer}
      onClosedSheet={onClosedFilterModal}
      stopDragCollapse={true}
      onBackPressHandle={onClosedFilterModal}
      animationInTiming={100}
      animationOutTiming={100}
      onModalHide={setPreviousData}
    >
      <View style={styles.bottomShetFilterContainer}>
        <View style={styles.headerFilter}>
          <Text text={translate("dineShopFilter.titleHeader")} style={styles.filterTitle} />
          <TouchableOpacity
            onPress={onClosedFilterModal}
            style={styles.btnCloseStyles}
            testID={`${COMPONENT_NAME}__CloseIcon`}
            accessibilityLabel={`${COMPONENT_NAME}__CloseIcon`}
          >
            <CloseCross
              width={24}
              height={24}
              fill="currentColor"
              style={filterableStyles?.closeIcon || closeIconStyles}
            />
          </TouchableOpacity>
        </View>
        <View style={styles.container}>
          <>
            {hasError ? (
              <ErrorScreen
                testID={`${COMPONENT_NAME}__ErrorScreen`}
                accessibilityLabel={`${COMPONENT_NAME}__ErrorScreen`}
                onReload={() => null}
              />
            ) : (
              <>
                {renderContent()}
                <View style={styles.buttonContainer}>
                  <View style={styles.flexRow}>
                    {handleCondition(
                      filterState.length > 0 && !isNotFilterable,
                      <Button
                        onPress={handleClearAllFilters}
                        style={styles.clearAllButton}
                        sizePreset={"large"}
                        statePreset={"default"}
                        typePreset={"secondary"}
                        text={translate("dineShopFilter.clearAll")}
                        textStyle={styles.purpleTextColor}
                        textPreset={"buttonLarge"}
                        testID={`${COMPONENT_NAME}__ButtonClearAllFilters`}
                        accessibilityLabel={`${COMPONENT_NAME}__ButtonClearAllFilters`}
                      />,
                      null,
                    )}
                    <LinearGradient
                      style={styles.buttonGradient}
                      start={{ x: 1, y: 0 }}
                      end={{ x: 0, y: 1 }}
                      colors={[color.palette.gradientColor1End, color.palette.gradientColor1Start]}
                    >
                      <Button
                        sizePreset={"large"}
                        statePreset={"default"}
                        typePreset={"primary"}
                        disabled={isNotFilterable}
                        text={translate("dineShopFilter.applyFilters")}
                        textPreset={"buttonLarge"}
                        onPress={onApplyFilter}
                        style={filterableStyles?.wrapper}
                        textStyle={filterableStyles?.label}
                        testID={`${COMPONENT_NAME}__ButtonApplyFilters`}
                        accessibilityLabel={`${COMPONENT_NAME}__ButtonApplyFilters`}
                      />
                    </LinearGradient>
                  </View>
                </View>
              </>
            )}
          </>
        </View>
      </View>
    </BottomSheet>
  )
}

export { DineFilter }
