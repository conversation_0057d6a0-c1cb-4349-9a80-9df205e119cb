import React, { useContext } from "react"
import { FlightLandingContext } from "../flight-props"


const FlightLandingListing = ({ renderFlightTab }) => {
  const { screenDirection } = useContext(FlightLandingContext)
  const screenIndex = screenDirection === 'DEP' ? 1 : 0
  
  const TabComponent = renderFlightTab?.tabList[screenIndex].component
  const TabProps = renderFlightTab.tabList[screenIndex].props
  return (
    <TabComponent {...TabProps} key={`FlightLandingListing-${screenDirection}`} />
  )
}

export default React.memo(FlightLandingListing)