import { useNavigation } from "@react-navigation/native";
import { Text } from "app/elements/text/text";
import React from "react"
import { Dimensions, Pressable, StyleSheet, View } from "react-native"
import Pdf from 'react-native-pdf';

const PDFView = ({ navigation }) => {
  const pdfRef = React.useRef(null)

  const source = { uri: 'https://www.antennahouse.com/hubfs/xsl-fo-sample/pdf/basic-link-1.pdf', cache: true };

  React.useLayoutEffect(() => {
    // console.log("PDF__useLayoutEffect", pdfRef)
    navigation.setOptions({
      title: 'New Screen Name', // <-- This changes the screen title
    })
  }, [])

  const onBack = () => {
    // goBack()
  }

  return (
    <View style={styles.container}>
      <Pdf
        ref={(pdf) => { pdfRef.current = pdf }}
        source={source}
        enablePaging={true}
        onLoadComplete={(numberOfPages, filePath) => {
          console.log(`PDF__Number of pages: ${numberOfPages}`)
        }}
        onPageChanged={(page, numberOfPages) => {
          console.log(`PDF__Current page: ${page}`)
        }}
        onError={(error) => {
          console.log(`PDF__error`, error)
        }}
        onPressLink={(uri) => {
          console.log(`PDF__Link pressed: ${uri}`)
        }}
        style={styles.pdf}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "flex-start",
    alignItems: "center",
    marginTop: 25,
  },
  pdf: {
    flex: 1,
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height,
  },
})

export default PDFView