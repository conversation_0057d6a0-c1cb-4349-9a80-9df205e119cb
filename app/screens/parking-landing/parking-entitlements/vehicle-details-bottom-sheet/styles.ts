import { Dimensions, Platform, StyleSheet } from "react-native"

import { color, typography } from "app/theme"
import { presets } from "app/elements/text"

const styles = StyleSheet.create({
  wrapper: {
    justifyContent: "flex-end",
    margin: 0,
  },

  bottomSheetEditContainer: {
    paddingHorizontal: 24,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: "95%",
    width: "100%",
    bottom: -1,
    backgroundColor: color.palette.almostWhiteGrey,
  },

  title: {
    ...presets.bodyTextBold,
    paddingVertical: 20,
    textAlign: "center",
    color: color.palette.almostBlackGrey,
  },
  closeButton: {
    top: 20,
    right: 16,
    position: "absolute",
  },
  vehicleDetailsIcon: {
    marginTop: 25,
    marginBottom: 24,
    alignSelf: "center",
  },

  guideTitle: {
    ...presets.subTitleBold,
    lineHeight: 28,
    color: color.palette.almostBlackGrey,
  },
  guideMessage: {
    ...presets.bodyTextRegular,
    fontSize: 14,
    marginTop: 8,
    marginBottom: 40,
    color: color.palette.darkestGrey,
  },

  btnUpdateContainer: {
    borderRadius: 60,
    flex: 1,
    height: 44,
    marginBottom: 37,
    marginTop: 8,
  },
  textBtnUpdateStyle: {
    color: color.palette.almostWhiteGrey,
    fontFamily: typography.bold,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 24,
    textAlign: "center",
  },
})

export default styles
