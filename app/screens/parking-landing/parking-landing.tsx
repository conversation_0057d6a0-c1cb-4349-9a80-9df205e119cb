import { useMemo, useState, useContext } from 'react';
import { <PERSON><PERSON>View, StatusBar, View } from "react-native"
import LinearGradient from "react-native-linear-gradient"
import { styles, TIER_BG_COLORS } from "./parking-landing.styles"
import { useRewardTier } from "app/hooks/useRewardTier"
import { Tier } from "app/models/enum"
import _isNumber from "lodash/isNumber"
import { useDispatch, useSelector } from "react-redux"
import { ifAllTrue, ifOneTrue } from "app/utils"
import {
  useParkingLandingEffects,
  useParkingLandingRequests,
  useParkingLandingStates,
  useECouponsRequest,
  useMoreServicesRequest
} from "./parking-landing.hooks"
import ParkingEntitlements from "./parking-entitlements"
import RedeemFromCRCatalogue from "./redeem-from-cr-catalogue"
import ProfileActions, { ProfileSelectors } from "app/redux/profileRedux"
import FreeParkingPromoSection from "./components/free-parking-promo"
import { handleModeCarPassCsmAPI } from "app/utils/navigation-helper"
import { useModal } from "app/hooks/useModal"
import { NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { ParkingEcoupon } from './parking-e-coupons';
import { ParkingMoreServices } from './parking-more-services';
import { useRef, useEffect } from "react"
import ParkLocation, { PARK_LOCATION_HEIGHT } from "./components/park-location"
import { runOnJS, useAnimatedReaction, useSharedValue, withDelay, withTiming } from "react-native-reanimated"
import QuickLinks from "./components/quick-links"
import ParkingHeader from "./components/parking-header"
import L2AnnouncementBanner from "app/components/l2-announcement/l2-announcement"
import { AccountContext } from "app/services/context/account"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import RefreshingIndicator from "./components/refreshing-indicator"
import { REFRESHING_DIMENSIONS } from "./constants"
import AemActions, { AEM_PAGE_NAME } from "app/redux/aemRedux";
import { ParkingFAQChips } from './parking-FAQ-chips';
import CSATSurvey from './csat-survey';
import { getParkingLandingTrackingValue } from 'app/utils/parking-landing';

const ParkingLandingScreen = ({ navigation, route }) => {
  const { l2AnnouncementFeatureFlag } = useContext(AccountContext)
  const showL2Announcement = isFlagOnCondition(l2AnnouncementFeatureFlag)
  const dispatch = useDispatch()
  const eCouponStateCodeRef = useRef("")
  const {
    initiateParkingCouponsRedirection,
    initiateParkingFreePromosRedirection,
    initiateParkingMoreServicesRedirection,
    initiateParkingMyIURedirection,
    stateCode = null,
  } = route?.params || {}
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const { currentTier } = useRewardTier()
  const tierColors = TIER_BG_COLORS[currentTier] || TIER_BG_COLORS[Tier.Member]
    const {
      openModal: openEditVehicleIUModal,
    } = useModal("parkingEntitlements")
  const {
    freeParkingPromosError,
    freeParkingPromosFetching: freeParkingPromosLoading,
    freeParkingPromosPayload,
    setFreeParkingPromosError,
    setFreeParkingPromosFetching,
    setFreeParkingPromosPayload,
    clickFreeParkingJewel,
    setClickFreeParkingJewel,
    eCouponsFetching: eCouponsLoading,
    setECouponseFetching,
    eCouponsPayload,
    setECouponsPayload,
    eCouponsPayloadError,
    setECouponsPayloadError,
    clickJewelAfterLogin,
    setClickJewelAfterLogin,
    setParkingLandingCardError,
    parkingLandingCardFetching: parkingLandingCardLoading,
    setParkingLandingCardFetching,
    parkingLandingCardError,
    parkingLandingCardPayload,
    setParkingLandingCardPayload,
    quickLinksError,
    setQuickLinksError,
    quickLinksFetching: quickLinksLoading,
    setQuickLinksFetching,
    quickLinksPayload,
    setQuickLinksPayload,
    moreServicesError,
    moreServicesFetching: moreServicesLoading,
    moreServicesPayload,
    setMoreServicesError,
    setMoreServicesFetching,
    setMoreServicesPayload,
    chipsError,
    chipsFetching,
    chipsPayload,
    setChipsError,
    setChipsFetching,
    setChipsPayload
  } = useParkingLandingStates()
  const { fetchFreeParkingPromos, fetchParkingQuickLinks } = useParkingLandingRequests({
    setFreeParkingPromosError,
    setFreeParkingPromosFetching,
    setFreeParkingPromosPayload,
    setParkingLandingCardError,
    setParkingLandingCardFetching,
    setParkingLandingCardPayload,
    setQuickLinksError,
    setQuickLinksFetching,
    setQuickLinksPayload,
  })
  const { fetchEcoupons } = useECouponsRequest({
    setECouponsPayloadError,
    setECouponsPayload,
    setECouponseFetching,
  })
  const { fetchMoreServices } = useMoreServicesRequest({
    setMoreServicesError,
    setMoreServicesFetching,
    setMoreServicesPayload
  })
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const scrollRef = useRef<any>(null)
  const parkingECouponSectionOffsetY = useSharedValue(0)
  const parkingPromotionsSectionOffsetY = useSharedValue(0)
  const pageOffsetY = useSharedValue(0)
  const parkingEntitlementOffsetY = useSharedValue(0)
  const indicatorHeight = useSharedValue(0)
  const isRefreshing = useSharedValue(false)
  const positionStartRefresh = useSharedValue(0)
  const parkingMoreServicesSectionOffsetY = useSharedValue(0)

  const [parkingECouponSectionOffsetYValue, setParkingECouponSectionOffsetYValue] = useState(0)
  const [parkingPromotionSectionOffsetYValue, setParkingPromotionSectionOffsetYValue] = useState(0)
  const [parkingMoreServicesSectionOffsetYValue, setParkingMoreServicesSectionOffsetYValue] = useState(0)

  const sectionRedirectRef = useRef<{
    initiateParkingCouponsRedirection?: number
    initiateParkingFreePromosRedirection?: number
    initiateParkingMoreServicesRedirection?: number
    initiateParkingMyIURedirection?: number
  }>({})
  const preloadHeightRef = useRef<number>(0)

  const [isAllLoading, setIsAllLoading] = useState(false)
  const [isInternetError, setIsInternetError] = useState(false)
  const freeParkingPromosFetching = isAllLoading || freeParkingPromosLoading
  const eCouponsFetching = isAllLoading || eCouponsLoading
  const moreServicesFetching = isAllLoading || moreServicesLoading
  const listChipsFetching = isAllLoading || chipsFetching
  const parkingLandingCardFetching = isAllLoading || parkingLandingCardLoading
  const quickLinksFetching = isAllLoading || quickLinksLoading
  const overallFetching = ifOneTrue([
    eCouponsFetching,
    freeParkingPromosFetching,
    parkingLandingCardFetching,
    quickLinksFetching,
    moreServicesFetching,
    listChipsFetching,
  ])

  const parkingLandingTrackValue = useMemo(() => {
    return getParkingLandingTrackingValue(parkingLandingCardPayload)
  }, [parkingLandingCardPayload])

  const processJewelFlow = (vehicleIU) => {
  if (vehicleIU?.length > 0) {
    handleModeCarPassCsmAPI(navigation, dispatch, "promo_jewelweekend")
    setClickJewelAfterLogin(false)
  } else {
    setClickFreeParkingJewel(true)
    openEditVehicleIUModal()
    eCouponStateCodeRef.current = ""
  }
}

  const handleOnPressJewel = () => {
    processJewelFlow(profilePayload?.vehicleIU)
  }

  const handleOnPressJewelAferLogin = () => {
    setClickJewelAfterLogin(true)
  }

  const handleOnPressJewelCheckLogin = () => {
    if (isLoggedIn) {
      handleOnPressJewel()
    } else {
      navigation.navigate(NavigationConstants.authScreen, {
        sourceSystem: SOURCE_SYSTEM.OTHERS,
        callBackAfterLoginSuccess: handleOnPressJewelAferLogin,
        callBackAfterLoginCancel: undefined,
      })
    }
  }

  const callBackUpdateVehicleSuccess = () => {
    if (clickFreeParkingJewel || clickJewelAfterLogin) {
      setClickFreeParkingJewel(false)
      setClickJewelAfterLogin(false)
      handleModeCarPassCsmAPI(navigation, dispatch, "promo_jewelweekend")
    }
    refreshData()
  }

  const callBackUpdateVehicleError = () => {
    setClickFreeParkingJewel(false)
    setClickJewelAfterLogin(false)
  }

  const stopRefreshing = () => {
    isRefreshing.value = false
    indicatorHeight.value = withDelay(1000, withTiming(0))
    setIsAllLoading(false)
  }
  const { refreshData } = useParkingLandingEffects({
    stopRefreshing,
    setIsInternetError,
    setFreeParkingPromosError,
    setFreeParkingPromosFetching,
    setFreeParkingPromosPayload,
    setECouponsPayloadError,
    setECouponsPayload,
    setECouponseFetching,
    setParkingLandingCardError,
    setParkingLandingCardFetching,
    setParkingLandingCardPayload,
    setQuickLinksError,
    setQuickLinksFetching,
    setQuickLinksPayload,
    setMoreServicesError,
    setMoreServicesFetching,
    setMoreServicesPayload,
    setChipsError,
    setChipsFetching,
    setChipsPayload
  })

  const pullToRefresh = () => {
    refreshData()
    if (isLoggedIn && !isAllLoading) {
      dispatch(ProfileActions.profileRequest())
    }
    dispatch(
      AemActions.getAemConfigData({
        forceRequest: true,
        name: AEM_PAGE_NAME.GET_ERROR_MAINTENANCE,
        pathName: "getErrorMaintenance",
      }),
    )
  }

  const gesture = Gesture.Pan()
    .onBegin((e) => {
      if (pageOffsetY.value === 0) {
        positionStartRefresh.value = e.y
      }
    })
    .onChange((e) => {
      if (pageOffsetY.value === 0 && !isRefreshing.value) {
        if (positionStartRefresh.value > 0) {
          if (positionStartRefresh.value + REFRESHING_DIMENSIONS.THRESH_HOLD <= e.y) {
            const newHeight = Math.min(
              Math.max(0, e.translationY),
              REFRESHING_DIMENSIONS.MAX_INDICATOR_HEIGHT,
            )
            indicatorHeight.value = withTiming(newHeight)
            isRefreshing.value = true
          } else {
            isRefreshing.value = false
            indicatorHeight.value = withTiming(0)
          }
        } else {
          positionStartRefresh.value = e.y
          isRefreshing.value = false
          indicatorHeight.value = withTiming(0)
        }
      }
    })
    .onEnd((e) => {
      if (isRefreshing.value) {
        runOnJS(pullToRefresh)()
        indicatorHeight.value = withTiming(REFRESHING_DIMENSIONS.MAX_INDICATOR_HEIGHT)
      }
    })

  useEffect(() => {
    if (profilePayload && clickJewelAfterLogin) {
      processJewelFlow(profilePayload?.vehicleIU)
    }
  }, [profilePayload, clickJewelAfterLogin])

  useAnimatedReaction(
    () => parkingECouponSectionOffsetY.value,
    (current, previous) => {
      if (current !== previous && current > 0) {
        runOnJS(setParkingECouponSectionOffsetYValue)(current)
      }
    },
    [],
  )

  useAnimatedReaction(
    () => parkingPromotionsSectionOffsetY.value,
    (current, previous) => {
      if (current !== previous && current > 0) {
        runOnJS(setParkingPromotionSectionOffsetYValue)(current)
      }
    },
    [],
  )

  useAnimatedReaction(
    () => parkingMoreServicesSectionOffsetY.value,
    (current, previous) => {
      if (current !== previous && current > 0) {
        runOnJS(setParkingMoreServicesSectionOffsetYValue)(current)
      }
    },
    [],
  )

  useEffect(() => {
    if (ifAllTrue([
      overallFetching,
      initiateParkingCouponsRedirection !== sectionRedirectRef?.current?.initiateParkingCouponsRedirection,
    ])) {
      preloadHeightRef.current = PARK_LOCATION_HEIGHT
    }
    if (
      ifAllTrue([
        !overallFetching,
        initiateParkingCouponsRedirection !== sectionRedirectRef?.current?.initiateParkingCouponsRedirection,
        scrollRef?.current,
        parkingECouponSectionOffsetYValue > 0,
      ])
    ) {
      if (!parkingLandingCardPayload?.parkingLocation) {
        preloadHeightRef.current = 0
      }
      scrollRef?.current?.scrollTo({
        y: parkingECouponSectionOffsetYValue + preloadHeightRef.current,
        animated: true,
      })
      sectionRedirectRef.current.initiateParkingCouponsRedirection = initiateParkingCouponsRedirection
      preloadHeightRef.current = 0
    }
  }, [initiateParkingCouponsRedirection, overallFetching, parkingECouponSectionOffsetYValue])

  useEffect(() => {
    if (ifAllTrue([
      overallFetching,
      initiateParkingFreePromosRedirection !== sectionRedirectRef?.current?.initiateParkingFreePromosRedirection,
    ])) {
      preloadHeightRef.current = PARK_LOCATION_HEIGHT
    }
    if (
      ifAllTrue([
        !overallFetching,
        initiateParkingFreePromosRedirection !== sectionRedirectRef?.current?.initiateParkingFreePromosRedirection,
        scrollRef?.current,
        parkingPromotionSectionOffsetYValue > 0,
      ])
    ) {
      if (!parkingLandingCardPayload?.parkingLocation) {
        preloadHeightRef.current = 0
      }
      scrollRef?.current?.scrollTo({
        y: parkingPromotionSectionOffsetYValue + preloadHeightRef.current,
        animated: true,
      })
      sectionRedirectRef.current.initiateParkingFreePromosRedirection = initiateParkingFreePromosRedirection
      preloadHeightRef.current = 0
    }
  }, [initiateParkingFreePromosRedirection, overallFetching, parkingPromotionSectionOffsetYValue])

  useEffect(() => {
    if (ifAllTrue([
      overallFetching,
      initiateParkingMoreServicesRedirection !== sectionRedirectRef?.current?.initiateParkingMoreServicesRedirection,
    ])) {
      preloadHeightRef.current = PARK_LOCATION_HEIGHT
    }
    if (
      ifAllTrue([
        !overallFetching,
        initiateParkingMoreServicesRedirection !== sectionRedirectRef?.current?.initiateParkingMoreServicesRedirection,
        scrollRef?.current,
        parkingMoreServicesSectionOffsetYValue > 0,
      ])
    ) {
      if (!parkingLandingCardPayload?.parkingLocation) {
        preloadHeightRef.current = 0
      }
      scrollRef?.current?.scrollTo({
        y: parkingMoreServicesSectionOffsetYValue + preloadHeightRef.current,
        animated: true,
      })
      sectionRedirectRef.current.initiateParkingMoreServicesRedirection = initiateParkingMoreServicesRedirection
      preloadHeightRef.current = 0
    }
  }, [initiateParkingMoreServicesRedirection, overallFetching, parkingMoreServicesSectionOffsetYValue])

  useEffect(() => {
    if (initiateParkingMyIURedirection) {
      openEditVehicleIUModal()
    }
  }, [initiateParkingMyIURedirection])

  useEffect(() => {
    if (
      ifAllTrue([
        !freeParkingPromosLoading,
        !eCouponsLoading,
        !moreServicesLoading,
        !parkingLandingCardLoading,
        !quickLinksLoading,
        isRefreshing.value
      ])
    ) {
      isRefreshing.value = false
      indicatorHeight.value = withTiming(0)
      setIsAllLoading(false)
    }

    if (ifAllTrue([!freeParkingPromosLoading, !eCouponsLoading, !moreServicesLoading, !parkingLandingCardLoading, !quickLinksLoading])) {
      setIsAllLoading(false)
    }
  }, [freeParkingPromosLoading, eCouponsLoading, moreServicesLoading, parkingLandingCardLoading, quickLinksLoading])

  return (
    <>
      <StatusBar translucent barStyle="default" />
      <ParkingHeader
        isStickyHeader
        navigation={navigation}
        pageOffsetY={pageOffsetY}
        parkingEntitlementOffsetY={parkingEntitlementOffsetY}
      />
      <GestureHandlerRootView>
        <GestureDetector gesture={Gesture.Simultaneous(gesture, Gesture.Native())}>
          <ScrollView
            bounces={false}
            style={styles.containerStyle}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}
            ref={scrollRef}
            onScroll={(evt) => {
              pageOffsetY.value = evt?.nativeEvent?.contentOffset?.y
            }}
          >
            <LinearGradient
              colors={tierColors}
              end={{ x: 0, y: 1 }}
              start={{ x: 0, y: 0 }}
              style={styles.bgCoverContainerStyle}
            />
            <ParkingHeader navigation={navigation} />
            <RefreshingIndicator indicatorHeight={indicatorHeight} />
            <ParkLocation parkingLandingCardPayload={parkingLandingCardPayload} />
            <ParkingEntitlements
              stateCode={stateCode}
              isInternetError={isInternetError}
              setIsAllLoading={setIsAllLoading}
              eCouponStateCodeRef={eCouponStateCodeRef}
              callBackUpdateVehicleSuccess={callBackUpdateVehicleSuccess}
              callBackUpdateVehicleError={callBackUpdateVehicleError}
              parkingECouponSectionOffsetY={parkingECouponSectionOffsetY}
              parkingPromotionsSectionOffsetY={parkingPromotionsSectionOffsetY}
              parkingLandingCardPayload={parkingLandingCardPayload}
              refreshData={refreshData}
              scrollRef={scrollRef}
              parkingEntitlementOffsetY={parkingEntitlementOffsetY}
              parkingLandingCardError={parkingLandingCardError}
              parkingLandingCardFetching={parkingLandingCardFetching}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />
            {isLoggedIn && !isInternetError && <CSATSurvey navigation={navigation} />}
            <QuickLinks
              fetchParkingQuickLinks={fetchParkingQuickLinks}
              navigation={navigation}
              openEditVehicleIUModal={openEditVehicleIUModal}
              parkingECouponSectionOffsetY={parkingECouponSectionOffsetY}
              parkingMoreServicesSectionOffsetY={parkingMoreServicesSectionOffsetY}
              parkingPromotionsSectionOffsetY={parkingPromotionsSectionOffsetY}
              quickLinksError={quickLinksError}
              quickLinksFetching={quickLinksFetching}
              quickLinksPayload={quickLinksPayload}
              scrollRef={scrollRef}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />
            <ParkingFAQChips
              listChipsFetching={listChipsFetching}
              chipsPayload={chipsPayload}
              chipsError={chipsError}
              navigation={navigation}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />
            {ifOneTrue([
              freeParkingPromosFetching,
              !!freeParkingPromosPayload?.length,
              freeParkingPromosError,
            ]) ? (
              <FreeParkingPromoSection
                fetchFreeParkingPromos={fetchFreeParkingPromos}
                freeParkingPromosError={freeParkingPromosError}
                freeParkingPromosFetching={freeParkingPromosFetching}
                freeParkingPromosPayload={freeParkingPromosPayload}
                handleOnPressJewel={handleOnPressJewelCheckLogin}
                parkingPromotionsSectionOffsetY={parkingPromotionsSectionOffsetY}
                parkingLandingTrackValue={parkingLandingTrackValue}
              />
            ) : (
              <View style={{ height: 16 }} />
            )}
            {!isInternetError && <RedeemFromCRCatalogue navigation={navigation} parkingLandingTrackValue={parkingLandingTrackValue} />}
            {eCouponsPayload?.data?.length > 0 && <ParkingEcoupon
              eCouponsFetching={eCouponsFetching}
              eCouponsPayload={eCouponsPayload}
              eCouponsPayloadError={eCouponsPayloadError}
              fetchEcoupons={fetchEcoupons}
              eCouponStateCodeRef={eCouponStateCodeRef}
              parkingECouponSectionOffsetY={parkingECouponSectionOffsetY}
              refreshData={refreshData}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />}
            <ParkingMoreServices
              moreServicesFetching={moreServicesFetching}
              moreServicesPayload={moreServicesPayload}
              moreServicesError={moreServicesError}
              fetchMoreServices={fetchMoreServices}
              parkingMoreServicesSectionOffsetY={parkingMoreServicesSectionOffsetY}
              parkingLandingTrackValue={parkingLandingTrackValue}
            />
          </ScrollView>
        </GestureDetector>
      </GestureHandlerRootView>
      {showL2Announcement && <L2AnnouncementBanner skipBottomNavigation = {true}/>}
    </>
  )
}

export default ParkingLandingScreen
