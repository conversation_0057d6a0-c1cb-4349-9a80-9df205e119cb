import { ExploreContext } from "app/services/context/explore"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { memo, useContext } from "react"
import ExploreScreenV2 from "../explore-screen-v2"
import ExploreScreen from "./explore-screen"

const ExploreScreenWrapper = (props) => {
  const { exploreScreenV2 } = useContext(ExploreContext)
  const isExploreScreenV2 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.EXPLORE_V2, exploreScreenV2)
  return isExploreScreenV2 ? <ExploreScreenV2 {...props} /> : <ExploreScreen {...props} />
}

export default memo(ExploreScreenWrapper)
