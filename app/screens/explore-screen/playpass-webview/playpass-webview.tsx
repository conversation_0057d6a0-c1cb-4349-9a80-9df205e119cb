import React, { useEffect, useRef, useState, useCallback, useContext } from "react"
import {
  Platform,
  ViewStyle,
  Alert,
  StatusBar,
  BackHandler,
  Linking,
  View,
} from "react-native"
import { useSelector, useDispatch } from "react-redux"
import { openSettings, PERMISSIONS, request, RESULTS } from "react-native-permissions"
import qs from "qs"
import { get, isEmpty } from "lodash"

import { ExtendedWebView } from "app/components/extended-webview/extended-webview"
import { env } from "app/config/env-params"
import { GAMI_DEEPLINKS, NavigationConstants, SOURCE_SYSTEM, StateCode, TrackingScreenName } from "app/utils/constants"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { translate } from "app/i18n"
import { AemSelectors } from "app/redux/aemRedux"
import TakeUploadPhoto from "app/sections/take-upload-photo"
import { WalletDirection } from "app/models/consts"
import { ProfileSelectors } from "app/redux/profileRedux"
import {
  AdobeTagName,
  commonTrackingScreen,
  getExperienceCloudId,
  trackAction,
} from "app/services/adobe"
import {
  getPreviousScreen,
  setPreviousScreen,
  useCurrentScreenActiveAndPreviousScreenHook,
  useGeneratePlayPassUrl,
} from "app/utils/screen-hook"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import DefaultHeader from "./components/header/default-header"
import ChangiMillionaireHeader from "./components/header/changi-millionaire-header"
import UploadReceiptCMScreen from "./components/upload-receipt-cm"
import ScanQRCMScreen from "./components/scan-qr-cm/scan-qr-cm"
import { ifAllTrue, simpleCondition, ifOneTrue, handleCondition } from "app/utils"
import { WebViewHeaderTypes } from "app/models/enum"
import ChangiEcardController from "app/components/changi-ecard/changi-ecard-controler"
import ExploreCreators, { ExploreSelectors } from "app/redux/exploreRedux"
import notificationAction from "app/redux/notificationRedux"
import { StorageKey } from "app/utils/storage/storage-key"
import { FlyCreators } from "app/redux/flyRedux"
import moment from "moment"
import { getDeepLink, getDeepLinkV2 } from "app/sagas/pageConfigSaga"
import GlobalLoadingController from "app/components/global-loading/global-loading-controller"
import { StackActions, useFocusEffect, useIsFocused, useNavigationState } from "@react-navigation/native"
import { setTriggerForShowRatingPopup } from "app/utils/screen-helper"
import AppscapadeHeader from "./components/header/appscapade-header"
import CookieManager from "@react-native-cookies/cookies"
import { getEnvSetting } from "app/utils/env-settings"
import { getCurrentTimeSingapore } from "app/utils/date-time/date-time"
import { REMOTE_CONFIG_FLAGS, isFlagON, isFlagOnCondition } from "app/services/firebase/remote-config"
import { AccountContext } from "app/services/context/account"
import { SafeAreaView } from "react-native-safe-area-context"
import { AirportLandingSelectors } from "app/redux/airportLandingRedux"
import { MytravelCreators } from "app/redux/mytravelRedux"
import { MODE_CODE } from "app/helpers/deeplink/deeplink-parameter"
import { goToRetroClaims } from "app/hooks/useRetroClaims"
import urlParse from "url-parse"
import useL3BaggagePrediction from "app/hooks/useL3BaggagePrediction"

enum CameraType {
  default = "default",
  uploadCM = "uploadCM",
  scanCM = "scanCM",
  scanBarCode = "scanBarCode",
}

enum PlayPassDeeplinks {
  openCamera = "cagichangi://launchimagepicker/",
  openImageGallery = "cagichangi://launchimagepicker/?ui=PBE&entrypoint=upload",
  scanQR = "cagichangi://launchqrcode",
  scanBarCode = "cagichangi://launchbarcode",
  eventDetails = "cagichangi://ppevent/",
  wallet = "cagichangi://wallet/",
  eCard = "cagichangi://cr_ecard",
  paymentConfirmation = "cagichangi://ppconfirmation/",
  exploreHomePage = "cagichangi://explore/",
  login = "cagichangi://login",
  exitCM = "cagichangi://exitCM",
  scanBoardingPass = "cagichangi://scanboardingpass",
  perkspage = "cagichangi://perkspage",
  searchflights = "cagichangi://searchflights",
  loginappscapade = "cagichangi://loginappscapade",
}

const container: ViewStyle = {
  flex: 1,
}

const bottomSheetStyle: ViewStyle = {
  flex: 1,
}

const WebViewContainer = Platform.OS === 'android' ? View : SafeAreaView
const removeSpecialChars = (text: string): string => {
  if (typeof text !== 'string') {
      throw new TypeError('Header must be a string');
  }
  const specialCharsRegex = /[^a-zA-Z0-9!#$%&'()*+\-./:<=>?@[\]^_`{|}~ ]/g;
  return text.replace(specialCharsRegex, '');
};

const getMessageCode = (messageCommon, code) => {
  if (Array.isArray(messageCommon) && messageCommon.length > 0) {
    return messageCommon.find((e) => e?.code === code) || {}
  }
  return {}
}

const runFunctionWithCondition = (condition, callback1, callback2) => {
  if (condition) {
    callback1?.()
  } else {
    callback2?.()
  }
}

export const setPPCookiesExploreChangi = async (
  data: {
    urlHost: string
    entryPoint: string
    uid_changiapp_playpass?: string
    eventName?: string
    perkTitle?: string
    cardID?: string
    tokenType?: string
  },
  callback: () => void,
) => {
  const ecid = await getExperienceCloudId()
  const dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ"
  const epxireDate = moment().add(30, "days").format(dateFormat).toString()
  const Settings = getEnvSetting()
  const hostURL = Settings.URL_HOST_PP_COOKES
  const promises = [
    CookieManager.set(hostURL, {
      name: "url_host",
      value: data.urlHost,
      version: "1",
      expires: epxireDate,
    }),
    CookieManager.set(hostURL, {
      name: "ecid_changiapp_playpass",
      value: ecid,
      version: "1",
      expires: epxireDate,
    }),
    CookieManager.set(hostURL, {
      name: "main_entry_page",
      value: data.entryPoint,
      version: "1",
      expires: epxireDate,
    }),
  ]
  if (data.uid_changiapp_playpass) {
    CookieManager.set(hostURL, {
      name: "uid_changiapp_playpass",
      value: data.uid_changiapp_playpass,
      version: "1",
      expires: epxireDate,
    })
  }
  if (data.eventName) {
    promises.push(
      CookieManager.set(hostURL, {
        name: "event_name",
        value: removeSpecialChars(data.eventName),
        version: "1",
        expires: epxireDate,
      }),
    )
  }
  if (data.perkTitle) {
    promises.push(
      CookieManager.set(hostURL, {
        name: "perk_tile",
        value: removeSpecialChars(data.perkTitle),
        version: "1",
        expires: epxireDate,
      }),
    )
  }
  if (data.cardID) {
    promises.push(
      CookieManager.set(hostURL, {
        name: "cart_id",
        value: data.cardID,
        version: "1",
        expires: epxireDate,
      }),
    )
  }
  if (data.tokenType) {
    promises.push(
      CookieManager.set(hostURL, {
        name: "token_type",
        value: data.tokenType,
        version: "1",
        expires: epxireDate,
      })
    )
  }
  await Promise.all(promises)
  callback()
}

const clearCookiesPP = async () => {
  const Settings = getEnvSetting()
  const hostURL = Settings.URL_HOST_PP_COOKES
  if (Platform.OS === "ios") {
    CookieManager.clearByName(hostURL, "url_host")
    CookieManager.clearByName(hostURL, "uid_changiapp_playpass")
    CookieManager.clearByName(hostURL, "ecid_changiapp_playpass")
    CookieManager.clearByName(hostURL, "main_entry_page")
    CookieManager.clearByName(hostURL, "event_name")
    CookieManager.clearByName(hostURL, "perk_tile")
    CookieManager.clearByName(hostURL, "cart_id")
    CookieManager.clearByName(hostURL, "token_type")
  } else {
    const dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ"
    const epxireDate = moment().subtract(1, "days").format(dateFormat).toString()
    CookieManager.set(hostURL, {
      name: "url_host",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "uid_changiapp_playpass",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "ecid_changiapp_playpass",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "main_entry_page",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "event_name",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "perk_tile",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "cart_id",
      value: "",
      version: "1",
      expires: epxireDate,
    })
    CookieManager.set(hostURL, {
      name: "token_type",
      value: "",
      version: "1",
      expires: epxireDate,
    })
  }
}

interface PlayPassWebviewParams {
  uri?: string
  title?: string
  needBackButton?: boolean
  needCloseButton?: boolean
  basicAuthCredential?: any
  onBackBtnPress?: any
  onCloseBtnPress?: any
  headerType?: WebViewHeaderTypes
  shouldShowLoadingCookies?: boolean
  loadingCookiesStyles?: ViewStyle
  loadingCookiesSection?: JSX.Element
  isIShopChangi?: boolean;
}

let canGoBackForPhysicalBack = false
const PlayPassWebView = (props) => {
  const webviewRef: any = useRef()
  const dispatch = useDispatch()
  const { navigation } = props
  const {
    uri,
    title,
    needBackButton,
    needCloseButton,
    basicAuthCredential,
    onBackBtnPress,
    onCloseBtnPress,
    shouldShowLoadingCookies,
    loadingCookiesStyles,
    loadingCookiesSection,
    headerType = WebViewHeaderTypes.default,
    isIShopChangi,
  }: PlayPassWebviewParams = props.route.params
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const [isLogIn, setLogIn] = useState(false)
  const statePlayPass = useRef(null)
  const shouldOpenGallery = useRef(false)
  const { getPlayPassUrl, loading } = useGeneratePlayPassUrl("playPassWebView", true)
  const { getPlayPassUrl: getPlayPassUrlFAQ, loading: loadingFAQ } = useGeneratePlayPassUrl("FAQupload", true)
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false)
  const [isBottomSheetCMVisible, setIsBottomSheetCMVisible] = useState(false)
  const [isBottomSheetQRScanisible, setIsBottomSheetQRScanVisible] = useState(false)
  const [isBottomSheetBarCodeScanVisible, setIsBottomSheetBarCodeScanVisible] = useState(false)
  const [canGoBack, setCanGoBack] = useState(false)
  const [webViewTitle, setWebViewTitle] = useState("")
  const messageCommon = useSelector(AemSelectors.getMessagesCommon)
  const msg61 = getMessageCode(messageCommon, "MSG61")
  const msg62 = getMessageCode(messageCommon, "MSG62")
  const [deepLink, setDeepLink] = useState({ path: "", params: "" })
  const isIOS = Platform.OS === "ios"
  const isPlaypassItemClicked = useSelector(ExploreSelectors.isPlaypassItemClicked)
  const profilePayload = useSelector(ProfileSelectors.profilePayload)
  const currentUrl = useRef(uri)
  const isFocused = useIsFocused()
  const navigationState = useNavigationState((state) => state)
	const { cm24Flag } = useContext(AccountContext)
  const playPassUrlPayloadFAQupload = useSelector(AirportLandingSelectors.getPlayPassUrlPayload("FAQupload"))
  const isCM24 = isFlagOnCondition(cm24Flag)
  const isCM24Page = headerType === WebViewHeaderTypes.changiMillionaire && isCM24
  const { handleOpenL3BaggagePrediction } = useL3BaggagePrediction()

  useEffect(() => {
    if(playPassUrlPayloadFAQupload?.error){
      navigation.dispatch(StackActions.popToTop())
    }
  }, [playPassUrlPayloadFAQupload?.error])

  const Rationale = {
    title: simpleCondition({
      condition: msg61?.title,
      ifValue: msg61?.title,
      elseValue: translate("requestPermission.camera.title"),
    }),
    message: simpleCondition({
      condition: msg61?.message,
      ifValue: msg61?.message,
      elseValue: translate("requestPermission.camera.message"),
    }),
    buttonPositive: simpleCondition({
      condition: msg61?.secondButton,
      ifValue: msg61?.secondButton,
      elseValue: translate("requestPermission.camera.buttonPositive"),
    }),
    buttonNegative: simpleCondition({
      condition: msg61?.firstButton,
      ifValue: msg61?.firstButton,
      elseValue: translate("requestPermission.camera.buttonNegative"),
    }),
  }

  const caculateNavigation = () => {
    const history = navigationState?.routes ?? []
    const screenPosition = history.length - 1

    const isFromSearchScreen = [
      NavigationConstants.search,
      NavigationConstants.searchResult,
    ].includes(history[screenPosition - 1]?.name)
    if (isFromSearchScreen) {
      return 1
    }

    const entryPointBottomNavigation = navigationState?.routes?.findIndex(
      (e) => e?.name === "bottomNavigation",
    )
    const entryPointFlightDetails = navigationState?.routes?.findIndex(
      (e) => e?.name === "flightDetails",
    )
    const entryPointPlayPassBookingDetail = navigationState?.routes?.findIndex(
      (e) => e?.name === NavigationConstants.playPassBookingDetail,
    )
    const isFlightDetailNearInstantWin =
      screenPosition - entryPointFlightDetails < screenPosition - entryPointBottomNavigation

    if (isFlightDetailNearInstantWin) {
      return screenPosition - entryPointFlightDetails
    }
    if (
      navigationState?.routes?.length - 2 === entryPointPlayPassBookingDetail
    ) {
      return 1
    }
    return screenPosition - entryPointBottomNavigation
  }

  useEffect(() => {
    if (!isFocused) {
      setDeepLink({ path: "", params: "" })
    }
  }, [])

  const fetchMytravelData = () => {
    if(isLoggedIn && profilePayload?.email && isIShopChangi){
      dispatch(MytravelCreators.flyMyTravelFlightsRequest(profilePayload.email))
    }
  }

  const onBackButtonPressed = () => {
    if (onBackBtnPress) {
      onBackBtnPress(navigation)
    } else {
      navigation.goBack()
    }
    fetchMytravelData()
    dispatch(dispatch(ExploreCreators.setIsPlaypassItemClicked(false)))
  }

  const onCloseButtonPressed = () => {
    if (onCloseBtnPress) {
      onCloseBtnPress()
    } else {
      navigation.goBack()
    }
    fetchMytravelData()
    dispatch(dispatch(ExploreCreators.setIsPlaypassItemClicked(false)))
  }

  const onBack = () => {
    if (isPlaypassItemClicked) {
      fetchMytravelData()
      navigation.goBack()
      dispatch(dispatch(ExploreCreators.setIsPlaypassItemClicked(false)))
    } else {
      webviewRef.current?.goBack()
    }
  }

  useEffect(() => {
    dispatch(notificationAction.forceClosePopupUserNotifications(true))
    if (headerType === WebViewHeaderTypes.changiMillionaire) {
      setTriggerForShowRatingPopup(StorageKey.isParticipatedCmWebview)
    }
    return () => {
      setPreviousScreen(TrackingScreenName.PlayPassWebView)
    }
  }, [])

  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        onCloseButtonPressed()
        return true
      }

      if (onCloseBtnPress) {
        navigation.setOptions({ gestureEnabled: false })
      }

      const subscription = BackHandler.addEventListener("hardwareBackPress", onBackPress)
      return () => subscription.remove()
    }, []),
  )

  const onAndroidBackPress = (): boolean => {
    let shouldReturn = false
    if (
      headerType === WebViewHeaderTypes.appscapadeLP ||
      headerType === WebViewHeaderTypes.appscapadeInstantWinLP
    ) {
      shouldReturn = true
    } else {
      if (canGoBackForPhysicalBack) {
        onBack()
      } else {
        onBackButtonPressed()
      }
      return true
    }
    return shouldReturn
  }

  useEffect((): (() => void) => {
    const backHandler = BackHandler.addEventListener("hardwareBackPress", onAndroidBackPress)
    return () => backHandler.remove()
  }, [])

  useEffect(() => {
    return () => {
      clearCookiesPP()
    }
  }, [])

  const onNavigationStateChange = (navState) => {
    canGoBackForPhysicalBack = navState?.canGoBack
    setCanGoBack(navState?.canGoBack)
    if (navState?.url.startsWith("cagichangi://")) {
      webviewRef.current?.injectJavaScript(`
      window.location.href = '${currentUrl.current}';
    `)
      return false
    }
    currentUrl.current = navState?.url
  }

  const onUploadReceiptScreen = (cameraType) => {
    request(
      handleCondition(Platform.OS === "ios", PERMISSIONS.IOS.CAMERA, PERMISSIONS.ANDROID.CAMERA),
      Rationale,
    ).then((result) => {
      if (result === RESULTS.BLOCKED) {
        Alert.alert(msg62?.title, msg62?.message, [
          {
            text: simpleCondition({
              condition: msg62?.firstButton,
              ifValue: msg62?.firstButton,
              elseValue: translate("scanCode.noBoardingPassDetected.firstButton"),
            }),
            style: "cancel",
            onPress: openSettings,
          },
          {
            text: simpleCondition({
              condition: msg62?.secondButton,
              ifValue: msg62?.secondButton,
              elseValue: translate("scanCode.noBoardingPassDetected.secondButton"),
            }),
            onPress: () => null,
          },
        ])
      } else if (result === RESULTS.GRANTED) {
        if (ifOneTrue([!cameraType, cameraType === CameraType.default])) {
          setIsBottomSheetVisible(true)
        } else if (cameraType === CameraType.uploadCM) {
          setIsBottomSheetCMVisible(true)
        } else if (cameraType === CameraType.scanCM) {
          setIsBottomSheetQRScanVisible(true)
        } else if (cameraType === CameraType.scanBarCode) {
          setIsBottomSheetBarCodeScanVisible(true)
        }
      }
    })
  }

  const injectBase64ToWebView = (base64Img) => {
    webviewRef.current?.injectJavaScript("setImage('" + base64Img + "')")
  }

  const handleTakePicture = (base64Img) => {
    injectBase64ToWebView(base64Img)
    onClosedSheet()
  }

  const handleTakePictureCM = (base64) => {
    injectBase64ToWebView(base64)
    onClosedSheetCM()
  }

  const handleUploadCM = (base64) => {
    injectBase64ToWebView(base64)
    onClosedSheetQRScan()
  }

  const handleQRScan = (codeValue) => {
    if (isBottomSheetBarCodeScanVisible) {
      webviewRef.current?.injectJavaScript(`setBarcode("${codeValue}")`)
    } else {
      webviewRef.current?.injectJavaScript(`setQRCode("${codeValue}")`)
    }
    onClosedSheetQRScan()
  }

  useEffect(() => {
    const stateCode = get(statePlayPass.current, "stateCode", "")
    const params = get(statePlayPass.current, "packageCode", "")
    const tokenType = get(statePlayPass.current, "tokenType", "")
    const openPlaypass = () => {
      const finalParams = tokenType ? `${tokenType}==${params}` : params
      if (ifAllTrue([isLogIn, stateCode, params])) {
        if(stateCode.includes(StateCode.PPRECEIPT)){
          getPlayPassUrlFAQ(stateCode, finalParams)
        } else {
          getPlayPassUrl(stateCode, finalParams)
        }
        statePlayPass.current = null
      }
    }
    openPlaypass()
  }, [isLogIn, getPlayPassUrl])

  const getParams = (url: string) => {
    try {
      const params = qs.parse(url)
      //@ts-ignore
      const [stateCode, packageCode, tokenType] = get(Object.values(params), "0", "")?.split("__")
      return { stateCode, packageCode, tokenType }
    } catch (error) {
      return { stateCode: null, packageCode: null, tokenType: null }
    }
  }
  useEffect(() => {
    if (ifAllTrue([!deepLink?.path, !isIOS])) return
    handleOpenApp()
  }, [deepLink?.path])

  const handleOpenApp = () => {
    switch (deepLink?.path) {
      case PlayPassDeeplinks.eCard:
        goToECard()
        break
      case PlayPassDeeplinks.wallet:
        goToWallet()
        break
      case PlayPassDeeplinks.exploreHomePage:
        goToExploreChangi()
        break
      case PlayPassDeeplinks.scanBoardingPass:
        openScanBoardingPass()
        break
      case PlayPassDeeplinks.perkspage:
        openPerkPage()
        break
      case PlayPassDeeplinks.searchflights:
        openSearchFlight()
        break
      case PlayPassDeeplinks.loginappscapade:
        handleLoginAppscapade()
        break
    }
  }

  const goToECard = () => {
    webviewRef.current?.stopLoading()
    ChangiEcardController.showModal(navigation)
  }

  const goToWallet = () => {
    webviewRef.current?.stopLoading()
    navigation.navigate(NavigationConstants.bookingsOrdersScreen)
  }

  const openPerkPage = () => {
    webviewRef.current?.stopLoading()
    navigation.navigate(NavigationConstants.redemptionCatalogueScreen, {
      screen: NavigationConstants.perksTab,
    })
  }

  const openSearchFlight = () => {
    webviewRef.current?.stopLoading()
    const currentDate = getCurrentTimeSingapore()
    const nextDateTime = moment(currentDate).add(1, "day").format("YYYY-MM-DD HH:mm").split(" ")
    const nextDate = nextDateTime[0]
    dispatch(FlyCreators.setFilterDateDeparture(new Date(nextDate)))
    dispatch(FlyCreators.setFilterDateArrival(new Date(nextDate)))
    trackAction(AdobeTagName.CAppFlyFlightListFilters, {
      [AdobeTagName.CAppFlyFlightListFilters]: moment(nextDate).format("YYYY-MM-DD"),
    })
    dispatch(FlyCreators.setFlightSearchDate(nextDate))
    navigation.navigate("flightResultLandingScreen", {
      screen: "DEP",
      isLoadFlightAfter24h: true,
      sourcePage: AdobeTagName.CAppFlightLanding,
      selectedDate: nextDate,
    })
  }

  const handleLoginAppscapade = () => {
    navigation.navigate(NavigationConstants.authScreen, {
      sourceSystem: SOURCE_SYSTEM.APPSCAPADE,
      callBackAfterLoginSuccess: async () => {
        GlobalLoadingController.showLoading(true)
        const response = await getDeepLink({
          stateCode: StateCode.APPSCAPADE_LP,
          input: { uid: profilePayload?.id },
        })
        GlobalLoadingController.hideLoading()
        if (response?.redirectUri) {
          navigation.replace(NavigationConstants.playpassWebview, {
            uri: response?.redirectUri,
            needBackButton: true,
            needCloseButton: true,
            headerType: WebViewHeaderTypes.appscapadeLP,
            basicAuthCredential: response?.basicAuth,
          })
        }
      },
    })
  }

  const openScanBoardingPass = () => {
    webviewRef.current?.stopLoading()
    navigation.navigate(NavigationConstants.appscapadeScanBoadingPass)
  }

  const goToExploreChangi = () => {
    navigation?.goBack()
    navigation?.navigate(NavigationConstants.explore, {
      isOpenApp: false,
      isScrollToExploreChangiSection: true,
    })
  }

  const handleForAA = (dataReq) => {
    const adobeAnalytics = get(dataReq, "adobeAnalytics")
    if (!isEmpty(adobeAnalytics)) {
      if (adobeAnalytics.aaTag === AdobeTagName.CAppL3FAQPage) {
        const { aaValue } = adobeAnalytics || {}
        const questionContent = aaValue?.split?.(" | ")?.[0]
        if (questionContent) {
          trackAction(AdobeTagName.CAppL3FAQPage, {
            [AdobeTagName.CAppL3FAQPage]: `Parking | ${questionContent} | Link`,
          })
        }
        return
      }
      trackAction(adobeAnalytics.aaTag, {
        [adobeAnalytics.aaTag]: adobeAnalytics.aaValue,
      })
    }

    const pageL3Info = get(dataReq, "pageL3Info")
    if (!isEmpty(pageL3Info)) {
      const pageName = `${pageL3Info.parentPageName}_${pageL3Info.pageName}`
      const pagePath = env()?.AEM_URL + pageL3Info.pagePath
      trackAction(AdobeTagName.common, {
        [AdobeTagName.cagPagePath]: pagePath,
      })
      trackAction(AdobeTagName.cagPagePath, {
        [pageName]: pagePath,
      })
      trackAction(AdobeTagName.cagPagePath, {
        [AdobeTagName.cagPagePath]: {
          [pageName]: pagePath,
        },
      })
      commonTrackingScreen(pageName, getPreviousScreen(), isLoggedIn)
    }
  }

  const goToNewUri = (newUri) => {
    const script = `window.location.href = "${newUri}";`
    webviewRef.current.injectJavaScript(script)
  }

  const deepLinkHandler = (newRequest) => {
    const { url = "" } = { ...newRequest }
    if (newRequest?.url.startsWith("http://")) {
      const newUri = newRequest?.url.replace(/^http:\/\//i, "https://")
      const redirectTo = 'window.location = "' + newUri + '"'
      webviewRef.current?.injectJavaScript(redirectTo)
      return false
    }
    canGoBackForPhysicalBack = newRequest?.canGoBack
    setCanGoBack(newRequest?.canGoBack)
    setDeepLink({ path: "", params: "" })
    if (
      handleCondition(
        url?.includes("openInDeviceBrowser") &&
          (headerType === WebViewHeaderTypes.appscapadeLP ||
            headerType === WebViewHeaderTypes.appscapadeInstantWinLP),
        true,
        false,
      )
    ) {
      Linking.openURL(url)
      return false
    }
    if (url.includes(PlayPassDeeplinks.perkspage)) {
      runFunctionWithCondition(
        isIOS,
        () => openPerkPage(),
        () => setDeepLink({ path: PlayPassDeeplinks.perkspage, params: "" }),
      )
      return false
    }
    if (url.includes(PlayPassDeeplinks.scanBoardingPass)) {
      runFunctionWithCondition(
        isIOS,
        () => openScanBoardingPass(),
        () => setDeepLink({ path: PlayPassDeeplinks.scanBoardingPass, params: "" }),
      )
      return false
    }
    if (url.includes(PlayPassDeeplinks.searchflights)) {
      runFunctionWithCondition(
        isIOS,
        () => openSearchFlight(),
        () => setDeepLink({ path: PlayPassDeeplinks.searchflights, params: "" }),
      )
      return false
    }

    handleForAA(newRequest)

    const isGamiDeepLinkItem = GAMI_DEEPLINKS.find(item => url?.startsWith(item))
    if (!!isGamiDeepLinkItem) {
      webviewRef?.current?.stopLoading?.()
      Linking.openURL(url)
      return false
    }

    if (url.includes(PlayPassDeeplinks.exitCM)) {
      navigation?.pop()
      return false
    }
    if (url.includes(PlayPassDeeplinks.loginappscapade)) {
      runFunctionWithCondition(
        isIOS,
        () => handleLoginAppscapade(),
        () => setDeepLink({ path: PlayPassDeeplinks.loginappscapade, params: "" }),
      )
      return false
    }
    if (url.includes(PlayPassDeeplinks.login)) {
      const params = getParams(url)
      statePlayPass.current = params
      const onLoggedIn = async () => {
        setLogIn(true)
        if (!isCM24Page) return
        let query = url.replace(PlayPassDeeplinks.login, "")
        query = /^\?/.test(query) ? query.slice(1) : query
        try {
          const getLink = await getDeepLinkV2({
            stateCode: StateCode.CM24,
            input: {
              receivedFromLink: query,
            },
          }, true)
          if (getLink?.redirectUri) {
            navigation.navigate(NavigationConstants.playpassWebview, {
              uri: getLink?.redirectUri,
              basicAuthCredential: getLink?.basicAuth,
              headerType,
            })
          }
        } catch {
          navigation.navigate(NavigationConstants.explore, {
            bottomSheetErrorHash: new Date().getTime(),
            isOpenApp: false,
          })
        }
      }
      runFunctionWithCondition(
        isLoggedIn,
        onLoggedIn,
        () => {
          navigation.navigate(NavigationConstants.authScreen, {
            sourceSystem: isCM24Page ? SOURCE_SYSTEM.CHANGI_MILLIONAIRE : SOURCE_SYSTEM.PLAY_PASS,
            callBackAfterLoginSuccess: onLoggedIn,
          })
        },
      )
      return false
    }
    if (url.includes(PlayPassDeeplinks.openCamera)) {
      shouldOpenGallery.current = url === PlayPassDeeplinks.openImageGallery
      onUploadReceiptScreen(CameraType.default)
      return false
    }
    if (
      ifAllTrue([
        url.includes(PlayPassDeeplinks.openCamera),
        headerType === WebViewHeaderTypes.changiMillionaire,
      ])
    ) {
      onUploadReceiptScreen(CameraType.uploadCM)
      return false
    }
    if (url.includes(PlayPassDeeplinks.scanQR)) {
      onUploadReceiptScreen(CameraType.scanCM)
      return false
    }
    if (url.includes(PlayPassDeeplinks.scanBarCode)) {
      onUploadReceiptScreen(CameraType.scanBarCode)
      return false
    }
    if (url.includes(PlayPassDeeplinks.exploreHomePage)) {
      runFunctionWithCondition(
        isIOS,
        goToExploreChangi,
        setDeepLink({ path: PlayPassDeeplinks.exploreHomePage, params: "" }),
      )
      return false
    }

    if (url.startsWith(PlayPassDeeplinks.wallet)) {
      runFunctionWithCondition(
        isIOS,
        goToWallet,
        setDeepLink({ path: PlayPassDeeplinks.wallet, params: "" }),
      )
      return false
    }
    if (url.startsWith(PlayPassDeeplinks.eCard)) {
      runFunctionWithCondition(
        isIOS,
        goToECard,
        setDeepLink({ path: PlayPassDeeplinks.eCard, params: "" }),
      )
      return false
    }

    // Handle dynamic link
    const decodedUrl = decodeURIComponent(url)
    const dynamicLinkPrefix = getEnvSetting().DYNAMIC_LINK_PREFIX
    const isDynamicLink = decodedUrl?.startsWith(dynamicLinkPrefix)
    if (isDynamicLink) {
      const parsedUrl = urlParse(decodedUrl, true)
      const modeValue = urlParse(parsedUrl?.query?.link, true)?.query?.mode

      if (modeValue === MODE_CODE.baggage_prediction) {
        const { terminal } = parsedUrl?.query || {}
        handleOpenL3BaggagePrediction({ queryData: { terminal }, goToExternalLink: goToNewUri })
        return false
      }
    }

    if (url) {
      const urlObj = new URL(url)
      const searchParams = urlObj?.searchParams
      const link = searchParams?.get?.("link") ? new URL(decodeURIComponent(searchParams?.get?.("link"))) : null
      const linkParams = link?.searchParams
      const mode = linkParams?.get?.("mode")
      if (mode === MODE_CODE.retro_claims) {
        goToRetroClaims({ navigation })
        return false
      }
    }
    return true
  }

  /**
   * Function to render the dummy view in the header
   * if we don't need CTA button in the header, to make the title alignment center a dummy view is used.
   */

  const onClosedSheet = useCallback(() => {
    shouldOpenGallery.current = false
    setIsBottomSheetVisible(false)
  }, [])

  const onClosedSheetCM = useCallback(() => {
    setIsBottomSheetCMVisible(false)
  }, [])

  const onClosedSheetQRScan = useCallback(() => {
    setIsBottomSheetQRScanVisible(false)
    setIsBottomSheetBarCodeScanVisible(false)
  }, [])

  const playpassOnLoadEnd = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent
    if (!isEmpty(nativeEvent)) {
      commonTrackingScreen(
        TrackingScreenName.PlayPassWebView,
        getPreviousScreen(),
        isLogIn,
      )
    }
  }

  const reloadScreen = () => {
    webviewRef.current?.reload()
  }

  const handlePopScreen = () => {
    const caculateValue = caculateNavigation()
    const step = handleCondition(caculateValue, caculateValue, 0)
    navigation.pop(step)
  }

  const onLoadEnd = (event) => {
    setCanGoBack(event?.nativeEvent?.canGoBack)
  }

  useCurrentScreenActiveAndPreviousScreenHook(TrackingScreenName.PlayPassWebView)

  const handleHeader = () => {
    const HeaderComponent = {
      [WebViewHeaderTypes.default]: (
        <DefaultHeader
          title={title}
          needBackButton={needBackButton}
          needCloseButton={needCloseButton}
          canGoBack={canGoBack}
          onBack={onBack}
          onBackButtonPressed={onBackButtonPressed}
          onCloseButtonPressed={onCloseButtonPressed}
          webViewTitle={webViewTitle}
        />
      ),
      [WebViewHeaderTypes.changiMillionaire]: (
        <ChangiMillionaireHeader
          onBackButtonPressed={onBackButtonPressed}
          reloadScreen={reloadScreen}
        />
      ),
      [WebViewHeaderTypes.appscapadeLP]: (
        <AppscapadeHeader
          title={title}
          onReload={reloadScreen}
          webViewTitle={webViewTitle}
          onCloseScreen={handlePopScreen}
          isDisable={false}
        />
      ),
      [WebViewHeaderTypes.appscapadeInstantWinLP]: (
        <AppscapadeHeader
          title={title}
          onReload={reloadScreen}
          webViewTitle={webViewTitle}
          onCloseScreen={onCloseButtonPressed || handlePopScreen}
          isDisable={false}
        />
      ),
    }
    return HeaderComponent?.[headerType]
  }

  return (
    <>
      <WebViewContainer style={container}>
        <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
        {handleHeader()}
        <ExtendedWebView
          isPlayPassView
          uri={uri}
          webviewRef={webviewRef}
          username={env()?.PLAYPASS_USERNAME}
          password={env()?.PLAYPASS_PASSWORD}
          navigationHandler={deepLinkHandler}
          needCredentialDecoding
          onTitleChange={setWebViewTitle}
          onNavigationStateChange={onNavigationStateChange}
          loading={loading || loadingFAQ}
          basicAuthCredential={basicAuthCredential}
          playpassOnLoadEnd={playpassOnLoadEnd}
          incognito={!isLoggedIn}
          onLoad={onLoadEnd}
          shouldShowLoadingCookies={shouldShowLoadingCookies}
          loadingCookiesStyles={loadingCookiesStyles}
          loadingCookiesSection={loadingCookiesSection}
          handleForAA={handleForAA}
        />
      </WebViewContainer>
      <BottomSheet
        isModalVisible={isBottomSheetVisible}
        onClosedSheet={onClosedSheet}
        containerStyle={bottomSheetStyle}
        stopDragCollapse
        onBackPressHandle={onClosedSheet}
        animationInTiming={200}
        animationOutTiming={200}
      >
        <TakeUploadPhoto
          isCameraGranted={true}
          onClosedSheet={onClosedSheet}
          handleTakePicture={handleTakePicture}
          shouldOpenGallery={shouldOpenGallery.current}
        />
      </BottomSheet>
      <BottomSheet
        isModalVisible={isBottomSheetCMVisible}
        onClosedSheet={onClosedSheetCM}
        containerStyle={bottomSheetStyle}
        stopDragCollapse
        onBackPressHandle={onClosedSheetCM}
        animationInTiming={200}
        animationOutTiming={200}
      >
        <UploadReceiptCMScreen
          onClosedSheet={onClosedSheetCM}
          handleTakePicture={handleTakePictureCM}
        />
      </BottomSheet>
      <BottomSheet
        isModalVisible={ifOneTrue([isBottomSheetQRScanisible, isBottomSheetBarCodeScanVisible])}
        onClosedSheet={onClosedSheetQRScan}
        containerStyle={bottomSheetStyle}
        stopDragCollapse
        onBackPressHandle={onClosedSheetQRScan}
        animationInTiming={200}
        animationOutTiming={200}
      >
        <ScanQRCMScreen
          onClosedSheet={onClosedSheetQRScan}
          handleQRScan={handleQRScan}
          handleUploadCM={handleUploadCM}
          isBottomSheetQRScanisible={isBottomSheetQRScanisible}
          isBottomSheetBarCodeScanVisible={isBottomSheetBarCodeScanVisible}
        />
      </BottomSheet>
    </>
  )
}

export { PlayPassWebView }
