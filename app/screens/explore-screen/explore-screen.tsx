import React, { useEffect, useRef, useState, useMemo, useCallback, useContext } from "react"
import {
  Platform,
  View,
  FlatList,
  TouchableOpacity,
  Linking,
  ViewStyle,
  LayoutChangeEvent,
  NativeSyntheticEvent,
  NativeScrollEvent,
  ScrollView,
  InteractionManager,
} from "react-native"
import DeviceInfo from "react-native-device-info"
import { useDispatch, useSelector } from "react-redux"
import { ShortcutLinkType } from "../../components/shortcut-link/shortcut-link.props"
import { ShortCutLinks } from "../../sections"
import HomePageMasthead from "../../sections/homepage-masthead/homepage-masthead"
import { ExploreContainer } from "./explore-container/explore-container"
import { TabBarContainer } from "./tab-bar-container/tab-bar-container"
import ExploreCreators, { ExploreSelectors, findSelectedCategory } from "app/redux/exploreRedux"
import {
  handleShowPopupRating,
  isCloseToBottom,
  scrollScreen,
} from "app/utils/screen-helper"
import { SCROLL_END_THRESHOLD } from "./explore-container/explore-container-props"
import { ExploreCategoryEnum } from "app/redux/types/explore/explore-categories"
import { useHandleScroll } from "app/navigators"
import { LoadingOverlay } from "app/components/loading-modal/loading-modal"
import {
  APP_DEEPLINKS,
  AppConfigPermissionTypes,
  CM24RedirectSource,
  NavigationConstants,
  NotificationL1Page,
  SHORTCUT_LINK_TYPES,
  StateCode,
  sectionTagName,
} from "app/utils/constants"
import { convertExploreChangiData, navigateToTab } from "./explore-helper"
import CartToast from "./cart-toast/cart-toast"
import { Text } from "app/elements/text/text"
import { translate } from "app/i18n"
import { ArrowDownWidth, InfoRed, SeeMore } from "ichangi-fe/assets/icons"
import { size, isEmpty, get } from "lodash"
import Geolocation from "react-native-geolocation-service"
import NativeAuthReducer, { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import { PERMISSIONS, request } from "react-native-permissions"
import NetInfo from "@react-native-community/netinfo"
import { styles } from "./explore-screen-styles"
import ShortcutLink from "../../components/shortcut-link/shortcut-link"
import { ProfileSelectors } from "app/redux/profileRedux"
import JustForYouCarousel from "./just-for-you-carousel/just-for-you-carousel-screen"
import UpComingFlightSection from "./upcoming-flight/upcoming-flight"
import { useFocusEffect, useIsFocused } from "@react-navigation/native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { AemSelectors, AEM_PAGE_NAME } from "app/redux/aemRedux"
import ExploreChangiModalFilter from "./explore-changi-modal-filter"
import SwipeUpDownModal from "./modal-swipe"
import {
  getPreviousScreen,
  useAdobeTargetHandle,
  useCurrentScreenActiveAndPreviousScreenHook,
  useGeneratePlayPassUrl,
  usePlayPassUrl,
} from "app/utils/screen-hook"
import {
  handleCondition,
  getQueryParams,
  convertToURLFormat,
  ifAllTrue,
  ifOneTrue,
} from "app/utils"
import { GrantPermissionModal } from "app/sections/notification-sections/grant-permission-modal"
import { AdobeTagName, commonTrackingScreen, trackAction } from "app/services/adobe"
import { store } from "app/redux/store"
import { NavigationAATag, NavigationPageSource, NavigationValueDeepLink, useHandleNavigation } from "app/utils/navigation-helper"
import { useCPay } from "app/helpers/changipay"
import { ForYouSelectors } from "app/redux/forYouRedux"
import { FocusAwareStatusBar } from "app/components/focus-status-bar"
import { analyticsLogEvent, ANALYTICS_LOG_EVENT_NAME, dtACtionLogEvent, dtBizEvent } from "app/services/firebase/analytics"
import { useGetConfigurationPermissionHelper } from "app/utils/get-configuration-permission"
import { mappingTierCode } from "app/components/homepage-masthead/homepage-masthead"
import { defaultExplorePageConfiguration } from "../dine-shop/json/exploreLanding"
import { env } from "app/config/env-params"
import BaseImage from "app/elements/base-image/base-image"
import ExploreStaffPerk from "app/sections/explore-staff-perk/explore-staff-perk"
import { load } from "app/utils/storage"
import { StorageKey } from "app/utils/storage/storage-key"
import { PageConfigSelectors } from "app/redux/pageConfigRedux"
import { REMOTE_CONFIG_FLAGS, getFeatureFlagInit, isFlagON, isFlagOnCondition } from "app/services/firebase/remote-config"
import AirportLandingCreator, { AirportLandingSelectors } from "app/redux/airportLandingRedux"
import { PlayPassEntryPoint } from "app/redux/types/explore/explore-item-type"
import { GrantPermissionModalOldVersion } from "app/sections/notification-sections/grant-permission-modal-old-version"
import MonarchOverlayController from "app/components/monarch-overlay/monarch-overlay-controler"
import MonarchOverlay from "app/components/monarch-overlay"
import { Tier } from "app/components/changi-rewards-member-card"
import { checkSurvey } from "app/services/survey/qualtrics"
import NotificationActions, { NotificationSelectors } from "app/redux/notificationRedux"
import { ShopSelectors } from "app/redux/shopRedux"
import { getViewerUID } from "app/utils/screen-helper"
import { USER_TYPES } from "app/utils/constants"
import TickerBand from "app/components/ticker-band/ticker-band"
import { useBottomTabBarHeight } from "@react-navigation/bottom-tabs"
import { AccountContext } from "app/services/context/account"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import { runOnJS, useSharedValue, withSpring } from "react-native-reanimated"
import { ScrollBuddy } from "app/components/scroll-buddy"
import { useBottomSheetError } from "./explore-screen.hooks"
import moment from 'moment-timezone'
import { ExploreContext } from "app/services/context/explore"
import { getPlayPassBookingFinished, setAllInitialPromptsAreDone, setIsShowModalCheckRatingPopup, getAllInitialPromptsAreDone } from "app/utils/storage/mmkv-storage"
import {
  AppRatingSession,
  useAppRatingOnSaveFlightExploreScreen,
  useTriggerAppRating,
} from "app/hooks/useAppRating"
import { PanResponderContext } from "app/services/context/pan-responder"
import Suspend from "app/components/suspend"
import { checkLoginState } from "app/utils/authentication"
import {
  useTickerbandMaintanance,
  MaintananceTickerBandType,
} from "app/hooks/useTickerbandMaintanence"
import { MytravelCreators, MytravelSelectors } from "app/redux/mytravelRedux"
import { AemGroupTwoSelectors } from "app/redux/aemGroupTwo"
import { usePlayPassStore } from "app/zustand/playpass"
import { getUpcomingEventRequest } from "app/services/api/playpass"

const MAX_ITEM_SHOW_SEE_MORE = 6
const SCREEN_NAME = "ExploreScreen"

const { TYPES, VALUES } = USER_TYPES
const {
  FLY_AIRPORTVISIT: USER_TYPE_FLY_AIRPORTVISIT,
  FLY_HOME: USER_TYPE_FLY_HOME,
  FLY_AIRPORT: USER_TYPE_FLY_AIRPORT,
  GENERAL: USER_TYPE_GENERAL,
  MONARC: USER_TYPE_MONARC,
} = TYPES
const {
  UNDEFINED: USER_TYPE_VALUE_UNDEFINED,
  FLY_AIRPORTVISIT: USER_TYPE_VALUE_FLY_AIRPORTVISIT,
  FLY_HOME: USER_TYPE_VALUE_FLY_HOME,
  FLY_AIRPORT: USER_TYPE_VALUE_FLY_AIRPORT,
  GENERAL: USER_TYPE_VALUE_GENERAL,
  MONARC: USER_TYPE_VALUE_MONARC,
} = VALUES

const wrapOrderExplore: ViewStyle = {
  marginTop: 24,
}

enum ExploreComponentTypes {
  timelineTile = "timelineTile",
  shortcutLink = "shortcutLink",
  latestHappening = "latestHappening",
  justForYou = "justForYou",
  staffPerkSwimlane = "staffPerkSwimlane",
}

const checkMonarchOverlay = async (isFinishedLoading, rewardsData, userProfile, isFocused) => {
  const ifTrue = handleCondition(isFinishedLoading && isFocused, true, false)
  if (ifTrue) {
    const isMonarchTier = handleCondition(
      rewardsData?.reward?.currentTierInfo?.replace(" ", "") === Tier.Monarch ||
        rewardsData?.reward?.currentTierInfo?.replace(" ", "") === Tier.StaffMonarch,
      true,
      false,
    )
    const listUserIdShowedMonarch = await load(StorageKey.monarchOveylayUserID)
    if (isMonarchTier && userProfile && !listUserIdShowedMonarch?.includes(userProfile?.id)) {
      MonarchOverlayController.showOverlay()
    }
  }
}

const useCheckSurveyOpen = (isTriggerPopupSuccess, appSettingData, currentForceUpdateFlow) => {
  useEffect(() => {
    if (isTriggerPopupSuccess) {
      checkSurvey(appSettingData, !!currentForceUpdateFlow.current)
    }
  }, [isTriggerPopupSuccess])
}

const SCROLL_BUDDY_THRESHOLD = 30

const ExploreScreen = ({ route, navigation }) => {
  const {
    isScrollToExploreChangiSection = false,
    section: exploreSection = "",
    initialFilterDate = "",
    initialFilterLocation = "",
  } = {
    ...route?.params || {},
  }
  const dispatch = useDispatch()
  const inset = useSafeAreaInsets()
  const timeoutSetShowScrollBuddy = useRef(null)
  const toastRef = useRef(null)
  const scrollViewRef = useRef(null)
  const scrollOffsetRef = useRef(0)
  const { getPlayPassUrl } = useGeneratePlayPassUrl("viewExplorePlayPass")

  // ANIMATED and REF
  const offsetY = useSharedValue(0)
  const isBegindScroll = useSharedValue(false)
  const scrollOffsets = useSharedValue({})
  const exploreChangiTabHeight = useSharedValue(0)
  const scrollBuddyOpacity = useSharedValue(1)

  const [isModalVisible, setModalVisible] = useState(false)
  const [animateModal, setanimateModal] = useState(false)
  const [showFilterModal, setShowFilterModal] = useState(false)
  const [isShowCardToast, setIsShowCardToast] = useState(false)
  const [filterDate, setFilterDate] = useState(initialFilterDate)
  const [checkedLocationState, setCheckedLocationState] = useState({})
  const announcementPayload = useSelector(NotificationSelectors.announcementPayload)
  const indexItemExploreChangi = useSelector(ExploreSelectors.indexItemExploreChangi)
  const offsetItemExploreChangi = useSelector(ExploreSelectors.offsetItemExploreChangi)
  const exploreDataPayload: any = useSelector(ExploreSelectors.exploreData)
  const { isLoading: isExploreDataLoading, hasError: isExploreDataError } = { ...exploreDataPayload }
  const hasMoreExploreData = useSelector(ExploreSelectors.hasMoreExploreData)
  const selectedExploreCategory = useSelector(ExploreSelectors.selectedExploreCategory)
  const isEventsCategorySelected = findSelectedCategory(
    selectedExploreCategory,
    ExploreCategoryEnum.events,
  )
  const isPkgCode = useSelector(ExploreSelectors.isPkgCode)
  const campaignCodeHappening = useSelector(ExploreSelectors.campaignCodeHappening)
  const pkgCodeLastedHappening = useSelector(ExploreSelectors.pkgCodeLastedHappening)
  const shortcutLinksExploreData = useSelector(ExploreSelectors.shortcutLinksExploreData)
  const playPassUrlPayload = useSelector(AirportLandingSelectors.getPlayPassUrlPayload("FAQupload"))
  const { loading: loadingExplorePlayPass } = usePlayPassUrl("viewExplorePlayPass")
  const { loading: loadingCartPlayPass } = usePlayPassUrl("viewCartPlayPass")
  const [defaultScrollOffset, setDefaultScrollOffset] = useState(0)
  const isLoggedIn = useSelector(AuthSelectors.isLoggedIn)
  const finishBiometric = useSelector(AuthSelectors.finishBiometric)
  const { handleScroll, scrollDirection } = useHandleScroll()
  const userProfile = useSelector(ProfileSelectors.profilePayload)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const rewardsData = useSelector(ForYouSelectors.rewardsData)
  const dataExplorePageConfiguration = useSelector(PageConfigSelectors.explorePagePayload)
  const scrollBuddyData = useSelector(AemGroupTwoSelectors.scrollBuddyPayload)
  const dataCommonAEM = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.AEM_COMMON_DATA))
  const pageLevelExploreScreen = get(dataExplorePageConfiguration, "list", [])?.filter(
    (e) => ExploreComponentTypes[e?.sectionComponent],
  )
  const configPage = handleCondition(
    !isEmpty(pageLevelExploreScreen),
    pageLevelExploreScreen,
    defaultExplorePageConfiguration,
  )
  const isGetRatingSuccess = useSelector(PageConfigSelectors.getRatingSuccess)
  const [staffPerkRequest, setStaffPerkRequest] = useState(false)
  const latestHappeningsRequest = useSelector(ExploreSelectors.latestHappeningsRequest)
  const upComingFlightFetching = useSelector(ExploreSelectors.upComingFlightFetching)
  const [justForYouCarouselFetching, setJustForYouDataFetching] = useState(false)
  const isTriggerPopupSuccess = useSelector(NotificationSelectors.isTriggerPopupSuccess)
  const recommendedProductsFetching = useSelector(ShopSelectors.recommendedProductsFetching)
  const dataErrorMaintenanceConfig = useSelector(
    AemSelectors.getAemConfig(AEM_PAGE_NAME.GET_ERROR_MAINTENANCE),
  )
  const listErrorMaintenance = get(dataErrorMaintenanceConfig, "data.list", [])
  const sectionMaintenanceObj = useMemo(() => {
    const data = listErrorMaintenance?.find(
      (obj: any) => obj.sectionName === sectionTagName.exploreChangi,
    )
    return data || {}
  }, [listErrorMaintenance])
  const [showGrantPermissionFlow, setShowGrantPermissionFlow] = useState(false)
  const mastHeadsAEM = get(dataCommonAEM, "data.pageLanding.explore.mastHeads")
  const tier = isLoggedIn ? rewardsData?.reward?.currentTierInfo?.replace(" ", "") : "Non-logged-in"
  const imageHomepageAEM = mappingTierCode(tier, mastHeadsAEM)
  const errorAEM = dataCommonAEM?.error
  const isFocused = useIsFocused()
  const { handleNavigation } = useHandleNavigation("EXPLORE_SCREEN")
  const [shortcutLinksProcessed, setShortcutLinksProcessed] = useState(null)
  const [mapRMFlag, setMapRMFlag] = useState(false)

  const shortcutLinkStyles: ViewStyle = { paddingBottom: Platform.OS === "ios" ? inset.bottom : 0 }
  const refScreen = useRef<number>(0)
  const loadingBookingData = usePlayPassStore((state) => state.playPassStickyCartLoading)
  const playPassStickyCart = usePlayPassStore((state) => state.playPassStickyCart)
  const { timerTs } = playPassStickyCart || {}
  const { nativeOnboardingScreen } = useContext(AccountContext)
  const isNativeOnboardingScreen = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.NATIVE_ONBOARDING_SCREEN, nativeOnboardingScreen)

  const { loadingGetConfig, getConfigApp, notifyDisableChangiPay } =
    useGetConfigurationPermissionHelper()

  // STATE
  const currentScrollOffset = useMemo(() => {
    return scrollOffsets.value?.[selectedExploreCategory]
  }, [selectedExploreCategory])

  const {
    isShowMaintenance,
    tickerBand,
    tickerBandDescription,
    tickerBandButtonText,
    onPressCTA,
    onCloseTickerBand,
  } = useTickerbandMaintanance(MaintananceTickerBandType.FLY_EXPLORE_SWIMLANE)
  const bottomTabHeight = useBottomTabBarHeight()
  const { exploreScrollBuddyFlag, exploreStaffPerksFlag, exploreJustForYouFlag } = useContext(ExploreContext)
  const { triggerShowAppRatingRef } = useContext(PanResponderContext)
  const isExistScrollBuddy = isFlagOnCondition(exploreScrollBuddyFlag)
  const isExploreStaffPerksFlag = isFlagOnCondition(exploreStaffPerksFlag)
  const isJustForYouFlagOn = isFlagOnCondition(exploreJustForYouFlag)
  const isDisplayTickerBand = useMemo(
    () =>
      !isEmpty(myTravelFlightsPayload?.getMyTravelFlightDetails) &&
      myTravelFlightsPayload?.getMyTravelFlightDetails?.length > 0 &&
      isShowMaintenance,
    [
      myTravelFlightsPayload?.getMyTravelFlightDetails,
      myTravelFlightsPayload?.getMyTravelFlightDetails?.length,
      isShowMaintenance,
    ],
  )

  const onExploreTabLayout = useCallback((e: LayoutChangeEvent) => {
    exploreChangiTabHeight.value = e.nativeEvent.layout.height
  }, [])

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    if (ifAllTrue([isLoggedIn, isConnected])) {
      dispatch(NativeAuthReducer.nativeAuthTokenVerifyRequest())
    }
  }

  useEffect(() => {
    const seconds = moment.duration(moment.tz(timerTs, "Asia/Singapore").utc().diff(moment().utc())).asSeconds()
    if(seconds > 0){
      setIsShowCardToast(true)
    } else {
      setIsShowCardToast(false)
    }
  }, [timerTs])

  const checkListTrigger = async () => {
    const isParticipatedCmWebview = await load(StorageKey.isParticipatedCmWebview)
    const isParticipatedInAppFolio = await load(StorageKey.isParticipatedInAppFolio)
    return ifOneTrue([isParticipatedCmWebview, isParticipatedInAppFolio])
  }

  useFocusEffect(
    React.useCallback(() => {
      if(scrollOffsetRef.current > SCROLL_BUDDY_THRESHOLD || loadingBookingData){
        runOnJS(hideScrollBuddy)()
      } else {
        runOnJS(displayScrollBuddy)()
      }
      return () => {
        runOnJS(hideScrollBuddy)()
      }
    }, [loadingBookingData]),
  )

  const { handleResetInactivityTimeout } = useTriggerAppRating(AppRatingSession.ExploreScreen)
  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        checkInternet()
      })
    }, [isLoggedIn])
  )

  // Get AEM L3 page
  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        handleResetInactivityTimeout()
      })
    }, [])
  )

  const handleTrackingShowRatingPopup = async () => {
    const listTrigger = await checkListTrigger()
    const { authTokenAgeHours = "48", ratingPopupDays = "120" } =
      store.getState()?.pageConfigReducer
    const checkAuthTokenAndcadence = ifAllTrue([
      !isEmpty(authTokenAgeHours),
      !isEmpty(ratingPopupDays),
      isGetRatingSuccess,
    ])
    if (
      ifAllTrue([
        !route?.params?.isOpenApp,
        listTrigger,
        checkLoginState(),
        checkAuthTokenAndcadence,
      ])
    ) {
      handleShowPopupRating(Number(authTokenAgeHours), Number(ratingPopupDays))
    }
  }

  useEffect(() => {
    if (triggerShowAppRatingRef?.current) {
      triggerShowAppRatingRef.current[AppRatingSession.OldLogic] = handleTrackingShowRatingPopup
    } else {
      triggerShowAppRatingRef.current = {
        [AppRatingSession.OldLogic]: handleTrackingShowRatingPopup,
      }
    }
  }, [handleTrackingShowRatingPopup])

  useAdobeTargetHandle({ isFlagOn: isJustForYouFlagOn })

  // Handle app rating popup logic
  useAppRatingOnSaveFlightExploreScreen({
    route,
    triggerShowAppRatingRef,
  })

  useEffect(() => {
    analyticsLogEvent(ANALYTICS_LOG_EVENT_NAME.LANDING)
    dtACtionLogEvent(ANALYTICS_LOG_EVENT_NAME.LANDING)
    dtBizEvent(SCREEN_NAME,ANALYTICS_LOG_EVENT_NAME.LANDING, 'App-Event',{})
  }, [])

  const requestLocationPermission = () => {
    if (Platform.OS === "ios") {
      Geolocation.requestAuthorization("whenInUse")
    } else {
      request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION)
    }
  }

  const handleDeepLink = async ({ url }) => {
    if (url?.startsWith(APP_DEEPLINKS.LOCATION_PERMISSION)) {
      requestLocationPermission()
    }
  }

  useEffect(() => {
    const linkingEvent: any = Linking.addEventListener("url", handleDeepLink)
    Linking.getInitialURL().then((url) => {
      handleDeepLink({ url })
    })
    return () => {
      linkingEvent?.remove()
    }
  }, [])

  useCurrentScreenActiveAndPreviousScreenHook("Home_Page")
  useFocusEffect(
    React.useCallback(() => {
      InteractionManager.runAfterInteractions(() => {
        commonTrackingScreen("Home_Page", getPreviousScreen(), isLoggedIn)
      })
      return () => {
        InteractionManager.runAfterInteractions(() => {
          navigation.setParams({ isOpenApp: false })
          clearTimeout(timeoutSetShowScrollBuddy.current)
        })
      }
    }, []),
  )

  const loadShortcutLinks = React.useCallback(() => {
    dispatch(ExploreCreators.shortcutLinksExplorePreRequest())
    Geolocation.getCurrentPosition(
      (position) => {
        getShortcutLinkExplorer(position)
      },
      (error) => {
        getShortcutLinkExplorer(null)
        console.log(error.code, error.message)
      },
      { enableHighAccuracy: false, timeout: 10000, maximumAge: 5000 },
    )
  }, [isLoggedIn, tier])

  useEffect(() => {
    if (isLoggedIn && !myTravelFlightsPayload?.loading && myTravelFlightsPayload?.getMyTravelFlightDetails && !isEmpty(userProfile?.email)) {
      const isPlayPassBookingFinished = getPlayPassBookingFinished()
      if (isPlayPassBookingFinished) {
        InteractionManager.runAfterInteractions(() => {
          getUpcomingEventRequest()
        })
      }
    }
  }, [myTravelFlightsPayload, isLoggedIn, userProfile?.email])

  const onReLoadUpComingEvent = () => {
    dispatch(MytravelCreators.flyMyTravelFlightsRequest(userProfile?.email))
  }

  const getShortcutLinkExplorer = (location: any) => {
    const params = {
      latitude: location?.coords?.latitude || "",
      longitude: location?.coords?.longitude || "",
      isViewMore: false,
    }
    dispatch(ExploreCreators.shortcutLinksExploreRequest(params))
  }

  useEffect(() => {
    if (!isNativeOnboardingScreen) {
      setAllInitialPromptsAreDone(true)
      dispatch(NotificationActions.toggleL1Advisory(true))
    }
  }, [isNativeOnboardingScreen])

  useEffect(() => {
    if (isNativeOnboardingScreen && finishBiometric) {
      dispatch(NotificationActions.toggleL1Advisory(false))
      if (Platform.OS === "ios") {
        Geolocation.requestAuthorization("whenInUse").then(() => {
          setShowGrantPermissionFlow(true)
        })
      } else {
        request(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION).then(() => {
          setShowGrantPermissionFlow(true)
        })
      }
    }
    return () => {
      dispatch(ExploreCreators.setNoInternetFilter(true))
    }
  }, [finishBiometric, isNativeOnboardingScreen])

  useEffect(() => {
    if(isLoggedIn){
      if(myTravelFlightsPayload?.done){
        loadShortcutLinks()
      }
    } else {
      loadShortcutLinks()
    }
  }, [loadShortcutLinks, isLoggedIn, myTravelFlightsPayload?.done])

  useEffect(() => {
    const isNotFinishedLoading = ifOneTrue([
      isExploreDataLoading,
      loadingExplorePlayPass,
      loadingCartPlayPass,
      loadingGetConfig,
      staffPerkRequest,
      shortcutLinksExploreData?.isLoading,
      latestHappeningsRequest,
      upComingFlightFetching,
      justForYouCarouselFetching,
      recommendedProductsFetching,
    ])
    checkMonarchOverlay(!isNotFinishedLoading, rewardsData, userProfile, isFocused)
    if (
      (isScrollToExploreChangiSection || exploreSection === "explorechangi") &&
      !isNotFinishedLoading
    ) {
      //scroll to the explore section
      InteractionManager.runAfterInteractions(() => {
        setTimeout(() => {
          scrollScreen(scrollViewRef, refScreen.current, false)
          navigation.setParams({ isScrollToExploreChangiSection: false, section: "" })
        }, 200)
      })
    }
  }, [
    defaultScrollOffset,
    isScrollToExploreChangiSection,
    navigation,
    exploreSection,
    isExploreDataLoading,
    loadingExplorePlayPass,
    loadingCartPlayPass,
    loadingGetConfig,
    staffPerkRequest,
    shortcutLinksExploreData?.isLoading,
    latestHappeningsRequest,
    upComingFlightFetching,
    justForYouCarouselFetching,
    isFocused,
    recommendedProductsFetching,
    refScreen.current,
  ])

  useEffect(() => {
    dispatch(
      NativeAuthReducer.saveLegacyUserInfo({
        info: {},
        isLegacy: false,
      }),
    )
  }, [])

  const updatePageNumber = React.useCallback(
    (event) => {
      const { contentOffset } = event.nativeEvent
      offsetY.value = contentOffset.y
      if (scrollDirection?.current === "up") return
      if (isCloseToBottom(event, SCROLL_END_THRESHOLD)) {
        hasMoreExploreData && dispatch(ExploreCreators.exploreDataSetPageNumber())
      }
    },
    [isEventsCategorySelected, hasMoreExploreData],
  )

  const {openChangiPay} = useCPay()

  const onChangiPay = async () => {
    trackAction(AdobeTagName.CAppHomeWallet, {
      [AdobeTagName.CAppHomeWallet]: "1",
    })
    getConfigApp({
      configKey: AppConfigPermissionTypes.changiappWalletEnabled,
      callbackSuccess: () => openChangiPay(),
      callbackFailure: () => notifyDisableChangiPay(),
    })
  }

  const displayScrollBuddy = useCallback(() => {
    scrollBuddyOpacity.value = withSpring(1)
  }, [])

  const hideScrollBuddy = useCallback(() => {
    scrollBuddyOpacity.value = withSpring(0)
  }, [])

  const onScroll = React.useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      if(event.nativeEvent.contentOffset.y < SCROLL_BUDDY_THRESHOLD){
        runOnJS(displayScrollBuddy)()
      } else {
        // runOnJS(hideScrollBuddy)()
      }
      handleScroll(event)
      scrollOffsetRef.current = event.nativeEvent.contentOffset.y
      if (isExploreDataLoading || isExploreDataError) return
      updatePageNumber(event)
    },
    [
      isExploreDataLoading,
      isExploreDataError,
      isEventsCategorySelected,
      hasMoreExploreData,
    ],
  )

  const onScrollBegin = React.useCallback(() => {
    isBegindScroll.value = true
  }, [])

  const setScrollOffsetObject = React.useCallback((newSelectedExploreCategory) => {
    const newValue = scrollOffsets.value
    newValue[newSelectedExploreCategory] = scrollOffsetRef?.current
    scrollOffsets.value = newValue
  }, [])

  const onScrollTouchEnd = React.useCallback(() => {
    if (isBegindScroll.value) {
      setScrollOffsetObject(selectedExploreCategory)
      isBegindScroll.value = false
    }
  }, [selectedExploreCategory])

  const onScrollEnd = React.useCallback(() => {
    setScrollOffsetObject(selectedExploreCategory)
  }, [selectedExploreCategory])

  const onExploreContainerLayout = React.useCallback((layoutOffset) => {
    refScreen.current = layoutOffset
    setDefaultScrollOffset(layoutOffset)
  }, [])

  const shortcutLinksOnPressed = React.useCallback(
    async (shortcutLinkDetail) => {
      let cappHomeQuicklinksValueToBeSent = shortcutLinkDetail?.title
      const isTrackBaggageItem = shortcutLinkDetail?.title === "Track Baggage"
      if (isTrackBaggageItem) {
        const UID = await getViewerUID({ shouldReturnNull: true })
        cappHomeQuicklinksValueToBeSent = `${cappHomeQuicklinksValueToBeSent} | ${UID}`
      }
      trackAction(AdobeTagName.CAppHomeQuicklinks, {
        [AdobeTagName.CAppHomeQuicklinks]: cappHomeQuicklinksValueToBeSent,
      })

      const userTypeFromQuery = shortcutLinksExploreData?.data?.userType

      let userTypeValue = USER_TYPE_VALUE_UNDEFINED
      if (userTypeFromQuery === USER_TYPE_FLY_AIRPORTVISIT) {
        userTypeValue = USER_TYPE_VALUE_FLY_AIRPORTVISIT
      }
      if (userTypeFromQuery === USER_TYPE_FLY_HOME) {
        userTypeValue = USER_TYPE_VALUE_FLY_HOME
      }
      if (userTypeFromQuery === USER_TYPE_FLY_AIRPORT) {
        userTypeValue = USER_TYPE_VALUE_FLY_AIRPORT
      }
      if (userTypeFromQuery === USER_TYPE_GENERAL) {
        userTypeValue = USER_TYPE_VALUE_GENERAL
      }
      if (userTypeFromQuery === USER_TYPE_MONARC) {
        userTypeValue = USER_TYPE_VALUE_MONARC
      }

      const indexValueToBeSent = shortcutLinkDetail?.index + 1
      const trackActionValueToBeSent = `${userTypeValue} | ${shortcutLinkDetail?.title} | ${indexValueToBeSent}`
      trackAction(AdobeTagName.CAppHomeQuicklinksPersonalization, {
        [AdobeTagName.CAppHomeQuicklinksPersonalization]: trackActionValueToBeSent,
      })

      const {
        navigationType,
        navigationValue,
        isSeeMore,
        redirect,
        shortcutLinkType,
        tabName,
        playpassCampaignCd,
        playpassPackageCd,
        redirectToShowOnListing,
      } = shortcutLinkDetail || {}
      if (isSeeMore) {
        setIsShowModalCheckRatingPopup(true)
        setModalVisible(true)
        return
      }

      const isPlaypassShortcutLink = shortcutLinkType === SHORTCUT_LINK_TYPES.PLAYPASS
      if (isPlaypassShortcutLink) {
        setModalVisible(false)
        setIsShowModalCheckRatingPopup(false)
        dispatch(ExploreCreators.setIndexItemExploreChangi(-1))
        const shouldScrollToExploreChangi =
          redirectToShowOnListing && (!!playpassCampaignCd || !!playpassPackageCd)
        if (shouldScrollToExploreChangi) {
          if (!isEmpty(sectionMaintenanceObj) && sectionMaintenanceObj?.enableMode) {
            scrollViewRef?.current?.scrollToEnd({ animated: true })
          } else {
            handleScrollToExploreChangi(tabName, playpassPackageCd, playpassCampaignCd)
          }
          return
        }

        const shouldRedirectToPlaypassPage = !redirectToShowOnListing && !!playpassPackageCd
        if (shouldRedirectToPlaypassPage) {
          if (isLoggedIn) {
            getPlayPassUrl(StateCode.PPEVENT, playpassPackageCd, {
              entryPoint: PlayPassEntryPoint.EXPLORE_CHANGI,
              eventName: "exploreShortcutLink",
            })
          } else {
            const params = `?app=3&package_code=${playpassPackageCd}`
            navigation.navigate(NavigationConstants.playpassWebview, {
              uri: `${env()?.PLAYPASS_URL_NONE_LOGIN}${params}`,
              title: "",
              needBackButton: true,
              needCloseButton: true,
            })
          }
          return
        }
      }

      if (!navigationValue || !navigationType) return
      setModalVisible(false)
      setIsShowModalCheckRatingPopup(false)
      if (navigationValue === "exploreChangi") {
        scrollScreen(scrollViewRef, defaultScrollOffset, true)
        return
      }
      setTimeout(() => {
        handleNavigation(navigationType, navigationValue, redirect, {
        redirectFrom: CM24RedirectSource.QuickLinks,
      })
      }, 500);
    },
    [navigation, currentScrollOffset, defaultScrollOffset, isLoggedIn, sectionMaintenanceObj?.enableMode],
  )

  useEffect(() => {
    if (isPkgCode && !isEmpty(pkgCodeLastedHappening)) {
      if (pkgCodeLastedHappening === "DEFAULT_CODE") {
        const index = exploreDataPayload?.exploreCategoriesData?.findIndex(
          (data: any) => data?.campaignCode === campaignCodeHappening,
        )

        if (index === -1) {
          scrollScreen(scrollViewRef, defaultScrollOffset, true)
          dispatch(ExploreCreators.resetPkgCodeLastedHappening())
        } else {
          dispatch(ExploreCreators.setIndexItemExploreChangi(index))
        }
      } else {
        const convertedExploreChangiData = convertExploreChangiData(exploreDataPayload?.exploreCategoriesData)
        const index = convertedExploreChangiData?.findIndex(
          (data) => data?.packageCode === pkgCodeLastedHappening,
        )
        dispatch(ExploreCreators.setIndexItemExploreChangi(index))
      }
    }
  }, [isPkgCode, pkgCodeLastedHappening, campaignCodeHappening])

  useEffect(() => {
    if (offsetItemExploreChangi > 0) {
      scrollViewRef?.current?.scrollTo({
        y: offsetItemExploreChangi + offsetY.value - exploreChangiTabHeight.value,
        animated: true,
      })
      dispatch(ExploreCreators.resetPkgCodeLastedHappening())
    }
  }, [indexItemExploreChangi, offsetItemExploreChangi])

  const handleScrollToExploreChangi = (
    tabName: string,
    packageCode: string,
    campaignCode: string,
  ) => {
    setFilterDate(initialFilterDate)
    setCheckedLocationState(initialFilterLocation)
    const pkgCode = isEmpty(packageCode) ? "DEFAULT_CODE" : packageCode
    dispatch(ExploreCreators.setCampaignCodeHappening(campaignCode || ""))
    dispatch(ExploreCreators.setPkgCodeLastedHappening(pkgCode))
    const category = isEmpty(tabName) ? "All" : tabName
    navigateToTab(selectedExploreCategory, currentScrollOffset, category, dispatch)
    dispatch(ExploreCreators.exploreDataReset())
    dispatch(ExploreCreators.resetExploreChangiLocation())
    dispatch(
      ExploreCreators.exploreDataRequest({
        pageNumber: 1,
        category: category,
        categoryCode: [],
        email: userProfile?.email,
        date: "",
        locations: [],
        pkgCode: packageCode || "",
      }),
    )
  }

  const onClosedSheet = () => {
    setModalVisible(false)
    setIsShowModalCheckRatingPopup(false)
  }

  useEffect(() => {
    const fetchAtomRMConfig = () => {
      const mapFlagEnable = isFlagON(REMOTE_CONFIG_FLAGS.ATOMS_MAP)
      setMapRMFlag(mapFlagEnable)
    }
    fetchAtomRMConfig()
  }, [])

  const {currentForceUpdateFlow} = useContext(PanResponderContext)
  useCheckSurveyOpen(isTriggerPopupSuccess, env(), currentForceUpdateFlow)

  useEffect(() => {
    let links = shortcutLinksExploreData?.data?.shortcutLinks
    if (!isEmpty(links)) {
      if (!mapRMFlag) {
        links = links?.filter((link) => link?.navigationValue !== "atomMap")
      }

      setShortcutLinksProcessed(links)
    }
  }, [shortcutLinksExploreData, mapRMFlag])

  const shortcutLinksData = useMemo(() => {
    if (
      shortcutLinksProcessed &&
      Array.isArray(shortcutLinksProcessed) &&
      shortcutLinksProcessed?.length > MAX_ITEM_SHOW_SEE_MORE
    ) {
      const seeMore = {
        icon: SeeMore,
        title: translate("exploreScreen.seeMore"),
        isSeeMore: true,
        isCustomIcon: true,
      }
      return [...shortcutLinksProcessed.slice(0, MAX_ITEM_SHOW_SEE_MORE), seeMore]
    }
    return shortcutLinksProcessed
  }, [shortcutLinksProcessed])

  const playPassItemOnPressed = (eventDetailsItem: any) => {
    const { navigationType, navigationValue, redirect } = eventDetailsItem || ""
    if (!navigationValue) return
    handleNavigation(navigationType, navigationValue, redirect)
  }

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        InteractionManager.runAfterInteractions(() => {
          setTimeout(() => {
            setShowFilterModal(false)
            setIsShowModalCheckRatingPopup(false)
          }, 300);
        });
      }
    }, []),
  )

  const scrollBuddyOnPress = () => {
    trackAction(AdobeTagName.CAppHomePlaypassLandingTiles, {
      [AdobeTagName.CAppHomePlaypassLandingTiles]: "Scroll buddy",
    })
    // Handle deep-link logic
    const navigationData = scrollBuddyData?.navigation
    if (navigationData) {
      handleNavigation(
        navigationData?.type,
        navigationData?.value,
        [
          NavigationValueDeepLink.gameMain,
          NavigationValueDeepLink.gameAsteroid,
          NavigationValueDeepLink.gameMissionpass,
        ].some((val) => val === navigationData?.value)
          ? {
              aaTag: NavigationAATag.ScrollBuddy,
              isLoggedInAtTriggerTime: checkLoginState(),
              pageSource: NavigationPageSource.ScrollBuddy,
            }
          : undefined,
      )
      return
    }
    const category = scrollBuddyData?.category || "All"
    if (!isEmpty(sectionMaintenanceObj) && sectionMaintenanceObj?.enableMode) {
      scrollViewRef?.current?.scrollToEnd({ animated: true })
    } else {
      handleScrollToExploreChangi(category, "", "")
    }
  }

  const renderScrollBuddyComponent = useMemo(() => {
    const validAnnouncementLength = announcementPayload?.filter?.((item) =>
      item?.extraJsonData?.screens?.some?.((scr) => scr?.tagName === NotificationL1Page.Explore),
    )?.length
    if (isShowCardToast || !isExistScrollBuddy || loadingBookingData || validAnnouncementLength) {
      return null
    }
    return (
      <ScrollBuddy
        opacity={scrollBuddyOpacity}
        scrollBuddyImage={scrollBuddyData?.image}
        onPress={scrollBuddyOnPress}
      />
    )
  }, [
    isShowCardToast,
    isExistScrollBuddy,
    scrollBuddyData,
    loadingBookingData,
    JSON.stringify(announcementPayload),
  ])

  const grantPermissionContent = useCallback(() => {
    return handleCondition(
      Platform.OS === "android",
      (() => {
        const androidLevel = DeviceInfo.getSystemVersion()
        return handleCondition(
          Number(androidLevel) < 13,
          <GrantPermissionModalOldVersion />,
          <GrantPermissionModal />,
        )
      })(),
      <GrantPermissionModal />,
    )
  }, [])

  const onShowFilterModal = useCallback(() => {
    setIsShowModalCheckRatingPopup(true)
    setShowFilterModal(true)
  }, [])

  useBottomSheetError({ route })

  return (
    <Suspend freeze={!isFocused}>
      <View testID="ExploreScreen" style={styles.containerStyle}>
        <FocusAwareStatusBar
          translucent
          backgroundColor="transparent"
          barStyle={
            imageHomepageAEM?.background === "Dark" || errorAEM ? "light-content" : "dark-content"
          }
        />
        {isDisplayTickerBand && (
          <TickerBand
            urgent={false}
            title={tickerBand}
            description={tickerBandDescription}
            buttonText={tickerBandButtonText}
            onCTAPress={onPressCTA}
            onClose={() => onCloseTickerBand()}
            tickerStyle={{ paddingTop: 50 }}
          />
        )}
        <ScrollView
          onScroll={onScroll}
          onScrollEndDrag={updatePageNumber}
          onScrollBeginDrag={onScrollBegin}
          onMomentumScrollEnd={onScrollEnd}
          onTouchEnd={onScrollTouchEnd}
          scrollEventThrottle={0.1}
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
          stickyHeaderIndices={[2]}
          testID={`${SCREEN_NAME}__ScrollView`}
          accessibilityLabel={`${SCREEN_NAME}__ScrollView`}
          contentContainerStyle={{ paddingBottom: bottomTabHeight }}
        >
          <HomePageMasthead
            testID={`${SCREEN_NAME}__HomePageMasthead`}
            accessibilityLabel={`${SCREEN_NAME}__HomePageMasthead`}
            onChangiPayPressed={onChangiPay}
            onReLoadUpComingEvent={onReLoadUpComingEvent}
          />
          <View style={wrapOrderExplore}>
            {configPage?.map((element, index) => {
              const needSeparator = size(configPage) - 1 !== index
              const isLastItem = size(configPage) === index + 1
              switch (element?.sectionComponent) {
                case ExploreComponentTypes.shortcutLink:
                  return shortcutLinksExploreData?.hasError ? (
                    <ErrorComponent
                      type={ErrorComponentType.standard}
                      onPressed={() => loadShortcutLinks()}
                      style={styles.errorComponent}
                      testID={`${SCREEN_NAME}__ErrorComponent`}
                      accessibilityLabel={`${SCREEN_NAME}__ErrorComponent`}
                      key={`${element?.sectionComponent}-${index}`}
                    />
                  ) : (
                    <ShortCutLinks
                      data={shortcutLinksData}
                      type={handleCondition(
                        shortcutLinksExploreData?.isLoading,
                        ShortcutLinkType.loading,
                        ShortcutLinkType.default,
                      )}
                      onPressed={shortcutLinksOnPressed}
                      testID={`${SCREEN_NAME}__ShortCutLinksExplore`}
                      accessibilityLabel={`${SCREEN_NAME}__ShortCutLinksExplore`}
                      useSeparator={needSeparator}
                      isLastItem={isLastItem}
                      key={`${element?.sectionComponent}-${index}`}
                    />
                  )
                case ExploreComponentTypes.timelineTile:
                  return (
                    // <UpComingFlightSection
                    //   useSeparator={needSeparator}
                    //   isLastItem={isLastItem}
                    //   key={`${element?.sectionComponent}-${index}`}
                    // />
                    <></>
                  )
                case ExploreComponentTypes.justForYou:
                  return (
                    <JustForYouCarousel
                      testID={`${SCREEN_NAME}__JustForYouCarousel`}
                      accessibilityLabel={`${SCREEN_NAME}__JustForYouCarousel`}
                      useSeparator={needSeparator}
                      isLastItem={isLastItem}
                      key={`${element?.sectionComponent}-${index}`}
                      setJustForYouDataFetching={setJustForYouDataFetching}
                    />
                  )
                case ExploreComponentTypes.staffPerkSwimlane:
                  return (
                    <ExploreStaffPerk
                      key={`${element?.sectionComponent}-${index}`}
                      navigation={navigation}
                      useSeparator={needSeparator}
                      isLastItem={isLastItem}
                      setStaffPerkRequest={setStaffPerkRequest}
                      isExploreStaffPerksFlag={isExploreStaffPerksFlag}
                    />
                  )
                default:
                  return null
              }
            })}
          </View>
          {/* ------------------------ End order ------------------------ */}
          <TabBarContainer
            scrollOffset={currentScrollOffset}
            testID={`${SCREEN_NAME}__ShortCutLinksExplore`}
            accessibilityLabel={`${SCREEN_NAME}__ShortCutLinksExplore`}
            setHeigtExploreChangiTab={onExploreTabLayout}
            showFilterModal={onShowFilterModal}
          />
          <ExploreContainer
            scrollViewRef={scrollViewRef}
            onExploreContainerLayout={onExploreContainerLayout}
            playPassItemOnPressed={(data) => playPassItemOnPressed(data)}
            showFilterModal={onShowFilterModal}
            heigtExploreChangiTab={exploreChangiTabHeight}
            testID={`${SCREEN_NAME}__ShortCutLinksExplore`}
            accessibilityLabel={`${SCREEN_NAME}__ShortCutLinksExplore`}
          />
          <View style={styles.paddingStyle} />
        </ScrollView>

        <SwipeUpDownModal
          modalVisible={isModalVisible}
          PressToanimate={animateModal}
          ContentModal={
            <View style={styles.containerContent}>
              <View style={styles.bottomSheetStyle}>
                <View style={styles.bottomSheetStyle}>
                  <Text preset="h2" style={styles.titleBottomSheet}>
                    {translate("exploreScreen.exploreChangi")}
                  </Text>
                  <View style={[styles.shortcutLinkContainer, shortcutLinkStyles]}>
                    <FlatList
                      data={shortcutLinksProcessed}
                      renderItem={({ item, index }) => {
                        const updatedItem = { ...item, index, shortcutLinkType: item?.type }
                        return (
                          <View style={styles.itemShortcutLinkView}>
                            <ShortcutLink
                              type={
                                shortcutLinksExploreData?.isLoading
                                  ? ShortcutLinkType.loading
                                  : ShortcutLinkType.default
                              }
                              {...updatedItem}
                              onPressed={shortcutLinksOnPressed}
                            />
                          </View>
                        )
                      }}
                      horizontal={false}
                      contentContainerStyle={styles.swipeModalContentContainerStyle}
                      scrollEnabled
                      showsVerticalScrollIndicator={false}
                      keyExtractor={(_, index) => index.toString()}
                      numColumns={3}
                      testID={`${SCREEN_NAME}__FlatListData`}
                      accessibilityLabel={`${SCREEN_NAME}__FlatListData`}
                      ItemSeparatorComponent={() => <SeparatorComponent />}
                    />
                  </View>
                </View>
              </View>
            </View>
          }
          HeaderStyle={styles.headerContent}
          HeaderContent={
            <View style={styles.containerHeader}>
              <View style={styles.swipeModalHeaderContent}>
                <TouchableOpacity onPress={onClosedSheet}>
                  <BaseImage
                    style={styles.iconCloseModalStyle}
                    source={ArrowDownWidth}
                    resizeMode={"contain"}
                  />
                </TouchableOpacity>
              </View>
            </View>
          }
          MainContainerModal={styles.mainContainerModal}
          onClose={() => {
            setModalVisible(false)
            setanimateModal(false)
            setIsShowModalCheckRatingPopup(false)
          }}
        />
        {renderScrollBuddyComponent}

        {isLoggedIn && (
          <CartToast
            toastRef={toastRef}
            testID={`${SCREEN_NAME}__CartToast`}
            accessibilityLabel={`${SCREEN_NAME}__CartToast`}
            setIsShowCardToast={setIsShowCardToast}
          />
        )}
        <ExploreChangiModalFilter
          showFilterModal={showFilterModal}
          setShowFilterModal={setShowFilterModal}
          filterDate={filterDate}
          setFilterDate={setFilterDate}
          checkedLocationState={checkedLocationState}
          setCheckedLocationState={setCheckedLocationState}
          initialFilterLocation={initialFilterLocation}
          testID={`${SCREEN_NAME}__ModalFilter`}
          accessibilityLabel={`${SCREEN_NAME}__ModalFilter`}
        />
        <LoadingOverlay
          visible={
            isExploreDataLoading ||
            loadingExplorePlayPass ||
            loadingCartPlayPass ||
            loadingGetConfig
          }
        />
        {showGrantPermissionFlow && grantPermissionContent()}
        <MonarchOverlay />
        <BottomSheetError
          icon={<InfoRed />}
          visible={Boolean(playPassUrlPayload?.error) && isFocused}
          title={translate("popupError.somethingWrongOneline")}
          errorMessage={translate("popupError.networkErrorMessage")}
          onClose={() => {
            dispatch(AirportLandingCreator.getPlayPassUrlReset("FAQupload"))
          }}
          onButtonPressed={() => {
            dispatch(AirportLandingCreator.getPlayPassUrlReset("FAQupload"))
          }}
          buttonText={translate("subscription.close")}
          testID={`${SCREEN_NAME}__BottomSheetErrorSomethingWrong__Playpass`}
          accessibilityLabel={`${SCREEN_NAME}__BottomSheetErrorSomethingWrong__Playpass`}
        />
      </View>
    </Suspend>
  )
}
const SeparatorComponent = () => <View style={styles.separatorStyle} />
export default React.memo(ExploreScreen)
