import React, { use<PERSON>emo, useState, useEffect, useContext } from "react"
import {
  View,
  StyleSheet,
  Dimensions,
  StatusBar,
  Platform,
  TouchableOpacity,
} from "react-native"
import { color, typography } from "app/theme"
import { Text } from "app/elements/text/text"
import { InfoItemView } from "./info-item"
import { isEmpty, get } from "lodash"
import { translate } from "app/i18n"
import { CloseCross, InfoRed } from "ichangi-fe/assets/icons"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import { presets } from "app/elements/text"
import { Button } from "app/elements/button/button"
import LinearGradient from "react-native-linear-gradient"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view"
import { InputWrapper } from "app/components/input-wrapper"
import { InputField } from "app/components/input-field"
import { PROFILE_SCREEN_CONTEXT, schemeLiteVehicleIU } from "../profile-screen.schema"
import { useForm } from "app/utils/form-hook"
import { UpdateProfileSectionKey } from "app/utils/constants"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import NetInfo from "@react-native-community/netinfo"
import { useSelector } from "react-redux"
import { AemSelectors } from "app/redux/aemRedux"
import { useFocusEffect } from "@react-navigation/native"
const iconNoInternet = <InfoRed />

const SECTION_NAME = "VehicleDetails"

const isValidVehicleIU = (str?: string) => {
  return str ? /^[0-9]+$/.test(str) : true
}

const closeIconStyles: any = { color: color.palette.lightPurple }

const VehicleDetails = (props) => {
  const { personalData, onUpdateProfile, screenName } = props
  const PROFILE_SCREEN_CONTEXT_HANDLER = useContext(PROFILE_SCREEN_CONTEXT).Handlers

  const [isModalVisible, setModalVisible] = useState(false)
  const [isFocusInput, setFocusInput] = useState(true)
  const errorCommon = useSelector(AemSelectors.getErrorsCommon)
  const errorEHR15 = errorCommon?.find((item: any) => item.code === "EHR15")
  const [isNoConnection, setNoConnection] = useState(false)
  const vehicleIUValue = useMemo(() => {
    return isEmpty(personalData?.vehicleIU) ? translate("common.none") : personalData?.vehicleIU
  }, [personalData])

  const normalizeFormData = (profile) => ({
    firstName: profile?.firstName || "",
    lastName: profile?.lastName || "",
    phoneNum: profile?.phoneNum || "",
    dob: profile?.dob || "",
    gender: profile?.gender || "",
    email: profile?.email || "",
    residentialCountry: profile?.residentialCountry || "SG",
    postalCode: profile?.postalCode || "",
    countryCode: profile?.countryCode || "65",
    address: profile?.address || "",
    nationality: profile?.nationality || "SG",
    vehicleIU: profile?.vehicleIU || "",
    isStaff: !!(profile?.staffPassExpiry || profile?.staffPassNumber),
    staffPassExpiry: profile?.staffPassExpiry || "",
    staffPassNumber: profile?.staffPassNumber || "",
  })

  const {
    formData,
    setFieldValue,
    validateAt,
    handleSubmit,
    setFormData,
    isDirty,
    reset,
  } = useForm({
    form: normalizeFormData(personalData),
    schema: PROFILE_SCREEN_CONTEXT_HANDLER.schema,
  })

  const disabled = useMemo(() => {
    if (isDirty) {
      try {
        return !schemeLiteVehicleIU.isValidSync(formData)
      } catch (error) {
        return true
      }
    }

    return true
  }, [formData, isDirty])

  const isValidFrom = useMemo(() => {
    if (isEmpty(formData.vehicleIU)) return false
    if (!isFocusInput) {
      return !schemeLiteVehicleIU.isValidSync(formData)
    }
    if (formData.vehicleIU?.length > 9 && isDirty) {
      return disabled
    }
    return false
  }, [formData.vehicleIU, isDirty, isFocusInput])

  useEffect(() => {
    reset()
  }, [personalData])

  const onClosedEditModal = () => {
    reset()
    setModalVisible(false)
  }

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }

  const btnUpdateOnPress = async (form) => {
    const isConnected = await checkInternet()
    setNoConnection(!isConnected)
    if (isConnected) {
      let { ...data } = form
      data = {
        ...data,
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
      }
      setFormData({ ...form, ...data })
      reset()
      setModalVisible(false)
      setTimeout(() => {
        onUpdateProfile({ vehicleIU: data?.vehicleIU }, UpdateProfileSectionKey.VEHICLE_DETAILS)
      }, 200)
    }
  }

  const getBackgroundButtonDisable = (isDisabled: boolean) => {
    if (isDisabled) {
      return {
        backgroundColor: color.palette.lightGrey,
      }
    }
  }

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        onClosedEditModal()
      }
    }, [])
  );

  return (
    <View style={styles.container}>
      <Text
        preset="h4"
        tx={"profile.sectionTitle.vehicleDetails"}
        style={styles.sectionTitleStyles}
      />
      <InfoItemView
        isEditValue={true}
        labelTx={"profile.vehicleIU"}
        value={vehicleIUValue}
        valueContainer={styles.valueContainer}
        onPress={() => setModalVisible(true)}
        testID={`${screenName}__${SECTION_NAME}__VehicleIUItemView`}
        accessibilityLabel={`${screenName}__${SECTION_NAME}__VehicleIUItemView`}
      />
      <BottomSheet
        isModalVisible={isModalVisible}
        containerStyle={styles.modalContainer}
        onClosedSheet={onClosedEditModal}
        stopDragCollapse={true}
        onBackPressHandle={onClosedEditModal}
        animationInTiming={100}
        animationOutTiming={100}
      >
        <View style={styles.bottomSheetEditContainer}>
          <View style={styles.headerEditModal}>
            <Text tx={"profile.modalEditTitle.verhicleDetail"} style={styles.editModalTitle} />
            <TouchableOpacity
              onPress={onClosedEditModal}
              style={styles.btnCloseModalStyles}
              testID={`${screenName}__${SECTION_NAME}__CloseModalEdit`}
              accessibilityLabel={`${screenName}__${SECTION_NAME}__CloseModalEdit`}
            >
              <CloseCross width={24} height={24} fill="currentColor" style={closeIconStyles} />
            </TouchableOpacity>
          </View>
          <KeyboardAwareScrollView
            showsVerticalScrollIndicator={false}
            enableResetScrollToCoords={false}
            style={styles.modalContentContainer}
          >
            <InputWrapper
              isInvalid={isValidFrom}
              labelTx="profile.vehicleIU"
              helpText={isValidFrom ? translate("profile.errorVehicleIU") : ""}
            >
              <InputField
                autoCapitalize="none"
                autoCorrect={false}
                keyboardType="numeric"
                maxLength={10}
                placeholderTx="profile.placeholderVehicleIU"
                value={formData.vehicleIU}
                isInvalid={isValidFrom}
                onChangeText={(value) => {
                  isValidVehicleIU(value) && setFieldValue("vehicleIU", value)
                  validateAt("vehicleIU", formData)
                }}
                onBlur={() => {
                  setFocusInput(false)
                }}
                onFocus={() => setFocusInput(true)}
                highlightOnFocused
                testID={`${screenName}__InputVehicleIU`}
                accessibilityLabel={`${screenName}__InputVehicleIU`}
              />
            </InputWrapper>
            <LinearGradient
              style={styles.btnUpdateContainer}
              start={{ x: 0, y: 1 }}
              end={{ x: 1, y: 0 }}
              colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
            >
              <Button
                tx="profile.submit"
                typePreset="primary"
                statePreset={disabled ? "disabled" : "default"}
                onPress={handleSubmit(btnUpdateOnPress)}
                backgroundPreset="light"
                textPreset="buttonLarge"
                sizePreset="large"
                style={getBackgroundButtonDisable(disabled)}
                textStyle={{
                  ...styles.textBtnUpdateStyle,
                  color: !disabled ? color.palette.almostWhiteGrey : color.palette.darkGrey,
                }}
                testID={`${screenName}__${SECTION_NAME}__ButtonSubmit`}
                accessibilityLabel={`${screenName}__${SECTION_NAME}__ButtonSubmit`}
              />
            </LinearGradient>
          </KeyboardAwareScrollView>
          <BottomSheetError
            visible={isNoConnection}
            title={get(errorEHR15, "header") || translate("popupError.somethingWrong")}
            errorMessage={
              get(errorEHR15, "subHeader") || translate("popupError.updateProfileMessage")
            }
            onClose={() => setNoConnection(false)}
            buttonText={get(errorEHR15, "buttonLabel") || translate("common.okay")}
            onButtonPressed={() => setNoConnection(false)}
            icon={iconNoInternet}
            testID={`${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
            accessibilityLabel={`${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
          />
        </View>
      </BottomSheet>
    </View>
  )
}

const styles = StyleSheet.create({
  bottomSheetEditContainer: {
    backgroundColor: color.palette.lightestGrey,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: "95%",
    width: "100%",
  },
  btnCloseModalStyles: {
    position: "absolute",
    right: 0,
  },
  btnUpdateContainer: {
    borderRadius: 60,
    flex: 1,
    height: 44,
    marginBottom: 37,
    marginTop: 24,
  },
  container: {
    backgroundColor: color.palette.lightestGrey,
    paddingBottom: 42,
    paddingHorizontal: 24,
    paddingTop: 50,
    width: "100%",
  },
  editModalTitle: {
    ...presets.subTitleBold,
    color: color.palette.almostBlackGrey,
  },
  headerEditModal: {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 24,
    marginVertical: 21,
  },
  modalContainer: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContentContainer: {
    paddingHorizontal: 24,
  },
  sectionTitleStyles: {
    color: color.palette.almostBlackGrey,
  },
  textBtnUpdateStyle: {
    color: color.palette.almostWhiteGrey,
    fontFamily: typography.bold,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 24,
    textAlign: "center",
  },
  valueContainer: {
    marginTop: 10,
  },
})
export default VehicleDetails
