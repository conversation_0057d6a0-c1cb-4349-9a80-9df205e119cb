import React, { useState, useMemo, useEffect, useRef } from "react"
import { View, TouchableOpacity, Platform, StyleSheet, Dimensions } from "react-native"
import { color, typography } from "app/theme"
import { Text } from "app/elements/text/text"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import {
  CloseCross,
  VerifiedGreen,
  PedingVerify,
  ArrowLeft,
  ErrorOutlined,
  InfoRed,
} from "ichangi-fe/assets/icons"
import { Button } from "app/elements/button/button"
import LinearGradient from "react-native-linear-gradient"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view"
import { InputWrapper } from "app/components/input-wrapper"
import modalStyles from "./modal-edit-styles"
import { InputPhone } from "app/components/input-phone"
import listCountries from "ichangi-fe/assets/data/countries.json"
import { getListContryByPriorityOrder } from "app/utils/screen-helper"
import { translate } from "app/i18n"
import { LoadingModal } from "app/components/loading-modal/loading-modal"
import OTPInputView from "@twotalltotems/react-native-otp-input"
import { presets } from "app/elements/text"
import { AemSelectors } from "app/redux/aemRedux"
import { get, isEmpty } from "lodash"
import { useSelector, useDispatch } from "react-redux"
import ProfileActions, { ProfileSelectors } from "app/redux/profileRedux"
import Toast from "react-native-root-toast"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import NetInfo from "@react-native-community/netinfo"
import { UpdateProfileSectionKey } from "app/utils/constants"
import { useCountTimer } from "./useCountTimer"
import { handleCondition, simpleCondition } from "app/utils"
import { ErrorOverlayNoConnection } from "app/components/error-overlay/error-overlay-no-connection"
// import { env } from "app/config/env-params"

const iconNoInternet = <InfoRed />

const screenWidth = Dimensions.get("screen").width

const closeIconStyles: any = { color: color.palette.lightPurple }

const listOfContry = getListContryByPriorityOrder(listCountries)

const countryCodeOptions = listOfContry.map((item) => ({
  value: item.code,
  label: item.country,
}))

const PhoneNumberVerifyModal = (props) => {
  const {
    resetFormData,
    hasError,
    formData,
    firstError,
    personalData,
    clearErrorAt,
    setFieldValue,
    disabled,
    validateAt,
    screenName,
    SECTION_NAME,
    modalEditMobileNumberVisible,
    setShowModalEditMobileNumber,
  } = props

  const dispatch = useDispatch()

  const errorCommon = useSelector(AemSelectors.getErrorsCommon)
  const [numberOTP, setNumberOTP] = useState("")
  const [stepEditPhoneNumber, setStepEditPhoneNumber] = useState(0)
  const messagesCommon = useSelector(AemSelectors.getMessagesCommon)
  const vToken = useSelector(ProfileSelectors.vToken)
  const editContactDetailKey = useSelector(ProfileSelectors.editContactDetailKey)
  const sendOtpLoading = useSelector(ProfileSelectors.sendOtpLoading)
  const verifyOtpLoading = useSelector(ProfileSelectors.verifyOtpLoading)
  const verifyOtpStatus = useSelector(ProfileSelectors.verifyOtpStatus)
  const sendOtpStatus = useSelector(ProfileSelectors.sendOtpStatus)
  const sendOtpMsgErr = useSelector(ProfileSelectors.sendOtpMsgErr)
  const errorEHR15 = errorCommon?.find((item: any) => item.code === "EHR15")
  const [isNoConnection, setNoConnection] = useState(false)

  const [isPassedCaptcha, setPassedCaptcha] = useState(false)
  const [isCaptchaErrorSend, setCaptchaErrorSend] = useState(false)
  const [isCaptchaErrorResend, setCaptchaErrorResend] = useState(false)
  const [isCaptchaNoConnection, setCaptchaNoConnection] = useState(false)
  const captchaRef = useRef(null)
  const currentAction = useRef<"SEND_OTP" | "RESEND_OTP">("SEND_OTP")
  const siteKey = "6LdGEhEpAAAAAKiuh3265mIDYDdFB8oj4Dbbu7X3"
  const baseUrl = "https://google.com"
  const timeoutRef = useRef(null)
  const otpInputRef = useRef(null)

  const { isCounting, timeLeft, start } = useCountTimer()
  const isErrorOTP = !isEmpty(verifyOtpStatus) && verifyOtpStatus !== "ok"
  const msg71 = messagesCommon?.find((item: any) => item.code === "MSG71")

  const isShowVerifyText = useMemo(() => !personalData?.verifiedMobile, [personalData, formData])

  const isShowVerifyPhoneNumBtn = useMemo(() => {
    if (!personalData?.verifiedMobile) return true
    return personalData?.phoneNum !== formData?.phoneNum
  }, [formData?.phoneNum, personalData?.verifiedMobile])

  const getBackgroundButtonDisable = (isDisabled: boolean) => {
    if (isDisabled) {
      return {
        backgroundColor: color.palette.lightGrey,
      }
    }
  }

  useEffect(() => {
    return clearTiming
  }, [])

  const startTiming = () => {
    clearTiming()
    timeoutRef.current = setTimeout(() => {
      const data = { nativeEvent: { data: "expired" } }
      onMessage(data)
    }, 1000 * 60 * 2)
  }

  const clearTiming = () => {
    clearTimeout(timeoutRef.current)
  }

  const showCaptchaView = () => {
    captchaRef.current?.show()
    startTiming()
  }

  useEffect(() => {
    if (modalEditMobileNumberVisible === false) {
      setPassedCaptcha(false)
      setCaptchaErrorSend(false)
      setCaptchaErrorResend(false)
    }
  }, [modalEditMobileNumberVisible])

  useEffect(() => {
    if (verifyOtpStatus === "ok" && editContactDetailKey === UpdateProfileSectionKey.PHONE_NUMBER) {
      resetFormData()
      setNumberOTP("")
      setShowModalEditMobileNumber(false)
      setStepEditPhoneNumber(0)
      dispatch(
        ProfileActions.updateProfileData({
          phoneNum: formData?.phoneNum,
          countryCode: formData?.countryCode,
          verifiedMobile: true,
        }),
      )
      dispatch(ProfileActions.resetVerifyData())
      Toast.show(
        get(
          messagesCommon?.find((item) => item.code === "MSG74"),
          "title",
        ) || translate("profile.msg74"),
        {
          duration: Toast.durations.SHORT,
          position: -40,
          shadow: false,
          animation: true,
          hideOnPress: true,
          opacity: 1,
          backgroundColor: color.palette.almostBlackGrey,
          textColor: color.palette.whiteGrey,
          textStyle: {
            paddingHorizontal: 3,
            paddingVertical: 4,
            fontFamily: "Lato",
            fontSize: 14,
            lineHeight: 18,
            fontStyle: "normal",
          },
        },
      )
    }
  }, [verifyOtpStatus])

  useEffect(() => {
    if (
      sendOtpStatus === "ok" &&
      editContactDetailKey === UpdateProfileSectionKey.PHONE_NUMBER &&
      vToken
    ) {
      setStepEditPhoneNumber(1)
      start()
      if (Platform.OS === "android") {
        otpInputRef.current?.focusField(
          numberOTP.length === 6 ? numberOTP.length - 1 : numberOTP.length,
        )
      }
      dispatch(ProfileActions.sendOTPReset())
    }
  }, [sendOtpStatus])

  const getPhoneNumContainer = () => {
    return {
      marginBottom: isShowVerifyText ? 16 : 4,
    }
  }

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }
  const btnSendOtpByPhone = async () => {
    const isConnected = await checkInternet()
    setNoConnection(!isConnected)
    if (isConnected) {
      // if (!isPassedCaptcha) {
      //   currentAction.current = "SEND_OTP"
      //   showCaptchaView()
      //   return
      // }
      onSendOtp()
    }
  }

  const onSendOtp = () => {
    clearTiming()
    setPassedCaptcha(false)
    const phoneNumber = "+" + formData?.countryCode + formData?.phoneNum
    dispatch(ProfileActions.sendOTPRequest(phoneNumber, ""))
  }

  const onResendOtp = () => {
    // clear input opt and message
    setNumberOTP("")
    dispatch(ProfileActions.resetVerifyData())
    otpInputRef.current?.focusField(0)
    clearTiming()
    setPassedCaptcha(false)
    const phoneNumber = "+" + formData?.countryCode + formData?.phoneNum
    dispatch(ProfileActions.sendOTPRequest(phoneNumber, ""))
    start()
  }

  const proccessError = (isError: boolean) => {
    if (stepEditPhoneNumber === 0) {
      // send
      setCaptchaErrorSend(isError)
    } else {
      //  resend
      setCaptchaErrorResend(isError)
    }
  }

  const onMessage = async (event) => {
    const { data } = event?.nativeEvent

    const hideCaptcha = () => captchaRef.current?.hide()

    const handleResendOrSend = () => {
      switch (currentAction.current) {
        case "RESEND_OTP":
          onResendOtp()
          break
        case "SEND_OTP":
          onSendOtp()
          break
        default:
          break
      }
    }

    if (!data) {
      hideCaptcha()
      return
    }

    switch (data) {
      case "cancel":
        hideCaptcha()
        break

      case "error":
      case "expired": {
        const isConnected = await checkInternet()
        isConnected ? proccessError(true) : setCaptchaNoConnection(true)
        hideCaptcha()
        break
      }

      default:
        setPassedCaptcha(true)
        proccessError(false)

        if (stepEditPhoneNumber === 1 && Platform.OS === "ios") {
          otpInputRef.current?.focusField(
            numberOTP.length === 6 ? numberOTP.length - 1 : numberOTP.length,
          )
        }

        hideCaptcha()

        setTimeout(handleResendOrSend, Platform.OS === "ios" ? 1500 : 0)
        break
    }
  }

  const onClosedEditModalMobileNumber = () => {
    resetFormData()
    setNumberOTP("")
    setStepEditPhoneNumber(0)
    setShowModalEditMobileNumber(false)
    dispatch(ProfileActions.setEditContactDetailKey(""))
    dispatch(ProfileActions.sendOTPReset())
    dispatch(ProfileActions.resetVerifyData())
  }

  const getResendTextStyles = () => {
    return {
      color: isCounting ? color.palette.darkestGrey : color.palette.lightPurple,
    }
  }

  const onResendPress = () => {
    if (!isCounting) {
      // if (!isPassedCaptcha) {
      //   currentAction.current = "RESEND_OTP"
      //   showCaptchaView()
      //   return
      // }
      onResendOtp()
    }
  }

  const onBackStepEditPhoneNumber = () => {
    setCaptchaErrorResend(false)
    dispatch(ProfileActions.resetVerifyData())
    setNumberOTP("")
    setStepEditPhoneNumber(0)
  }

  const timer = useMemo(() => {
    return isCounting ? Math.floor(timeLeft / 1000) : 0
  }, [timeLeft])

  const renderStatusComponent = (
    sendOtpMsgError,
    isShowVerifyPhoneNumButton,
    verifiedMobile,
    phoneNum,
  ) => {
    if (
      !isEmpty(sendOtpMsgError) ||
      verifiedMobile === undefined ||
      (!verifiedMobile && isEmpty(phoneNum))
    ) {
      return null
    }

    if (personalData?.verifiedMobile) {
      if (!isShowVerifyPhoneNumButton) {
        return (
          <View style={modalStyles.verifyContainer}>
            <VerifiedGreen width={12} height={12} />
            <Text tx={"profile.verified"} style={modalStyles.verifiedTextStyles} />
          </View>
        )
      }
      return null
    }

    return (
      <View style={modalStyles.verifyContainer}>
        <PedingVerify width={12} height={12} />
        <Text tx={"profile.pendingVerify"} style={modalStyles.pendingVerifyTextStyles} />
      </View>
    )
  }

  const isInvalidPhoneNum = () => {
    if (isEmpty(sendOtpMsgErr)) {
      return hasError("phoneNum")
    } else return true
  }

  const phongeNumMsgErr = () => {
    if (isEmpty(sendOtpMsgErr)) {
      return firstError("phoneNum")
    } else return sendOtpMsgErr
  }

  const onReloadData = async () => {
    const isConnected = await checkInternet()
    setCaptchaNoConnection(!isConnected)
  }

  const renderComponentByStep = () => {
    if (isCaptchaNoConnection) {
      return (
        <ErrorOverlayNoConnection
          reload
          header={false}
          hideScreenHeader={true}
          headerBackgroundColor="transparent"
          visible={true}
          testID={`ErrorOverlayNoConnection`}
          onReload={onReloadData}
          storyMode={true}
          noInternetOverlayStyle={styles.noInternetConnection}
        />
      )
    }
    return (
      <>
        {stepEditPhoneNumber === 0 ? (
          <>
            <InputWrapper
              isInvalid={isInvalidPhoneNum()}
              labelTx="profile.phoneNum"
              helpText={phongeNumMsgErr()}
              style={getPhoneNumContainer()}
            >
              <InputPhone
                isInvalid={isInvalidPhoneNum()}
                countryOptions={countryCodeOptions}
                phoneNumber={formData.phoneNum}
                countryCode={formData.countryCode}
                maxLength={15}
                onPhoneChange={(value) => {
                  clearErrorAt("phoneNum")
                  setFieldValue("phoneNum", value)
                }}
                onCountryCodeChange={(value) => setFieldValue("countryCode", value)}
                onBlurInput={() => validateAt("phoneNum", formData)}
                onFocus={() => dispatch(ProfileActions.sendOTPReset())}
                highlightOnFocused
              />
            </InputWrapper>
            <>
              {renderStatusComponent(
                sendOtpMsgErr,
                isShowVerifyPhoneNumBtn,
                personalData?.verifiedMobile,
                personalData?.phoneNum,
              )}
              {simpleCondition({
                condition: isCaptchaErrorSend,
                ifValue: (
                  <Text style={styles.captchaError}>
                    {translate("profile.captchaError1")}
                    <Text style={styles.captchaErrorDescription}>
                      {translate("profile.captchaError2")}
                    </Text>
                  </Text>
                ),
                elseValue: <></>,
              })}

              {isShowVerifyPhoneNumBtn && (
                <LinearGradient
                  style={modalStyles.btnUpdateContainer}
                  start={{ x: 0, y: 1 }}
                  end={{ x: 1, y: 0 }}
                  colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
                >
                  <Button
                    tx="profile.verifyViaOTP"
                    typePreset="primary"
                    statePreset={simpleCondition({
                      condition: disabled,
                      ifValue: "disabled",
                      elseValue: "default",
                    })}
                    onPress={btnSendOtpByPhone}
                    backgroundPreset="light"
                    textPreset="buttonLarge"
                    sizePreset="large"
                    style={getBackgroundButtonDisable(disabled)}
                    textStyle={{
                      ...modalStyles.textBtnUpdateStyle,
                      color: !disabled ? color.palette.almostWhiteGrey : color.palette.darkGrey,
                    }}
                    testID={`${screenName}__${SECTION_NAME}__ButtonVerifyPhoneNum`}
                    accessibilityLabel={`${screenName}__${SECTION_NAME}__ButtonVerifyPhoneNum`}
                  />
                </LinearGradient>
              )}
            </>
          </>
        ) : (
          <>
            <Text style={modalStyles.mobileNumberLable}>
              {translate("profile.modalEditTitle.mobileNumberLable2")}
              <Text
                text={
                  "+" +
                  formData?.countryCode +
                  " ****" +
                  formData?.phoneNum.substring(formData?.phoneNum.length - 4)
                }
                style={{
                  fontWeight: Platform.select({ ios: "700", android: "normal" }),
                  fontFamily: typography.bold,
                }}
              />
            </Text>
            <View style={styles.otpInputNumberContainer}>
              <OTPInputView
                ref={otpInputRef}
                autoFocusOnLoad
                style={styles.wrapInputNumber}
                pinCount={6}
                code={numberOTP?.toString()}
                onCodeChanged={(code) => {
                  setNumberOTP(code)
                }}
                onCodeFilled={(code) => onCodeFilled(code)}
                codeInputFieldStyle={{
                  ...styles.inputNumber,
                  ...(isErrorOTP ? styles.errorInputStyle : {}),
                }}
                codeInputHighlightStyle={{
                  ...styles.borderStyleHighLighted,
                  ...(isErrorOTP ? styles.errorInputStyle : {}),
                }}
                selectionColor={color.palette.lightPurple}
              />
              {isErrorOTP && (
                <View style={modalStyles.errorContainerStyle}>
                  <ErrorOutlined width={20} height={20} style={modalStyles.errorIconStyle} />
                  <Text
                    style={styles.otpErrorMsgStyles}
                    text={get(msg71, "title")}
                    numberOfLines={1}
                  />
                </View>
              )}
            </View>
            <Text style={styles.resendOtpView}>
              <Text onPress={onResendPress} style={getResendTextStyles()} preset="bold">
                Resend OTP
              </Text>
              {handleCondition(
                timer,
                <Text style={styles.timerTextStyles}>{` in ${timer} secs`}</Text>,
                null,
              )}
            </Text>
            <TouchableOpacity
              style={styles.editMobileNumberButton}
              onPress={onBackStepEditPhoneNumber}
            >
              <Text tx={"profile.editMobileNumber"} style={styles.editMobileNumberLink} />
            </TouchableOpacity>
            {simpleCondition({
              condition: isCaptchaErrorResend,
              ifValue: (
                <Text style={styles.captchaError}>
                  {translate("profile.captchaError1")}
                  <Text style={styles.captchaErrorDescription}>
                    {translate("profile.captchaError2")}
                  </Text>
                </Text>
              ),
              elseValue: <></>,
            })}
          </>
        )}
      </>
    )
  }

  const onCodeFilled = (otpCode: string) => {
    const input = {
      otp: otpCode,
      phoneNumber: "+" + formData?.countryCode + formData?.phoneNum,
      vToken: vToken,
    }
    dispatch(ProfileActions.verifyOTPRequest(input))
  }

  return (
    <BottomSheet
      isModalVisible={modalEditMobileNumberVisible}
      containerStyle={modalStyles.modalContainer}
      onClosedSheet={onClosedEditModalMobileNumber}
      stopDragCollapse={true}
      onBackPressHandle={onClosedEditModalMobileNumber}
      animationInTiming={100}
      animationOutTiming={100}
    >
      <View style={modalStyles.bottomSheetEditContainer}>
        <View style={modalStyles.headerEditModal}>
          {stepEditPhoneNumber === 1 && (
            <TouchableOpacity
              onPress={onBackStepEditPhoneNumber}
              style={modalStyles.btnBackModalStyles}
              testID={`${screenName}__${SECTION_NAME}__BackStepModalEdit`}
              accessibilityLabel={`${screenName}__${SECTION_NAME}__BackStepModalEdit`}
            >
              <ArrowLeft width="24" height="24" />
            </TouchableOpacity>
          )}
          <Text
            tx={
              stepEditPhoneNumber === 0
                ? "profile.modalEditTitle.editMobileNumber"
                : "profile.modalEditTitle.verifyMobileNumber"
            }
            style={modalStyles.editModalTitle}
          />
          <TouchableOpacity
            onPress={onClosedEditModalMobileNumber}
            style={modalStyles.btnCloseModalStyles}
            testID={`${screenName}__${SECTION_NAME}__CloseModalEdit`}
            accessibilityLabel={`${screenName}__${SECTION_NAME}__CloseModalEdit`}
          >
            <CloseCross width={24} height={24} fill="currentColor" style={closeIconStyles} />
          </TouchableOpacity>
        </View>
        <KeyboardAwareScrollView
          showsVerticalScrollIndicator={false}
          enableResetScrollToCoords={false}
          style={modalStyles.modalContentContainer}
        >
          {renderComponentByStep()}
        </KeyboardAwareScrollView>

        <LoadingModal visible={verifyOtpLoading || sendOtpLoading} />
      </View>
      <BottomSheetError
        visible={isNoConnection}
        title={get(errorEHR15, "header") || translate("popupError.somethingWrong")}
        errorMessage={get(errorEHR15, "subHeader") || translate("popupError.updateProfileMessage")}
        onClose={() => setNoConnection(false)}
        buttonText={get(errorEHR15, "buttonLabel") || translate("common.okay")}
        onButtonPressed={() => setNoConnection(false)}
        icon={iconNoInternet}
        testID={`${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
        accessibilityLabel={`${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
      />
    </BottomSheet>
  )
}

const styles = StyleSheet.create({
  borderStyleHighLighted: {
    borderColor: color.palette.lightPurple,
  },
  captchaError: {
    ...presets.caption1Bold,
    color: color.palette.red,
    marginTop: 36,
  },
  captchaErrorDescription: {
    ...presets.caption1Regular,
    color: color.palette.red,
  },
  editMobileNumberButton: {
    marginTop: 16,
  },
  editMobileNumberLink: {
    ...presets.textLink,
    fontSize: 14,
    lineHeight: 18,
  },
  errorInputStyle: {
    borderColor: color.palette.baseRed,
  },
  inputNumber: {
    ...presets.h4,
    backgroundColor: color.palette.whiteGrey,
    borderColor: color.palette.lightGrey,
    borderRadius: 8,
    color: color.palette.almostBlackGrey,
    height: 48,
    textAlign: "center",
    width: 40,
  },
  noInternetConnection: {
    height: "80%",
    justifyContent: "flex-start",
    paddingVertical: 30,
    width: "100%",
  },
  otpErrorMsgStyles: {
    color: color.palette.baseRed,
    marginTop: 4,
  },
  otpInputNumberContainer: {
    marginTop: 0,
  },
  resendOtpView: {
    ...presets.caption1Bold,
    marginTop: 24,
    textAlign: "left",
  },
  timerTextStyles: {
    ...presets.caption1Regular,
  },
  wrapInputNumber: {
    ...presets.h4,
    backgroundColor: color.palette.lightestGrey,
    borderRadius: 8,
    color: color.palette.lightPurple,
    height: 48,
    textAlign: "center",
    width: screenWidth * 0.75,
  },
})

export default PhoneNumberVerifyModal
