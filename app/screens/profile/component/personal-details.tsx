/* eslint-disable no-useless-escape */
import React, { useMemo, useState, useEffect, useContext } from "react"
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
  StatusBar,
} from "react-native"
import { color, typography } from "app/theme"
import { Text } from "app/elements/text/text"
import { presets } from "app/elements/text"
import { Edit, CloseCross, DownArrowGrey, InfoRed } from "ichangi-fe/assets/icons"
import { InfoItemView } from "./info-item"
import { translate } from "app/i18n"
import { DateFormats, toDate } from "app/utils/date-time/date-time"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import LinearGradient from "react-native-linear-gradient"
import { Button } from "app/elements/button/button"
import { InputWrapper } from "app/components/input-wrapper"
import { SelectPicker } from "app/elements/select-picker"
import { useForm } from "app/utils/form-hook"
import { PROFILE_SCREEN_CONTEXT, schemeLitePersonalDetail } from "../profile-screen.schema"
import { InputField } from "app/components/input-field"
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view"
import moment from "moment"
import { DatePicker } from "app/elements/date-picker"
import { UpdateProfileSectionKey } from "app/utils/constants"
import { isEmpty, get } from "lodash"
import { BottomSheetError } from "app/components/bottom-sheet-error"
import NetInfo from "@react-native-community/netinfo"
import { useSelector } from "react-redux"
import { AemSelectors } from "app/redux/aemRedux"
import { useFocusEffect } from "@react-navigation/native"
const iconNoInternet = <InfoRed />

const SECTION_NAME = "PersonalDetails"

const isValidName = (name?: string) => {
  return name ? /^[^<>\[\]\{\};:|\\\"!?@#$%*^+=_`~\t\n\r1-9]{1,50}$/.test(name) : true
}

const trimSpace = (str?: string) => {
  return str ? str.replace(/\s\s+/g, " ") : ""
}

const genderOptions = [
  { value: "MALE", label: "Male" },
  { value: "FEMALE", label: "Female" },
  { value: "U", label: "Prefer not to say" },
]

const PersonalDetails = (props) => {
  const { personalData, countryOptions, nationOptions, onUpdateProfile, screenName } = props
  const [isModalVisible, setModalVisible] = useState(false)
  const PROFILE_SCREEN_CONTEXT_HANDLER = useContext(PROFILE_SCREEN_CONTEXT).Handlers

  const errorCommon = useSelector(AemSelectors.getErrorsCommon)
  const errorEHR15 = errorCommon?.find((item: any) => item.code === "EHR15")
  const [isNoConnection, setNoConnection] = useState(false)

  const closeIconStyles: any = { color: color.palette.lightPurple }

  const normalizeFormData = (profile) => ({
    firstName: profile?.firstName || "",
    lastName: profile?.lastName || "",
    phoneNum: profile?.phoneNum || "",
    dob: profile?.dob || "",
    gender: profile?.gender || "",
    email: profile?.email || "",
    residentialCountry: profile?.residentialCountry || "SG",
    postalCode: profile?.postalCode || "",
    countryCode: profile?.countryCode || "65",
    address: profile?.address || "",
    nationality: profile?.nationality || "",
    vehicleIU: profile?.vehicleIU || "",
    isStaff: !!(profile?.staffPassExpiry || profile?.staffPassNumber),
    staffPassExpiry: profile?.staffPassExpiry || "",
    staffPassNumber: profile?.staffPassNumber || "",
  })

  const {
    formData,
    hasError,
    firstError,
    setFieldValue,
    validateAt,
    handleSubmit,
    setFormData,
    isDirty,
    reset,
  } = useForm({
    form: normalizeFormData(personalData),
    schema: PROFILE_SCREEN_CONTEXT_HANDLER.schema,
  })

  const disabled = useMemo(() => {
    if (isDirty) {
      try {
        return !schemeLitePersonalDetail.isValidSync(formData)
      } catch (error) {
        return true
      }
    }

    return true
  }, [formData, isDirty])

  useEffect(() => {
    reset()
  }, [personalData])

  const residentialCountry = useMemo(() => {
    const option = countryOptions?.find((item) => item.value === personalData?.residentialCountry)

    if (option) {
      return option.label || option.value
    }

    return ""
  }, [countryOptions, personalData])

  const nationalityValue = useMemo(() => {
    const option = nationOptions?.find((item) => item.value === personalData?.nationality)
    if (option) {
      return option.label || option.value
    }
    return translate("common.none")
  }, [nationOptions, personalData])

  const genderValue = useMemo(() => {
    const option = genderOptions?.find((item) => item.value === personalData?.gender)
    if (option) {
      return option.label || option.value
    }
    return translate("profile.genderNone")
  }, [genderOptions, personalData])

  const onClosedEditModal = () => {
    reset()
    setModalVisible(false)
  }

  const checkInternet = async () => {
    const { isConnected } = await NetInfo.fetch()
    return isConnected
  }

  const btnUpdateOnPress = async (form) => {
    const isConnected = await checkInternet()
    setNoConnection(!isConnected)
    if (isConnected) {
      let { ...data } = form
      data = {
        ...data,
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
      }
      setFormData({ ...form, ...data })
      const dataNeedUpdate = {
        firstName: data?.firstName.trim(),
        lastName: data?.lastName.trim(),
        dob: data?.dob,
        residentialCountry: data?.residentialCountry,
        nationality: data?.nationality,
        gender: data?.gender,
      }
      reset()
      setModalVisible(false)
      setTimeout(() => {
        onUpdateProfile(dataNeedUpdate, UpdateProfileSectionKey.PERSONAL_DETAILS)
      }, 200)
    }
  }

  const getSpacingCharactor = () => {
    return !isEmpty(personalData?.firstName) && !isEmpty(personalData?.lastName) ? " " : ""
  }

  const getBackgroundButtonDisable = (isDisabled: boolean) => {
    if (isDisabled) {
      return {
        backgroundColor: color.palette.lightGrey,
      }
    }
  }

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        onClosedEditModal()
      }
    }, []),
  )

  return (
    <>
      <View style={styles.container}>
        <View style={styles.sectionTitleView}>
          <Text
            preset="h4"
            tx={"profile.sectionTitle.personalDetails"}
            style={styles.sectionTitleStyles}
          />
          <TouchableOpacity
            onPress={() => setModalVisible(true)}
            testID={`${screenName}__${SECTION_NAME}__btnEdit`}
            accessibilityLabel={`${screenName}__${SECTION_NAME}__btnEdit`}
          >
            <Edit />
          </TouchableOpacity>
        </View>
        <InfoItemView
          labelTx={"profile.name"}
          value={personalData?.firstName + getSpacingCharactor() + personalData?.lastName}
          testID={`${screenName}__${SECTION_NAME}__NameItemView`}
          accessibilityLabel={`${screenName}__${SECTION_NAME}__NameItemView`}
        />
        <InfoItemView
          labelTx={"profile.dob"}
          value={toDate(formData.dob, DateFormats.DayMonthYear)}
          testID={`${screenName}__${SECTION_NAME}__DateOfBirthItemView`}
          accessibilityLabel={`${screenName}__${SECTION_NAME}__DateOfBirthItemView`}
        />
        <InfoItemView
          labelTx={"profile.residentialCountry"}
          value={residentialCountry}
          numberOfLinesValue={1}
          testID={`${screenName}__${SECTION_NAME}__ResidentialCountryItemView`}
          accessibilityLabel={`${screenName}__${SECTION_NAME}__ResidentialCountryItemView`}
        />
        <InfoItemView labelTx={"profile.nationality"} value={nationalityValue} />
        <InfoItemView
          labelTx={"profile.gender"}
          value={genderValue}
          testID={`${screenName}__${SECTION_NAME}__GenderItemView`}
          accessibilityLabel={`${screenName}__${SECTION_NAME}__GenderItemView`}
        />
      </View>
      <BottomSheet
        isModalVisible={isModalVisible}
        containerStyle={styles.modalContainer}
        onClosedSheet={onClosedEditModal}
        stopDragCollapse={true}
        onBackPressHandle={onClosedEditModal}
        animationInTiming={100}
        animationOutTiming={100}
      >
        <View style={styles.bottomSheetEditContainer}>
          <View style={styles.headerEditModal}>
            <Text tx={"profile.modalEditTitle.personalDetail"} style={styles.editModalTitle} />
            <TouchableOpacity
              onPress={onClosedEditModal}
              style={styles.btnCloseModalStyles}
              testID={`${screenName}__${SECTION_NAME}__CloseModalEdit`}
              accessibilityLabel={`${screenName}__${SECTION_NAME}__CloseModalEdit`}
            >
              <CloseCross width={24} height={24} fill="currentColor" style={closeIconStyles} />
            </TouchableOpacity>
          </View>
          <KeyboardAwareScrollView
            showsVerticalScrollIndicator={false}
            enableResetScrollToCoords={false}
            style={styles.modalContentContainer}
          >
            <View style={styles.nameFieldEditView}>
              <InputWrapper
                isInvalid={hasError("firstName")}
                labelTx="profile.firstName"
                helpText={firstError("firstName")}
                style={styles.firstNameView}
                numberOfLinesError={3}
              >
                <InputField
                  autoCapitalize="none"
                  autoCorrect={false}
                  maxLength={50}
                  value={formData.firstName}
                  isInvalid={hasError("firstName")}
                  onChangeText={(value) => {
                    isValidName(value) && setFieldValue("firstName", trimSpace(value))
                  }}
                  onBlur={() => validateAt("firstName", formData)}
                  highlightOnFocused
                  testID={`${screenName}__InputFirstName`}
                  accessibilityLabel={`${screenName}__InputFirstName`}
                />
              </InputWrapper>
              <InputWrapper
                isInvalid={hasError("lastName")}
                labelTx="profile.lastName"
                helpText={firstError("lastName")}
                style={styles.lastNameView}
                numberOfLinesError={3}
              >
                <InputField
                  autoCapitalize="none"
                  autoCorrect={false}
                  maxLength={50}
                  value={formData.lastName}
                  isInvalid={hasError("lastName")}
                  onChangeText={(value) => {
                    isValidName(value) && setFieldValue("lastName", trimSpace(value))
                  }}
                  onBlur={() => validateAt("lastName", formData)}
                  highlightOnFocused
                  testID={`${screenName}__InputLastName`}
                  accessibilityLabel={`${screenName}__InputLastName`}
                />
              </InputWrapper>
            </View>
            <DatePicker
              disabled
              textTx={"profile.dob"}
              isTouched={false}
              errorMessage={null}
              value={moment(formData.dob, DateFormats.YearMonthDay)}
              displayFormat={DateFormats.DayMonthYearWithSlash}
              testID={`${screenName}__DatePickerDOB`}
              accessibilityLabel={`${screenName}__DatePickerDOB`}
            />
            <Text tx={"profile.dobSuggestionToUpdate"} style={styles.dobSuggestionToUpdate} />

            <InputWrapper
              isInvalid={hasError("residentialCountry")}
              labelTx="profile.residentialCountry"
              helpText={firstError("residentialCountry")}
              style={styles.residentialCountryView}
              numberOfLinesError={3}
            >
              <SelectPicker
                options={countryOptions}
                isInvalid={hasError("residentialCountry")}
                value={formData.residentialCountry}
                onChangeValue={(value) => setFieldValue("residentialCountry", value)}
                onBlur={() => validateAt("residentialCountry", formData)}
                highlightOnFocused
                icon={DownArrowGrey}
                testID={`${screenName}__ResidentialCountry__SelectPicker`}
                accessibilityLabel={`${screenName}__ResidentialCountry__SelectPicker`}
              />
            </InputWrapper>
            <InputWrapper
              isInvalid={hasError("nationality")}
              labelTx="profile.nationality"
              helpText={firstError("nationality")}
              style={styles.nationalityView}
              numberOfLinesError={3}
            >
              <SelectPicker
                options={nationOptions}
                isInvalid={hasError("nationality")}
                value={formData.nationality}
                onChangeValue={(value) => setFieldValue("nationality", value)}
                onBlur={() => validateAt("nationality", formData)}
                highlightOnFocused
                icon={DownArrowGrey}
                testID={`${screenName}__Nationality__SelectPicker`}
                accessibilityLabel={`${screenName}__Nationality__SelectPicker`}
              />
            </InputWrapper>
            <InputWrapper
              isInvalid={hasError("gender")}
              labelTx="profile.gender"
              helpText={firstError("gender")}
              style={styles.nationalityView}
              numberOfLinesError={3}
            >
              <SelectPicker
                options={genderOptions}
                isInvalid={hasError("gender")}
                value={formData.gender}
                onChangeValue={(value) => setFieldValue("gender", value)}
                onBlur={() => validateAt("gender", formData)}
                highlightOnFocused
                icon={DownArrowGrey}
                testID={`${screenName}__Gender__SelectPicker`}
                accessibilityLabel={`${screenName}__Gender__SelectPicker`}
              />
            </InputWrapper>
            <LinearGradient
              style={styles.btnUpdateContainer}
              start={{ x: 0, y: 1 }}
              end={{ x: 1, y: 0 }}
              colors={[color.palette.gradientColor1Start, color.palette.gradientColor1End]}
            >
              <Button
                tx="profile.submit"
                typePreset="primary"
                statePreset={disabled ? "disabled" : "default"}
                onPress={handleSubmit(btnUpdateOnPress)}
                backgroundPreset="light"
                textPreset="buttonLarge"
                sizePreset="large"
                style={getBackgroundButtonDisable(disabled)}
                textStyle={{
                  ...styles.textBtnUpdateStyle,
                  color: !disabled ? color.palette.almostWhiteGrey : color.palette.darkGrey,
                }}
                testID={`${screenName}__${SECTION_NAME}__ButtonSubmit`}
                accessibilityLabel={`${screenName}__${SECTION_NAME}__ButtonSubmit`}
              />
            </LinearGradient>
          </KeyboardAwareScrollView>
        </View>
        <BottomSheetError
          visible={isNoConnection}
          title={get(errorEHR15, "header") || translate("popupError.somethingWrong")}
          errorMessage={
            get(errorEHR15, "subHeader") || translate("popupError.updateProfileMessage")
          }
          icon={iconNoInternet}
          onClose={() => setNoConnection(false)}
          buttonText={get(errorEHR15, "buttonLabel") || translate("common.okay")}
          onButtonPressed={() => setNoConnection(false)}
          testID={`${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
          accessibilityLabel={`${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`}
        />
      </BottomSheet>
    </>
  )
}

const styles = StyleSheet.create({
  bottomSheetEditContainer: {
    backgroundColor: color.palette.lightestGrey,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: "95%",
    width: "100%",
  },
  btnCloseModalStyles: {
    position: "absolute",
    right: 0,
  },
  btnUpdateContainer: {
    borderRadius: 60,
    flex: 1,
    height: 44,
    marginBottom: 37,
    marginTop: 24,
  },
  container: {
    backgroundColor: color.palette.lightestGrey,
    paddingBottom: 42,
    paddingHorizontal: 24,
    paddingTop: 40,
    width: "100%",
  },
  dobSuggestionToUpdate: {
    ...presets.caption1Italic,
    color: color.palette.darkGrey,
    fontFamily: typography.regular,
    marginTop: 5,
  },
  editModalTitle: {
    ...presets.subTitleBold,
    color: color.palette.almostBlackGrey,
  },
  firstNameView: {
    flex: 1,
    marginRight: 7.5,
  },
  headerEditModal: {
    flexDirection: "row",
    justifyContent: "center",
    marginHorizontal: 24,
    marginVertical: 21,
  },
  lastNameView: {
    flex: 1,
    marginLeft: 7.5,
  },
  modalContainer: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContentContainer: {
    paddingHorizontal: 24,
  },
  nameFieldEditView: {
    flexDirection: "row",
    marginTop: 24,
  },
  nationalityView: {
    marginTop: 16,
  },
  residentialCountryView: {
    marginTop: 32,
  },
  sectionTitleStyles: {
    color: color.palette.almostBlackGrey,
    flex: 1,
  },
  sectionTitleView: {
    flexDirection: "row",
  },
  textBtnUpdateStyle: {
    color: color.palette.almostWhiteGrey,
    fontFamily: typography.bold,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 24,
    textAlign: "center",
  },
})
export default PersonalDetails
