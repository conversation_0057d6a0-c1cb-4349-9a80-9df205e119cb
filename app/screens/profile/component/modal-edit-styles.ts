import { StyleSheet, Dimensions, StatusBar, Platform } from "react-native"
import { color, typography } from "app/theme"
import { presets } from "app/elements/text"

export const styles = StyleSheet.create({
  bottomSheetEditContainer: {
    backgroundColor: color.palette.lightestGrey,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: "95%",
    width: "100%",
  },
  btnBackModalStyles: {
    left: 0,
    position: "absolute",
  },
  btnCloseModalStyles: {
    position: "absolute",
    right: 0,
  },
  btnUpdateContainer: {
    borderRadius: 60,
    flex: 1,
    height: 44,
    marginBottom: 37,
    marginTop: 24,
  },
  editModalTitle: {
    ...presets.subTitleBold,
    color: color.palette.almostBlackGrey,
  },
  errorContainerStyle: {
    alignItems: "center",
    display: "flex",
    flexDirection: "row",
  },
  errorIconStyle: {
    marginRight: 5,
    marginTop: 5,
  },
  headerEditModal: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 45,
    marginHorizontal: 24,
    marginTop: 21,
  },
  mobileNumberLable: {
    ...presets.bodyTextBlackRegular,
    color: color.palette.almostBlackGrey,
    marginBottom: 24,
  },
  modalContainer: {
    justifyContent: "flex-end",
    margin: 0,
  },
  modalContentContainer: {
    paddingHorizontal: 24,
  },
  pendingVerifyTextStyles: {
    color: color.palette.orangeDark,
    fontFamily: typography.regular,
    fontSize: 14,
    fontWeight: "400",
    letterSpacing: 0,
    lineHeight: 18,
    marginLeft: 4,
    textAlign: "left",
  },
  textBtnUpdateStyle: {
    color: color.palette.almostWhiteGrey,
    fontFamily: typography.bold,
    fontSize: 16,
    fontWeight: Platform.select({ ios: "700", android: "normal" }),
    lineHeight: 24,
    textAlign: "center",
  },
  verifiedTextStyles: {
    color: color.palette.basegreen,
    fontFamily: typography.regular,
    fontSize: 14,
    fontWeight: "400",
    letterSpacing: 0,
    lineHeight: 18,
    marginLeft: 4,
    textAlign: "left",
  },
  verifyContainer: {
    alignItems: "center",
    flexDirection: "row",
    width: "100%",
  },
})

export default styles
