import React, { useEffect, useRef, useState, useMemo, useContext } from "react"
import { StatusBar, View, TouchableOpacity } from "react-native"
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs"
import { TopTabNavBar } from "app/navigators/navigation-utilities"
import { styles } from "./notification-styles"
import { translate } from "app/i18n"
import { Setting, CrossBlue, Delete, Info, ArrowLeftGray, SettingFill, DeleteFill } from "ichangi-fe/assets/icons"
import { NavigationConstants, NOTIFICATION_TYPES } from "app/utils/constants"
import { StackActions, useNavigation } from "@react-navigation/native"

import { HeaderComponent } from "./header-component"
import { HeaderComponentV2 } from './header-componentV2'
import { AllNotificationScreen } from "./tab-screen/all-screen"
import { FlightNotificationScreen } from "./tab-screen/flights-screen"
import { AdvisoriesNotificationScreen } from "./tab-screen/advisories-screen"
import { EventAndPromotionNotificationScreen } from "./tab-screen/events-promotion-screen"
import { EventAndPerkNotificationScreen } from "./tab-screen/events-perks-screen"
import { useDispatch, useSelector } from "react-redux"
import ProfileActions from "app/redux/profileRedux"
import { commonTabStyles } from "./tab-screen/styles.styles"
import { trackAction, AdobeTagName } from "app/services/adobe"
import { updateBadgeCount } from "app/services/firebase"
import NotificationAction, { NotificationSelectors } from "app/redux/notificationRedux"
import { isEmpty } from "lodash"
import BottomSheet from "app/components/bottom-sheet/bottom-sheet"
import * as errorStyles from "app/components/bottom-sheet-error/bottom-sheet-error.styles"
import { Text } from "app/elements/text/text"
import LinearGradient from "react-native-linear-gradient"
import { Button } from "app/elements/button/button"
import { color } from "app/theme"
import { LoadingOverlay } from "app/components/loading-modal"
import CartToastRemoveNotification from "./tab-screen/cart-toast-remove-notication"
import { FeedBackToast, FeedBackToastType, DURATION } from "app/components/feedback-toast"
import NetInfo from "@react-native-community/netinfo"
import Braze from "@braze/react-native-sdk"
import DeleteToastRef from "./toast/delete-toast-ref"
import { getViewerUID, trackActionNewFormat } from "app/utils/screen-helper"
import TickerBand from "app/components/ticker-band/ticker-band"
import {
  MaintananceTickerBandType,
  useTickerbandMaintanance,
} from "app/hooks/useTickerbandMaintanence"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { NOTIFICATION_TAB_SCREENS } from "./constants"
import DeviceInfo from "react-native-device-info"
import { isFlagOnCondition } from "app/services/firebase/remote-config"
import { NotificationInboxV2 } from './notification-inbox-v2';
import { AccountContext } from "app/services/context/account"

const SCREEN_NAME = "NotificationScreen"
const Tab = createMaterialTopTabNavigator()

const VirtualView = () => {
  return <View style={commonTabStyles.container} />
}

export const sendAATrackingNotification = async (type, value, shouldIncludeUID?: boolean) => {
  if (value) {
    let valueToSend = `${type} | ${value}`
    if (shouldIncludeUID) {
      const UID = await getViewerUID({ shouldReturnNull: true })
      valueToSend = `${type} | ${value} | ${UID}`
    }
    trackAction(AdobeTagName.CAppNotificationLanding, {
      [AdobeTagName.CAppNotificationLanding]: valueToSend,
    })
  }
}
export const NotificationValueForAA = {
  flight: "Flights",
  advisories: "Advisories",
  appUpdate: "App Update",
  playpass: "Playpass",
  perks: "Perks",
  eventsAndPerks: "Events & Perks",
  appStore: "App Store",
  playStore: "Play Store",
  credits: "Credits",
  personalisedBaggageExperience: "Personalised Baggage Experience",
  eventsAndPromotion: "Events and Promotions",
}

export const NotificationScreen = () => {
  const navigation = useNavigation()
  const toastRef = useRef(null)
  const toastDeleteSingleNotificationRef = useRef(null)
  const toastErrorRef = useRef(null)
  const [isModalConfirmDeleteAllVisible, setModalConfirmDeleteAllVisible] = useState(false)
  const deleteNotificationLoading = useSelector(NotificationSelectors.deleteNotificationLoading)
  const deleteNotificationResult = useSelector(NotificationSelectors.deleteNotificationResult)
  const deleteSingleNotificationResult = useSelector(
    NotificationSelectors.deleteSingleNotificationResult,
  )
  const eventAndPromotionNotificationPayload = useSelector(
    NotificationSelectors.eventAndPromotionNotificationPayload,
  )
  const enableNotificationInboxV2 = isFlagOnCondition(useContext(AccountContext)?.notificationInboxV2)
  const tabIndexRef = useRef(1)
  const dispatch = useDispatch()
  const insets = useSafeAreaInsets()
  const [maintananceTickerBandType, setMaintananceTickerBandType] = useState(
    MaintananceTickerBandType.NOTIFICATION_TAB_ALL,
  )
  const settingOnPress = async () => {
    const UID = await getViewerUID({ shouldReturnNull: true })
    trackAction(AdobeTagName.CAppNotificationCenter, {
      [AdobeTagName.CAppNotificationCenter]: `Notification Settings | ${UID}`,
    })

    const navigationState = navigation?.getState?.()
    const bottomNavigationItem = navigationState?.routes?.find?.(i => i?.name === NavigationConstants.bottomNavigation)
    const isNavigatedToAccountNavigator = !!bottomNavigationItem?.state?.history?.find?.(
      (i: any) => i?.key?.split?.("-")?.[0] === NavigationConstants.account,
    )

    if (isNavigatedToAccountNavigator) {
      navigation?.navigate?.(NavigationConstants.bottomNavigation, {
        screen: NavigationConstants.account,
        params: {
          screen: NavigationConstants.settingNotificationsScreen,
          params: { isFromInbox: true },
        },
      })
    } else {
      navigation?.navigate?.(NavigationConstants.bottomNavigation, {
        screen: NavigationConstants.account,
      })
      setTimeout(() => {
        navigation?.navigate?.(NavigationConstants.bottomNavigation, {
          screen: NavigationConstants.account,
          params: {
            screen: NavigationConstants.settingNotificationsScreen,
            params: { isFromInbox: true },
          },
        })
      }, 10)
    }
  }

  const messageDeleteSingle = translate("notification.deleteSuccessful")
  const messageDeleteAll = translate("notification.deleteAllSuccessful")
  const errorDelete = translate("notification.deleteErrorToast")

  const getTabNameFocused = (tabIndex: number) => {
    switch (tabIndex) {
      case 1:
        return translate("notificationTabName.all")
      case 2:
        return translate("notificationTabName.flights")
      case 3:
        return translate("notificationTabName.advisores")
      case 4:
        return translate("notificationTabName.eventAndPerk")
      case 5:
        return translate("notificationTabName.eventAndPromotion")
      default:
        return ""
    }
  }

  const deleteOnPress = () => {
    const contextData = {
      deleteAll: "Delete All",
      tabNameFocus: getTabNameFocused(tabIndexRef?.current),
    }
    trackActionNewFormat(AdobeTagName.CAppNotificationCenter, contextData, true)
    setModalConfirmDeleteAllVisible(true)
  }

  const arrRightIcon: RightIconOjbect[] = [
    {
      Icon: enableNotificationInboxV2 ? SettingFill : Setting,
      onPress: settingOnPress,
    },
    {
      Icon: enableNotificationInboxV2 ? DeleteFill : Delete,
      onPress: deleteOnPress,
    },
  ]

  const onPressHeaderGoBackButton = () => {
    const deviceId = DeviceInfo.getUniqueIdSync()
    dispatch(NotificationAction.notificationsCountRequest(deviceId))
    const popToAction = StackActions.popTo(NavigationConstants.bottomNavigation, {
      screen: NavigationConstants.account,
      params: { screen: NavigationConstants.forYouScreen },
    })
    navigation.dispatch(popToAction)
  }

  useEffect(() => {
    if (!enableNotificationInboxV2) {
      dispatch(ProfileActions.settingNotificationRequest())
    }
    updateBadgeCount(0)
    DeleteToastRef.setDeleteSingleToastRef(toastDeleteSingleNotificationRef)
    DeleteToastRef.setErrorToastRef(toastErrorRef)
    return () => {
      dispatch(NotificationAction.resetResultDeleteNotification())
      dispatch(NotificationAction.resetResultDeleteSingleNotification())
    }
  }, [])

  const onCloseConfirmDeleteAllModal = () => {
    setModalConfirmDeleteAllVisible(false)
  }

  const getCategoryName = useMemo(() => {
    switch (tabIndexRef?.current) {
      case 1:
        return ""
      case 2:
        return " [Flights]"
      case 3:
        return " [Advisories]"
      case 4:
        return " [Events & Perks]"
      case 5:
        return " [Events & Promotions]"
      default:
        return ""
    }
  }, [tabIndexRef?.current])

  const onDeleteAllNotifications = async () => {
    let category = ""
    switch (tabIndexRef?.current) {
      case 1:
        // all
        category = NOTIFICATION_TYPES.ALL
        break
      case 2:
        // flights
        category = `${NOTIFICATION_TYPES.FLIGHTS}, ${NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE}`
        break
      case 3:
        // advisories
        category = NOTIFICATION_TYPES.ADVISORIES
        break
      case 4:
        // event & perks
        category = `${NOTIFICATION_TYPES.EVENT_PP_NEW_PERK}, ${NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK}, ${NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING}, ${NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS}, ${NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS}`
        break
      case 5:
        category = NOTIFICATION_TYPES.EVENTS_PROMOTIONS
        // event & promotion
        break
      default:
        return ""
    }
    onCloseConfirmDeleteAllModal()
    const { isConnected } = await NetInfo.fetch()
    if (isConnected) {
      if (category === NOTIFICATION_TYPES.EVENTS_PROMOTIONS) {
        toastRef?.current?.closeNow()
        eventAndPromotionNotificationPayload.forEach((_notificationItem) => {
          Braze.logContentCardDismissed(_notificationItem.id)
        })
        setTimeout(() => {
          Braze.requestContentCardsRefresh()
          toastRef?.current?.show(DURATION.LENGTH_MAX)
        }, 1000)
      } else {
        dispatch(NotificationAction.deleteAllNotificationRequest(category))
        if (category === NOTIFICATION_TYPES.ALL) {
          if (eventAndPromotionNotificationPayload?.length) {
            eventAndPromotionNotificationPayload.forEach((_notificationItem) => {
              Braze.logContentCardDismissed(_notificationItem.id)
            })
            setTimeout(() => {
              Braze.requestContentCardsRefresh()
            }, 1000)
          }
        }
      }
    } else {
      toastErrorRef.current?.show(DURATION.LENGTH_MAX)
    }
  }

  useEffect(() => {
    dispatch(NotificationAction.resetResultDeleteNotification())
    dispatch(NotificationAction.resetResultDeleteSingleNotification())
  }, [tabIndexRef?.current])

  useEffect(() => {
    if (isEmpty(deleteNotificationResult.status) || deleteNotificationResult.code === -1) return
    if (deleteNotificationResult.status === "Success" && deleteNotificationResult.code === 200) {
      toastRef.current?.show(DURATION.LENGTH_MAX)
    } else {
      toastErrorRef.current?.show(DURATION.LENGTH_MAX)
    }
  }, [deleteNotificationResult])

  useEffect(() => {
    if (
      isEmpty(deleteSingleNotificationResult.status) ||
      deleteSingleNotificationResult.code === -1
    ) {
      return
    }
    if (deleteSingleNotificationResult.status === "Success") {
      toastDeleteSingleNotificationRef.current?.show(DURATION.LENGTH_MAX)
    }
    if (deleteSingleNotificationResult.status === "Failed") {
      toastErrorRef.current?.show(DURATION.LENGTH_MAX)
    }
  }, [deleteSingleNotificationResult])

  const {
    isShowTickerband,
    tickerBand,
    tickerBandDescription,
    tickerBandButtonText,
    onPressCTA,
    onCloseTickerBand,
  } = useTickerbandMaintanance(maintananceTickerBandType)

  return (
    <View style={styles.container}>
      <StatusBar barStyle={"dark-content"} />
      {isShowTickerband && maintananceTickerBandType && (
        <TickerBand
          urgent={false}
          title={tickerBand}
          description={tickerBandDescription}
          buttonText={tickerBandButtonText}
          onCTAPress={onPressCTA}
          onClose={onCloseTickerBand}
          isLanding={false}
          tickerStyle={{ paddingTop: insets.top }}
        />
      )}
      {enableNotificationInboxV2 ? <HeaderComponentV2 onGoBack={onPressHeaderGoBackButton}
        leftIcon={enableNotificationInboxV2 ? <ArrowLeftGray /> : null}
        title={translate("notificationsScreen.inbox")}
        rightIconArray={arrRightIcon}
        testID={`${SCREEN_NAME}__HeaderComponent`}
        accessibilityLabel={`${SCREEN_NAME}__HeaderComponent`}
        containerStyle={{
          marginTop: maintananceTickerBandType && isShowTickerband ? 10 : insets.top,
        }} /> : <HeaderComponent
        onGoBack={onPressHeaderGoBackButton}
        title={translate("notificationsScreen.inbox")}
        rightIconArray={arrRightIcon}
        testID={`${SCREEN_NAME}__HeaderComponent`}
        accessibilityLabel={`${SCREEN_NAME}__HeaderComponent`}
        containerStyle={{
          marginTop: maintananceTickerBandType && isShowTickerband ? 10 : insets.top,
        }}
      />}
      {enableNotificationInboxV2 ? <NotificationInboxV2 /> : 
      <Tab.Navigator
        tabBar={(props) => {
          if (tabIndexRef.current !== props?.state?.index) {
            tabIndexRef.current = props?.state?.index
            toastRef?.current?.closeNow()
            toastDeleteSingleNotificationRef.current?.closeNow()
            toastErrorRef.current?.closeNow()
          }
          return (
            <TopTabNavBar
              props={{ ...props }}
              scrollToEndIndex={4}
              topTabParentStyle={styles.topTabParentStyle}
              topTabTouchableOpacityStyle={styles.topTabTouchableOpacityStyle}
              topTabActiveIndicatorStyle={styles.topTabActiveIndicatorStyle}
              topTabActiveLabelStyle={styles.topTabActiveStyle}
              topTabInActiveLabelStyle={styles.topTabInActiveStyle}
            />
          )
        }}
        initialRouteName={NOTIFICATION_TAB_SCREENS.NOTIFICATION_TAB_ALL}
        backBehavior="none"
      >
        <Tab.Screen
          name={"vituralTab"}
          component={VirtualView}
          options={{
            title: "",
          }}
        />
        <Tab.Screen
          name={NOTIFICATION_TAB_SCREENS.NOTIFICATION_TAB_ALL}
          component={AllNotificationScreen}
          options={{
            lazy: true,
            swipeEnabled: false,
            title: translate("notificationTabName.all"),
            tabBarTestID: `${SCREEN_NAME}__TabAllNotifications`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabAllNotifications`,
          }}
          listeners={{
            tabPress: async () => {
              setMaintananceTickerBandType(MaintananceTickerBandType.NOTIFICATION_TAB_ALL)
              const UID = await getViewerUID({ shouldReturnNull: true })
              trackAction(AdobeTagName.CAppNotificationsListingTopMenuToggle, {
                [AdobeTagName.CAppNotificationsListingTopMenuToggle]: translate(
                  "notificationTabName.all",
                ),
                [AdobeTagName.CAppNotificationCenter]: `Filter | ${translate(
                  "notificationTabName.all",
                )} | ${UID}`,
              })
            },
          }}
        />
        <Tab.Screen
          name={"notificationTabNameFlights"}
          component={FlightNotificationScreen}
          options={{
            lazy: true,
            swipeEnabled: false,
            title: translate("notificationTabName.flights"),
            tabBarTestID: `${SCREEN_NAME}__TabFlightNotifications`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabFlightNotifications`,
          }}
          listeners={{
            tabPress: async () => {
              setMaintananceTickerBandType(MaintananceTickerBandType.NOTIFICATION_TAB_FLIGHTS)
              const UID = await getViewerUID({ shouldReturnNull: true })
              trackAction(AdobeTagName.CAppNotificationsListingTopMenuToggle, {
                [AdobeTagName.CAppNotificationsListingTopMenuToggle]: translate(
                  "notificationTabName.flights",
                ),
                [AdobeTagName.CAppNotificationCenter]: `Filter | ${translate(
                  "notificationTabName.flights",
                )} | ${UID}`,
              })
            },
          }}
        />
        <Tab.Screen
          name={"notificationTabNameAdvisores"}
          component={AdvisoriesNotificationScreen}
          options={{
            lazy: true,
            swipeEnabled: false,
            title: translate("notificationTabName.advisores"),
            tabBarTestID: `${SCREEN_NAME}__TabAdvisoresNotifications`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabAdvisoresNotifications`,
          }}
          listeners={{
            tabPress: async () => {
              setMaintananceTickerBandType(null)
              const UID = await getViewerUID({ shouldReturnNull: true })
              trackAction(AdobeTagName.CAppNotificationsListingTopMenuToggle, {
                [AdobeTagName.CAppNotificationsListingTopMenuToggle]: translate(
                  "notificationTabName.advisores",
                ),
                [AdobeTagName.CAppNotificationCenter]: `Filter | ${translate(
                  "notificationTabName.advisores",
                )} | ${UID}`,
              })
            },
          }}
        />
        <Tab.Screen
          name={"notificationTabNameEventAndPerk"}
          component={EventAndPerkNotificationScreen}
          options={{
            lazy: true,
            swipeEnabled: false,
            title: translate("notificationTabName.eventAndPerk"),
            tabBarTestID: `${SCREEN_NAME}__TabEventAndPerkNotifications`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabEventAndPerkNotifications`,
          }}
          listeners={{
            tabPress: async () => {
              setMaintananceTickerBandType(null)
              const UID = await getViewerUID({ shouldReturnNull: true })
              trackAction(AdobeTagName.CAppNotificationsListingTopMenuToggle, {
                [AdobeTagName.CAppNotificationsListingTopMenuToggle]: translate(
                  "notificationTabName.eventAndPerk",
                ),
                [AdobeTagName.CAppNotificationCenter]: `Filter | ${translate(
                  "notificationTabName.eventAndPerk",
                )} | ${UID}`,
              })
            },
          }}
        />
        <Tab.Screen
          name={"notificationTabNameEventAndPromotion"}
          component={EventAndPromotionNotificationScreen}
          options={{
            lazy: true,
            swipeEnabled: false,
            title: translate("notificationTabName.eventAndPromotion"),
            tabBarTestID: `${SCREEN_NAME}__TabEventAndPromotionNotifications`,
            tabBarAccessibilityLabel: `${SCREEN_NAME}__TabEventAndPromotionNotifications`,
          }}
          listeners={{
            tabPress: async () => {
              setMaintananceTickerBandType(null)
              const UID = await getViewerUID({ shouldReturnNull: true })
              trackAction(AdobeTagName.CAppNotificationsListingTopMenuToggle, {
                [AdobeTagName.CAppNotificationsListingTopMenuToggle]: translate(
                  "notificationTabName.eventAndPromotion",
                ),
                [AdobeTagName.CAppNotificationCenter]: `Filter | ${translate(
                  "notificationTabName.eventAndPromotion",
                )} | ${UID}`,
              })
            },
          }}
        />
      </Tab.Navigator>
      }
      <BottomSheet
        isModalVisible={isModalConfirmDeleteAllVisible}
        containerStyle={styles.modalContainer}
        onClosedSheet={onCloseConfirmDeleteAllModal}
        stopDragCollapse={true}
        onBackPressHandle={onCloseConfirmDeleteAllModal}
        animationInTiming={100}
        animationOutTiming={100}
      >
        <View
          style={{
            ...errorStyles.containerStyle,
            ...{ height: "auto" },
          }}
        >
          <View style={errorStyles.iconStyle}>
            <Info />
          </View>
          <TouchableOpacity
            style={errorStyles.dismissIconContainer}
            onPress={onCloseConfirmDeleteAllModal}
          >
            <CrossBlue />
          </TouchableOpacity>
          <View style={errorStyles.textContainerStyle}>
            <Text
              style={[errorStyles.titleStyle, styles.bottomSheetTitleStyle]}
              text={translate("notification.deleteAllTitle")}
            />
            <Text
              style={errorStyles.messageStyle}
              text={translate("notification.deleteAllMessage").replace(
                "[categoryName]",
                getCategoryName,
              )}
            />
          </View>
          <LinearGradient
            style={errorStyles.buttonStyle}
            start={{ x: 1, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={[color.palette.gradientColor1End, color.palette.gradientColor1Start]}
          >
            <Button
              sizePreset="large"
              textPreset="buttonLarge"
              typePreset="secondary"
              text={translate("notification.deleteAllFirstButton")}
              statePreset="default"
              backgroundPreset="light"
              onPress={onDeleteAllNotifications}
              testID={`${SCREEN_NAME}__YesDeleteButton`}
              accessibilityLabel={`${SCREEN_NAME}__YesDeleteButton`}
            />
          </LinearGradient>
          <TouchableOpacity
            onPress={onCloseConfirmDeleteAllModal}
            testID={`${SCREEN_NAME}__NoDeleteButton`}
            accessibilityLabel={`${SCREEN_NAME}__NoDeleteButton`}
            style={styles.secondButtonStyles}
          >
            <Text
              style={styles.lableButton2Styles}
              text={translate("notification.deleteAllSecondButton")}
            />
          </TouchableOpacity>
        </View>
      </BottomSheet>
      <LoadingOverlay visible={deleteNotificationLoading} />
      <CartToastRemoveNotification
        toastRef={toastRef}
        title={messageDeleteAll}
        testID={`${SCREEN_NAME}__DeleteAll`}
        accessibilityLabel={`${SCREEN_NAME}__DeleteAll`}
      />
      <FeedBackToast
        ref={toastDeleteSingleNotificationRef}
        style={styles.errorToastStyle}
        position="custom"
        type={FeedBackToastType.fullWidthFeedBackWithCTA}
        text={messageDeleteSingle}
        testID={`${SCREEN_NAME}__DeleteSingleNotificationError`}
        accessibilityLabel={`${SCREEN_NAME}__DeleteSingleNotificationError`}
      />
      <FeedBackToast
        ref={toastErrorRef}
        style={styles.errorToastStyle}
        position="custom"
        type={FeedBackToastType.fullWidthFeedBack}
        text={errorDelete}
        testID={`${SCREEN_NAME}__DeleteAllError`}
        accessibilityLabel={`${SCREEN_NAME}__DeleteAllError`}
      />
    </View>
  )
}
