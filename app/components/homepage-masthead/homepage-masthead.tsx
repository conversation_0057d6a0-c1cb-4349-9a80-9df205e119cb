import React, { use<PERSON>emo, useEffect, Fragment } from "react"
import { TouchableOpacity, View, Platform, Dimensions, ViewStyle } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import { get, isEmpty } from "lodash"
import { AemSelectors, AEM_PAGE_NAME } from "app/redux/aemRedux"
import { NativeAuthSelectors as AuthSelectors } from "app/redux/nativeAuthRedux"
import ProfileActions, { ProfileSelectors } from "app/redux/profileRedux"
import ForYouActions, { ForYouSelectors } from "app/redux/forYouRedux"
import { handleCondition, mappingUrlAem, simpleCondition } from "app/utils"
import { Wallet, ReloadIcon, Butterfly } from "ichangi-fe/assets/icons"
import { sectionsPayloadForExplore } from "ichangi-fe/app/screens/explore-screen/json/sectionsData"
import ShimmerPlaceholder from "../../helpers/shimmer-placeholder"
import { Search } from "../../elements/search/search"
import { Text } from "../../elements/text/text"
import { color } from "../../theme/color"
import {
  AppConfigPermissionTypes,
  NavigationConstants,
  PLACEHOLDER_ANIMATION_SPEED_IN_MS,
  } from "../../utils/constants"
import { HomepageMastheadProps } from "./homepage-masthead.props"
import { accessibility, ifAllTrue } from "../../utils"
import {
  styles,
  lightGreyLoadingColors,
  whiteGreyLoadingColors,
  loadingElementsLayout,
} from "./homepage-masthead.styles"
import { ExploreSelectors } from "app/redux/exploreRedux"
import { UpComingEventComponent } from "./upcoming-event"
import { UpComingEventState } from "./upcoming-event/upcoming-event-props"
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import { translate } from "app/i18n"
import { useNavigation } from "@react-navigation/native"
import { useGetConfigurationPermissionHelper } from "app/utils/get-configuration-permission"
import { LoadingModal } from "../loading-modal"
import { Tier } from "../changi-rewards-member-card"
import BaseImage from "app/elements/base-image/base-image"
import BaseImageBackground from "app/elements/base-image-background/base-image-background"
import { MytravelSelectors } from "app/redux/mytravelRedux"

const NON_LOGGED_IN = "Non-logged-in"
const heightScreen = Dimensions.get("window").height

const loadingView = () => {
  return (
    <>
      <View style={styles.loadingContainerStyle}>
        <ShimmerPlaceholder
          duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
          shimmerColors={lightGreyLoadingColors}
          shimmerStyle={styles.shimmerStyle}
        />

        <View style={styles.bottomSkeletonStyle}>
          {loadingElementsLayout.map((item, index) => (
            <View key={index}>
              <ShimmerPlaceholder
                duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                shimmerColors={whiteGreyLoadingColors}
                shimmerStyle={item}
              />
            </View>
          ))}
        </View>
      </View>
    </>
  )
}

const transformRewardPoint = (rewardsData) => {
  const points = get(rewardsData, "reward.point")
  if (points) {
    const pointSubstr = points.substring(0, 8)
    if (points.length > 10) {
      return `${pointSubstr}...`
    }
    return points
  }
  return ""
}

export const mappingTierCode = (tierCode, arrTier) => {
  if (tierCode === Tier.StaffGold) {
    return arrTier?.find((tierIcon) => tierIcon.tier === Tier.Gold)
  } else if (tierCode === Tier.StaffMember) {
    return arrTier?.find((tierIcon) => tierIcon.tier === Tier.Member)
  } else if (tierCode === Tier.StaffPlatinum) {
    return arrTier?.find((tierIcon) => tierIcon.tier === Tier.Platinum)
  } else if (tierCode === Tier.StaffMonarch) {
    return arrTier?.find((tierIcon) => tierIcon.tier === Tier.Monarch)
  }
  return arrTier?.find((tierIcon) => tierIcon.tier === tierCode)
}

const transformTierIcon = (rewardsData, isLoggedIn, tierIconsAEM) => {
  const tier = isLoggedIn ? rewardsData?.reward?.currentTierInfo?.replace(" ", "") : NON_LOGGED_IN
  const tierAEM = mappingTierCode(tier, tierIconsAEM)
  return mappingUrlAem(tierAEM?.icon)
}

const getThemeHomePage = (isLoggedIn, rewardsData, mastHeadsAEM) => {
  const tier = isLoggedIn ? rewardsData?.reward?.currentTierInfo?.replace(" ", "") : NON_LOGGED_IN
  const imageHomepageAEM = mappingTierCode(tier, mastHeadsAEM)
  return {
    srcImageHomepage: mappingUrlAem(imageHomepageAEM?.image),
    isDarkTheme: imageHomepageAEM?.background === "Dark",
  }
}

const getThemeColor = (isDarkTheme, errorAEM, profileCardNoMissing) => {
  if (isDarkTheme || errorAEM || profileCardNoMissing) {
    return color.palette.whiteGrey
  }
  return color.palette.almostBlackGrey
}

const getSourceImage = (errorAEM, rewardsError, srcImageHomepage, profileCardNoMissing) => {
  if(profileCardNoMissing){
    return sectionsPayloadForExplore.homePageMastHead.default.loggedIn.imageUrl
  }
  if (errorAEM || rewardsError) {
    return sectionsPayloadForExplore.homePageMastHead.default.loggedIn.imageUrl
  }
  return srcImageHomepage
}

const getTitleHomePage = (isLoggedIn, name, welcomeText, profileLandingError) => {
  if (profileLandingError) {
    return ""
  }
  if (isLoggedIn) {
    return `Hi ${name || ""}`
  }
  return translate(welcomeText)
}

const getMastheadContainer = (isShowUpcomingEventView) => {
  return {
    height: heightScreen * 0.35 + (isShowUpcomingEventView ? 45 : 0),
    backgroundColor: color.palette.lightestGrey,
    marginBottom: isShowUpcomingEventView ? 14 : 0,
  }
}

const marginBottomValue = (isShowUpcomingEventView) => (isShowUpcomingEventView ? 64 : 16)

const pointTextComponent = (rewardsError, rewardPoints, themeColor, _errorAEM, profileError) => {
  let marginLeft = 0
  let marginRight = 0
  const errorState = rewardsError || profileError
  if (errorState) {
    marginLeft = 5
    marginRight = 7
  }

  return (
    <Text
      tx={errorState && "homepageMasthead.reload"}
      text={!errorState && `${rewardPoints} pts`}
      preset="subTitleBold"
      style={{ ...styles.pointsTextStyle, color: themeColor, marginLeft, marginRight }}
      numberOfLines={1}
    />
  )
}

const walletTextComponent = (
  { rewardsError, rewardsFetching, rewardsData, profileFetching },
  themeColor,
  profileError,
) => {
  const errorState = rewardsError || profileError
  if (ifAllTrue([rewardsFetching, isEmpty(rewardsData)]) || profileFetching) {
    return (
      <ShimmerPlaceholder
        duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
        shimmerColors={lightGreyLoadingColors}
        shimmerStyle={styles.walletTextLoading}
      />
    )
  }
  return (
    <>
      <Text
        tx={handleCondition(errorState, "homepageMasthead.reload", "")}
        text={handleCondition(!errorState, translate("homepageMasthead.wallet"), "")}
        preset="subTitleBold"
        style={{
          ...styles.pointsTextStyle,
          color: themeColor,
          marginRight: handleCondition(errorState, 7, 0),
        }}
        numberOfLines={1}
      />
      {handleCondition(
        errorState,
        <ReloadIcon color={themeColor} width={14} height={14} />,
        null,
      )}
    </>
  )
}
const { width, height } = Dimensions.get("screen")
const overLayLoading: ViewStyle = {
  width: width,
  height: height,
  position: "absolute",
}

export function HomepageMasthead(props: HomepageMastheadProps) {
  const {
    sourceSystem,
    onChangiPayPressed,
    onSearchBarPressed,
    onChangiRewardsIconPressed,
    onChangiRewardsPointsPressed,
    onReLoadUpComingEvent,
    testID = "HomepageMasthead",
    accessibilityLabel = "HomepageMasthead",
  } = props  
  const dispatch = useDispatch()
  const navigation = useNavigation()
  const userProfile = useSelector(ProfileSelectors.profilePayload)
  const profileLandingError = useSelector(ProfileSelectors.profileLandingError)
  const profileCardNoMissing = useSelector(ProfileSelectors.profileCardNoMissing)
  const profileFetching = useSelector(ProfileSelectors.profileFetching)
  const profileError = useSelector(ProfileSelectors.profileError)
  const isLoggedIn: boolean = useSelector(AuthSelectors.isLoggedIn)
  const rewardsData = useSelector(ForYouSelectors.rewardsData)
  const rewardsError = useSelector(ForYouSelectors.rewardsError)
  const rewardsFetching = useSelector(ForYouSelectors.rewardsFetching)
  const dataCommonAEM = useSelector(AemSelectors.getAemConfig(AEM_PAGE_NAME.AEM_COMMON_DATA))
  const tierIconsAEM = get(dataCommonAEM, "data.pageLanding.explore.tierIcons")
  const mastHeadsAEM = get(dataCommonAEM, "data.pageLanding.explore.mastHeads")
  const isLoadingAem = dataCommonAEM?.loading
  const errorAEM = dataCommonAEM?.error
  const firstName = get(userProfile, "firstName")
  const logInText = "homepageMasthead.logInText"
  const welcomeText = "homepageMasthead.welcomeText"
  const getUpComingLoading = useSelector(ExploreSelectors.getUpComingLoading)
  const getUpcomingEventsSuccess = useSelector(ExploreSelectors.getUpcomingEventsSuccess)
  const upComingEventData = useSelector(ExploreSelectors.upComingEventsData)
  const getUpComingEventError = useSelector(ExploreSelectors.getUpComingEventError)
  const myTravelFlightsPayload = useSelector(MytravelSelectors.myTravelFlightsPayload)
  const rewardPoints = useMemo(() => transformRewardPoint(rewardsData), [rewardsData])
  const tierIcon = useMemo(() => transformTierIcon(rewardsData, isLoggedIn, tierIconsAEM), [
    rewardsData,
    tierIconsAEM,
    isLoggedIn,
  ])
  const errorState = rewardsError || profileError
  const {
    loadingGetConfig,
    getConfigApp,
    notifyDisableChangiRewards,
  } = useGetConfigurationPermissionHelper()

  const { srcImageHomepage, isDarkTheme } = useMemo(
    () => getThemeHomePage(isLoggedIn, rewardsData, mastHeadsAEM),
    [rewardsData, mastHeadsAEM, isLoggedIn],
  )

  const themeColor = getThemeColor(isDarkTheme, errorAEM, profileCardNoMissing)

  const loadingType = useMemo(() => {
    return ifAllTrue([!getUpComingLoading, !myTravelFlightsPayload?.loading])
  }, [getUpComingLoading, myTravelFlightsPayload])

  const isShowUpcomingEventView = useMemo(() => {
    if (ifAllTrue([isLoggedIn, !isEmpty(upComingEventData) || getUpComingEventError])) return true
    return false
  }, [isLoggedIn, upComingEventData, getUpComingEventError, getUpcomingEventsSuccess, profileError])

  const handleRequestReward = (callbackfunc) => {
    if (isLoggedIn && !rewardsFetching && errorState) {
      if (profileError || profileCardNoMissing) {
        dispatch(ProfileActions.profilePayloadReset())
        dispatch(ProfileActions.profileRequest())
      }
      dispatch(ForYouActions.rewardsRequest(userProfile?.cardNo || ""))
      return
    }
    callbackfunc?.()
  }

  const handleLogin = () => {
    //@ts-ignore
    navigation.navigate(NavigationConstants.authScreen, { sourceSystem })
  }

  const handleOpenChangiRewards = () => {
    getConfigApp({
      configKey: AppConfigPermissionTypes.changiappCREnabled,
      callbackSuccess: () => handleRequestReward(onChangiRewardsPointsPressed(isLoggedIn)),
      callbackFailure: () => notifyDisableChangiRewards(),
    })
  }

  return isLoadingAem ? (
    loadingView()
  ) : (
    <Fragment>
      <View style={getMastheadContainer(isShowUpcomingEventView)}>
        <BaseImageBackground
          source={{
            uri: getSourceImage(errorAEM, rewardsError, srcImageHomepage, profileCardNoMissing),
          }}
          imageStyle={styles.containerStyle}
        >
          <View style={styles.payContainerStyle}>
            <View />
            <Search onPressed={onSearchBarPressed} />
          </View>
          <View
            style={[
              styles.bottomContainerStyle,
              { marginBottom: marginBottomValue(isShowUpcomingEventView) },
            ]}
          >
            <View style={styles.viewInfoContainer}>
              <Text
                text={getTitleHomePage(isLoggedIn, firstName, welcomeText, profileLandingError)}
                preset="h3"
                numberOfLines={1}
                style={{ color: themeColor }}
              />
              <View style={styles.viewStyle}>
                <TouchableOpacity
                  disabled={rewardsFetching}
                  style={styles.pointsContainerStyle}
                  onPress={handleCondition(isLoggedIn, handleOpenChangiRewards, handleLogin)}
                  {...accessibility({
                    testID: `${testID}__TouchableChangiRewardPoint`,
                    accessibilityLabel: `${accessibilityLabel}__TouchableChangiRewardPoint`,
                    OS: Platform.OS,
                  })}
                >
                  {handleCondition(
                    ifAllTrue([rewardsFetching, isEmpty(rewardsData)]) || profileFetching,
                    <ShimmerPlaceholder
                      duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                      shimmerColors={lightGreyLoadingColors}
                      shimmerStyle={styles.pointTextLoading}
                    />,
                    <>
                      {simpleCondition({
                        condition: handleCondition(isLoggedIn && errorState, true, false),
                        ifValue: <Butterfly width={20} height={20} color={themeColor} />,
                        elseValue: <></>,
                      })}
                      {handleCondition(
                        !isLoggedIn,
                        <Text
                          tx={logInText}
                          preset="subTitleBold"
                          style={{ ...styles.pointsTextStyle, color: themeColor, marginLeft: 0 }}
                          numberOfLines={1}
                        />,
                        <>
                          {pointTextComponent(
                            rewardsError,
                            rewardPoints,
                            themeColor,
                            errorAEM,
                            profileError,
                          )}
                          {handleCondition(
                            errorState,
                            <ReloadIcon color={themeColor} width={14} height={14} />,
                            null,
                          )}
                        </>,
                      )}
                    </>,
                  )}
                </TouchableOpacity>
                {handleCondition(
                  isLoggedIn,
                  <TouchableOpacity
                    style={styles.walletContainerStyle}
                    onPress={() => {
                      if (rewardsFetching) {
                        return
                      }
                      handleRequestReward(onChangiPayPressed)
                    }}
                    {...accessibility({
                      testID: `${testID}__TouchableChangiWallet`,
                      accessibilityLabel: `${accessibilityLabel}__TouchableChangiWallet`,
                      OS: Platform.OS,
                    })}
                  >
                    <Wallet color={themeColor} />
                    {walletTextComponent(
                      { rewardsError, rewardsFetching, rewardsData, profileFetching },
                      themeColor,
                      profileError,
                    )}
                  </TouchableOpacity>,
                  null,
                )}
              </View>
            </View>
            <View style={styles.viewRewardContainer}>
              {handleCondition(
                ifAllTrue([rewardsFetching, isEmpty(rewardsData)]) || profileFetching,
                <ShimmerPlaceholder
                  duration={PLACEHOLDER_ANIMATION_SPEED_IN_MS}
                  shimmerColors={lightGreyLoadingColors}
                  shimmerStyle={styles.eCardButtonLoading}
                />,
                <TouchableOpacity
                  style={styles.eCardButtonContainer}
                  onPress={() => {
                    if (rewardsFetching) {
                      return
                    }
                    handleRequestReward(() => onChangiRewardsIconPressed(isLoggedIn))
                  }}
                  {...accessibility({
                    testID: `${testID}__TouchableChangiRewardIcon`,
                    accessibilityLabel: `${accessibilityLabel}__TouchableChangiRewardIcon`,
                    OS: Platform.OS,
                  })}
                >
                  {handleCondition(
                    isLoggedIn && errorState,
                    <>
                      <Text
                        tx={handleCondition(errorState, "homepageMasthead.reload", "")}
                        text={handleCondition(!errorState, `${rewardPoints} pts`, null)}
                        preset="subTitleBold"
                        style={{ ...styles.rewardECardLoading }}
                        numberOfLines={1}
                      />
                      <ReloadIcon
                        color={color.palette.lightPurple}
                        width={14}
                        height={14}
                      />
                    </>,
                    <>
                      {handleCondition(
                        errorAEM,
                        <Butterfly width={18} height={18} style={styles.eCardDefaultIcon} />,
                        <BaseImage
                          source={{ uri: tierIcon }}
                          style={styles.imageTierHeaderStyle}
                          resizeMode="contain"
                        />,
                      )}
                      <Text style={styles.eCardText} tx="homepageMasthead.rewardsCard" />
                    </>,
                  )}
                </TouchableOpacity>,
              )}
            </View>
          </View>
        </BaseImageBackground>
        {handleCondition(
          getUpComingEventError !== null && loadingType && isLoggedIn,
          <View style={styles.upcomingEventErrContainer}>
            <ErrorComponent
              type={ErrorComponentType.standard}
              onPressed={onReLoadUpComingEvent}
              style={{ margin: 0 }}
            />
          </View>,
          isShowUpcomingEventView && (
            <UpComingEventComponent
              data={upComingEventData}
              type={UpComingEventState.default}
            />
          ),
        )}
      </View>
      {handleCondition(
        loadingGetConfig,
        <View style={overLayLoading}>
          <LoadingModal visible={loadingGetConfig} />
        </View>,
        null,
      )}
    </Fragment>
  )
}
