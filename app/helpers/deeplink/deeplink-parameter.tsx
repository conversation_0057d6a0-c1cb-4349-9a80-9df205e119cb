/* eslint-disable dot-notation */
import parse from "url-parse"
import { get } from "lodash"
import { ifAllTrue, serializeObjectToUrlParameter } from "app/utils"
import { StateCode, NavigationConstants, SOURCE_SYSTEM } from "app/utils/constants"
import { FlightDirection } from "app/screens/fly/flights/flight-props"
import { AdobeTagName, trackAction, adobeCampaignSetLinkageFields } from "app/services/adobe"
import { WebViewHeaderTypes } from "app/models/enum"
import { getFeatureFlagInit, REMOTE_CONFIG_FLAGS } from "app/services/firebase/remote-config"
import { store } from "app/redux/store"
import { NavigationTypeEnum } from "app/redux/types/explore/navigation-type"

enum STATE_CODE {
  ChangiBingo = "ChangiBingo",
}

enum MODE_CODE {
  iSCsessionid = "iSCsessionid",
  accountpa = "accountpa",
  myaccount = "myaccount",
  changi_passport = "changi_passport",
  changi_playpass = "changi_playpass",
  changipay = "changipay",
  flight = "flight",
  changieats = "changieats",
  baggage_tracking = "baggage_tracking",
  changi_surewin = "changi_surewin",

  // changi millionaire
  changi_wwc = "changi_wwc",
  changi_cm = "changi_cm",
  changi_cm_login = "changi_cm_login",

  changi_event = "changi_event",
  changi_cfv = "changi_cfv",

  changi_bingo = "changi_bingo",
  hatch_bingo = "hatch_bingo",
  appscapade = "appscapade",
  shop = "shop_landing",
  dine = "dine_landing",
  dine_reservation = "dine_reservation",
  flight_details = "flight_details",
  game = "game",
  retro_claims = "retro_claims",
  pay = "pay",
  retro_claims_details = "retro_claims_details",

  baggage_prediction = "baggage_prediction",
  carpass_csm = "carpass_csm",
  parking = "parking",
}

interface IObjectDeeplinkNavigation {
  type: "in-app" | "deeplink" | "function",
  name: string,
  value: any,
  params: any,
  sourceSystem?: string,
  authCallbackParams?: any,
  proceedAfterCancelLogin?: boolean
}
interface IObjectDeeplink {
  mode: string,
  navigation: IObjectDeeplinkNavigation | {},
}

const defaulParamForAccount: IObjectDeeplinkNavigation = {
  type: "in-app",
  name: "explore",
  value: undefined,
  params: undefined,
  authCallbackParams: undefined,
}
export const triggerAdobeCampaignTrackingCodeUTM = (query) => {
  if (query) {
    // Send Campaign with UTM parameter to AA
    const newObjForUtmData = {}
    for (const [key, value] of Object.entries(query)) {
      if (key.startsWith("utm_")) {
        newObjForUtmData[key] = value
      }
    }
    adobeCampaignSetLinkageFields(newObjForUtmData)
  }
}

function getDataFromDeepLink(link) {
  if (link && link?.url) {
    const parsedUrl = parse(link.url, true)
    return get(parsedUrl, "query")
  }

  return null
}

function getGameCodeFromDeepLink(link) {
  const query = getDataFromDeepLink(link)
  const stateCode = get(query, "StateCode", "")
  const gameCode = get(query, "GameCode", "")

  const modeCode = get(query, "mode", "")
  const gameMode = get(query, "game_mode", "")
  const qrCodeId = get(query, "QRCodeID", "")
  const interstitialMode = get(query, "interstitial_mode", "")

  triggerAdobeCampaignTrackingCodeUTM(query)
  if (stateCode === STATE_CODE.ChangiBingo) {
    return {
      gameCode,
      qrCodeId,
      interstitialMode,
    }
  } else if ([MODE_CODE.changi_bingo, MODE_CODE.hatch_bingo].includes(modeCode)) {
    return {
      gameCode: gameMode,
      qrCodeId,
      interstitialMode,
    }
  }
  return null
}

function getParamsCarPassFromDeepLink(link) {
  const query = getDataFromDeepLink(link)
  const state = get(query, "state", "")
  const [stateCode] = state.split("__")

  triggerAdobeCampaignTrackingCodeUTM(query)
  if (stateCode === StateCode.CARPASS) {
    return stateCode
  }
  return null
}

export const getISCInputParamsDeepLink = (url: string, cutPath: string = ".com", needCut = true) => {
  const searchParams = {}
  const iscPath = url.split("?")[0]
  const search = url.split("?")[1]
  if (search) {
    search.split("&").forEach((param) => {
      const [key, value] = param.split("=")
      searchParams[key] = value
    })
  }
  let redirectTarget = iscPath
  if (needCut) {
    redirectTarget = iscPath.split(cutPath)[1]
  }
  const utmCampaign = searchParams["utm_campaign"]
  const utmMedium = searchParams["utm_medium"]
  const utmSource = searchParams["utm_source"]
  const utmContent = searchParams["utm_content"]
  const utmTerm = searchParams["utm_term"]
  const input = {
    redirectTarget,
    utmParameters: {
      content: utmContent,
      campaign: utmCampaign,
      medium: utmMedium,
      term: utmTerm,
      source: utmSource,
    }
  }
  return input
}

export const extractPathAndQuery = (url) => {
  try {
    const parsedUrl = new URL(url);
    return `${parsedUrl.pathname}${parsedUrl.search}`;
  } catch (error) {
    console.error('Invalid URL:', error);
    return null;
  }
};

export const getISCLinkRedirectTarget = (url: string, cutPath: string = ".com") => {
  const searchParams = {}
  const search = url.split("?")[1]
  if (search) {
    search.split("&").forEach((param) => {
      const [key, value] = param.split("=")
      searchParams[key] = value
    })
  }
  const redirectTarget = extractPathAndQuery(url)
  const utmCampaign = searchParams["utm_campaign"]
  const utmMedium = searchParams["utm_medium"]
  const utmSource = searchParams["utm_source"]
  const utmContent = searchParams["utm_content"]
  const utmTerm = searchParams["utm_term"]
  const input = {
    redirectTarget,
    utmParameters: {
      content: utmContent,
      campaign: utmCampaign,
      medium: utmMedium,
      term: utmTerm,
      source: utmSource,
    }
  }
  return input
}

export const getIsISCLinkDomain = (url: string) => {
  const splits = url.split(".com")
  return splits[0].includes("www.ishopchangi")
}

const getTargetFromModeOfDeepLink = async ({
  link,
  isLoggedIn,
  ecid,
  navigation,
}) => {
  const query = getDataFromDeepLink(link)
  const isDriveParking = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.DRIVE_PARKING)
  const url = link?.url
  let mode = get(query, "mode", "")
  if (mode === MODE_CODE.parking && !isDriveParking) {
    mode = MODE_CODE.carpass_csm
  }
  triggerAdobeCampaignTrackingCodeUTM(query)
  if (!mode) {
    return null
  }
  const obj: IObjectDeeplink = {
    mode,
    navigation: {
      type: "in-app",
      name: NavigationConstants.mainStack,
      value: {
        screen: NavigationConstants.webview,
        params: {
          uri: url
        }
      },
      params: {},
      authCallbackParams: undefined,
    },
  }
  const actions = [
    {
      condition: mode === MODE_CODE.iSCsessionid, // done
      action: () => {
        obj.navigation = {
          type: "deeplink",
          name: NavigationConstants.webview,
          value: undefined,
          params: {
            stateCode: StateCode.ISHOPCHANGI,
            input: getISCInputParamsDeepLink(url),
          },
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.accountpa, // done
      action: () => {
        obj.navigation = defaulParamForAccount
      },
    },
    {
      condition: mode === MODE_CODE.myaccount, // done
      action: () => {
        if (isLoggedIn) {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.bottomNavigation,
              params: {
                screen: NavigationConstants.account,
              }
            },
            params: undefined,
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            authCallbackParams: undefined,
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.changi_passport, // not done yet
      action: () => {
        if (isLoggedIn) {
          obj.navigation = {
            type: "deeplink",
            name: NavigationConstants.webview,
            value: undefined,
            params: {
              stateCode: StateCode.CHANGI_PASSPORT,
              input: {},
            },
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            authCallbackParams: undefined,
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.changi_playpass, // done
      action: () => {
        if (isLoggedIn) {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.webview,
              params: {
                uri: url,
              }
            },
            params: undefined,
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            authCallbackParams: undefined,
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.changipay, // done
      action: () => {
        if (isLoggedIn) {
          obj.navigation.type = "function"
          obj.navigation.name = "pay"
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            sourceSystem: SOURCE_SYSTEM.CPAY,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            authCallbackParams: {
              type: "function",
              name: "pay",
            },
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.flight, // done
      action: () => {
        const flow = Number(get(query, "flow", 1))
        obj.navigation = {
          type: "in-app",
          name: NavigationConstants.mainStack,
          value: {
            screen: "flightResultLandingScreen",
            params: {
              screen: flow === 2 ? FlightDirection.departure : FlightDirection.arrival,
              sourcePage: AdobeTagName.CAppFlightLanding,
            },
          },
          params: undefined,
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: [MODE_CODE.changi_event, MODE_CODE.changi_cfv].includes(mode), // done
      action: () => {
        const packageCode = get(query, "event", "")
        const filter = get(query, "filter", "")
        obj.navigation = {
          type: "function",
          name: MODE_CODE.changi_event,
          value: {
            packageCode,
            filter,
            input: getISCInputParamsDeepLink(url),
          },
          params: undefined,
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.changieats, // done
      action: () => {
        obj.navigation = {
          type: "deeplink",
          name: NavigationConstants.webview,
          value: undefined,
          params: {
            stateCode: StateCode.CHANGI_EATS,
          },
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.baggage_tracking, // done
      action: () => {
        obj.navigation = {
          type: "function",
          name: MODE_CODE.baggage_tracking,
          value: undefined,
          params: undefined,
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.changi_surewin, // will be implemented in future
      action: () => {
        obj.navigation = defaulParamForAccount
      },
    },
    {
      condition: [MODE_CODE.changi_wwc, MODE_CODE.changi_cm].includes(mode), // not done yet
      action: () => {
        const newQueryObj = Object.assign({}, query)
        delete newQueryObj.mode
        const newQueryStr = serializeObjectToUrlParameter(newQueryObj)
        const isCM24 = getFeatureFlagInit(REMOTE_CONFIG_FLAGS.CSM_CM24)

        const NAVIGATION_PARAMS: IObjectDeeplinkNavigation = {
          type: "deeplink",
          name: NavigationConstants.playpassWebview,
          value: {
            headerType: WebViewHeaderTypes.changiMillionaire,
            needBackButton: true,
            needCloseButton: true,
          },
          params: isCM24
            ? {
                stateCode: StateCode.CM24,
                params: undefined,
                input: {
                  receivedFromLink: newQueryStr,
                },
              }
            : {
                stateCode: StateCode.CHANGI_MILLIONAIRE,
                params: newQueryStr,
              },
          authCallbackParams: undefined,
        }
        if (isLoggedIn || (mode === MODE_CODE.changi_cm && isCM24)) {
          obj.navigation = {
            ...NAVIGATION_PARAMS,
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            authCallbackParams: {
              ...NAVIGATION_PARAMS,
            },
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.changi_cm_login,
      action: () => {
        const newQueryObj = Object.assign({}, query)
        delete newQueryObj.mode
        const newQueryStr = serializeObjectToUrlParameter(newQueryObj)

        const NAVIGATION_PARAMS: IObjectDeeplinkNavigation = {
          type: "deeplink",
          name: NavigationConstants.playpassWebview,
          value: {
            headerType: WebViewHeaderTypes.changiMillionaire,
            needBackButton: true,
            needCloseButton: true,
          },
          params: {
            stateCode: StateCode.CM24,
            params: undefined,
            input: {
              receivedFromLink: newQueryStr,
            },
          },
          authCallbackParams: undefined,
        }
        if (isLoggedIn) {
          obj.navigation = {
            ...NAVIGATION_PARAMS,
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            sourceSystem: SOURCE_SYSTEM.CHANGI_MILLIONAIRE,
            authCallbackParams: {
              ...NAVIGATION_PARAMS,
            },
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.appscapade,
      action: () => {
        obj.navigation = {
          type: "deeplink",
          name: NavigationConstants.playpassWebview,
          value: {
            headerType: WebViewHeaderTypes.appscapadeLP,
            needBackButton: true,
            needCloseButton: true,
          },
          params: {
            stateCode: StateCode.APPSCAPADE_LP,
          },
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.dine,
      action: () => {
        obj.navigation = {
          type: "in-app",
          name: NavigationConstants.mainStack,
          value: {
            screen: NavigationConstants.dineShop,
            params: {
              screen: NavigationConstants.dine,
            }
          },
          params: {},
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.shop,
      action: () => {
        obj.navigation = {
          type: "in-app",
          name: NavigationConstants.mainStack,
          value: {
            screen: NavigationConstants.dineShop,
            params: {
              screen: NavigationConstants.shop,
            }
          },
          params: {},
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.dine_reservation,
      action: () => {
        obj.navigation = {
          type: "in-app",
          name: NavigationConstants.mainStack,
          value: {
            screen: NavigationConstants.dineFilterResultsScreen,
          },
          params: {},
          authCallbackParams: undefined,
        }
      },
    },
    {
      condition: mode === MODE_CODE.flight_details,
      action: () => {
        const flightDetailsParams: IObjectDeeplinkNavigation = {
          type: "in-app",
          name: NavigationConstants.mainStack,
          value: {
            screen: NavigationConstants.flightDetails,
            params: {
              payload: {
                item: {
                  flightNumber: query?.flightNo ?? "",
                  flightDate: query?.scheduledDate ?? "",
                  direction: query?.direction ?? "",
                },
              },
              direction: query?.direction ?? "",
              referrer: query?.uid ?? "",
            },
          },
          params: undefined,
          authCallbackParams: undefined,
        }
        if (isLoggedIn) {
          obj.navigation = {
            ...flightDetailsParams,
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: { screen: NavigationConstants.authScreen },
            params: undefined,
            authCallbackParams: flightDetailsParams,
            proceedAfterCancelLogin: true,
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.game,
      action: () => {
        const hasUtm = [
          query?.utm_campaign,
          query?.utm_content,
          query?.utm_medium,
          query?.utm_source,
          query?.utm_term,
        ].some((val) => !!val)
        const gamificationParams: IObjectDeeplinkNavigation = {
          type: "deeplink",
          name: NavigationConstants.playpassWebview,
          value: {
            needBackButton: true,
            needCloseButton: true,
          },
          params: {
            stateCode: StateCode.GAMIFICATION,
            params: undefined,
            input: {
              ecid,
              landingPage: query?.landing_page,
              redeemCode: query?.redeem_code,
              referrer: query?.referrer,
              staff: store.getState()?.profileReducer?.profilePayload?.airportStaff ? 1 : 0,
              utmParameters: hasUtm
                ? {
                    campaign: query?.utm_campaign,
                    content: query?.utm_content,
                    medium: query?.utm_medium,
                    source: query?.utm_source,
                    term: query?.utm_term,
                  }
                : undefined,
            },
          },
          authCallbackParams: undefined,
        }
        if (isLoggedIn) {
          obj.navigation = {
            ...gamificationParams,
            authCallbackParams: undefined,
          }
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
              params: {
                sourceSystem: SOURCE_SYSTEM.GAME_2024,
                callBackAfterLoginCancel: () => {
                  navigation?.navigate?.(NavigationConstants.explore)
                },
              }
            },
            params: undefined,
            authCallbackParams: gamificationParams,
          }
        }
      },
    },
    {
     condition: mode === MODE_CODE.carpass_csm,
     action: () => {
        obj.navigation = {
          type: "deeplink",
          name: "carpass_csm",
          params: {
            input: {
              stateCode: query?.state_code ? query?.state_code : "main"
            }
          },
        }
      },
    },
    {
      condition: mode === MODE_CODE.retro_claims,
      action: () => {
        const retroClaimsParams = {
          type: NavigationTypeEnum.function,
          name: MODE_CODE.retro_claims,
        } as IObjectDeeplinkNavigation
        if (isLoggedIn) {
          obj.navigation = retroClaimsParams
        } else {
          obj.navigation = {
            type: "in-app",
            name: NavigationConstants.mainStack,
            value: {
              screen: NavigationConstants.authScreen,
            },
            params: undefined,
            authCallbackParams: retroClaimsParams,
          }
        }
      },
    },
    {
      condition: mode === MODE_CODE.retro_claims_details,
      action: () => {
        obj.navigation = {}
        // obj.navigation = {
        //   type: "in-app",
        //   name: NavigationConstants.retroClaimsNotifications,
        //   value: { data: query },
        //   params: {},
        //   authCallbackParams: undefined,
        // }
      },
    },
    {
      condition: mode === MODE_CODE.baggage_prediction,
      action: () => {
        const baggagePredictionParams = {
          type: NavigationTypeEnum.function,
          name: MODE_CODE.baggage_prediction,
          value: query,
        } as IObjectDeeplinkNavigation
        obj.navigation = baggagePredictionParams
      }
    },
    {
      condition: mode === MODE_CODE.parking,
      action: () => {
        const initialSection = get(query, "section", "")
        let navigationValue = {
          initiateParkingCouponsRedirection:
            initialSection === "ecoupons" ? new Date().getTime() : undefined,
          initiateParkingFreePromosRedirection:
            initialSection === "promos" ? new Date().getTime() : undefined,
          initiateParkingMoreServicesRedirection:
            initialSection === "services" ? new Date().getTime() : undefined,
        }
        obj.navigation = {
          type: "in-app",
          name: NavigationConstants.mainStack,
          value: {
            screen: NavigationConstants.parkingLanding,
            params: navigationValue,
          },
          params: {},
          authCallbackParams: undefined,
        }
      }
    }
  ]
  const selectedAction = actions.find((action) => action.condition)
  if (selectedAction) {
    await selectedAction.action()
  }

  return obj
}

const onCollectUtmTracking = (link) => {
  if (link && link?.url) {
    const parsedUrl = parse(link.url, true)
    const query = parsedUrl.query
    if (ifAllTrue([query.utm_campaign, query.utm_medium, query.utm_source, query.utm_content])) {
      const { utm_campaign, utm_medium, utm_source, utm_content } = query
      const mode = query.mode || ""
      const event = query.mode || ""
      trackAction(AdobeTagName.Campaign, {
        Campaign: `${utm_campaign}|${utm_medium}|${utm_source}|${utm_content}|mode|${mode}|event|${event}|apn=com.changiairport.cagapp|isi=391730848|ibi=com.changiairport.cagapp`,
      })
    }
  }
}

export {
  getGameCodeFromDeepLink,
  getDataFromDeepLink,
  getParamsCarPassFromDeepLink,
  getTargetFromModeOfDeepLink,
  onCollectUtmTracking,
  MODE_CODE,
}
