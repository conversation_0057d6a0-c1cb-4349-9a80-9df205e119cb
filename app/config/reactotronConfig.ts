import Reactotron from "reactotron-react-native"
import { reactotronRedux as reduxPlugin } from "reactotron-redux"
import Immutable from "seamless-immutable"
import Config from "../config/debugConfig"

const reactotron = Reactotron.configure({ name: "<PERSON><PERSON>", host: 'localhost' })
  .useReactNative({
    overlay: true,
    networking: {
      ignoreUrls: /generate_204/,
    },
    log: false,
  })
  .use(reduxPlugin({ onRestore: Immutable }))
// .use(sagaPlugin())

if (Config.useReactotron) {
  // https://github.com/infinitered/reactotron for more options!

  reactotron.connect()

  // Let's clear Reactotron on every time we load the app
  reactotron.clear()

  const ignoredKeywords = [
    'Setting a timer',
    'VirtualizedLists should never be nested',
    'Remote debugger',
    '[Dynatrace',
  ];
  const shouldIgnore = (msg) =>
    ignoredKeywords.some(keyword => msg.includes(keyword));

  const originalLog = console.log;
  console.log = (...args) => {
    const msg = args.join(' ');
    originalLog(...args); // always log to Metro
    if (!shouldIgnore(msg)) {
      Reactotron.log?.(...args); // only send to Reactotron if not ignored
    }
  };

  // Totally hacky, but this allows you to not both importing reactotron-react-native
  // on every file.  This is just DEV mode, so no big deal.
}
export default reactotron
console.tron = reactotron
