import { NativeModules, Platform } from "react-native"
import CookieManager from "@react-native-cookies/cookies"
import moment from "moment"

export const removeCookie = (url: string, name: string) => {
  const dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ"
  const expiredDate = moment().subtract(1, "days").format(dateFormat).toString()
  if (Platform.OS === "ios") {
    CookieManager.clearByName(url, name)
  } else {
    CookieManager.set(url, {
      name: name,
      value: "",
      version: "1",
      expires: expiredDate,
    })
  }
}

export const removeCookiesByListName = async (url: string, array: any[]) => {
  if (!array.length) return false
  const dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ"
  const expiredDate = moment().subtract(1, "days").format(dateFormat).toString()
  if (Platform.OS === "ios") {
    for (let i = 0; i < array.length; i++) {
      await CookieManager.clearByName(url, array[i], true)
    }
  } else {
    for (let i = 0; i < array.length; i++) {
      await CookieManager.set(url, {
        name: array[i],
        value: "",
        version: "1",
        expires: expiredDate,
      })
    }
  }
}

export const removeAllCookies = async () => {
  CookieManager.clearAll()
  if (Platform.OS === "ios") {
    await NativeModules.ClearWebviewCache.clearWebviewIOS()
  }
}