import * as React from "react"
import _isEmpty from 'lodash/isEmpty'
import { FlatList, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { MasonryListing } from "../../components/masonry-listing/masonry-listing"
import { Text } from "../../elements/text/text"
import { translate } from "../../i18n"
import { YouMayAlsoLikeProps } from "./you-may-also-like.props"
import { color } from "../../theme/color"
import { scale } from "react-native-size-matters"
import { useDispatch, useSelector } from "react-redux"
import { RootState } from "../../redux/store"
import ShopCreators, { Shop, ShopSelectors } from "../../redux/shopRedux"
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import { NavigationConstants, getDotUnicode } from "app/utils/constants"
import { useNavigation } from "@react-navigation/core"
import { AdobeTagName, trackAction } from "app/services/adobe"

const parentContainer: ViewStyle = {
  backgroundColor: color.palette.lightestGrey,
  marginBottom: 50,
}

const titleStyle: ViewStyle = {
  paddingStart: 24,
}

const flatListStyle: ViewStyle = {
  paddingRight: 13,
}

const flatListItemsStyle: ViewStyle = {
  paddingRight: 13,
  paddingBottom: 18,
}

const tenantStyle: ViewStyle = {
  top: scale(-41),
}

const shortTenantView: ViewStyle = {
  top: 0,
}

const viewAllTextStyle: TextStyle = {
  alignSelf: "center",
}

const titleViewStyle: ViewStyle = {
  marginBottom: 17,
}

const flatListViewStyle: ViewStyle = {
  marginLeft: scale(22),
}

const viewAllViewStyle: ViewStyle = {
  alignSelf: "center",
}

const errorContainerTitle: ViewStyle = {
  marginLeft: 24,
}

const errorContainer: ViewStyle = {
  marginBottom: 3,
}

const emptyListText: TextStyle = {
  color: color.palette.almostBlackGrey,
}

const renderMasonryItem = ({ item, index }, navigation) => {
  item.id = index
  if (index === 1 || index % 3 === 1) {
    item.imageUrl = item.longImage || item.logoImage
  } else {
    item.imageUrl = item.shortImage || item.logoImage
  }

  item.onPressed = () => {
    trackAction(AdobeTagName.CAppShopExploreMoreOutlets, {
      [AdobeTagName.CAppShopExploreMoreOutlets]: item?.titleName || "",
    })
    navigation.navigate(NavigationConstants.shopDetailsScreen, {
      tenantId: item?.tenantId,
      name: item?.titleName,
    })
  }
  let tenantViewStyle = {}
  let tenantShortViewStyle = {}
  if (index === 0) {
    tenantShortViewStyle = shortTenantView
  }
  if (index % 2 === 0) {
    tenantViewStyle = tenantStyle
  }

  const getLocationContent = () => {
    return [item?.location, item?.areaDisplay].filter(value => !_isEmpty(value)).join(` ${getDotUnicode()} `)
  }

  return (
    <View style={flatListItemsStyle} key={item?.tenantId}>
      <View
        style={
          index === 1 || index % 3 === 1 ? tenantViewStyle : [tenantViewStyle, tenantShortViewStyle]
        }
      >
        <MasonryListing {...item} location={getLocationContent()} />
      </View>
    </View>
  )
}

const ListEmptyComponent = () => (
  <Text
    style={emptyListText}
    preset="caption1Regular"
    tx="shopScreen.unableToDisplayShops"
  />
)

const COMPONENT_NAME = "YouMayAlsoLike"
export const YouMayAlsoLike = (props: YouMayAlsoLikeProps) => {
  const { title } = props
  const dispatch = useDispatch()
  const navigation = useNavigation()
  const masonryPayload: any = useSelector<RootState, Shop>((data) =>
    ShopSelectors.masonryData(data),
  )
  const filterState = useSelector((state) => ShopSelectors.filterItems(state))
  const filterTitles: any = useSelector<RootState, Shop>((state) =>
    ShopSelectors.filterTitles(state),
  )
  const [marginTopMinus, setMarginTopMinus] = React.useState(0)

  const loadMasonry = React.useCallback(() => {
    dispatch(ShopCreators.shopMasonryRequest())
  }, [])

  React.useEffect(() => {
    loadMasonry()
  }, [loadMasonry])

  if (masonryPayload?.errorFlag) {
    return (
      <View style={errorContainer}>
        <View style={errorContainerTitle}>
          <Text preset={"h4"} text={title} />
        </View>
        <ErrorComponent
          type={ErrorComponentType.standard}
          onPressed={() => {
            loadMasonry()
          }}
        />
      </View>
    )
  }

  const onLayout = (event) => {
    const height = event.nativeEvent.layout.height
    const realHeight = scale(150) + scale(110) * 2 + 68 * 3 + 18 * 2
    setMarginTopMinus(-(height - realHeight - 24))
  }

  return (
    <View style={parentContainer}>
      <View style={titleViewStyle}>
        <Text preset="h4" style={titleStyle}>
          {title}
        </Text>
      </View>
      <View style={flatListViewStyle} onLayout={onLayout}>
        <FlatList
          numColumns={2}
          data={masonryPayload?.data || []}
          renderItem={(item) => renderMasonryItem(item, navigation)}
          style={flatListStyle}
          showsVerticalScrollIndicator={false}
          keyExtractor={(_, index) => String(index)}
          scrollEnabled={false}
          ListEmptyComponent={<ListEmptyComponent />}
        />
      </View>
      {masonryPayload?.hasMoreRecords && (
        <TouchableOpacity
          style={[viewAllViewStyle, { marginTop: marginTopMinus }]}
          onPress={() => {
            trackAction(AdobeTagName.CAppShopExploreMoreViewAll, {
              [AdobeTagName.CAppShopExploreMoreViewAll]: "1",
            })
            dispatch(ShopCreators.shopSetFilterTitles(filterTitles))
            dispatch(ShopCreators.startRequestFilter(true))
            navigation.navigate(NavigationConstants.shopFilterResultsScreen as never)
          }}
          testID={`${COMPONENT_NAME}__ViewAllButton`}
          accessibilityLabel={translate("common.viewAll")}
        >
          <Text preset="textLink" style={viewAllTextStyle}>
            {translate("common.viewAll")}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  )
}
