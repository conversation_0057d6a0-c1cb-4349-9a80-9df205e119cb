import * as React from "react"
import { FlatList, InteractionManager, TouchableOpacity, View, ViewStyle, TextStyle } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import { useNavigation } from "@react-navigation/native"
import _isEmpty from 'lodash/isEmpty'
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import Creators, { Dine, DineSelectors } from "app/redux/dineRedux"
import { RootState } from "app/redux/store"
import { DINE_DIETARY_TYPE, NavigationConstants, getDotUnicode } from "app/utils/constants"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { TenantListingHorizontal } from "../../components/tenant-listing-horizontal/tenant-listing-horizontal"
import { Text } from "../../elements/text/text"
import { translate } from "../../i18n"
import { color } from "../../theme/color"
import { ExploreMoreProps } from "./explore-more.props"
import { DietaryGlutenFreeOptionsIcon, DietaryHalalIcon, DietaryVeganOptionsIcon, DietaryVegetarianFriendlyIcon } from "ichangi-fe/assets/icons"
import { useDineShopFlags } from "app/screens/dine-shop-v2/dine-shop-v2.hooks"

const COMPONENT_NAME = "ExploreMore"
const parentContainer: ViewStyle = {
  backgroundColor: color.palette.lightestGrey,
  paddingBottom: 50,
}

const titleStyle: ViewStyle = {
  paddingStart: 24,
}

const viewAllStyle: ViewStyle = {
  alignSelf: "center",
  paddingTop: 12,
}

const flatListStyle: ViewStyle = {
  paddingStart: 24,
  paddingEnd: 24,
}

const flatListItemsStyle: ViewStyle = {
  paddingVertical: 16,
}

const dividerStyle: ViewStyle = {
  height: 1,
  backgroundColor: color.palette.lighterGrey,
}

const errorContainerTitle: ViewStyle = {
  marginLeft: 24,
}

const errorContainer: ViewStyle = {
  marginBottom: 3,
}

const emptyListText: TextStyle = {
  marginTop: 16,
  color: color.palette.almostBlackGrey,
}

const renderFlatListData = ({ item }, navigation) => {
  item.onPressed = () => {
    trackAction(AdobeTagName.CAppDineExploreMoreOutlets, {
      [AdobeTagName.CAppDineExploreMoreOutlets]: item?.tenantName || "",
    })
    navigation.navigate(NavigationConstants.restaurantDetailScreen, {
      tenantId: item?.tenentId,
      name: item?.tenantName,
    })
  }

  const getLocationContent = () => {
    return [item?.locationDisplay, item?.areaDisplay].filter(value => !_isEmpty(value)).join(` ${getDotUnicode()} `);
  }

  const getCategoryContent = () => {
    if (!item?.categories?.length) return '';
    return item.categories.slice(0, 3).join(', ');
  }

  const getDietaryData = () => {
    if (!item?.dietary?.length) return undefined;

    const dietaryItem = item?.dietary?.[0];
    const result = { content: dietaryItem?.tagTitle, icon: undefined };
    switch (dietaryItem?.tagTitle) {
      case DINE_DIETARY_TYPE.GLUTEN_FREE:
      case DINE_DIETARY_TYPE.GLUTEN_FREE_OPTIONS:
        result.icon = <DietaryGlutenFreeOptionsIcon />;
        break;
      case DINE_DIETARY_TYPE.HALAL:
        result.icon = <DietaryHalalIcon />;
        break;
      case DINE_DIETARY_TYPE.VEGAN:
      case DINE_DIETARY_TYPE.VEGAN_OPTIONS:
        result.icon = <DietaryVeganOptionsIcon />;
        break;
      case DINE_DIETARY_TYPE.VEGETARIAN:
      case DINE_DIETARY_TYPE.VEGETARIAN_FRIENDLY:
        result.icon = <DietaryVegetarianFriendlyIcon />;
        break;
      default:
        break;
    }

    return result;
  }

  return (
    <View style={flatListItemsStyle}>
      <TenantListingHorizontal
        {...item}
        categoryContent={getCategoryContent()}
        dietaryData={getDietaryData()}
        location={getLocationContent()}
      />
    </View>
  )
}

const renderSeparator = () => <View style={dividerStyle} />

const ListEmptyComponent = () => (
  <Text
    style={emptyListText}
    preset="caption1Regular"
    tx="dineScreen.unableToDisplayRestaurants"
  />
)

const ExploreMore = (props: ExploreMoreProps) => {
  const { title } = props
  const dispatch = useDispatch()
  const navigation = useNavigation()
  const exploreMorePayload: any = useSelector<RootState, Dine>((state) =>
    DineSelectors.exploreMore(state),
  )
  const filterState = useSelector((state) => DineSelectors.filterItems(state))
  const filterTitles: any = useSelector<RootState, Dine>((state) =>
    DineSelectors.filterTitles(state),
  )
  const {isShopDineEpicV2On} = useDineShopFlags()
  const loadExploreMore = React.useCallback(() => {
    dispatch(Creators.dineExploreMoreRequest())
  }, [dispatch])

  React.useLayoutEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      loadExploreMore()
    })
  }, [])

  if (exploreMorePayload?.hasError) {
    return (
      <View style={errorContainer}>
        <View style={errorContainerTitle}>
          <Text preset={"h4"} text={title} />
        </View>
        <ErrorComponent
          type={ErrorComponentType.standard}
          onPressed={() => {
            loadExploreMore()
          }}
        />
      </View>
    )
  }

  return (
    <View style={parentContainer}>
      <Text preset="h4" style={titleStyle}>
        {title}
      </Text>
      <FlatList
        style={flatListStyle}
        data={exploreMorePayload?.data || []}
        renderItem={(item) => renderFlatListData(item, navigation)}
        ItemSeparatorComponent={renderSeparator}
        showsVerticalScrollIndicator={false}
        scrollEnabled={!isShopDineEpicV2On}
        ListEmptyComponent={<ListEmptyComponent />}
      />
      {exploreMorePayload?.isMoreRecords && (
        <TouchableOpacity
          onPress={() => {
            trackAction(AdobeTagName.CAppDineExploreMoreViewAll, {
              [AdobeTagName.CAppDineExploreMoreViewAll]: "1",
            })
            dispatch(Creators.dineSetFilterTitles(filterTitles))
            dispatch(Creators.startRequestFilter(true))
            navigation.navigate(NavigationConstants.dineFilterResultsScreen as never)
          }}
          style={viewAllStyle}
          testID={`${COMPONENT_NAME}__ViewAllButton`}
          accessibilityLabel={translate("common.viewAll")}
        >
          <Text preset="textLink">{translate("common.viewAll")}</Text>
        </TouchableOpacity>
      )}
    </View>
  )
}

export { ExploreMore }
