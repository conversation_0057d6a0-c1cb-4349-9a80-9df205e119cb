import { Chip } from "app/elements/chip/chip"
import { ChipType } from "app/elements/chip/chip.props"
import React, { useCallback, useEffect } from "react"
import { View, ViewStyle, ScrollView, InteractionManager } from "react-native"
import { useDispatch, useSelector } from "react-redux"
import Dine<PERSON><PERSON>s, { <PERSON><PERSON>, <PERSON>eSelectors } from "../../redux/dineRedux"
import ShopCreators, { Shop, ShopSelectors } from "../../redux/shopRedux"
import { RootState } from "../../redux/store"
import { ChipFilterProps } from "./chip-filter.props"
import { color } from "../../theme/color"
import { useNavigation } from "@react-navigation/core"
import { ChipFilterType } from "app/components/chip-filter/chip-filter.props"
import { ErrorComponent, ErrorComponentType } from "app/components/error"
import { AdobeTagName, trackAction } from "app/services/adobe"
import { useFocusEffect } from "@react-navigation/native"
import map from "lodash/map"

const COMPONENT_NAME = "ChipFilterSection"
const viewPaddingStyle: ViewStyle = {
  paddingLeft: 5,
  backgroundColor: color.palette.lightestGrey,
}

const containerStyle: ViewStyle = {
  backgroundColor: color.palette.lightestGrey,
}

const scrollContainerStyle: ViewStyle = {
  paddingHorizontal: 21,
  marginTop: 12,
  marginBottom: 24,
}

const errorContainerStyle: ViewStyle = {
  backgroundColor: color.palette.lightestGrey,
  paddingHorizontal: 21,
  paddingBottom: 24,
  marginTop: 12,
}

export const ChipFilterSection = (props: ChipFilterProps) => {
  const { screen } = props
  const isDineScreen = screen === "DINE"
  const dispatch = useDispatch()
  const navigation = useNavigation()
  let filterItems: any = isDineScreen
    ? useSelector<RootState, Dine>((data) => DineSelectors.filterItems(data))
    : useSelector<RootState, Shop>((data) => ShopSelectors.filterItems(data))
  if (!filterItems) {
    filterItems = []
  }
  const { filterPillsPayload }: any = isDineScreen
    ? useSelector<RootState, Dine>((data) => DineSelectors.filterPillsData(data)) || {}
    : useSelector<RootState, Shop>((data) => ShopSelectors.filterPillsData(data)) || {}

  const filterTiles: any = isDineScreen
    ? useSelector<RootState, Dine>((state) => DineSelectors.filterTitles(state))
    : useSelector<RootState, Shop>((state) => ShopSelectors.filterTitles(state))

  const loadFilterPills = () => {
    isDineScreen
      ? dispatch(DineCreators.dineFilterPillsRequest())
      : dispatch(ShopCreators.shopFilterPillsRequest())
  }

  useEffect(() => {
    InteractionManager.runAfterInteractions(() => {
      loadFilterPills()
    })
  }, [])

  useFocusEffect(
    useCallback(() => {
      const interactionHandle = InteractionManager.runAfterInteractions(() => {
        isDineScreen ? dispatch(DineCreators.dineResetFilterItems()) : dispatch(ShopCreators.shopResetFilterItems())
      })
      return () => {
        if (interactionHandle) {
          interactionHandle.cancel()
        }
      }
    }, [isDineScreen])
  )

  const handleFilter = (item) => {
    isDineScreen
      ? dispatch(DineCreators.dineHandleFilterItems(item))
      : dispatch(ShopCreators.shopHandleFilterItems(item))
  }

  if (
    !filterPillsPayload ||
    (filterPillsPayload?.data?.length === 0 &&
      filterPillsPayload?.errorFlag &&
      filterPillsPayload?.type === ChipFilterType.default)
  ) {
    return (
      <View style={errorContainerStyle}>
        <ErrorComponent
          type={ErrorComponentType.filter}
          onPressed={() => {
            loadFilterPills()
          }}
        />
      </View>
    )
  } else if (
    !filterPillsPayload ||
    !filterPillsPayload?.data ||
    filterPillsPayload?.data?.length === 0
  ) {
    return <></>
  }
  const isLoading = filterPillsPayload?.type === ChipType.loading
  return (
    <View style={containerStyle}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={scrollContainerStyle}
        nestedScrollEnabled
      >
        {map(filterPillsPayload?.data, (item, key) => {
          return (
            <View key={key} style={viewPaddingStyle}>
              <Chip
                onPressed={() => {
                  handleFilter(item)
                  if (isDineScreen) {
                    trackAction(AdobeTagName.CAppDineSearchFilterDirectory, {
                      [AdobeTagName.CAppDineSearchFilterDirectory]: item?.tagTitle,
                    })
                    dispatch(DineCreators.dineSetFilterTitles(filterTiles))
                    dispatch(DineCreators.startRequestFilter(true))
                    navigation.navigate("dineFilterResults", { filteredData: filterItems })
                    return
                  }
                  trackAction(AdobeTagName.CAppShopSearchFilterDirectory, {
                    [AdobeTagName.CAppShopSearchFilterDirectory]: item?.tagTitle,
                  })
                  dispatch(ShopCreators.shopSetFilterTitles(filterTiles))
                  dispatch(ShopCreators.startRequestFilter(true))
                  navigation.navigate("shopFilterResults", { filteredData: filterItems })
                }}
                text={item.tagTitle}
                type={isLoading ? ChipType.loading : ChipType.unSelected}
              />
            </View>
          )
        })}
      </ScrollView>
    </View>
  )
}
