import * as React from "react"
import { Dimensions, View } from "react-native"
import { TabNavBarCustom } from "app/navigators/navigation-utilities"
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated"

interface Tabs {
  tabList: any
}

interface TabViewProps {
  routes: any
  tabs: Tabs
  topTabParentStyle?: any
  topTabTouchableOpacityStyle?: any
  isAnimate?: boolean
  timeOut?: number
  tabIndexInit?: number
  tabOnClickCallback?: any
}
const WIDTH = Dimensions.get("window").width

export const TabView = (props: TabViewProps) => {
  const {
    routes,
    tabs,
    topTabParentStyle,
    topTabTouchableOpacityStyle,
    isAnimate = false,
    timeOut = 200,
    tabIndexInit = 0,
    tabOnClickCallback = () => {
      return true
    },
  } = props
  const animatedPosition = useSharedValue(0)
  const [tabIndex, setTabIndex] = React.useState(0)

  React.useEffect(() => {
    onPress({ index: tabIndexInit })
  }, [tabIndexInit])

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateX: animatedPosition.value,
        },
      ],
    };
  });

  const onPress = ({ index }) => {
    if (isAnimate) {
      const toValue = index > tabIndex ? -WIDTH : WIDTH
      animatedPosition.value = 0
      animatedPosition.value = withTiming(toValue, {
        duration: 200,
      });
      const wait = new Promise((resolve) => setTimeout(resolve, timeOut))
      wait.then(() => {
        setTabIndex(index)
        tabOnClickCallback({ index })
        animatedPosition.value = 0
      })
    } else {
      setTabIndex(index)
      tabOnClickCallback({ index })
    }
  }

  const tabBarProps = {
    state: {
      index: tabIndex,
      routes,
    },
    descriptors: routes.reduce((acc, cur) => {
      const newValue = acc
      newValue[cur.key] = { options: {} }
      return newValue
    }, {}),
  }

  const tabContent = () => {
    if (!tabs?.tabList?.length) {
      return <></>
    }
    const TabComponent = tabs?.tabList[tabIndex].component
    const TabProps = tabs.tabList[tabIndex].props
    if (!isAnimate) {
      return <TabComponent {...TabProps} key={`TabView-${tabIndex?.toString()}`}/>
    }
    return (
      <Animated.View
        key={`TabView-${tabIndex?.toString()}`}
        style={animatedStyle}
      >
        <TabComponent {...TabProps} />
      </Animated.View>
    )
  }

  return (
    <View>
      <TabNavBarCustom
        props={tabBarProps}
        onPress={onPress}
        topTabLabelsPresets={"tabsSmall"}
        topTabParentStyle={topTabParentStyle}
        topTabTouchableOpacityStyle={topTabTouchableOpacityStyle}
        tabIndexInit={tabIndex}
      />
      <View>{tabContent()}</View>
    </View>
  )
}
