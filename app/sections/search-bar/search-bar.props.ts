import { NativeSyntheticEvent, ReturnKeyTypeOptions, TextInputSubmitEditingEventData, ViewStyle } from "react-native"
import { InputFieldProps } from "app/sections/search-bar/input-field"
import { InputFieldProps as InputFieldV2Props } from "app/sections/search-bar/input-field-v2"

export interface SearchBarProps {
  tab: string
  keyword?: string
  onPressBack?: () => void
  onChangeKeyword?: (s: string) => void
  onSearchClear?: () => void
  testID: string
  accessibilityLabel: string
  onSubmitEditing?: () => void
  onSubmitLocal?: (e: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => void
  searchAutoCompleteFlag?: string
  isShowBack?: boolean
  inputProps?: InputFieldProps | InputFieldV2Props
  arrowLeftColor?: string
  containerStyle?: ViewStyle
  inputContainerStyle?: ViewStyle
  useInputFieldV2?: boolean
  shouldFocusAfterClear?: boolean
  returnKeyType?: ReturnKeyTypeOptions | undefined
  autoFocusTextInput?: boolean
}
