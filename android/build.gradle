// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    apply from: "../node_modules/@dynatrace/react-native-plugin/files/plugin.gradle", to: buildscript
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 28
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
        androidXAnnotation = "1.4.0"
        androidXBrowser = "1.4.0"
        cpaySdkVersion = "1.13.4-sandbox"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        classpath 'com.google.firebase:firebase-appdistribution-gradle:4.2.0'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }

        google()
        configurations.all {
            resolutionStrategy {
                force 'com.google.android.gms:play-services-location:21.0.1'
            }
        }
        maven { url 'https://www.jitpack.io' }
        maven {
            url "https://s3-us-west-2.amazonaws.com/si-mobile-sdks/android/"
        }

 maven       {
            url "https://liquidpay-045015120051.d.codeartifact.ap-southeast-1.amazonaws.com/maven/com.liquid.widget/"
            credentials {
                username = "aws"
                // this token just valid for 12 hours only.
                password = project.findProperty("codeArtifactToken") ?: ""
            }
        }
    }
}
apply from: "../node_modules/@dynatrace/react-native-plugin/files/dynatrace.gradle"
apply plugin: "com.facebook.react.rootproject"

task printToken {
    doLast {
        def token = project.findProperty("codeArtifactToken")
        def green = "\u001B[32m"
        def magenta = "\u001B[35m"
        def yellow = "\u001B[33m"
        def reset = "\u001B[0m"

        def first = token?.take(4) ?: "N/A"
        def last = token?.takeRight(4) ?: "N/A"

        def label = "${green}AWS CODE-ARTIFACT TOKEN (sanitized):${reset}"
        def value = "${magenta}${first}*****************************${last}${reset}" // masking token

        def content = "${label} ${value}"
        def contentLength = content.replaceAll("\u001B\\[[;\\d]*m", "").length()  
        def border = "${yellow}+" + "-".repeat(contentLength + 2) + "+${reset}"

        println border
        println "${yellow}|${reset} ${content} ${yellow}|${reset}"
        println border
    }
}