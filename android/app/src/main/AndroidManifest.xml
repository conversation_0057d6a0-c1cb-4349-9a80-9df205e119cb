<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission tools:node="remove" android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission tools:node="remove" android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>

    <application
      android:name="com.changiairport.cagapp3.MainApplication"
      android:largeHeap="true"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:usesCleartextTraffic="true"
      android:networkSecurityConfig="@xml/lp_network_security_config"
      android:theme="@style/AppTheme"
      tools:replace="android:name,android:allowBackup,android:usesCleartextTraffic"
      android:supportsRtl="true">
      <activity
        android:name="com.changiairport.cagapp3.MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:exported="true"
        android:taskAffinity="">
        <!-- <intent-filter android:autoVerify="true">
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter> -->
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.MAIN" />
              <category android:name="android.intent.category.LAUNCHER" />
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="cagapp3.page.link" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />
              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data android:host="cagapp3sit.onelink.me" android:scheme="https" />
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW" />
              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data android:host="cagapp3uat.onelink.me" android:scheme="https" />
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="www.changiairport.com" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="changimillionaire.com" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="cm-staging.fairtech.com.sg" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="mi-web01.ascentis.com" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="rewards.changiairport.com" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="ishopchangi.com" android:scheme="https"/>
          </intent-filter>
          <intent-filter android:autoVerify="true">
              <action android:name="android.intent.action.VIEW"/>
              <category android:name="android.intent.category.DEFAULT"/>
              <category android:name="android.intent.category.BROWSABLE"/>
              <data android:host="playpass.changiairport.com" android:scheme="https"/>
          </intent-filter>
          <intent-filter>
              <action android:name="android.intent.action.VIEW" />
              <category android:name="android.intent.category.DEFAULT" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data android:scheme="cagichangi" />
          </intent-filter>
      </activity>
      <service android:name="com.braze.push.BrazeFirebaseMessagingService"
        android:exported="false">
        <intent-filter>
          <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
      </service>
      <receiver android:name=".MyWidgetProvider" android:exported="true">
        <intent-filter>
            <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
        </intent-filter>
        <meta-data
            android:name="android.appwidget.provider"
            android:resource="@xml/my_widget_info" />
      </receiver>
      <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
      <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
      <meta-data android:name="com.google.android.geo.API_KEY" android:value="@string/google_maps_key"/>
      <provider android:authorities="com.facebook.app.FacebookContentProvider2948691728764548"
        android:name="com.facebook.FacebookContentProvider"
        android:exported="true" />
    </application>

</manifest>
