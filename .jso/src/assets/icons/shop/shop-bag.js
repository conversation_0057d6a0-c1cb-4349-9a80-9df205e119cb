  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[2]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ShopBagIcon = function ShopBagIcon(props) {
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, Object.assign({
      xmlns: "http://www.w3.org/2000/svg",
      width: 24,
      height: 24,
      fill: "none"
    }, props, {
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Path, {
        fill: "#7A35B0",
        d: "M18.222 7.286h-1.778C16.444 4.92 14.454 3 12 3 9.547 3 7.556 4.92 7.556 7.286H5.778C4.8 7.286 4 8.057 4 9v10.286C4 20.229 4.8 21 5.778 21h12.444C19.2 21 20 20.229 20 19.286V9c0-.943-.8-1.714-1.778-1.714ZM12 4.714c1.476 0 2.667 1.149 2.667 2.572H9.333c0-1.423 1.191-2.572 2.667-2.572Zm6.222 14.572H5.778V9h12.444v10.286ZM12 12.429c-1.159 0-2.142-.709-2.51-1.701-.172-.46-.555-.87-1.046-.87-.49 0-.898.402-.794.881.422 1.943 2.21 3.404 4.35 3.404 2.14 0 3.928-1.46 4.35-3.404.104-.48-.303-.882-.794-.882-.491 0-.874.41-1.045.87-.37.993-1.352 1.702-2.511 1.702Z"
      })
    }));
  };
  var _default = exports.default = ShopBagIcon;
