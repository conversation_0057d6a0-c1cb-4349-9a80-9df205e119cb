  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PrivilegesList = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _privilegesItem = _$$_REQUIRE(_dependencyMap[9]);
  var _privilegesItem2 = _$$_REQUIRE(_dependencyMap[10]);
  var _privilegesRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[12]);
  var _error = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[14]));
  var _utils = _$$_REQUIRE(_dependencyMap[15]);
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _privilegesList = _$$_REQUIRE(_dependencyMap[17]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingData = Array(3).fill({
    type: _privilegesItem2.PrivilegesItemType.loading
  });
  var SCREEN_NAME = "PrivilegesList";
  var PrivilegesList = exports.PrivilegesList = function PrivilegesList(props) {
    var data = props.data,
      loadMore = props.loadMore,
      fetchPrivileges = props.fetchPrivileges,
      visible = props.visible,
      informativePrivileges = props.informativePrivileges,
      title = props.title,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "PrivilegesList" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "PrivilegesList" : _props$accessibilityL;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var isFetching = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.privilegesFetching);
    var error = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.privilegesError);
    var privilegesAemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.PRIVILEGES_PAGE));
    var isShowLoadMore = !isFetching && data && data.length > visible;
    var icon = (0, _lodash.get)(informativePrivileges, "icon");
    var header = (0, _lodash.get)(informativePrivileges, "title", "");
    var informativeText = (0, _lodash.get)(informativePrivileges, "informativeText", "");
    var privileges = (0, _lodash.get)(privilegesAemData, "data.0");
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("PrivilegesList"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var dataMerge = (0, _react.useMemo)(function () {
      if (isFetching) {
        return loadingData;
      }
      return data;
    }, [isFetching, data]);
    var onOptionPress = function onOptionPress(item) {
      if (item.navigationType === _menuOption.NavigationType.external && item.navigationValue) {
        navigation.navigate(_constants.NavigationConstants.webview, {
          uri: item.navigationValue
        });
        return;
      }
      if (item.navigationType === _menuOption.NavigationType.inapp && Object.values(_constants.NavigationConstants).includes(item.navigationValue)) {
        navigation.navigate(item.navigationValue);
        return;
      }
      handleNavigation(item.navigationType, item.navigationValue);
    };
    var callBackAfterSuccess = function callBackAfterSuccess(dataList) {
      var _dataList$list;
      if ((dataList == null || (_dataList$list = dataList.list) == null ? undefined : _dataList$list.length) > 0) {
        return dataList.list.reduce(function (previousValue, currentValue) {
          if (currentValue.displayScreen === _constants.DisplayScreens.forYouPrivileges) {
            var _currentValue$navigat, _currentValue$navigat2;
            return [].concat((0, _toConsumableArray2.default)(previousValue), [Object.assign({}, currentValue, {
              navigationType: currentValue == null || (_currentValue$navigat = currentValue.navigation) == null ? undefined : _currentValue$navigat.type,
              navigationValue: currentValue == null || (_currentValue$navigat2 = currentValue.navigation) == null ? undefined : _currentValue$navigat2.value,
              icon: (0, _utils.mappingUrlAem)(currentValue.icon)
            })]);
          }
          return previousValue;
        }, []);
      }
      return null;
    };
    (0, _react.useEffect)(function () {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.PRIVILEGES_PAGE,
        pathName: "getForYouMoreOptions",
        callBackAfterSuccess: callBackAfterSuccess
      }));
    }, []);
    if (!(0, _lodash.isEmpty)(dataMerge)) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _privilegesList.styles.privilegesContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: title,
          preset: "bodyTextRegular",
          style: _privilegesList.styles.enjoyText
        }), dataMerge == null ? undefined : dataMerge.slice(0, visible).map(function (item, id) {
          return (0, _jsxRuntime.jsx)(_privilegesItem.PrivilegesItem, Object.assign({}, item, {
            index: id
          }), id);
        }), isShowLoadMore && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: !(0, _lodash.isEmpty)(error) ? fetchPrivileges : loadMore,
          style: _privilegesList.styles.loadMore,
          testID: `${testID}__TouchableViewMore`,
          accessibilityLabel: `${accessibilityLabel}__TouchableViewMore`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "privilegesScreen.viewMore",
            preset: "textLink"
          })
        })]
      });
    }
    if (!(0, _lodash.isEmpty)(error)) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _privilegesList.styles.privilegesContainer,
        children: (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _error.ErrorComponentType.cloud,
          style: _privilegesList.styles.resetMargin,
          errorText: "screenError.somethingWrong",
          errorHeader: "screenError.oop",
          cloudTextStyle: _privilegesList.styles.cloudTextStyle,
          cloudButtonStyle: _privilegesList.styles.cloudButtonStyle,
          onPressed: fetchPrivileges,
          testID: `${testID}__ErrorComponent`,
          accessibilityLabel: `${accessibilityLabel}__ErrorComponent`
        })
      });
    }
    if ((0, _lodash.isEmpty)(dataMerge) && (0, _lodash.isEmpty)(error)) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _privilegesList.styles.privilegesContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _privilegesList.styles.empty,
          children: [icon ? (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _privilegesList.styles.iconStyle,
            testID: `${SCREEN_NAME}__TouchableCrown`,
            accessibilityLabel: `${SCREEN_NAME}__TouchableCrown`,
            children: (0, _jsxRuntime.jsx)(_baseImage.default, {
              source: {
                uri: (0, _utils.mappingUrlAem)(icon)
              },
              style: _privilegesList.styles.imageStyle,
              resizeMode: "contain"
            })
          }) : null, header ? (0, _jsxRuntime.jsx)(_text.Text, {
            text: header,
            preset: "h2",
            style: _privilegesList.styles.h2
          }) : null, informativeText ? (0, _jsxRuntime.jsx)(_text.Text, {
            text: informativeText,
            style: _privilegesList.styles.textAlign,
            preset: "caption1Regular"
          }) : null]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _privilegesList.styles.marginLink,
            children: (0, _jsxRuntime.jsx)(_menuOption.MenuOption, {
              type: _menuOption.MenuOptionType.default,
              icon: privileges == null ? undefined : privileges.icon,
              title: privileges == null ? undefined : privileges.title,
              redirection: privileges == null ? undefined : privileges.redirection,
              navigationType: privileges == null ? undefined : privileges.navigationType,
              navigationValue: privileges == null ? undefined : privileges.navigationValue,
              onPress: onOptionPress,
              testID: `${testID}__MenuOptionButterflyMember`,
              accessibilityLabel: `${accessibilityLabel}__MenuOptionButterflyMember`
            })
          })
        })]
      });
    }
    return null;
  };
