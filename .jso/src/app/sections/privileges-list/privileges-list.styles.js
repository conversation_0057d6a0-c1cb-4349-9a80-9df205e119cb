  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    cloudButtonStyle: {
      width: "100%"
    },
    cloudTextStyle: {
      marginTop: 16,
      paddingHorizontal: 0
    },
    empty: {
      alignItems: "center",
      marginBottom: 24,
      marginTop: 30
    },
    enjoyText: {
      color: _theme.color.palette.almostBlackGrey,
      marginVertical: 32
    },
    feedBackToastStyle: {
      marginBottom: 20,
      width: "100%"
    },
    h2: {
      marginBottom: 12
    },
    iconStyle: {
      alignItems: "center",
      alignSelf: "center",
      height: 40,
      justifyContent: "center",
      marginBottom: 0,
      width: 40
    },
    imageStyle: {
      height: "100%",
      width: "100%"
    },
    loadMore: {
      alignSelf: "center",
      color: _theme.color.palette.lightPurple,
      marginTop: 24
    },
    marginLink: {
      marginBottom: 14
    },
    privilegesContainer: {
      paddingHorizontal: 24
    },
    resetMargin: {
      margin: 0,
      paddingBottom: 0
    },
    textAlign: {
      textAlign: "center"
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey
    })
  });
