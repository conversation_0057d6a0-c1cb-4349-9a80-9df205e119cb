  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useDealsPromosListingRequests = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[4]);
  var _queries = _$$_REQUIRE(_dependencyMap[5]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _envParams = _$$_REQUIRE(_dependencyMap[7]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _dealsPromosCategoryListing = _$$_REQUIRE(_dependencyMap[9]);
  var getDealsPromosListingReq = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      var params = {
        input: {
          sort_by_a_to_z: false,
          page_number: 1,
          page_size: 9999
        }
      };
      try {
        var _env, _env2;
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getDealsPromos, params),
          parameters: {},
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        if (response.statusCode === 200 && response.success) {
          var _response$data;
          if (response != null && (_response$data = response.data) != null && _response$data.errors) {
            var _response$data2;
            return {
              errors: response == null || (_response$data2 = response.data) == null ? undefined : _response$data2.errors
            };
          } else {
            var _response$data3;
            return response == null || (_response$data3 = response.data) == null || (_response$data3 = _response$data3.data) == null ? undefined : _response$data3.getDealsPromosListing;
          }
        }
      } catch (err) {}
    });
    return function getDealsPromosListingReq() {
      return _ref.apply(this, arguments);
    };
  }();
  var useDealsPromosListingRequests = exports.useDealsPromosListingRequests = function useDealsPromosListingRequests() {
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingDealsPromosList = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      hasError = _useState4[0],
      setError = _useState4[1];
    var _useState5 = (0, _react.useState)([]),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      listData = _useState6[0],
      setListData = _useState6[1];
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      originalListData = _useState8[0],
      setOriginalListData = _useState8[1];
    var getData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        try {
          setLoading(true);
          setError(null);
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setError(_dealsPromosCategoryListing.ErrorType.NoInternet);
            setLoading(false);
            return;
          }
          var response = yield getDealsPromosListingReq();
          if (response) {
            if (response != null && response.errors) {
              setError(_dealsPromosCategoryListing.ErrorType.ErrorDefault);
            } else {
              setListData(response == null ? undefined : response.promos);
              setOriginalListData(response == null ? undefined : response.promos);
            }
          } else {
            setError(_dealsPromosCategoryListing.ErrorType.ErrorDefault);
          }
        } catch (error) {
          setError(_dealsPromosCategoryListing.ErrorType.ErrorDefault);
        } finally {
          setLoading(false);
        }
      });
      return function getData() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onRefresh = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        yield getData();
      });
      return function onRefresh() {
        return _ref3.apply(this, arguments);
      };
    }();
    var handlePressReloadError = function handlePressReloadError() {
      getData();
    };
    (0, _react.useEffect)(function () {
      getData();
    }, []);
    return {
      hasError: hasError,
      listData: listData,
      loadingDealsPromosList: loadingDealsPromosList,
      handlePressReloadError: handlePressReloadError,
      setListData: setListData,
      originalListData: originalListData,
      onRefresh: onRefresh
    };
  };
