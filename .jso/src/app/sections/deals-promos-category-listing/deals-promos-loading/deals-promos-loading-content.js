  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DealsPromosLoading = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _exploreStaffPerkItem = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var dataLoading = new Array(3).fill(null);
  var DealsPromosLoading = exports.DealsPromosLoading = _react.default.memo(function (_ref) {
    var containerStyle = _ref.containerStyle;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.viewRootContainer, containerStyle],
      children: dataLoading.map(function (_, index) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.viewRow,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
              shimmerStyle: styles.imgLoading
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.loadingTxt,
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
                shimmerStyle: styles.titleLoading
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _exploreStaffPerkItem.lightGreyLoadingColors,
                shimmerStyle: styles.contentLoading
              })]
            })]
          }), index !== (dataLoading == null ? undefined : dataLoading.length) - 1 && (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.viewRowBottom
          })]
        }, index);
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    viewRootContainer: {
      paddingHorizontal: 20,
      paddingTop: 10,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    txtTitle: {
      fontFamily: _theme.typography.black,
      fontSize: 16,
      lineHeight: 20,
      fontWeight: "900",
      color: _theme.color.palette.almostBlackGrey
    },
    filterLoading: {
      width: "100%",
      height: 24,
      borderRadius: 4,
      marginTop: 14,
      marginBottom: 24
    },
    viewRow: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 16
    },
    loadingTxt: {
      flex: 1
    },
    imgLoading: {
      width: 120,
      height: 80,
      borderRadius: 4,
      marginRight: 16
    },
    titleLoading: {
      width: "100%",
      height: 12,
      borderRadius: 4,
      marginBottom: 12
    },
    contentLoading: {
      width: "40%",
      height: 12,
      borderRadius: 4
    },
    viewRowBottom: {
      width: "100%",
      height: 1,
      backgroundColor: _theme.color.palette.lighterGrey,
      marginBottom: 24
    }
  });
