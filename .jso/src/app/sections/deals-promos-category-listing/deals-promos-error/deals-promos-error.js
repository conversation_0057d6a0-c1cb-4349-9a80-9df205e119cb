  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DealsPromosError = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _dealsPromosCategoryListing = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _collapsibleHeader = _$$_REQUIRE(_dependencyMap[11]);
  var _styles = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DealsPromosError = exports.DealsPromosError = _react.default.memo(function (props) {
    var _rootItemOffsetRef$cu, _renderContent, _renderContent2, _renderContent3, _renderContent4, _renderContent5, _renderContent6;
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr42 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR42";
    });
    var handlePressReload = props.handlePressReload,
      typeError = props.typeError,
      rootItemOffsetRef = props.rootItemOffsetRef;
    var dynamicStyles = (0, _react.useMemo)(function () {
      return _reactNative.StyleSheet.create({
        container: {
          marginTop: _collapsibleHeader.BACKGROUND_IMAGE_HEIGHT,
          paddingTop: 96,
          paddingHorizontal: 24,
          alignItems: "center",
          backgroundColor: _theme.color.palette.whiteGrey,
          height: "100%",
          borderTopLeftRadius: _styles.FILTER_BORDER_RADIUS,
          borderTopRightRadius: _styles.FILTER_BORDER_RADIUS
        }
      });
    }, [rootItemOffsetRef == null || (_rootItemOffsetRef$cu = rootItemOffsetRef.current) == null ? undefined : _rootItemOffsetRef$cu[1]]);
    var renderContent = function renderContent() {
      var titleTx = ehr42 != null && ehr42.header ? ehr42 == null ? undefined : ehr42.header : "errorOverlay.variant1.title";
      var messageTx = ehr42 != null && ehr42.subHeader ? ehr42 == null ? undefined : ehr42.subHeader : "errorOverlay.variant1.message";
      var reloadTx = ehr42 != null && ehr42.buttonLabel ? ehr42 == null ? undefined : ehr42.buttonLabel : "errorOverlay.variant1.reload";
      return {
        titleTx: titleTx,
        messageTx: messageTx,
        reloadTx: reloadTx
      };
    };
    if (typeError === _dealsPromosCategoryListing.ErrorType.NoInternet) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: dynamicStyles.container,
        children: [(0, _jsxRuntime.jsx)(_icons.CloudOpsIcon, {
          width: 120,
          height: 120
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          tx: "errorOverlay.variant3.title"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.messageTextStyle,
          tx: "errorOverlay.variant3.message"
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.reloadButtonStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            onPress: handlePressReload,
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "primary",
            tx: "errorOverlay.variant3.retry",
            backgroundPreset: "light",
            statePreset: "default"
          })
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: dynamicStyles.container,
      children: [(0, _jsxRuntime.jsx)(_icons.CloudOpsIcon, {
        width: 120,
        height: 120
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.titleTextStyle,
        tx: ehr42 ? null : (_renderContent = renderContent()) == null ? undefined : _renderContent.titleTx,
        text: ehr42 ? (_renderContent2 = renderContent()) == null ? undefined : _renderContent2.titleTx : null
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.messageTextStyle,
        tx: ehr42 ? null : (_renderContent3 = renderContent()) == null ? undefined : _renderContent3.messageTx,
        text: ehr42 ? (_renderContent4 = renderContent()) == null ? undefined : _renderContent4.messageTx : null
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: styles.reloadButtonStyle,
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          onPress: handlePressReload,
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "primary",
          tx: ehr42 ? null : (_renderContent5 = renderContent()) == null ? undefined : _renderContent5.reloadTx,
          text: ehr42 ? (_renderContent6 = renderContent()) == null ? undefined : _renderContent6.reloadTx : null,
          backgroundPreset: "light",
          statePreset: "default"
        })
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    // container styles moved to dynamic styles
    titleTextStyle: Object.assign({}, _text.presets.subTitleBold, {
      marginVertical: 16,
      textAlign: "center"
    }),
    messageTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    reloadButtonStyle: {
      width: "100%",
      borderRadius: 60,
      paddingHorizontal: 24,
      marginTop: 24
    }
  });
