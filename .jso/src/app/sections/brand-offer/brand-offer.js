  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.BrandOffer = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _color = _$$_REQUIRE(_dependencyMap[7]);
  var _spotlightBrandOffer = _$$_REQUIRE(_dependencyMap[8]);
  var _productOffer = _$$_REQUIRE(_dependencyMap[9]);
  var _productOffer2 = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _shopRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[14]));
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[15]);
  var _error = _$$_REQUIRE(_dependencyMap[16]);
  var _errorProps = _$$_REQUIRE(_dependencyMap[17]);
  var _lodash = _$$_REQUIRE(_dependencyMap[18]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[20]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[21]);
  var _theme = _$$_REQUIRE(_dependencyMap[22]);
  var _icons = _$$_REQUIRE(_dependencyMap[23]);
  var _adobe = _$$_REQUIRE(_dependencyMap[24]);
  var _deeplinkParameter = _$$_REQUIRE(_dependencyMap[25]);
  var _constants3 = _$$_REQUIRE(_dependencyMap[26]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[27]);
  var _enum = _$$_REQUIRE(_dependencyMap[28]);
  var _native = _$$_REQUIRE(_dependencyMap[29]);
  var _envParams = _$$_REQUIRE(_dependencyMap[30]);
  var _constants4 = _$$_REQUIRE(_dependencyMap[31]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _dineShopV = _$$_REQUIRE(_dependencyMap[33]);
  var _dineShopTabBar = _$$_REQUIRE(_dependencyMap[34]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[35]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var flatListStyle = {
    position: "absolute",
    top: 40
  };
  var flatListItemsStyle = {
    marginRight: 16,
    left: 9
  };
  var emptyHeaderStyle = {
    marginLeft: 16
  };
  var headerFlatListParentContainerStyle = {
    flexDirection: "row",
    marginEnd: 16
  };
  var specificBrandContainerStyle = {
    width: 114,
    marginStart: 25,
    marginTop: 30
  };
  var specificBrandLogoStyle = {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: "hidden"
  };
  var specificBrandTextContainerStyle = {
    marginTop: 16
  };
  var backgroundWaveGradientColorStyle = {
    backgroundColor: _color.color.palette.almostWhiteGrey
  };
  var specificBrandTitleStyle = {
    width: 114,
    overflow: "hidden"
  };
  var specificBrandSubTitleStyle = Object.assign({}, specificBrandTitleStyle, {
    marginTop: 8
  });
  var specificBrandTitleColorStyle = {
    color: _color.color.palette.whiteGrey
  };
  var buttonShopAll = {
    width: 93,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    borderRadius: 60,
    backgroundColor: _color.color.palette.whiteGrey,
    marginTop: 8,
    gap: 4
  };
  var txtButtonShopAll = {
    fontFamily: _theme.typography.bold,
    color: _color.color.palette.lightPurple,
    fontSize: 14,
    fontWeight: _reactNative2.Platform.select({
      ios: "700",
      android: "normal"
    }),
    lineHeight: 18
  };
  var handlePrice = function handlePrice(item) {
    var _item$originalPrice, _item$salePrice;
    var originalPrice = !(0, _lodash.isEmpty)(item == null ? undefined : item.originalPrice) && (0, _lodash.isArray)(item == null ? undefined : item.originalPrice) && (item == null || (_item$originalPrice = item.originalPrice) == null ? undefined : _item$originalPrice.find(function (e) {
      return (e == null ? undefined : e.currency) === "SGD";
    }));
    var salePrice = !(0, _lodash.isEmpty)(item == null ? undefined : item.salePrice) && (0, _lodash.isArray)(item == null ? undefined : item.originalPrice) && (item == null || (_item$salePrice = item.salePrice) == null ? undefined : _item$salePrice.find(function (e) {
      return (e == null ? undefined : e.currency) === "SGD";
    }));
    return Object.assign({}, item, {
      originalPrice: originalPrice == null ? undefined : originalPrice.value,
      salePrice: salePrice == null ? undefined : salePrice.value
    });
  };
  var renderFlatListData = function renderFlatListData(item, type, handleClick, index) {
    item.type = type;
    item.onPressed = function () {
      return handleClick(item, index);
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: flatListItemsStyle,
      children: (0, _jsxRuntime.jsx)(_productOffer.ProductOffer, Object.assign({}, handlePrice(item)))
    });
  };
  var lightGreyLoadingColors = [_color.color.palette.lightGrey, _color.color.background, _color.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_color.color.palette.lighterGrey, _color.color.background, _color.color.palette.lighterGrey];
  var loadingTextStyles = [{
    width: 100,
    height: 13,
    borderRadius: 4
  }, {
    width: 80,
    height: 13,
    borderRadius: 4,
    marginTop: 8
  }, {
    width: 60,
    height: 13,
    borderRadius: 4,
    marginTop: 8
  }, {
    width: 100,
    height: 13,
    borderRadius: 4,
    marginTop: 16
  }];
  var renderFlatListHeaderLoading = function renderFlatListHeaderLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: specificBrandContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: specificBrandLogoStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: specificBrandTextContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingTextStyles[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingTextStyles[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingTextStyles[2]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingTextStyles[3]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingTextStyles[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: loadingTextStyles[2]
        })]
      })]
    });
  };
  var renderFlatListHeader = function renderFlatListHeader(payload) {
    var navigation = (0, _native.useNavigation)();
    if (!payload) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: emptyHeaderStyle
      });
    }
    var sectionTitle = payload.sectionTitle,
      sectionSubTitle = payload.sectionSubTitle,
      logo = payload.logo;
    var handleNavigateCSMIShopchangi = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (url) {
        _globalLoadingController.default.showLoading();
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var target = (0, _deeplinkParameter.getISCInputParamsDeepLink)(url);
        var payload = {
          stateCode: _constants3.StateCode.ISHOPCHANGI,
          input: Object.assign({}, target, {
            ecid: ecid
          })
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            navigation.navigate(_constants3.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            navigation.navigate(_constants3.NavigationConstants.webview, {
              uri: url
            });
          }
        } catch (error) {
          navigation.navigate(_constants3.NavigationConstants.webview, {
            uri: url
          });
        } finally {
          _globalLoadingController.default.hideLoading();
        }
      });
      return function handleNavigateCSMIShopchangi(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var onPressShopAll = function onPressShopAll() {
      var _env;
      var ISHOPCHANGI_DOMAIN = ((_env = (0, _envParams.env)()) == null ? undefined : _env.ISHOPCHANGI_URL) || _constants4.DEFAULT_ISHOPCHANGI_URL;
      handleNavigateCSMIShopchangi(`${ISHOPCHANGI_DOMAIN}${_constants4.ISHOPCHANGI_HOMEPAGE_BRAND_OFFER}`);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: specificBrandContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        style: specificBrandLogoStyle,
        source: {
          uri: logo
        }
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: specificBrandTextContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: specificBrandTitleStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: sectionTitle,
            preset: "h4",
            style: specificBrandTitleColorStyle,
            numberOfLines: 4
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: specificBrandSubTitleStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: sectionSubTitle,
            preset: "caption1Regular",
            style: specificBrandTitleColorStyle,
            numberOfLines: 4
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: buttonShopAll,
        onPress: onPressShopAll,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "dineShopScreen.shopAll",
          style: txtButtonShopAll
        }), (0, _jsxRuntime.jsx)(_icons.ArrowRight, {
          width: 16,
          height: 16
        })]
      })]
    });
  };
  var BrandOffer = exports.BrandOffer = function BrandOffer(props) {
    var _handlers$shop, _pageLevelState$child, _handlers$shop3, _spotlightBrandOfferP2;
    var styleContainerProps = props.styleContainerProps,
      handleClickProps = props.handleClickProps,
      _props$isShowError = props.isShowError,
      isShowError = _props$isShowError === undefined ? true : _props$isShowError;
    var dispatch = (0, _reactRedux.useDispatch)();
    var sectionRef = (0, _react.useRef)(null);
    var isScrolledRef = (0, _react.useRef)(false);
    var _useDineShopFlags = (0, _dineShopV.useDineShopFlags)(),
      isShopDineEpicV2On = _useDineShopFlags.isShopDineEpicV2On;
    var handlers = (0, _react.useContext)(_dineShopContext.DineShopContext).Handlers;
    var handleClick = handlers == null || (_handlers$shop = handlers.shop) == null ? undefined : _handlers$shop.handleSpotlightBrandOfferComponentClick;
    var _React$useState = _react.default.useState(false),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      isOnFlagJFY = _React$useState2[0],
      setFlagJFY = _React$useState2[1];
    var _useSelector = (0, _reactRedux.useSelector)(function (data) {
        return _shopRedux.ShopSelectors.spotlightBrandOfferData(data);
      }),
      spotlightBrandOfferPayload = _useSelector.spotlightBrandOfferPayload,
      pageLevelState = _useSelector.pageLevelState;
    var isLoading = _productOffer2.ProductOfferType.loading === (spotlightBrandOfferPayload == null ? undefined : spotlightBrandOfferPayload.type);
    var containerStyle;
    var brandOfferIndex = pageLevelState == null || (_pageLevelState$child = pageLevelState.children) == null ? undefined : _pageLevelState$child.findIndex(function (item) {
      return (item == null ? undefined : item.name) === "brandOffer";
    });
    if (brandOfferIndex === 0) {
      containerStyle = {
        marginTop: 0
      };
    }
    var CONTAINER_MARGIN_BOTTOM = 50;
    var marginBottomStyle = {
      marginBottom: CONTAINER_MARGIN_BOTTOM
    };
    var loadSpotlightBrandOffer = function loadSpotlightBrandOffer() {
      dispatch(_shopRedux.default.shopSpotlightBrandOfferRequest());
    };
    (0, _react.useEffect)(function () {
      var checkShowJFY = function checkShowJFY() {
        var isOn = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.SHOP_JUSTFORYOU);
        setFlagJFY(isOn);
      };
      checkShowJFY();
    }, []);
    (0, _react.useEffect)(function () {
      if (isOnFlagJFY) {
        loadSpotlightBrandOffer();
      }
    }, [isOnFlagJFY]);
    (0, _react.useEffect)(function () {
      var _spotlightBrandOfferP;
      var _ref2 = handlers.shop.routeParams || {},
        section = _ref2.section,
        screen = _ref2.screen;
      if (!isLoading && !(isScrolledRef != null && isScrolledRef.current) && section === _constants2.SHOP_SECTIONS.ishopchangi && screen === _constants2.DINE_SHOP_TAB_SCREENS.shop && spotlightBrandOfferPayload != null && (_spotlightBrandOfferP = spotlightBrandOfferPayload.products) != null && _spotlightBrandOfferP.length) {
        setTimeout(function () {
          var _sectionRef$current, _handlers$shop2;
          sectionRef == null || (_sectionRef$current = sectionRef.current) == null || _sectionRef$current.measureLayout == null || _sectionRef$current.measureLayout((_handlers$shop2 = handlers.shop) == null || (_handlers$shop2 = _handlers$shop2.overallScrollRef) == null || (_handlers$shop2 = _handlers$shop2.current) == null || _handlers$shop2.getInnerViewNode == null ? undefined : _handlers$shop2.getInnerViewNode(), function (x, y) {
            var _handlers$shop$overal;
            isScrolledRef.current = true;
            var otherSectionsEffectValue = isShopDineEpicV2On ? _dineShopTabBar.NO_PADDING_TAB_BAR_HEIGHT + _dineShopTabBar.TAB_BAR_PADDING_N_REFINEMENT + CONTAINER_MARGIN_BOTTOM : 0;
            (_handlers$shop$overal = handlers.shop.overallScrollRef) == null || (_handlers$shop$overal = _handlers$shop$overal.current) == null || _handlers$shop$overal.scrollTo == null || _handlers$shop$overal.scrollTo({
              y: y - otherSectionsEffectValue,
              animated: true
            });
          });
        }, 2500);
      }
    }, [isLoading, isShopDineEpicV2On, sectionRef == null ? undefined : sectionRef.current, handlers == null || (_handlers$shop3 = handlers.shop) == null ? undefined : _handlers$shop3.routeParams, spotlightBrandOfferPayload == null || (_spotlightBrandOfferP2 = spotlightBrandOfferPayload.products) == null ? undefined : _spotlightBrandOfferP2.length]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      return function () {
        isScrolledRef.current = false;
      };
    }, []));

    // priority flag above the other conditions
    if (!isOnFlagJFY) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {});
    }
    if (spotlightBrandOfferPayload != null && spotlightBrandOfferPayload.errorFlag && isShowError) {
      return (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
        type: _errorProps.ErrorComponentType.standard,
        onPressed: function onPressed() {
          loadSpotlightBrandOffer();
        }
      });
    }
    if (!spotlightBrandOfferPayload || (spotlightBrandOfferPayload == null ? undefined : spotlightBrandOfferPayload.products.length) === 0) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
    var isTenantName = spotlightBrandOfferPayload.isTenantNameAvailable;
    var handleTenantName = function handleTenantName() {
      if (isLoading) {
        return (0, _jsxRuntime.jsx)(_spotlightBrandOffer.BackGroundGradientWithTenantName, {
          preserveAspectRatio: "xMinYMax slice",
          width: "100%",
          style: backgroundWaveGradientColorStyle
        });
      } else {
        if (isTenantName) {
          return (0, _jsxRuntime.jsx)(_spotlightBrandOffer.BackGroundGradientWithTenantName, {
            preserveAspectRatio: "xMinYMax slice",
            width: "100%",
            style: backgroundWaveGradientColorStyle
          });
        } else {
          return (0, _jsxRuntime.jsx)(_spotlightBrandOffer.BackGroundGradientWithoutTenantName, {
            preserveAspectRatio: "xMinYMax slice",
            width: "100%",
            style: backgroundWaveGradientColorStyle
          });
        }
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      ref: sectionRef,
      style: [containerStyle, marginBottomStyle, styleContainerProps],
      children: [handleTenantName(), (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        scrollEnabled: true,
        style: flatListStyle,
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        nestedScrollEnabled: true,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: headerFlatListParentContainerStyle,
          children: [isLoading ? renderFlatListHeaderLoading() : renderFlatListHeader(spotlightBrandOfferPayload), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
              data: spotlightBrandOfferPayload.products,
              renderItem: function renderItem(_ref3) {
                var item = _ref3.item,
                  index = _ref3.index;
                return renderFlatListData(item, spotlightBrandOfferPayload.type, handleClickProps ? handleClickProps : handleClick, index);
              },
              showsHorizontalScrollIndicator: false,
              horizontal: true,
              scrollEnabled: false
            })
          })]
        })
      })]
    });
  };
