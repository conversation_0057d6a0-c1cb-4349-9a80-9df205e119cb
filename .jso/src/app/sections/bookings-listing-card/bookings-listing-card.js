  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _bookingsListingCard = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _bookingsListingCard2 = _$$_REQUIRE(_dependencyMap[4]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var BookingsListingCard = function BookingsListingCard(props) {
    var isFirstCard = props.isFirstCard,
      item = props.item,
      _onPress = props.onPress;
    var imageUrl = item == null ? undefined : item.image;
    var title = item == null ? undefined : item.title;
    var passName = item == null ? undefined : item.passName;
    var quantity = item == null ? undefined : item.quantity;
    var location = item == null ? undefined : item.location;
    var dateTime = item == null ? undefined : item.startScheduledTxt;
    if ((item == null ? undefined : item.type) !== _bookingsListingCard.BookingType.Event) {
      passName = item == null ? undefined : item.productName;
      dateTime = item == null ? undefined : item.endScheduledTxt;
    }
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: function onPress() {
        return _onPress == null ? undefined : _onPress(item);
      },
      style: isFirstCard ? [_bookingsListingCard2.styles.containerStyle, _bookingsListingCard2.styles.firstCardContainerStyle] : _bookingsListingCard2.styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: imageUrl
        },
        style: _bookingsListingCard2.styles.imageStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _bookingsListingCard2.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _bookingsListingCard2.styles.headerContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _bookingsListingCard2.styles.titleContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _bookingsListingCard2.styles.typeTextStyle,
              tx: "bookingsOrdersScreen.bookingCardTitle"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 3,
              style: _bookingsListingCard2.styles.titleTextStyle,
              text: title
            }), passName && (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 3,
              style: _bookingsListingCard2.styles.passNameTextStyle,
              text: passName
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _bookingsListingCard2.styles.quantityContainerStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _bookingsListingCard2.styles.quantityTextStyle,
              text: quantity
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _bookingsListingCard2.styles.bottomContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _bookingsListingCard2.styles.bottomInfoContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.LocationOutline, {
              color: _theme.color.palette.darkestGrey,
              height: 12,
              width: 12
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              style: _bookingsListingCard2.styles.bottomInfoTextStyle,
              text: location
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _bookingsListingCard2.styles.bottomInfoContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.CalendarOutline, {
              color: _theme.color.palette.darkestGrey,
              height: 12,
              width: 12
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              style: _bookingsListingCard2.styles.bottomInfoTextStyle,
              text: dateTime
            })]
          })]
        })]
      })]
    });
  };
  var _default = exports.default = BookingsListingCard;
