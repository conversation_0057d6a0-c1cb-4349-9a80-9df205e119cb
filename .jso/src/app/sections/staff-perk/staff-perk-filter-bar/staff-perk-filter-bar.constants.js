  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TERMINAL_FILTER_DATA = exports.StaffPerkCategory = exports.STAFF_PERK_CATEGORY_PILLS = exports.COMPONENT_NAME = exports.AREA_FILTER_DATA = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[0]);
  var COMPONENT_NAME = exports.COMPONENT_NAME = "StaffPerkFilterBar";
  var StaffPerkCategory = exports.StaffPerkCategory = /*#__PURE__*/function (StaffPerkCategory) {
    StaffPerkCategory["NewlyAdded"] = "NEW_STAFF_PERKS";
    StaffPerkCategory["Dining"] = "DINING_PERKS";
    StaffPerkCategory["Shopping"] = "SHOPPING_PERKS";
    StaffPerkCategory["IShopChangiOffers"] = "ISHOPCHANGI_OFFERS";
    StaffPerkCategory["Services"] = "SERVICES";
    StaffPerkCategory["LimitedOffers"] = "SEASONAL";
    return StaffPerkCategory;
  }({});
  var AREA_FILTER_DATA = exports.AREA_FILTER_DATA = [{
    tagTitle: "Public",
    tagName: "public",
    filterType: "areas",
    sequenceNumber: 1000,
    childTags: []
  }, {
    tagTitle: "Transit",
    tagName: "transit",
    filterType: "areas",
    sequenceNumber: 1000,
    childTags: []
  }];
  var TERMINAL_FILTER_DATA = exports.TERMINAL_FILTER_DATA = [{
    tagTitle: "Jewel",
    tagName: "jewel-changi-airport",
    filterType: "locations",
    sequenceNumber: 1000,
    childTags: [],
    tagCode: "tj"
  }, {
    tagTitle: "Terminal 1",
    tagName: "terminal-1",
    filterType: "locations",
    sequenceNumber: 1000,
    childTags: [],
    tagCode: "t1"
  }, {
    tagTitle: "Terminal 2",
    tagName: "terminal-2",
    filterType: "locations",
    sequenceNumber: 1000,
    childTags: [],
    tagCode: "t2"
  }, {
    tagTitle: "Terminal 3",
    tagName: "terminal-3",
    filterType: "locations",
    sequenceNumber: 1000,
    childTags: [],
    tagCode: "t3"
  }, {
    tagTitle: "Terminal 4",
    tagName: "terminal-4",
    filterType: "locations",
    sequenceNumber: 1000,
    childTags: [],
    tagCode: "t4"
  }];
  var STAFF_PERK_CATEGORY_PILLS = exports.STAFF_PERK_CATEGORY_PILLS = [{
    label: "staffPerkListing.filterBar.categoryPill.newlyAdded",
    value: StaffPerkCategory.NewlyAdded
  }, {
    label: "staffPerkListing.filterBar.categoryPill.dining",
    value: StaffPerkCategory.Dining
  }, {
    label: "staffPerkListing.filterBar.categoryPill.shopping",
    value: StaffPerkCategory.Shopping
  }, {
    icon: _icons.IShopChangiIcon,
    label: "staffPerkListing.filterBar.categoryPill.iShopChangi",
    value: StaffPerkCategory.IShopChangiOffers
  }, {
    label: "staffPerkListing.filterBar.categoryPill.services",
    value: StaffPerkCategory.Services
  }, {
    icon: _icons.SaleTagIcon,
    label: "staffPerkListing.filterBar.categoryPill.limitedOffer",
    value: StaffPerkCategory.LimitedOffers
  }];
