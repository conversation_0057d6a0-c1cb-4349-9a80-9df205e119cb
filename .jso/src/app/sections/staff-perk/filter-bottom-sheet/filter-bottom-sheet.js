  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _filterBottomSheet = _$$_REQUIRE(_dependencyMap[6]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _filterPill = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _filterBottomSheet2 = _$$_REQUIRE(_dependencyMap[14]);
  var _sectionTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _checkboxOption = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_9313913433442_init_data = {
    code: "function filterBottomSheetTsx1(){const{color,underlinePosition,sectionTabWidth}=this.__closure;return{backgroundColor:color.palette.lightPurple,bottom:0,height:2,position:\"absolute\",transform:[{translateX:underlinePosition.value}],width:sectionTabWidth};}"
  };
  var _worklet_6418985463320_init_data = {
    code: "function filterBottomSheetTsx2(event){const{scrollY,categoryY,runOnJS,setActiveSection,FILTER_SECTION,underlinePosition,withTiming,sectionTabWidth,locationY}=this.__closure;const newY=event.contentOffset.y;scrollY.value=newY;if(newY>=categoryY.value-25){runOnJS(setActiveSection)(FILTER_SECTION.CATEGORIES);underlinePosition.value=withTiming(sectionTabWidth,{duration:200});}else if(newY>=locationY.value&&newY<locationY.value+24){runOnJS(setActiveSection)(FILTER_SECTION.LOCATION);underlinePosition.value=withTiming(0,{duration:200});}}"
  };
  var StaffPerkFilterBottomSheet = function StaffPerkFilterBottomSheet(props) {
    var areaFilterData = props.areaFilterData,
      areaFilters = props.areaFilters,
      categoryFilters = props.categoryFilters,
      focusTab = props.focusTab,
      originalFiltersRef = props.originalFiltersRef,
      setAreaFilters = props.setAreaFilters,
      setCategoryFilters = props.setCategoryFilters,
      setTerminalFilters = props.setTerminalFilters,
      setVisible = props.setVisible,
      terminalFilterData = props.terminalFilterData,
      terminalFilters = props.terminalFilters,
      visible = props.visible;
    var _useState = (0, _react.useState)(_filterBottomSheet2.FILTER_SECTION.LOCATION),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      activeSection = _useState2[0],
      setActiveSection = _useState2[1];
    var underlinePosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollY = (0, _reactNativeReanimated.useSharedValue)(0);
    var locationY = (0, _reactNativeReanimated.useSharedValue)(0);
    var categoryY = (0, _reactNativeReanimated.useSharedValue)(0);
    var sectionTabWidth = _reactNative.Dimensions.get("window").width / 2;
    var scrollRef = (0, _react.useRef)();
    var locationSectionRef = (0, _react.useRef)();
    var categorySectionRef = (0, _react.useRef)();
    var animatedUnderlineStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var filterBottomSheetTsx1 = function filterBottomSheetTsx1() {
        return {
          backgroundColor: _theme.color.palette.lightPurple,
          bottom: 0,
          height: 2,
          position: "absolute",
          transform: [{
            translateX: underlinePosition.value
          }],
          width: sectionTabWidth
        };
      };
      filterBottomSheetTsx1.__closure = {
        color: _theme.color,
        underlinePosition: underlinePosition,
        sectionTabWidth: sectionTabWidth
      };
      filterBottomSheetTsx1.__workletHash = 9313913433442;
      filterBottomSheetTsx1.__initData = _worklet_9313913433442_init_data;
      return filterBottomSheetTsx1;
    }(), [sectionTabWidth, underlinePosition.value, locationY.value, categoryY.value]);
    var moveToLocation = function moveToLocation() {
      var _locationSectionRef$c, _scrollRef$current;
      locationSectionRef == null || (_locationSectionRef$c = locationSectionRef.current) == null || _locationSectionRef$c.measureLayout == null || _locationSectionRef$c.measureLayout(scrollRef == null || (_scrollRef$current = scrollRef.current) == null ? undefined : _scrollRef$current.getInnerViewNode(), function (x, y) {
        // Scroll to the position of the ref
        scrollRef.current.scrollTo({
          y: y,
          animated: true
        });
      });
    };
    var moveToCategory = function moveToCategory() {
      var _categorySectionRef$c, _scrollRef$current2;
      categorySectionRef == null || (_categorySectionRef$c = categorySectionRef.current) == null || _categorySectionRef$c.measureLayout == null || _categorySectionRef$c.measureLayout(scrollRef == null || (_scrollRef$current2 = scrollRef.current) == null ? undefined : _scrollRef$current2.getInnerViewNode(), function (x, y) {
        // Scroll to the position of the ref
        scrollRef.current.scrollTo({
          y: y,
          animated: true
        });
      });
    };
    var moveToSection = function moveToSection(sectionName) {
      switch (sectionName) {
        case _filterBottomSheet2.FILTER_SECTION.LOCATION:
          moveToLocation();
          break;
        case _filterBottomSheet2.FILTER_SECTION.CATEGORIES:
          moveToCategory();
          break;
        default:
          break;
      }
    };
    var clearSectionOptions = function clearSectionOptions(sectionName) {
      switch (sectionName) {
        case _filterBottomSheet2.FILTER_SECTION.LOCATION:
          setAreaFilters([]);
          setTerminalFilters([]);
          break;
        case _filterBottomSheet2.FILTER_SECTION.CATEGORIES:
          setCategoryFilters([]);
          break;
        default:
          break;
      }
    };
    var handleToggleAreaOption = function handleToggleAreaOption(item, newValue) {
      setAreaFilters(function (list) {
        if (newValue) {
          return list == null || list.concat == null ? undefined : list.concat(item);
        }
        return list == null || list.filter == null ? undefined : list.filter(function (area) {
          return (area == null ? undefined : area.tagName) !== (item == null ? undefined : item.tagName);
        });
      });
    };
    var handleToggleLocationOption = function handleToggleLocationOption(item, newValue) {
      setTerminalFilters(function (list) {
        if (newValue) {
          var newList = list == null || list.concat == null ? undefined : list.concat(item);
          return _filterBottomSheet2.TERMINAL_SEQUENCE == null || _filterBottomSheet2.TERMINAL_SEQUENCE.reduce == null ? undefined : _filterBottomSheet2.TERMINAL_SEQUENCE.reduce(function (sequence, code) {
            var foundIndex = newList == null || newList.findIndex == null ? undefined : newList.findIndex(function (terminal) {
              return (terminal == null ? undefined : terminal.tagCode) === code;
            });
            var foundData = newList[foundIndex];
            if (foundData) {
              newList.splice(foundIndex, 1);
              return sequence.concat(foundData);
            }
            return sequence;
          }, []);
        }
        return list == null || list.filter == null ? undefined : list.filter(function (terminal) {
          return (terminal == null ? undefined : terminal.tagName) !== (item == null ? undefined : item.tagName);
        });
      });
    };
    var handleToggleCategoryOption = function handleToggleCategoryOption(item, newValue) {
      setCategoryFilters(function (list) {
        if (newValue) {
          return list == null || list.concat == null ? undefined : list.concat(item == null ? undefined : item.value);
        }
        return list == null || list.filter == null ? undefined : list.filter(function (val) {
          return val !== (item == null ? undefined : item.value);
        });
      });
    };
    var handleClearAllFilters = function handleClearAllFilters() {
      setAreaFilters([]);
      setTerminalFilters([]);
      setCategoryFilters([]);
    };
    var handleApplyFilters = function handleApplyFilters() {
      setVisible(false);
      var dataTrackingArea = areaFilters == null ? undefined : areaFilters.map(function (item) {
        return `${item == null ? undefined : item.tagTitle} Area`;
      }).join(", ");
      var dataTrackingTerminal = terminalFilters == null ? undefined : terminalFilters.map(function (item) {
        return `${item == null ? undefined : item.tagTitle}`;
      }).join(", ");
      var dataTrackingCategory = _filterBottomSheet2.CATEGORY_LIST.filter(function (item) {
        return categoryFilters == null ? undefined : categoryFilters.includes(item == null ? undefined : item.value);
      }).map(function (item) {
        return (0, _i18n.translate)(item == null ? undefined : item.label);
      }).join(", ");
      var dataTrackingAreaEmpty = areaFilterData == null ? undefined : areaFilterData.map(function (item) {
        return `${item == null ? undefined : item.tagTitle} Area`;
      }).join(", ");
      var dataTrackingTerminalEmpty = terminalFilterData == null ? undefined : terminalFilterData.map(function (item) {
        return `${item == null ? undefined : item.tagTitle}`;
      }).join(", ");
      var dataTrackingCategoryEmpty = _filterBottomSheet2.CATEGORY_LIST == null ? undefined : _filterBottomSheet2.CATEGORY_LIST.map(function (item) {
        return `${(0, _i18n.translate)(item == null ? undefined : item.label)}`;
      }).join(", ");
      var haveArea = (dataTrackingArea == null ? undefined : dataTrackingArea.length) > 0;
      var haveTerminal = (dataTrackingTerminal == null ? undefined : dataTrackingTerminal.length) > 0;
      var haveCategory = (dataTrackingCategory == null ? undefined : dataTrackingCategory.length) > 0;
      if (haveArea || haveTerminal || haveCategory) {
        var stringArea = `${haveArea ? dataTrackingArea : dataTrackingAreaEmpty} `;
        var stringTerminal = `: ${haveTerminal ? dataTrackingTerminal : dataTrackingTerminalEmpty} `;
        var stringCategory = `| ${haveCategory ? dataTrackingCategory : dataTrackingCategoryEmpty} `;
        var dataTrackingGalaxy = `Apply Filter | ${stringArea}${stringTerminal}${stringCategory}| Latest Added Date`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyFilters, dataTrackingGalaxy));
      } else if (!haveArea && !haveTerminal && !haveCategory) {
        var _dataTrackingGalaxy = `Apply Filter | ${dataTrackingAreaEmpty}: ${dataTrackingTerminalEmpty} | ${dataTrackingCategoryEmpty} | Latest Added Date`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyFilters, _dataTrackingGalaxy));
      }
      // Reset section tab underline's position
      underlinePosition.value = 0;
      setActiveSection(_filterBottomSheet2.FILTER_SECTION.LOCATION);
    };
    var handleCloseBS = function handleCloseBS() {
      var _originalFiltersRef$c, _originalFiltersRef$c2, _originalFiltersRef$c3;
      setAreaFilters(((_originalFiltersRef$c = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c.areaFilters) || []);
      setTerminalFilters(((_originalFiltersRef$c2 = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c2.terminalFilters) || []);
      setCategoryFilters(((_originalFiltersRef$c3 = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c3.categoryFilters) || []);
      setVisible(false);
      // Reset section tab underline's position
      underlinePosition.value = 0;
      setActiveSection(_filterBottomSheet2.FILTER_SECTION.LOCATION);
    };
    var handleScrollContent = (0, _reactNativeReanimated.useAnimatedScrollHandler)(function () {
      var filterBottomSheetTsx2 = function filterBottomSheetTsx2(event) {
        var newY = event.contentOffset.y;
        scrollY.value = newY;
        if (newY >= categoryY.value - 25) {
          (0, _reactNativeReanimated.runOnJS)(setActiveSection)(_filterBottomSheet2.FILTER_SECTION.CATEGORIES);
          underlinePosition.value = (0, _reactNativeReanimated.withTiming)(sectionTabWidth, {
            duration: 200
          });
        } else if (newY >= locationY.value && newY < locationY.value + 24) {
          (0, _reactNativeReanimated.runOnJS)(setActiveSection)(_filterBottomSheet2.FILTER_SECTION.LOCATION);
          underlinePosition.value = (0, _reactNativeReanimated.withTiming)(0, {
            duration: 200
          });
        }
      };
      filterBottomSheetTsx2.__closure = {
        scrollY: scrollY,
        categoryY: categoryY,
        runOnJS: _reactNativeReanimated.runOnJS,
        setActiveSection: setActiveSection,
        FILTER_SECTION: _filterBottomSheet2.FILTER_SECTION,
        underlinePosition: underlinePosition,
        withTiming: _reactNativeReanimated.withTiming,
        sectionTabWidth: sectionTabWidth,
        locationY: locationY
      };
      filterBottomSheetTsx2.__workletHash = 6418985463320;
      filterBottomSheetTsx2.__initData = _worklet_6418985463320_init_data;
      return filterBottomSheetTsx2;
    }(), []);
    (0, _react.useEffect)(function () {
      if (visible) {
        originalFiltersRef.current = {
          areaFilters: areaFilters,
          categoryFilters: categoryFilters,
          terminalFilters: terminalFilters
        };
        if (activeSection !== focusTab) {
          switch (focusTab) {
            case _filterBottomSheet2.FILTER_SECTION.LOCATION:
              setActiveSection(_filterBottomSheet2.FILTER_SECTION.LOCATION);
              underlinePosition.value = (0, _reactNativeReanimated.withTiming)(0, {
                duration: 200
              });
              moveToLocation();
              break;
            case _filterBottomSheet2.FILTER_SECTION.CATEGORIES:
              setActiveSection(_filterBottomSheet2.FILTER_SECTION.CATEGORIES);
              underlinePosition.value = (0, _reactNativeReanimated.withTiming)(sectionTabWidth, {
                duration: 200
              });
              setTimeout(function () {
                moveToCategory();
              }, 1);
              break;
            default:
              break;
          }
        }
      }
    }, [visible, focusTab]);
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      animationInTiming: 400,
      animationOutTiming: 400,
      containerStyle: _filterBottomSheet.styles.containerStyle,
      isModalVisible: visible,
      onClosedSheet: handleCloseBS,
      stopDragCollapse: true,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _filterBottomSheet.styles.headerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _filterBottomSheet.styles.titleTextStyle,
          tx: "staffPerkListing.filterBs.title"
        }), (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handleCloseBS,
          style: _filterBottomSheet.styles.closeBtnStyle,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            color: _theme.color.palette.darkestGrey,
            height: 24,
            width: 24
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _filterBottomSheet.styles.sectionTabBarContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_sectionTab.default, {
          activeTab: activeSection,
          isActiveDot: !!(areaFilters != null && areaFilters.length) || !!(terminalFilters != null && terminalFilters.length),
          label: (0, _i18n.translate)("staffPerkListing.filterBs.sectionLocation"),
          name: _filterBottomSheet2.FILTER_SECTION.LOCATION,
          onPress: function onPress() {
            return moveToSection(_filterBottomSheet2.FILTER_SECTION.LOCATION);
          }
        }), (0, _jsxRuntime.jsx)(_sectionTab.default, {
          activeTab: activeSection,
          isActiveDot: !!(categoryFilters != null && categoryFilters.length),
          label: (0, _i18n.translate)("staffPerkListing.filterBs.sectionCategories"),
          name: _filterBottomSheet2.FILTER_SECTION.CATEGORIES,
          onPress: function onPress() {
            return moveToSection(_filterBottomSheet2.FILTER_SECTION.CATEGORIES);
          }
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: animatedUnderlineStyle
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.ScrollView, {
        onScroll: handleScrollContent,
        ref: scrollRef,
        showsVerticalScrollIndicator: false,
        style: _filterBottomSheet.styles.filterFormContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          onLayout: function onLayout(e) {
            return locationY.value = e.nativeEvent.layout.y;
          },
          ref: locationSectionRef,
          style: _filterBottomSheet.styles.sectionContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _filterBottomSheet.styles.sectionHeaderContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _filterBottomSheet.styles.sectionTitleTextStyle,
              tx: "staffPerkListing.filterBs.location.title"
            }), (!!(areaFilters != null && areaFilters.length) || !!(terminalFilters != null && terminalFilters.length)) && (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
              onPress: function onPress() {
                return clearSectionOptions(_filterBottomSheet2.FILTER_SECTION.LOCATION);
              },
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _filterBottomSheet.styles.sectionClearBtnStyle,
                tx: "staffPerkListing.filterBs.clearBtn"
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _filterBottomSheet.styles.areaFiltersContainerStyle,
            children: areaFilterData == null || areaFilterData.map == null ? undefined : areaFilterData.map(function (item) {
              var isActive = areaFilters == null || areaFilters.some == null ? undefined : areaFilters.some(function (area) {
                return (area == null ? undefined : area.tagName) === (item == null ? undefined : item.tagName);
              });
              var itemTx = "";
              var Icon = function Icon() {
                return null;
              };
              switch (item == null ? undefined : item.tagName) {
                case "public":
                  itemTx = "staffPerkListing.filterBs.location.publicArea";
                  Icon = _icons.PublicAreaIcon;
                  break;
                case "transit":
                  itemTx = "staffPerkListing.filterBs.location.transitArea";
                  Icon = isActive ? _icons.TransitAreaFillIcon : _icons.TransitAreaIcon;
                  break;
                default:
                  break;
              }
              return (0, _jsxRuntime.jsx)(_filterPill.default, {
                active: isActive,
                size: "lg",
                label: (0, _i18n.translate)(itemTx),
                labelColor: _theme.color.palette.almostBlackGrey,
                IconComponent: Icon,
                onPress: function onPress() {
                  return handleToggleAreaOption(item, !isActive);
                },
                variant: "outline"
              });
            })
          }), terminalFilterData == null || terminalFilterData.map == null ? undefined : terminalFilterData.map(function (item) {
            var checked = terminalFilters == null || terminalFilters.some == null ? undefined : terminalFilters.some(function (terminal) {
              return (terminal == null ? undefined : terminal.tagName) === (item == null ? undefined : item.tagName);
            });
            return (0, _jsxRuntime.jsx)(_checkboxOption.default, {
              checked: checked,
              label: item.tagTitle,
              onCheck: function onCheck(newValue) {
                return handleToggleLocationOption(item, newValue);
              }
            }, `${item.tagName}_${checked}`);
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          onLayout: function onLayout(e) {
            return categoryY.value = e.nativeEvent.layout.y;
          },
          ref: categorySectionRef,
          style: [_filterBottomSheet.styles.sectionContainerStyle, _filterBottomSheet.styles.lastSectionContainerStyle],
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: [_filterBottomSheet.styles.sectionHeaderContainerStyle, _filterBottomSheet.styles.sectionHeaderNoMarginContainerStyle],
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _filterBottomSheet.styles.sectionTitleTextStyle,
              tx: "staffPerkListing.filterBs.category.title"
            }), !!(categoryFilters != null && categoryFilters.length) && (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
              onPress: function onPress() {
                return clearSectionOptions(_filterBottomSheet2.FILTER_SECTION.CATEGORIES);
              },
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _filterBottomSheet.styles.sectionClearBtnStyle,
                tx: "staffPerkListing.filterBs.clearBtn"
              })
            })]
          }), _filterBottomSheet2.CATEGORY_LIST.map(function (item, index) {
            var checked = categoryFilters == null || categoryFilters.some == null ? undefined : categoryFilters.some(function (val) {
              return val === item.value;
            });
            return (0, _jsxRuntime.jsx)(_checkboxOption.default, {
              checked: checked,
              containerStyle: index === 0 ? {
                marginTop: 12
              } : undefined,
              label: (0, _i18n.translate)(item == null ? undefined : item.label),
              onCheck: function onCheck(newValue) {
                return handleToggleCategoryOption(item, newValue);
              }
            }, `${item.value}_${checked}`);
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _filterBottomSheet.styles.footerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handleClearAllFilters,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _filterBottomSheet.styles.clearAllBtnStyle,
            tx: "staffPerkListing.filterBs.clearAllBtn"
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _filterBottomSheet.styles.applyFiltersBtnContainerStyle,
          start: {
            x: 0,
            y: 1
          },
          end: {
            x: 1,
            y: 0
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            onPress: handleApplyFilters,
            style: _filterBottomSheet.styles.applyFiltersBtnStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _filterBottomSheet.styles.applyFiltersBtnLabelTextStyle,
              tx: "staffPerkListing.filterBs.applyFiltersBtn"
            })
          })
        })]
      })]
    });
  };
  var _default = exports.default = StaffPerkFilterBottomSheet;
