  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TerminalFilter = exports.TERMINAL_SEQUENCE = exports.SortBy = exports.FILTER_SECTION = exports.CATEGORY_LIST = exports.AreaFilter = undefined;
  var _staffPerkFilterBar = _$$_REQUIRE(_dependencyMap[0]);
  var FILTER_SECTION = exports.FILTER_SECTION = {
    LOCATION: "location",
    CATEGORIES: "categories"
  };
  var AreaFilter = exports.AreaFilter = /*#__PURE__*/function (AreaFilter) {
    AreaFilter["Public"] = "public";
    AreaFilter["Transit"] = "transit";
    return AreaFilter;
  }({});
  var TerminalFilter = exports.TerminalFilter = /*#__PURE__*/function (TerminalFilter) {
    TerminalFilter["Jewel"] = "jewel";
    TerminalFilter["Terminal1"] = "t1";
    TerminalFilter["Terminal2"] = "t2";
    TerminalFilter["Terminal3"] = "t3";
    TerminalFilter["Terminal4"] = "t4";
    return TerminalFilter;
  }({});
  var SortBy = exports.SortBy = /*#__PURE__*/function (SortBy) {
    SortBy["LatestAddedDate"] = "latestAddedDate";
    SortBy["AZ"] = "az";
    return SortBy;
  }({});
  var CATEGORY_LIST = exports.CATEGORY_LIST = [{
    label: "staffPerkListing.filterBs.category.newlyAdded",
    value: _staffPerkFilterBar.StaffPerkCategory.NewlyAdded
  }, {
    label: "staffPerkListing.filterBs.category.dining",
    value: _staffPerkFilterBar.StaffPerkCategory.Dining
  }, {
    label: "staffPerkListing.filterBs.category.shopping",
    value: _staffPerkFilterBar.StaffPerkCategory.Shopping
  }, {
    label: "staffPerkListing.filterBs.category.iShopChangiOffers",
    value: _staffPerkFilterBar.StaffPerkCategory.IShopChangiOffers
  }, {
    label: "staffPerkListing.filterBs.category.services",
    value: _staffPerkFilterBar.StaffPerkCategory.Services
  }, {
    label: "staffPerkListing.filterBs.category.limitedOffers",
    value: _staffPerkFilterBar.StaffPerkCategory.LimitedOffers
  }];
  var TERMINAL_SEQUENCE = exports.TERMINAL_SEQUENCE = ['t1', 't2', 't3', 't4', 'tj'];
