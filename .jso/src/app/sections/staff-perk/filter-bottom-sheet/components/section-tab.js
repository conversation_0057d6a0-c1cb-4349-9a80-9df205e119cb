  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[0]);
  var _utils = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var SectionTab = function SectionTab(props) {
    var activeTab = props.activeTab,
      disabled = props.disabled,
      isActiveDot = props.isActiveDot,
      label = props.label,
      name = props.name,
      onPress = props.onPress;
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      disabled: disabled,
      onPress: onPress,
      style: styles.sectionTabContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: (0, _utils.handleCondition)(name === activeTab, [styles.sectionTabTextStyle, styles.sectionTabActiveTextStyle], styles.sectionTabTextStyle),
        text: label
      }), isActiveDot && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.tabActiveDotStyle
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    sectionTabContainerStyle: {
      alignItems: "center",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey,
      flexDirection: "row",
      justifyContent: "center",
      height: 40,
      width: _reactNative.Dimensions.get("window").width / 2
    },
    sectionTabTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.darkGrey999
    }),
    sectionTabActiveTextStyle: {
      color: _theme.color.palette.almostBlackGrey
    },
    tabActiveDotStyle: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 5,
      height: 8,
      marginLeft: 8,
      width: 8
    }
  });
  var _default = exports.default = SectionTab;
