  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreMoreRewards = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[3]);
  var _lodash = _$$_REQUIRE(_dependencyMap[4]);
  var _redemptionCatalogueCard = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _redemptionCatalogueCard2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _error = _$$_REQUIRE(_dependencyMap[8]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _redeemRewardDetailRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingData = Array(5).fill({
    type: _redemptionCatalogueCard.RedemptionCatalogueType.loading
  });
  var ExploreMoreRewards = exports.ExploreMoreRewards = function ExploreMoreRewards(props) {
    var title = props.title,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ExploreMoreRewards" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ExploreMoreRewards" : _props$accessibilityL;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isError = (0, _reactRedux.useSelector)(_redeemRewardDetailRedux.RedeemRewardDetailSelectors.moreRewardsError);
    var payload = (0, _reactRedux.useSelector)(_redeemRewardDetailRedux.RedeemRewardDetailSelectors.moreRewardsPayload);
    var isFetching = (0, _reactRedux.useSelector)(_redeemRewardDetailRedux.RedeemRewardDetailSelectors.moreRewardsFetching);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var tier = (0, _react.useMemo)(function () {
      var _rewardsData$reward;
      if (!(0, _lodash.isEmpty)(rewardsData)) return rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null ? undefined : _rewardsData$reward.currentTierInfo;
    }, [rewardsData]);
    (0, _react.useEffect)(function () {
      dispatch(_redeemRewardDetailRedux.default.exploreMoreRewardsRequest((profilePayload == null ? undefined : profilePayload.cardNo) || "", tier || ""));
    }, []);
    var renderSection = function renderSection(data) {
      return (0, _jsxRuntime.jsxs)(_reactNativeGestureHandler.GestureHandlerRootView, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          preset: "h4",
          tx: title
        }), (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.FlatList, {
          contentContainerStyle: styles.contentContainerStyle,
          data: data,
          renderItem: function renderItem(_ref) {
            var item = _ref.item,
              index = _ref.index;
            return (0, _jsxRuntime.jsx)(_redemptionCatalogueCard2.default, Object.assign({}, item, {
              index: index,
              testID: `${testID}__RedemptionCatalogueCard`,
              accessibilityLabel: `${accessibilityLabel}__RedemptionCatalogueCard`
            }));
          },
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          keyExtractor: function keyExtractor(_, index) {
            return index.toString();
          },
          testID: `${testID}__FlatList`,
          accessibilityLabel: `${accessibilityLabel}__FlatList`
        })]
      });
    };
    if (isFetching) {
      return renderSection(loadingData);
    }
    if ((payload == null ? undefined : payload.length) > 0) {
      return renderSection(payload);
    }
    if (isError) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          preset: "h4",
          tx: title
        }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          style: styles.errorStyle,
          type: _error.ErrorComponentType.standard,
          onPressed: function onPressed() {
            return dispatch(_redeemRewardDetailRedux.default.exploreMoreRewardsRequest((profilePayload == null ? undefined : profilePayload.cardNo) || "", tier || ""));
          },
          testID: `${testID}__ErrorComponent`,
          accessibilityLabel: `${accessibilityLabel}__ErrorComponent`
        })]
      });
    }
    return null;
  };
