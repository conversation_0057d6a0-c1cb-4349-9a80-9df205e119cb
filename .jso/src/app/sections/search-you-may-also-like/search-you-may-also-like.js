  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _recommendation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _recommendationProps = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _searchRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var numColumns = 3;
  var loadingArr = [{}, {}, {}];
  var SearchYouMayAlsoLike = function SearchYouMayAlsoLike(props) {
    var containerStyles = props.containerStyles,
      flatListStyles = props.flatListStyles,
      emptyScreenStyles = props.emptyScreenStyles;
    var getYouMayAlsoLikeLoading = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.getYouMayAlsoLikeLoading);
    var listSearchYouMayAlsoLike = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.listSearchYouMayAlsoLike);
    var getItemStyles = function getItemStyles(index) {
      return {
        paddingHorizontal: (0, _utils.simpleCondition)({
          condition: isMiddleItem(index),
          ifValue: 16,
          elseValue: 0
        })
      };
    };
    var _renderItem = function renderItem(item, index) {
      var logoImage = item.logoImage,
        title = item.title,
        locationDisplay = item.locationDisplay;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: getItemStyles(index),
        onPress: function onPress() {
          (props == null ? undefined : props.itemOnPress) !== undefined && props.itemOnPress(item);
        },
        children: (0, _jsxRuntime.jsx)(_recommendation.default, {
          state: (0, _utils.simpleCondition)({
            condition: getYouMayAlsoLikeLoading,
            ifValue: _recommendationProps.RecommendationState.loading,
            elseValue: _recommendationProps.RecommendationState.default
          }),
          logoImage: logoImage,
          title: title,
          locationDisplay: locationDisplay
        })
      });
    };
    var getMiddleIndexes = function getMiddleIndexes(dataLength, numColumns) {
      var middleIndexes = [];
      for (var i = 0; i < dataLength; i += numColumns) {
        var middleIndex = Math.floor(i + numColumns / 2);
        middleIndexes.push(middleIndex);
      }
      return middleIndexes;
    };
    var isMiddleItem = function isMiddleItem(index) {
      return middleIndexes.includes(index);
    };
    var middleIndexes = getMiddleIndexes(getYouMayAlsoLikeLoading ? numColumns : listSearchYouMayAlsoLike == null ? undefined : listSearchYouMayAlsoLike.length, numColumns);
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: !getYouMayAlsoLikeLoading && (listSearchYouMayAlsoLike == null ? undefined : listSearchYouMayAlsoLike.length) === 0 ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [Object.assign({}, styles.emptyScreen), emptyScreenStyles]
      }) : (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [Object.assign({}, styles.container), containerStyles],
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: "YOU MAY ALSO LIKE",
            preset: "subTitleBold",
            style: styles.titleTextStyles
          }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            data: getYouMayAlsoLikeLoading ? loadingArr : listSearchYouMayAlsoLike,
            scrollEnabled: true,
            renderItem: function renderItem(_ref) {
              var item = _ref.item,
                index = _ref.index;
              return _renderItem(item, index);
            },
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            },
            numColumns: 3,
            style: [Object.assign({}, styles.flatListStyles), flatListStyles]
          })]
        })
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginTop: 40,
      paddingHorizontal: 24
    },
    emptyScreen: {
      height: 94
    },
    flatListStyles: {
      paddingBottom: 94,
      width: "100%"
    },
    titleTextStyles: {
      color: _theme.color.palette.almostBlackGrey
    }
  });
  var _default = exports.default = SearchYouMayAlsoLike;
