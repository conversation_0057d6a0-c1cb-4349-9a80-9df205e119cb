  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _dealsPromosFilterBar = _$$_REQUIRE(_dependencyMap[3]);
  var _dealsPromosFilterBar2 = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _filterPill = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _filterBottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _filterBottomSheet2 = _$$_REQUIRE(_dependencyMap[10]);
  var _utils = _$$_REQUIRE(_dependencyMap[11]);
  var _theme = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DealsPromosFilterBar = function DealsPromosFilterBar(_ref) {
    var isShow = _ref.isShow,
      areaFilters = _ref.areaFilters,
      setAreaFilters = _ref.setAreaFilters,
      terminalFilters = _ref.terminalFilters,
      setTerminalFilters = _ref.setTerminalFilters,
      categoryFilters = _ref.categoryFilters,
      setCategoryFilters = _ref.setCategoryFilters,
      sortBy = _ref.sortBy,
      setSortBy = _ref.setSortBy;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isBSVisible = _useState2[0],
      setIsBSVisible = _useState2[1];
    var originalFiltersRef = (0, _react.useRef)({});
    var publicArea = (0, _react.useMemo)(function () {
      var item = _filterBottomSheet2.AREA_LIST.find(function (area) {
        return area.value === "public";
      });
      return {
        tagName: item == null ? undefined : item.value,
        tagTitle: (0, _i18n.translate)(item == null ? undefined : item.label),
        tagCode: item == null ? undefined : item.value
      };
    }, []);
    var transitArea = (0, _react.useMemo)(function () {
      var item = _filterBottomSheet2.AREA_LIST.find(function (area) {
        return area.value === "transit";
      });
      return {
        tagName: item == null ? undefined : item.value,
        tagTitle: (0, _i18n.translate)(item == null ? undefined : item.label),
        tagCode: item == null ? undefined : item.value
      };
    }, []);
    var locationDisplayText = (0, _react.useMemo)(function () {
      if (!(areaFilters != null && areaFilters.length) && !(terminalFilters != null && terminalFilters.length)) {
        return (0, _i18n.translate)("dealsPromosListing.filterBs.location.title");
      }
      // Get terminal display text
      var terminalDisplayText = (0, _i18n.translate)("dealsPromosListing.filterBs.location.title");
      if (terminalFilters != null && terminalFilters.length && (terminalFilters == null ? undefined : terminalFilters.length) < (_filterBottomSheet2.TERMINAL_LIST == null ? undefined : _filterBottomSheet2.TERMINAL_LIST.length)) {
        var _terminalFilters$map;
        terminalDisplayText = terminalFilters == null || terminalFilters.map == null || (_terminalFilters$map = terminalFilters.map(function (terminal) {
          var _terminal$tagCode;
          if ((terminal == null ? undefined : terminal.tagCode) === "jewel") {
            return terminal == null ? undefined : terminal.tagTitle;
          }
          return terminal == null || (_terminal$tagCode = terminal.tagCode) == null || _terminal$tagCode.toUpperCase == null ? undefined : _terminal$tagCode.toUpperCase();
        })) == null || _terminalFilters$map.join == null ? undefined : _terminalFilters$map.join(", ");
      }
      return terminalDisplayText;
    }, [JSON.stringify(terminalFilters)]);
    var handlePressFilterIcon = function handlePressFilterIcon() {
      setIsBSVisible(true);
    };
    var handlePressFilterPill = function handlePressFilterPill(item, newValue) {
      setCategoryFilters(function (list) {
        var newList = list == null || list.filter == null ? undefined : list.filter(function (val) {
          return val !== (item == null ? undefined : item.value);
        });
        if (newValue) {
          newList = list == null || list.concat == null ? undefined : list.concat(item == null ? undefined : item.value);
        }
        originalFiltersRef.current = Object.assign({}, originalFiltersRef.current, {
          categoryFilters: newList
        });
        return newList;
      });
    };
    var handlePressAreaFilterPill = function handlePressAreaFilterPill(item, newValue) {
      setAreaFilters(function (list) {
        var newList = list == null || list.filter == null ? undefined : list.filter(function (val) {
          return (val == null ? undefined : val.tagName) !== (item == null ? undefined : item.tagName);
        });
        if (newValue) {
          newList = list == null || list.concat == null ? undefined : list.concat(item);
        }
        originalFiltersRef.current = Object.assign({}, originalFiltersRef.current, {
          areaFilters: newList
        });
        return newList;
      });
    };
    var handleToggleSortBy = function handleToggleSortBy() {
      setSortBy(function (val) {
        if (val === _filterBottomSheet2.SortBy.LatestAddedDate) {
          return _filterBottomSheet2.SortBy.AZ;
        }
        return _filterBottomSheet2.SortBy.LatestAddedDate;
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Container`,
        style: [_dealsPromosFilterBar2.styles.containerStyle, {
          opacity: isShow ? 1 : 0
        }],
        testID: `${_dealsPromosFilterBar.COMPONENT_NAME}_Container`,
        children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
          accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Pill_Container`,
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          style: [_dealsPromosFilterBar2.styles.filterPillsScrollViewStyle],
          testID: `${_dealsPromosFilterBar.COMPONENT_NAME}_Pill_Container`,
          contentContainerStyle: [_dealsPromosFilterBar2.styles.filterPillsContainerStyle],
          pointerEvents: !isShow ? "none" : "auto",
          children: [(0, _jsxRuntime.jsx)(_filterPill.default, {
            accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Icon_Category`,
            active: !!(categoryFilters != null && categoryFilters.length) || sortBy !== _filterBottomSheet2.SortBy.LatestAddedDate || !!(terminalFilters != null && terminalFilters.length) || !!(areaFilters != null && areaFilters.length),
            IconComponent: _icons.Filter,
            label: "",
            onPress: handlePressFilterIcon,
            variant: "outline",
            containerStyle: {
              minWidth: 36,
              flexDirection: "row",
              borderColor: !!(categoryFilters != null && categoryFilters.length) || sortBy !== _filterBottomSheet2.SortBy.LatestAddedDate || !!(terminalFilters != null && terminalFilters.length) || !!(areaFilters != null && areaFilters.length) ? _theme.color.palette.purpleD5BBEA : _theme.color.palette.lighterGrey
            },
            iconSize: 16,
            showDot: true
          }, "filter_category"), (0, _jsxRuntime.jsx)(_filterPill.default, {
            accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Icon_Sort`,
            active: sortBy !== _filterBottomSheet2.SortBy.LatestAddedDate,
            IconComponent: _icons.SortWithArrowIcon,
            label: (0, _i18n.translate)("dealsPromosListing.filterBar.sortBy.az"),
            onPress: handleToggleSortBy,
            variant: "outline",
            containerStyle: {
              minWidth: 40,
              flexDirection: "row",
              gap: 2,
              borderColor: sortBy !== _filterBottomSheet2.SortBy.LatestAddedDate ? _theme.color.palette.purpleD5BBEA : _theme.color.palette.lighterGrey
            },
            iconSize: 16,
            showDot: true
          }, "filter_sort"), (0, _jsxRuntime.jsx)(_filterPill.default, {
            accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Icon_Location`,
            active: !!(terminalFilters != null && terminalFilters.length),
            IconComponent: _icons.LocationOutline,
            label: locationDisplayText,
            onPress: handlePressFilterIcon,
            variant: "outline",
            containerStyle: {
              flexDirection: "row",
              gap: 4,
              borderColor: !!(terminalFilters != null && terminalFilters.length) ? _theme.color.palette.purpleD5BBEA : _theme.color.palette.lighterGrey
            },
            rightIconComponent: (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
              color: (0, _utils.handleCondition)(!!(terminalFilters != null && terminalFilters.length), _theme.color.palette.lighterPurple, _theme.color.palette.darkestGrey),
              height: 16,
              width: 16
            }),
            iconSize: 16
          }, "filter_location"), (0, _jsxRuntime.jsx)(_filterPill.default, {
            accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Pill_Public`,
            active: areaFilters == null || areaFilters.some == null ? undefined : areaFilters.some(function (item) {
              return item.tagName === "public";
            }),
            label: (0, _i18n.translate)("dealsPromosListing.filterBar.area.public"),
            onPress: function onPress() {
              var isActive = areaFilters == null || areaFilters.some == null ? undefined : areaFilters.some(function (item) {
                return item.tagName === "public";
              });
              handlePressAreaFilterPill(publicArea, !isActive);
            },
            variant: "outline",
            containerStyle: {
              borderColor: areaFilters != null && areaFilters.some != null && areaFilters.some(function (item) {
                return item.tagName === "public";
              }) ? _theme.color.palette.purpleD5BBEA : _theme.color.palette.lighterGrey
            }
          }, "filter_public"), (0, _jsxRuntime.jsx)(_filterPill.default, {
            accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Pill_Transit`,
            active: areaFilters == null || areaFilters.some == null ? undefined : areaFilters.some(function (item) {
              return item.tagName === "transit";
            }),
            label: (0, _i18n.translate)("dealsPromosListing.filterBar.area.transit"),
            onPress: function onPress() {
              var isActive = areaFilters == null || areaFilters.some == null ? undefined : areaFilters.some(function (item) {
                return item.tagName === "transit";
              });
              handlePressAreaFilterPill(transitArea, !isActive);
            },
            variant: "outline",
            containerStyle: {
              borderColor: areaFilters != null && areaFilters.some != null && areaFilters.some(function (item) {
                return item.tagName === "transit";
              }) ? _theme.color.palette.purpleD5BBEA : _theme.color.palette.lighterGrey
            }
          }, "filter_transit"), _dealsPromosFilterBar.DEALS_PROMOS_CATEGORY_PILLS.map(function (item) {
            var isActive = categoryFilters.some(function (val) {
              return val === item.value;
            });
            return (0, _jsxRuntime.jsx)(_filterPill.default, {
              accessibilityLabel: `${_dealsPromosFilterBar.COMPONENT_NAME}_Pill_Item_${item.value}`,
              active: isActive,
              IconComponent: item == null ? undefined : item.icon,
              label: (0, _i18n.translate)(item.label),
              onPress: function onPress() {
                return handlePressFilterPill(item, !isActive);
              },
              variant: "outline",
              containerStyle: {
                borderColor: isActive ? _theme.color.palette.purpleD5BBEA : _theme.color.palette.lighterGrey
              },
              iconSize: 16
            }, `${item.value}${isActive ? "_active" : ""}`);
          })]
        })
      }), (0, _jsxRuntime.jsx)(_filterBottomSheet.default, {
        areaFilters: areaFilters,
        categoryFilters: categoryFilters,
        originalFiltersRef: originalFiltersRef,
        setAreaFilters: setAreaFilters,
        setCategoryFilters: setCategoryFilters,
        setTerminalFilters: setTerminalFilters,
        setVisible: setIsBSVisible,
        terminalFilters: terminalFilters,
        visible: isBSVisible
      })]
    });
  };
  var _default = exports.default = DealsPromosFilterBar;
