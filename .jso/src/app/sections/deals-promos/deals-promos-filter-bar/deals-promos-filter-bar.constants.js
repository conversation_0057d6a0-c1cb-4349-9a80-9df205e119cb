  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DealsPromosCategory = exports.DEALS_PROMOS_CATEGORY_PILLS = exports.COMPONENT_NAME = undefined;
  var _icons = _$$_REQUIRE(_dependencyMap[0]);
  var COMPONENT_NAME = exports.COMPONENT_NAME = "DealsPromosFilterBar";
  var DealsPromosCategory = exports.DealsPromosCategory = /*#__PURE__*/function (DealsPromosCategory) {
    DealsPromosCategory["NewlyAdded"] = "NEW_STAFF_PERKS";
    DealsPromosCategory["Dining"] = "DINING_PERKS";
    DealsPromosCategory["Shopping"] = "SHOPPING_PERKS";
    DealsPromosCategory["IShopChangiOffers"] = "ISHOPCHANGI_OFFERS";
    DealsPromosCategory["Services"] = "SERVICES";
    DealsPromosCategory["LimitedOffers"] = "SEASONAL";
    return DealsPromosCategory;
  }({});
  var DEALS_PROMOS_CATEGORY_PILLS = exports.DEALS_PROMOS_CATEGORY_PILLS = [{
    label: "dealsPromosListing.filterBar.categoryPill.newlyAdded",
    value: DealsPromosCategory.NewlyAdded
  }, {
    label: "dealsPromosListing.filterBar.categoryPill.dining",
    value: DealsPromosCategory.Dining
  }, {
    label: "dealsPromosListing.filterBar.categoryPill.shopping",
    value: DealsPromosCategory.Shopping
  }, {
    label: "dealsPromosListing.filterBar.categoryPill.services",
    value: DealsPromosCategory.Services
  }, {
    icon: _icons.IShopChangiIcon,
    label: "dealsPromosListing.filterBar.categoryPill.iShopChangi",
    value: DealsPromosCategory.IShopChangiOffers
  }, {
    icon: _icons.SaleTagIcon,
    label: "dealsPromosListing.filterBar.categoryPill.limitedOffer",
    value: DealsPromosCategory.LimitedOffers
  }];
