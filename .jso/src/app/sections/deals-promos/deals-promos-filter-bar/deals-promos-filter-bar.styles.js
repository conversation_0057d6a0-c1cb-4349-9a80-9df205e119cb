  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.INDEX_ITEM_HEIGHT = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var INDEX_ITEM_HEIGHT = exports.INDEX_ITEM_HEIGHT = 14;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      overflow: "hidden",
      justifyContent: 'center',
      height: 70
    },
    filterIconContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.halfLighterGrey,
      borderRadius: 16,
      flexDirection: "row",
      height: 32,
      justifyContent: "space-between",
      minWidth: 40,
      paddingHorizontal: 12
    },
    filterIconFullContainerStyle: {
      flex: 1
    },
    filterIconIconOnlyContainerStyle: {
      justifyContent: "center"
    },
    filterIconSubIconStyle: {
      paddingHorizontal: 9
    },
    filterIconTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    filterPillsScrollViewStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    filterPillsContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      columnGap: 8,
      paddingHorizontal: 20,
      alignItems: "center"
    },
    activeDotIconStyle: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderWidth: 1,
      borderColor: _theme.color.palette.whiteGrey,
      borderRadius: 5,
      height: 9,
      position: "absolute",
      right: 0,
      top: 0,
      width: 9
    },
    alphabeticalIndexSearchContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      position: "absolute",
      right: 0,
      top: 224,
      // 144px (filter bar container) + 80px (gap between)
      width: 15
    },
    indexContainerStyle: {
      alignItems: "center",
      height: 14,
      justifyContent: "center",
      width: 14
    },
    indexTextStyle: Object.assign({}, _text.newPresets.bold, {
      fontSize: 10,
      lineHeight: 12
    })
  });
