  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _filterBottomSheet = _$$_REQUIRE(_dependencyMap[5]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _react = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _filterPill = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _filterBottomSheet2 = _$$_REQUIRE(_dependencyMap[12]);
  var _checkboxOption = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  var DealsPromosFilterBottomSheet = function DealsPromosFilterBottomSheet(props) {
    var areaFilters = props.areaFilters,
      categoryFilters = props.categoryFilters,
      originalFiltersRef = props.originalFiltersRef,
      setAreaFilters = props.setAreaFilters,
      setCategoryFilters = props.setCategoryFilters,
      setTerminalFilters = props.setTerminalFilters,
      setVisible = props.setVisible,
      terminalFilters = props.terminalFilters,
      visible = props.visible;
    var newTerminalFilterData = (0, _react.useMemo)(function () {
      return _filterBottomSheet2.TERMINAL_LIST.map(function (item) {
        return {
          tagCode: item.value,
          tagTitle: (0, _i18n.translate)(item.label),
          tagName: item.value
        };
      });
    }, []);
    var clearLocationOptions = function clearLocationOptions() {
      setAreaFilters([]);
      setTerminalFilters([]);
    };
    var clearCategoryOptions = function clearCategoryOptions() {
      setCategoryFilters([]);
    };
    var handleToggleAreaOption = function handleToggleAreaOption(item, newValue) {
      setAreaFilters(function (list) {
        if (newValue) {
          return list == null || list.concat == null ? undefined : list.concat(item);
        }
        return list == null || list.filter == null ? undefined : list.filter(function (area) {
          return (area == null ? undefined : area.tagName) !== (item == null ? undefined : item.tagName);
        });
      });
    };
    var otherOptions = newTerminalFilterData == null ? undefined : newTerminalFilterData.slice(1);
    var allOptionsSelected = (0, _react.useMemo)(function () {
      if ((terminalFilters == null ? undefined : terminalFilters.length) === 0) return false;
      return otherOptions == null ? undefined : otherOptions.every(function (option) {
        return terminalFilters == null ? undefined : terminalFilters.some(function (terminal) {
          return (terminal == null ? undefined : terminal.tagName) === (option == null ? undefined : option.tagName);
        });
      });
    }, [terminalFilters, newTerminalFilterData]);
    var handleToggleAllLocationOptions = function handleToggleAllLocationOptions(newValue) {
      if (newValue) {
        setTerminalFilters(newTerminalFilterData);
      } else {
        setTerminalFilters([]);
      }
    };
    var handleToggleLocationOption = function handleToggleLocationOption(item, newValue) {
      if (item.tagName === "all") {
        handleToggleAllLocationOptions(newValue);
        return;
      }
      setTerminalFilters(function (list) {
        if (newValue) {
          var newList = list == null || list.concat == null ? undefined : list.concat(item);
          if (newList.length === (otherOptions == null ? undefined : otherOptions.length)) {
            var allOption = newTerminalFilterData == null ? undefined : newTerminalFilterData.find(function (option) {
              return option.tagName === "all";
            });
            return allOption ? [allOption].concat((0, _toConsumableArray2.default)(newList)) : newList;
          }
          return _filterBottomSheet2.TERMINAL_SEQUENCE == null || _filterBottomSheet2.TERMINAL_SEQUENCE.reduce == null ? undefined : _filterBottomSheet2.TERMINAL_SEQUENCE.reduce(function (sequence, code) {
            var foundIndex = newList == null || newList.findIndex == null ? undefined : newList.findIndex(function (terminal) {
              return (terminal == null ? undefined : terminal.tagCode) === code;
            });
            var foundData = newList[foundIndex];
            if (foundData) {
              newList.splice(foundIndex, 1);
              return sequence.concat(foundData);
            }
            return sequence;
          }, []);
        }
        // Handle unchecking
        var filteredList = list == null || list.filter == null ? undefined : list.filter(function (terminal) {
          return (terminal == null ? undefined : terminal.tagName) !== (item == null ? undefined : item.tagName);
        });
        if (allOptionsSelected) {
          return filteredList == null || filteredList.filter == null ? undefined : filteredList.filter(function (terminal) {
            return (terminal == null ? undefined : terminal.tagName) !== "all";
          });
        }
        return filteredList;
      });
    };
    var handleToggleCategoryOption = function handleToggleCategoryOption(item, newValue) {
      setCategoryFilters(function (list) {
        if (newValue) {
          return list == null || list.concat == null ? undefined : list.concat(item == null ? undefined : item.value);
        }
        return list == null || list.filter == null ? undefined : list.filter(function (val) {
          return val !== (item == null ? undefined : item.value);
        });
      });
    };
    var handleClearAllFilters = function handleClearAllFilters() {
      setAreaFilters([]);
      setTerminalFilters([]);
      setCategoryFilters([]);
    };
    var handleApplyFilters = function handleApplyFilters() {
      setVisible(false);
    };
    var handleCloseBS = function handleCloseBS() {
      var _originalFiltersRef$c, _originalFiltersRef$c2, _originalFiltersRef$c3;
      setAreaFilters(((_originalFiltersRef$c = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c.areaFilters) || []);
      setTerminalFilters(((_originalFiltersRef$c2 = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c2.terminalFilters) || []);
      setCategoryFilters(((_originalFiltersRef$c3 = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c3.categoryFilters) || []);
      setVisible(false);
    };
    (0, _react.useEffect)(function () {
      if (visible) {
        originalFiltersRef.current = {
          areaFilters: areaFilters,
          categoryFilters: categoryFilters,
          terminalFilters: terminalFilters
        };
      }
    }, [visible]);
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      animationInTiming: 400,
      animationOutTiming: 400,
      containerStyle: _filterBottomSheet.styles.containerStyle,
      isModalVisible: visible,
      onClosedSheet: handleCloseBS,
      stopDragCollapse: true,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _filterBottomSheet.styles.headerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _filterBottomSheet.styles.titleTextStyle,
          tx: "staffPerkListing.filterBs.title"
        }), (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handleCloseBS,
          style: _filterBottomSheet.styles.closeBtnStyle,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            color: _theme.color.palette.darkestGrey,
            height: 24,
            width: 24
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        style: _filterBottomSheet.styles.filterFormContainerStyle,
        showsVerticalScrollIndicator: false,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _filterBottomSheet.styles.sectionContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _filterBottomSheet.styles.sectionHeaderContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _filterBottomSheet.styles.sectionTitleTextStyle,
              tx: "staffPerkListing.filterBs.location.title"
            }), (!!(areaFilters != null && areaFilters.length) || !!(terminalFilters != null && terminalFilters.length)) && (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
              onPress: clearLocationOptions,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _filterBottomSheet.styles.sectionClearBtnStyle,
                tx: "staffPerkListing.filterBs.clearBtn"
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _filterBottomSheet.styles.areaFiltersContainerStyle,
            children: _filterBottomSheet2.AREA_LIST == null || _filterBottomSheet2.AREA_LIST.map == null ? undefined : _filterBottomSheet2.AREA_LIST.map(function (item) {
              var areaItem = {
                tagName: item.value,
                tagTitle: (0, _i18n.translate)(item.label),
                tagCode: item.value
              };
              var isActive = areaFilters == null || areaFilters.some == null ? undefined : areaFilters.some(function (area) {
                return (area == null ? undefined : area.tagName) === item.value;
              });
              var Icon = function Icon() {
                return null;
              };
              switch (item.value) {
                case "public":
                  Icon = _icons.PublicAreaIcon;
                  break;
                case "transit":
                  Icon = isActive ? _icons.TransitAreaFillIcon : _icons.TransitAreaIcon;
                  break;
                default:
                  break;
              }
              return (0, _jsxRuntime.jsx)(_filterPill.default, {
                active: isActive,
                size: "lg",
                label: (0, _i18n.translate)(item.label),
                labelColor: _theme.color.palette.darkestGrey,
                IconComponent: Icon,
                onPress: function onPress() {
                  return handleToggleAreaOption(areaItem, !isActive);
                },
                variant: "outline",
                iconSize: 16
              }, item.value);
            })
          }), newTerminalFilterData == null || newTerminalFilterData.map == null ? undefined : newTerminalFilterData.map(function (item) {
            var checked = terminalFilters == null || terminalFilters.some == null ? undefined : terminalFilters.some(function (terminal) {
              return (terminal == null ? undefined : terminal.tagName) === (item == null ? undefined : item.tagName);
            });
            return (0, _jsxRuntime.jsx)(_checkboxOption.default, {
              checked: checked,
              label: item.tagTitle,
              onCheck: function onCheck(newValue) {
                return handleToggleLocationOption(item, newValue);
              }
            }, `${item.tagName}_${checked}`);
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: [_filterBottomSheet.styles.sectionContainerStyle, _filterBottomSheet.styles.lastSectionContainerStyle],
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _filterBottomSheet.styles.categoryHeaderContainerStyle,
            children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: _filterBottomSheet.styles.categoryTitleContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _filterBottomSheet.styles.sectionTitleTextStyle,
                tx: "staffPerkListing.filterBs.category.title"
              }), !!(categoryFilters != null && categoryFilters.length) && (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
                onPress: clearCategoryOptions,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: _filterBottomSheet.styles.sectionClearBtnStyle,
                  tx: "staffPerkListing.filterBs.clearBtn"
                })
              })]
            })
          }), _filterBottomSheet2.CATEGORY_LIST.map(function (item, index) {
            var checked = categoryFilters == null || categoryFilters.some == null ? undefined : categoryFilters.some(function (val) {
              return val === item.value;
            });
            return (0, _jsxRuntime.jsx)(_checkboxOption.default, {
              checked: checked,
              containerStyle: index === 0 ? {
                marginTop: 12
              } : undefined,
              label: (0, _i18n.translate)(item == null ? undefined : item.label),
              onCheck: function onCheck(newValue) {
                return handleToggleCategoryOption(item, newValue);
              }
            }, `${item.value}_${checked}`);
          })]
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _filterBottomSheet.styles.footerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handleClearAllFilters,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _filterBottomSheet.styles.clearAllBtnStyle,
            tx: "staffPerkListing.filterBs.clearAllBtn"
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _filterBottomSheet.styles.applyFiltersBtnContainerStyle,
          start: {
            x: 0,
            y: 1
          },
          end: {
            x: 1,
            y: 0
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            onPress: handleApplyFilters,
            style: _filterBottomSheet.styles.applyFiltersBtnStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _filterBottomSheet.styles.applyFiltersBtnLabelTextStyle,
              tx: "staffPerkListing.filterBs.applyFiltersBtn"
            })
          })
        })]
      })]
    });
  };
  var _default = exports.default = DealsPromosFilterBottomSheet;
