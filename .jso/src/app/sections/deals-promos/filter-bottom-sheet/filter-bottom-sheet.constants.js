  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TerminalFilter = exports.TERMINAL_SEQUENCE = exports.TERMINAL_LIST = exports.SortBy = exports.FILTER_SECTION = exports.CATEGORY_LIST = exports.AreaFilter = exports.AREA_LIST = undefined;
  var _dealsPromosFilterBar = _$$_REQUIRE(_dependencyMap[0]);
  var FILTER_SECTION = exports.FILTER_SECTION = {
    LOCATION: "location",
    CATEGORIES: "categories"
  };
  var AreaFilter = exports.AreaFilter = /*#__PURE__*/function (AreaFilter) {
    AreaFilter["Public"] = "public";
    AreaFilter["Transit"] = "transit";
    return AreaFilter;
  }({});
  var TerminalFilter = exports.TerminalFilter = /*#__PURE__*/function (TerminalFilter) {
    TerminalFilter["All"] = "all";
    TerminalFilter["Jewel"] = "jewel";
    TerminalFilter["Terminal1"] = "t1";
    TerminalFilter["Terminal2"] = "t2";
    TerminalFilter["Terminal3"] = "t3";
    TerminalFilter["Terminal4"] = "t4";
    return TerminalFilter;
  }({});
  var SortBy = exports.SortBy = /*#__PURE__*/function (SortBy) {
    SortBy["LatestAddedDate"] = "latestAddedDate";
    SortBy["AZ"] = "az";
    return SortBy;
  }({});
  var CATEGORY_LIST = exports.CATEGORY_LIST = [{
    label: "dealsPromosListing.filterBs.category.newlyAdded",
    value: _dealsPromosFilterBar.DealsPromosCategory.NewlyAdded
  }, {
    label: "dealsPromosListing.filterBs.category.dining",
    value: _dealsPromosFilterBar.DealsPromosCategory.Dining
  }, {
    label: "dealsPromosListing.filterBs.category.shopping",
    value: _dealsPromosFilterBar.DealsPromosCategory.Shopping
  }, {
    label: "dealsPromosListing.filterBs.category.iShopChangiOffers",
    value: _dealsPromosFilterBar.DealsPromosCategory.IShopChangiOffers
  }, {
    label: "dealsPromosListing.filterBs.category.services",
    value: _dealsPromosFilterBar.DealsPromosCategory.Services
  }, {
    label: "dealsPromosListing.filterBs.category.limitedOffers",
    value: _dealsPromosFilterBar.DealsPromosCategory.LimitedOffers
  }];
  var AREA_LIST = exports.AREA_LIST = [{
    label: "dealsPromosListing.filterBs.location.publicArea",
    value: AreaFilter.Public
  }, {
    label: "dealsPromosListing.filterBs.location.transitArea",
    value: AreaFilter.Transit
  }];
  var TERMINAL_LIST = exports.TERMINAL_LIST = [{
    label: "dealsPromosListing.filterBs.location.all",
    value: TerminalFilter.All
  }, {
    label: "dealsPromosListing.filterBs.location.jewel",
    value: TerminalFilter.Jewel
  }, {
    label: "dealsPromosListing.filterBs.location.terminal1",
    value: TerminalFilter.Terminal1
  }, {
    label: "dealsPromosListing.filterBs.location.terminal2",
    value: TerminalFilter.Terminal2
  }, {
    label: "dealsPromosListing.filterBs.location.terminal3",
    value: TerminalFilter.Terminal3
  }, {
    label: "dealsPromosListing.filterBs.location.terminal4",
    value: TerminalFilter.Terminal4
  }];
  var TERMINAL_SEQUENCE = exports.TERMINAL_SEQUENCE = ["t1", "t2", "t3", "t4", "jewel"];
