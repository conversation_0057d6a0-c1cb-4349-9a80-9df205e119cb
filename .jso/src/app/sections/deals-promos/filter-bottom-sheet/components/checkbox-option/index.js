  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _checkbox = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var CheckBoxOption = function CheckBoxOption(props) {
    var accessibilityLabel = props.accessibilityLabel,
      checked = props.checked,
      containerStyle = props.containerStyle,
      label = props.label,
      onCheck = props.onCheck,
      testID = props.testID;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      accessibilityLabel: accessibilityLabel,
      style: [styles.containerStyle, containerStyle],
      testID: testID,
      children: [(0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
        onToggle: onCheck,
        outlineStyle: styles.uncheckStyle,
        preventHighlightTextOnCheck: true,
        textStyle: styles.checkboxLabelTextStyle,
        text: label,
        value: checked
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.dividerStyle
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      height: 38,
      marginTop: 18,
      paddingBottom: 16
    },
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      bottom: 0,
      height: 1,
      left: 0,
      position: "absolute",
      width: _reactNative.Dimensions.get("window").width - 48
    },
    uncheckStyle: {
      borderColor: _theme.color.palette.midGrey,
      borderWidth: 1
    },
    checkboxLabelTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
  var _default = exports.default = CheckBoxOption;
