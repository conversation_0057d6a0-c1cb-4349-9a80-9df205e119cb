  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      height: _reactNative.Dimensions.get("window").height - 79,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopEndRadius: 16
    },
    headerContainerStyle: {
      alignItems: "center",
      height: 64,
      justifyContent: "center"
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    closeBtnStyle: {
      position: "absolute",
      right: 16,
      top: 20
    },
    filterFormContainerStyle: {
      flex: 1
    },
    footerContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderTopColor: _theme.color.palette.lighterGrey,
      borderTopWidth: 1,
      flexDirection: "row",
      height: 96,
      justifyContent: "space-between",
      paddingBottom: 40,
      paddingHorizontal: 20,
      paddingTop: 12
    },
    sectionContainerStyle: {
      paddingHorizontal: 20,
      paddingTop: 24,
      marginBottom: 28
    },
    lastSectionContainerStyle: {
      paddingBottom: 62
    },
    sectionHeaderContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 12,
      gap: 8
    },
    sectionTitleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    }),
    sectionClearBtnStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    areaFiltersContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 8,
      marginBottom: 16,
      rowGap: 8
    },
    clearAllBtnStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    applyFiltersBtnContainerStyle: {
      borderRadius: 60,
      height: 44
    },
    applyFiltersBtnStyle: {
      height: 44,
      paddingHorizontal: 42,
      width: "100%",
      justifyContent: "center"
    },
    applyFiltersBtnLabelTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey
    }),
    categoryHeaderContainerStyle: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: 12
    },
    categoryTitleContainerStyle: {
      flexDirection: "row",
      alignItems: "center",
      gap: 8
    }
  });
