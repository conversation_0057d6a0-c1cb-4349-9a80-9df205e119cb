  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.TakeUploadPhotoContext = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNativeImageCropPicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[8]);
  var _envParams = _$$_REQUIRE(_dependencyMap[9]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _takePhotoScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _firebase = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  var TakeUploadPhotoContext = exports.TakeUploadPhotoContext = (0, _react.createContext)({
    isCameraGranted: false,
    setIsCameraGranted: function setIsCameraGranted(state) {},
    retroClaimConfigs: undefined,
    onOpenGalleryToSelect: function onOpenGalleryToSelect() {}
  });
  var TakeUploadPhoto = function TakeUploadPhoto(_ref) {
    var initCameraGranted = _ref.isCameraGranted,
      shouldOpenGallery = _ref.shouldOpenGallery,
      onClosedSheet = _ref.onClosedSheet,
      handleTakePicture = _ref.handleTakePicture;
    var _useState = (0, _react.useState)(initCameraGranted),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isCameraGranted = _useState2[0],
      setIsCameraGranted = _useState2[1];
    var _useState3 = (0, _react.useState)(undefined),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      retroClaimConfigs = _useState4[0],
      setRetroClaimConfigs = _useState4[1];
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg901 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG90.1";
    });
    var msg902 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG90.2";
    });
    var isIOSDevice = _reactNative.Platform.OS === "ios";
    var rationale = {
      title: (msg901 == null ? undefined : msg901.title) || (0, _i18n.translate)("requestPermission.gallery.title"),
      message: (msg901 == null ? undefined : msg901.informativeText) || (0, _i18n.translate)("requestPermission.gallery.message"),
      buttonPositive: (msg901 == null ? undefined : msg901.secondButton) || (0, _i18n.translate)("requestPermission.gallery.buttonPositive"),
      buttonNegative: (msg901 == null ? undefined : msg901.firstButton) || (0, _i18n.translate)("requestPermission.gallery.buttonNegative")
    };
    var handleChooseImageFromGallery = function handleChooseImageFromGallery() {
      var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_BAGGAGE_TRACKER_OPEN_GALLERY);
      setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        try {
          var selectedImageData;
          if (isIOSDevice) {
            selectedImageData = yield (0, _mediaHelper.selectImageByImageCropPicker)({
              multiple: false
            });
          } else {
            selectedImageData = yield (0, _mediaHelper.choosePictureFromGallery)({
              multiple: false
            });
          }
          if (selectedImageData) {
            var _selectedImageData, _selectedImageData2, _selectedImageData3, _selectedImageData4;
            dtAction.reportStringValue("selected", "success");
            _reactNativeImageCropPicker.default.openCropper({
              mediaType: "photo",
              path: isIOSDevice ? (_selectedImageData = selectedImageData) == null ? undefined : _selectedImageData.path : (_selectedImageData2 = selectedImageData) == null ? undefined : _selectedImageData2.uri,
              freeStyleCropEnabled: false,
              cropperCircleOverlay: true,
              width: (_selectedImageData3 = selectedImageData) == null ? undefined : _selectedImageData3.width,
              height: (_selectedImageData4 = selectedImageData) == null ? undefined : _selectedImageData4.width,
              // For Android
              cropperStatusBarColor: "#000000",
              cropperActiveWidgetColor: "#ab76d5",
              cropperToolbarColor: "black",
              cropperToolbarWidgetColor: "#ffffff",
              // For iOS
              cropperCancelColor: "#ab76d5",
              cropperChooseText: "Next",
              cropperChooseColor: "#ab76d5"
            }).then(/*#__PURE__*/function () {
              var _ref3 = (0, _asyncToGenerator2.default)(function* (croppingResult) {
                dtAction.reportStringValue("cropping-result", "success");
                var _ref4 = retroClaimConfigs || {},
                  retro_claim_quality = _ref4.retro_claim_quality,
                  retro_claim_max_height = _ref4.retro_claim_max_height,
                  retro_claim_max_width = _ref4.retro_claim_max_width;
                var resizeImageConfigs = Object.assign({}, retro_claim_max_width && {
                  maxWidth: Number(retro_claim_max_width)
                }, retro_claim_max_height && {
                  maxHeight: Number(retro_claim_max_height)
                }, retro_claim_quality && {
                  quality: Number(retro_claim_quality)
                });
                dtAction.reportStringValue("configs-quality", `${retro_claim_quality}`);
                dtAction.reportStringValue("configs-max-width", `${retro_claim_max_width}`);
                dtAction.reportStringValue("configs-max-height", `${retro_claim_max_height}`);
                var base64Image = yield (0, _mediaHelper.createResizedImage)(Object.assign({
                  imageData: croppingResult
                }, resizeImageConfigs));
                handleTakePicture(base64Image);
              });
              return function (_x) {
                return _ref3.apply(this, arguments);
              };
            }()).catch(function (error) {
              dtAction.reportStringValue("cropping-result", `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
              if (shouldOpenGallery) {
                onClosedSheet == null || onClosedSheet();
              }
            }).finally(function () {
              dtAction.leaveAction();
            });
          } else {
            dtAction.reportStringValue("selected", "failed");
            dtAction.leaveAction();
            if (shouldOpenGallery) {
              onClosedSheet == null || onClosedSheet();
            }
          }
        } catch (error) {
          dtAction.reportStringValue("selected", `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
          dtAction.leaveAction();
          if (shouldOpenGallery) {
            onClosedSheet == null || onClosedSheet();
          }
        }
      }), 200);
    };
    var onOpenGalleryToSelect = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        if (_reactNative.Platform.OS === "android") {
          var _systemVersion$split;
          var systemVersion = _reactNativeDeviceInfo.default.getSystemVersion();
          var androidVersion = systemVersion == null || (_systemVersion$split = systemVersion.split(".")) == null ? undefined : _systemVersion$split[0];
          if (Number(androidVersion) < 13) {
            handleChooseImageFromGallery();
            return;
          }
        }
        (0, _reactNativePermissions.request)(isIOSDevice ? _reactNativePermissions.PERMISSIONS.IOS.PHOTO_LIBRARY : _reactNativePermissions.PERMISSIONS.ANDROID.READ_MEDIA_IMAGES, rationale).then(function (result) {
          if ([_reactNativePermissions.RESULTS.BLOCKED, _reactNativePermissions.RESULTS.DENIED].some(function (val) {
            return val === result;
          })) {
            _reactNative.Alert.alert((msg902 == null ? undefined : msg902.title) || (0, _i18n.translate)("requestPermission.gallery.cannotTitle"), (msg902 == null ? undefined : msg902.informativeText) || (0, _i18n.translate)("requestPermission.gallery.cannotMessage"), [{
              text: (msg902 == null ? undefined : msg902.firstButton) || (0, _i18n.translate)("requestPermission.gallery.firstButton"),
              isPreferred: true,
              onPress: _reactNativePermissions.openSettings
            }, {
              text: (msg902 == null ? undefined : msg902.secondButton) || (0, _i18n.translate)("requestPermission.gallery.secondButton"),
              onPress: function onPress() {
                return null;
              }
            }]);
          } else if (result === _reactNativePermissions.RESULTS.GRANTED || result === _reactNativePermissions.RESULTS.LIMITED) {
            handleChooseImageFromGallery();
          }
        });
      });
      return function onOpenGalleryToSelect() {
        return _ref5.apply(this, arguments);
      };
    }();
    var contextValue = (0, _react.useMemo)(function () {
      return {
        isCameraGranted: isCameraGranted,
        setIsCameraGranted: setIsCameraGranted,
        retroClaimConfigs: retroClaimConfigs,
        onOpenGalleryToSelect: onOpenGalleryToSelect
      };
    }, [isCameraGranted, setIsCameraGranted, retroClaimConfigs, onOpenGalleryToSelect]);
    (0, _react.useEffect)(function () {
      if (shouldOpenGallery) {
        onOpenGalleryToSelect();
      }
    }, [shouldOpenGallery]);
    (0, _react.useEffect)(function () {
      var isMounted = true;
      var getRetroClaimConfigs = /*#__PURE__*/function () {
        var _ref6 = (0, _asyncToGenerator2.default)(function* () {
          var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_BAGGAGE_TRACKER_GET_CONFIGS);
          try {
            var _env, _response$data;
            var paramsArray = _apis.default.getConfigurations.split(" ");
            var method = paramsArray[0] || "POST";
            var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.API_GATEWAY_URL) + paramsArray[1];
            var data = {
              keys: ["retro_claim_quality", "retro_claim_max_height", "retro_claim_max_width"]
            };
            var response = yield (0, _request.default)({
              url: url,
              method: method,
              data: data
            });
            if (response != null && (_response$data = response.data) != null && _response$data.retro_claim_max_width) {
              var _response$data2, _response$data3, _response$data4;
              dtAction.reportStringValue("status", "success");
              dtAction.reportStringValue("configs-quality", `${response == null || (_response$data2 = response.data) == null ? undefined : _response$data2.retro_claim_quality}`);
              dtAction.reportStringValue("configs-max-width", `${response == null || (_response$data3 = response.data) == null ? undefined : _response$data3.retro_claim_max_width}`);
              dtAction.reportStringValue("configs-max-height", `${response == null || (_response$data4 = response.data) == null ? undefined : _response$data4.retro_claim_max_height}`);
              if (isMounted) setRetroClaimConfigs(response == null ? undefined : response.data);
            } else {
              dtAction.reportStringValue("status", "failed");
            }
          } catch (error) {
            dtAction.reportStringValue("status", `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
          } finally {
            dtAction.leaveAction();
          }
        });
        return function getRetroClaimConfigs() {
          return _ref6.apply(this, arguments);
        };
      }();
      getRetroClaimConfigs();
      return function () {
        isMounted = false;
      };
    }, []);
    return (0, _jsxRuntime.jsx)(TakeUploadPhotoContext.Provider, {
      value: contextValue,
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: {
          flex: 1,
          overflow: "hidden"
        },
        children: (0, _jsxRuntime.jsx)(_takePhotoScreen.default, {
          onClosedSheet: onClosedSheet,
          handleTakePicture: handleTakePicture
        })
      })
    });
  };
  var _default = exports.default = TakeUploadPhoto;
