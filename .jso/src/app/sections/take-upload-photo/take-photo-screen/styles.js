  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var CROPPER_FRAME_BACKGROUND = "rgba(18, 18, 18, 0.3)";
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: {
      flex: 1
    },
    rnCameraWrapper: {
      flex: 1,
      alignItems: "center",
      justifyContent: "flex-end"
    },
    deniedStyle: {
      flex: 1,
      width: "100%",
      height: "100%",
      position: "absolute",
      backgroundColor: _theme.color.palette.lightGrey
    },
    headerWrapper: {
      width: "100%",
      paddingLeft: 16,
      paddingRight: 16,
      paddingBottom: 16,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      backgroundColor: "rgba(18, 18, 18, 0.8)",
      paddingTop: _reactNative.Platform.select({
        ios: 60,
        android: 40
      })
    },
    backButton: {
      borderRadius: 28,
      backgroundColor: "rgba(18, 18, 18, 0.8)"
    },
    backArrowIcon: {
      color: _theme.color.palette.almostBlackGrey
    },
    questionMarkIcon: {
      color: _theme.color.palette.whiteGrey
    },
    cropperOverlay: {
      flex: 1,
      borderColor: CROPPER_FRAME_BACKGROUND
    },
    cropperOverlayLeft: {
      height: "100%",
      borderRightWidth: 1,
      position: "absolute",
      backgroundColor: CROPPER_FRAME_BACKGROUND,
      width: _constants.CROPPER_OVERLAY_STYLES.paddingVertical,
      borderRightColor: _theme.color.palette.almostWhiteGrey
    },
    cropperOverlayRight: {
      right: 0,
      height: "100%",
      borderLeftWidth: 1,
      position: "absolute",
      backgroundColor: CROPPER_FRAME_BACKGROUND,
      width: _constants.CROPPER_OVERLAY_STYLES.paddingVertical,
      borderLeftColor: _theme.color.palette.almostWhiteGrey
    },
    cropperOverlayTop: {
      borderBottomWidth: 1,
      position: "absolute",
      backgroundColor: CROPPER_FRAME_BACKGROUND,
      width: _reactNative.Dimensions.get("window").width - 116,
      left: _constants.CROPPER_OVERLAY_STYLES.paddingVertical,
      borderBottomColor: _theme.color.palette.almostWhiteGrey
    },
    cropperOverlayBottom: {
      bottom: 0,
      borderBottomWidth: 1,
      position: "absolute",
      backgroundColor: CROPPER_FRAME_BACKGROUND,
      width: _reactNative.Dimensions.get("window").width - 116,
      left: _constants.CROPPER_OVERLAY_STYLES.paddingVertical,
      borderBottomColor: _theme.color.palette.almostWhiteGrey
    },
    bottomWrapper: {
      bottom: 0,
      padding: 20,
      width: "100%",
      backgroundColor: "rgba(18, 18, 18, 0.8)"
    },
    guideMessage: Object.assign({}, _text.presets.bodyTextBold, {
      fontSize: 14,
      marginBottom: 26,
      textAlign: "center",
      color: _theme.color.palette.whiteGrey
    }),
    bottomButtons: {
      marginBottom: 42,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center"
    },
    bottomButtonItem: {
      padding: 10,
      borderRadius: 40,
      backgroundColor: "rgba(252, 252, 252, 0.3)"
    },
    bottomButtonIcon: {
      color: _theme.color.palette.whiteGrey
    },
    takePhotoButton: {
      marginLeft: 42,
      marginRight: 42,
      borderRadius: 56,
      paddingTop: 12,
      paddingLeft: 12,
      paddingRight: 12,
      paddingBottom: 12,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    takePhotoIcon: {
      top: 1.2
    }
  });
