  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.waveStyle = exports.titleStyle = exports.tagTextStyle = exports.tagContainerStyle = exports.lottieStyle = exports.lottieBackgroundStyle = exports.inf13TextStyles = exports.gradientStyle = exports.descriptionStyle = exports.contentContainerStyle = exports.containerStyle = exports.confettiStyle = exports.checkStyle = exports.cardTitleStyle = exports.cardStyle = exports.cardDescriptionTextStyle = exports.buttonTextStyleV2 = exports.buttonTextStyle = exports.buttonContainerStyleV2 = exports.buttonContainerStyle = exports.backgroundStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var containerStyle = exports.containerStyle = {
    flex: 1,
    paddingBottom: 50,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var backgroundStyle = exports.backgroundStyle = {
    resizeMode: "stretch",
    width: "100%"
  };
  var gradientStyle = exports.gradientStyle = {
    borderBottomLeftRadius: 40
  };
  var lottieBackgroundStyle = exports.lottieBackgroundStyle = {
    transform: [{
      scale: 2
    }],
    width: _reactNative.Dimensions.get('window').width,
    height: _reactNative.Dimensions.get('window').width / 1242 * 2688 / 2,
    position: 'absolute'
  };
  var lottieStyle = exports.lottieStyle = {
    width: width / 2,
    height: width / 2
  };
  var contentContainerStyle = exports.contentContainerStyle = {
    paddingHorizontal: 24,
    paddingTop: 120,
    alignItems: "center"
  };
  var checkStyle = exports.checkStyle = {
    position: "absolute",
    top: -20,
    height: 180,
    width: "100%",
    justifyContent: "center",
    display: "flex",
    alignItems: "center"
  };
  var titleStyle = exports.titleStyle = Object.assign({}, _text.presets.h2, {
    lineHeight: 28,
    color: _theme.color.palette.almostWhiteGrey,
    textAlign: "center",
    marginBottom: 12,
    paddingHorizontal: 34
  });
  var descriptionStyle = exports.descriptionStyle = Object.assign({}, _text.presets.caption1Regular, {
    color: _theme.color.palette.almostWhiteGrey,
    textAlign: "center",
    marginBottom: 24
  });
  var cardStyle = exports.cardStyle = Object.assign({
    backgroundColor: _theme.color.palette.almostWhiteGrey,
    padding: 24,
    marginHorizontal: 24,
    borderRadius: 16,
    marginBottom: 24,
    zIndex: 9999
  }, _theme.shadow.secondaryShadow);
  var cardTitleStyle = exports.cardTitleStyle = Object.assign({}, _text.presets.subTitleBold, {
    color: _theme.color.palette.almostBlackGrey,
    textAlign: "center",
    marginBottom: 16
  });
  var cardDescriptionTextStyle = exports.cardDescriptionTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.almostBlackGrey,
    textAlign: "center",
    marginBottom: 24
  });
  var tagContainerStyle = exports.tagContainerStyle = {
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: _theme.color.palette.almostWhiteGrey,
    alignSelf: "center",
    marginBottom: 24,
    borderColor: _theme.color.palette.lighterPurple,
    borderWidth: 1
  };
  var tagTextStyle = exports.tagTextStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.lightPurple
  });
  var buttonContainerStyle = exports.buttonContainerStyle = {
    paddingHorizontal: 47
  };
  var buttonContainerStyleV2 = exports.buttonContainerStyleV2 = {
    alignSelf: 'center'
  };
  var buttonTextStyle = exports.buttonTextStyle = {
    color: _theme.color.palette.lightPurple
  };
  var buttonTextStyleV2 = exports.buttonTextStyleV2 = {
    color: _theme.color.palette.lightPurple,
    paddingHorizontal: 12
  };
  var waveStyle = exports.waveStyle = {
    position: "absolute",
    bottom: 0
  };
  var confettiStyle = exports.confettiStyle = {
    position: "absolute",
    top: _reactNative.Platform.OS === "ios" ? -55 : -115,
    end: 15
  };
  var inf13TextStyles = exports.inf13TextStyles = Object.assign({}, _text.presets.bodyTextBlackRegular, {
    lineHeight: 20,
    color: _theme.color.palette.almostBlackGrey,
    textAlign: "center",
    marginBottom: 16
  });
