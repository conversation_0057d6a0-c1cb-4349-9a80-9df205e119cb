  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.RedeemSuccessSection = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _native = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _button = _$$_REQUIRE(_dependencyMap[7]);
  var _redeemRewardDetailRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _check_mark = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _confetti = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _lodash = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable no-extra-boolean-cast */

  var RedeemSuccessSection = exports.RedeemSuccessSection = function RedeemSuccessSection(props) {
    var _getRedeemSuccessfulC, _getRedeemSuccessfulC2, _route$params, _redeemPayloadData$da3, _redeemPayloadData$da4;
    var dispatch = (0, _reactRedux.useDispatch)();
    var route = (0, _native.useRoute)();
    var navigation = (0, _native.useNavigation)();
    var redeemPayload = (0, _reactRedux.useSelector)(_redeemRewardDetailRedux.RedeemRewardDetailSelectors.redeemPayload);
    var getRedeemSuccessfulContentData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.REDEEM_SUCCESS_SECTION));
    var informativesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var dataAemCommon = (0, _lodash.get)(informativesCommon, "data.informatives", []);
    var redeemContent = getRedeemSuccessfulContentData != null && (_getRedeemSuccessfulC = getRedeemSuccessfulContentData.data) != null && (_getRedeemSuccessfulC = _getRedeemSuccessfulC.list) != null && _getRedeemSuccessfulC.length ? getRedeemSuccessfulContentData == null || (_getRedeemSuccessfulC2 = getRedeemSuccessfulContentData.data) == null ? undefined : _getRedeemSuccessfulC2.list[0] : {};
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      redeemPayloadData = _useState2[0],
      setRedeemPayloadData = _useState2[1];
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "RedeemSuccessSection" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "RedeemSuccessSection" : _props$accessibilityL;
    var inf11Text = (dataAemCommon == null ? undefined : dataAemCommon.find(function (item) {
      return item.code === "INF11";
    })) || "";
    var inf12Text = (dataAemCommon == null ? undefined : dataAemCommon.find(function (item) {
      return item.code === "INF12";
    })) || "";
    var inf13Text = (dataAemCommon == null ? undefined : dataAemCommon.find(function (item) {
      return item.code === "INF13";
    })) || "";
    var hideViewPerk = route == null || (_route$params = route.params) == null ? undefined : _route$params.hideViewPerk;
    var isHasError = (0, _react.useMemo)(function () {
      var _redeemPayloadData$ov, _redeemPayloadData$ov2;
      return redeemPayloadData != null && (_redeemPayloadData$ov = redeemPayloadData.overallStatus) != null && _redeemPayloadData$ov.voucherIssuanceStatus ? !(redeemPayloadData != null && (_redeemPayloadData$ov2 = redeemPayloadData.overallStatus) != null && (_redeemPayloadData$ov2 = _redeemPayloadData$ov2.voucherIssuanceStatus) != null && _redeemPayloadData$ov2.success) : false;
    }, [redeemPayloadData]);
    var onViewWalletPress = function onViewWalletPress() {
      var _navigation$getState, _navigation$getState2;
      var isInMainNavigator = navigation == null || (_navigation$getState = navigation.getState()) == null || (_navigation$getState = _navigation$getState.routes) == null ? undefined : _navigation$getState.some(function (route) {
        return route.name === "bottomNavigation";
      });
      if (isInMainNavigator) {
        var _targetScreen = props.isPromoCode ? _constants.NavigationConstants.promoCodes : _constants.NavigationConstants.vouchersPrizesRedemptionsScreen;
        navigation == null || navigation.navigate(_targetScreen, {
          backPressToVouchersPrizesRedemptionsScreen: true
        });
        return;
      }
      var routesLength = (navigation == null || (_navigation$getState2 = navigation.getState()) == null || (_navigation$getState2 = _navigation$getState2.routes) == null ? undefined : _navigation$getState2.length) || 0;
      var action = routesLength > 1 ? _native.StackActions.pop(routesLength - 2) : _native.StackActions.popToTop();
      navigation == null || navigation.dispatch(action);
      var targetScreen = props.isPromoCode ? _constants.NavigationConstants.promoCodes : _constants.NavigationConstants.vouchersPrizesRedemptionsScreen;
      navigation == null || navigation.navigate(targetScreen);
    };
    var onContactUs = (0, _react.useCallback)(function () {
      if (!!(redeemContent != null && redeemContent.email)) {
        _reactNative.Linking.openURL(`mailto:${redeemContent == null ? undefined : redeemContent.email}`);
      } else if (!!(redeemContent != null && redeemContent.descriptionError)) {
        var descriptionErrorArray = redeemContent.descriptionError.split(" ");
        var email = descriptionErrorArray.find(function (el) {
          return el.includes("@");
        });
        _reactNative.Linking.openURL(`mailto:${email}`);
      }
    }, [redeemContent == null ? undefined : redeemContent.email, redeemContent == null ? undefined : redeemContent.descriptionError]);
    (0, _react.useEffect)(function () {
      dispatch(_redeemRewardDetailRedux.default.redeemReset());
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.REDEEM_SUCCESS_SECTION,
        pathName: "getRedeemSuccessfullContent"
      }));
      return function () {
        dispatch(_redeemRewardDetailRedux.default.redeemResetAll());
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (redeemPayload) {
        setRedeemPayloadData(redeemPayload);
      }
    }, [redeemPayload]);
    var renderContentSection = function renderContentSection() {
      var _redeemPayloadData$da;
      if (!!isHasError) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.cardDescriptionTextStyle,
          text: inf12Text == null ? undefined : inf12Text.informativeText
        });
      }
      if (!!(redeemPayloadData != null && (_redeemPayloadData$da = redeemPayloadData.data) != null && _redeemPayloadData$da.numberOfReward)) {
        var _redeemPayloadData$da2;
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.tagContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.tagTextStyle,
            children: redeemPayloadData == null || (_redeemPayloadData$da2 = redeemPayloadData.data) == null ? undefined : _redeemPayloadData$da2.numberOfReward
          })
        });
      }
      return null;
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.containerStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNativeLinearGradient.default, {
        start: {
          x: 0,
          y: 1
        },
        end: {
          x: 1,
          y: 0
        },
        colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
        style: styles.gradientStyle,
        children: [(0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          style: styles.lottieBackgroundStyle,
          source: _confetti.default,
          autoPlay: true,
          loop: true
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: Object.assign({}, styles.contentContainerStyle, {
            bottom: isHasError ? -29 : 0
          }),
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.checkStyle,
            children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
              style: styles.lottieStyle,
              source: _check_mark.default,
              autoPlay: true,
              loop: true
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleStyle,
            numberOfLines: 2,
            children: redeemPayloadData == null || (_redeemPayloadData$da3 = redeemPayloadData.data) == null ? undefined : _redeemPayloadData$da3.successText
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.descriptionStyle,
            text: inf11Text == null ? undefined : inf11Text.informativeText
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.cardStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            style: styles.cardTitleStyle,
            children: redeemPayloadData == null || (_redeemPayloadData$da4 = redeemPayloadData.data) == null ? undefined : _redeemPayloadData$da4.nameOfReward
          }), renderContentSection(), (props == null ? undefined : props.isKrisflyer) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.inf13TextStyles,
            text: inf13Text.informativeText
          }), !hideViewPerk && (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.buttonContainerStyleV2,
            children: !isHasError ? (0, _jsxRuntime.jsx)(_button.Button, {
              sizePreset: "medium",
              typePreset: "secondary",
              tx: props != null && props.isPromoCode ? "redeemRewardSuccess.viewInPromoCodes" : "redeemRewardSuccess.viewInVPR",
              textPreset: "buttonSmall",
              textStyle: styles.buttonTextStyleV2,
              onPress: onViewWalletPress,
              testID: `${testID}__Button`,
              accessibilityLabel: `${accessibilityLabel}__Button`
            }) : (0, _jsxRuntime.jsx)(_button.Button, {
              sizePreset: "large",
              typePreset: "secondary",
              tx: "redeemRewardSuccess.contactUs",
              textPreset: "buttonLarge",
              textStyle: styles.buttonTextStyle,
              onPress: onContactUs,
              testID: `${testID}__Button`,
              accessibilityLabel: `${accessibilityLabel}__Button`
            })
          })]
        })]
      })
    });
  };
