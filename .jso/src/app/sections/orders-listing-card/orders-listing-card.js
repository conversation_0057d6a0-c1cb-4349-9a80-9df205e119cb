  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _ordersCardHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _ordersCardConsignment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _ordersListingCard = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var OrdersListingCard = function OrdersListingCard(props) {
    var _item$detailsData;
    var isFirstCard = props.isFirstCard,
      item = props.item,
      _onPress = props.onPress;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: isFirstCard ? [_ordersListingCard.styles.containerStyle, _ordersListingCard.styles.firstCardContainerStyle] : _ordersListingCard.styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_ordersCardHeader.default, {
        item: item,
        onPress: function onPress() {
          return _onPress({
            orderItem: item
          });
        }
      }), (0, _jsxRuntime.jsx)(_ordersCardConsignment.default, {
        consignments: item == null || (_item$detailsData = item.detailsData) == null ? undefined : _item$detailsData.consignments,
        onPress: function onPress(consignment) {
          return _onPress({
            consignment: consignment,
            orderItem: item
          });
        }
      })]
    });
  };
  var _default = exports.default = OrdersListingCard;
