  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[2]);
  var _ordersListingCard = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _ordersCardConsignment = _$$_REQUIRE(_dependencyMap[8]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _enum = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var CHECKOUT_TYPE_ICON = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _enum.OrderConsignmentCheckoutType.ARRIVAL, _icons.PlaneOutlineArrival), _enum.OrderConsignmentCheckoutType.DEPARTURE, _icons.PlaneOutlineDeparture), _enum.OrderConsignmentCheckoutType.LANDSIDE, _icons.BarOutline2), "DEFAULT", _icons.DeliveryOutline);
  var OrdersCardConsignment = function OrdersCardConsignment(props) {
    var consignments = props.consignments,
      _onPress = props.onPress;
    return consignments == null || consignments.map == null ? undefined : consignments.map(function (item, index, list) {
      var _products$;
      var checkoutType = item.checkoutType,
        products = item.products,
        statusDisplay = item.statusDisplay,
        subTotal = item.subTotal,
        title = item.title,
        totalItems = item.totalItems;
      var contentCard = (0, _i18n.translate)((0, _utils.toNumber)(totalItems) > 1 ? "bookingsOrdersScreen.orderConsignment.content_plural" : "bookingsOrdersScreen.orderConsignment.content_singular", {
        price: subTotal || "N/A",
        itemAmount: totalItems || "N/A"
      });
      var orderStatusText = statusDisplay == null ? undefined : statusDisplay.text;
      var orderStatusColor = statusDisplay == null ? undefined : statusDisplay.color;
      var containerStyle = index === (list == null ? undefined : list.length) - 1 ? _ordersCardConsignment.styles.cardContainer : [_ordersCardConsignment.styles.cardContainer, _ordersCardConsignment.styles.borderBottomItem];
      var Icon = CHECKOUT_TYPE_ICON[checkoutType] || CHECKOUT_TYPE_ICON.DEFAULT;
      var iconProps = Object.assign({
        color: _theme.color.palette.darkestGrey
      }, [_enum.OrderConsignmentCheckoutType.ARRIVAL, _enum.OrderConsignmentCheckoutType.DEPARTURE].includes(checkoutType) ? {
        height: 18,
        width: 18
      } : {
        height: 20,
        width: 20
      });
      var imageUrl = products == null || (_products$ = products[0]) == null ? undefined : _products$.imageUrl;
      return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        accessibilityLabel: `${_ordersListingCard.ORDER_CONSIGNMENT_NAME}TouchableOpacity`,
        onPress: function onPress() {
          return _onPress == null ? undefined : _onPress(item);
        },
        style: containerStyle,
        testID: `${_ordersListingCard.ORDER_CONSIGNMENT_NAME}TouchableOpacity`,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          accessibilityLabel: `${_ordersListingCard.ORDER_CONSIGNMENT_NAME}IconLeft`,
          style: _ordersCardConsignment.styles.iconContainerStyle,
          testID: `${_ordersListingCard.ORDER_CONSIGNMENT_NAME}IconLeft`,
          children: (0, _jsxRuntime.jsx)(Icon, Object.assign({}, iconProps))
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          accessibilityLabel: `${_ordersListingCard.ORDER_CONSIGNMENT_NAME}RightContent`,
          style: _ordersCardConsignment.styles.textCardContainer,
          testID: `${_ordersListingCard.ORDER_CONSIGNMENT_NAME}RightContent`,
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _ordersCardConsignment.styles.headerCard,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 1,
              style: _ordersCardConsignment.styles.titleStyle,
              text: title
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            style: _ordersCardConsignment.styles.contentCard,
            text: contentCard
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            style: [_ordersCardConsignment.styles.orderStatusTextStyle, {
              color: orderStatusColor
            }],
            text: orderStatusText
          })]
        }), imageUrl && (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: imageUrl
          },
          style: _ordersCardConsignment.styles.imageCardStyle
        })]
      });
    });
  };
  var _default = exports.default = OrdersCardConsignment;
