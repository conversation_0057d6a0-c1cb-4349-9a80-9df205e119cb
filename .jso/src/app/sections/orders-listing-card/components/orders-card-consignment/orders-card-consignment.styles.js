  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    borderBottomItem: {
      borderBottomWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      paddingBottom: 16
    },
    cardContainer: {
      alignItems: "flex-start",
      flexDirection: "row",
      paddingTop: 16
    },
    contentCard: Object.assign({}, _text.regularFontStyle, {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.almostBlackGrey
    }),
    headerCard: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 4
    },
    iconContainerStyle: {
      paddingHorizontal: 4
    },
    imageCardStyle: {
      borderRadius: 8,
      height: 50,
      resizeMode: "contain",
      width: 50
    },
    orderStatusTextStyle: Object.assign({}, _text.boldFontStyle, {
      fontSize: 12,
      lineHeight: 16,
      marginTop: 4
    }),
    textCardContainer: {
      flex: 1,
      marginLeft: 8,
      marginRight: 8
    },
    titleStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      lineHeight: 20
    })
  });
