  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[1]);
  var _ordersCardHeader = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _currency = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var IconPlaceholder = function IconPlaceholder() {
    return (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
      duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
      shimmerColors: _theme.color.shimmerPlacholderColor,
      shimmerStyle: _ordersCardHeader.loadingStyles.iconStyle
    });
  };
  var OrdersCardHeader = function OrdersCardHeader(props) {
    var _props$item = props.item,
      item = _props$item === undefined ? {} : _props$item,
      loading = props.loading,
      onPress = props.onPress;
    var image_url = item.image_url,
      title = item.title,
      total = item.total,
      transaction_number = item.transaction_number;
    var totalPrice = total ? (0, _currency.toCurrencyAmount)(total) : "";
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      style: _ordersCardHeader.styles.containerStyle,
      onPress: onPress,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [loading && (0, _jsxRuntime.jsx)(IconPlaceholder, {}), !loading && (image_url ? (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: image_url
          },
          style: _ordersCardHeader.styles.iconStyle
        }) : (0, _jsxRuntime.jsx)(IconPlaceholder, {}))]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _ordersCardHeader.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _ordersCardHeader.styles.typeTextStyle,
          tx: "bookingsOrdersScreen.orderCardTitle"
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _ordersCardHeader.styles.titleContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            style: _ordersCardHeader.styles.titleTextStyle,
            text: title
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            text: totalPrice,
            numberOfLines: 2,
            style: _ordersCardHeader.styles.totalPriceTextStyle
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            style: _ordersCardHeader.styles.contentTextStyle,
            text: transaction_number
          })
        })]
      })]
    });
  };
  var _default = exports.default = OrdersCardHeader;
