  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.loadingStyles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      flexDirection: "row",
      gap: 12,
      marginBottom: 8
    },
    iconStyle: {
      height: 60,
      width: 60
    },
    contentContainerStyle: {
      flex: 1,
      justifyContent: "space-between"
    },
    typeTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 11,
      lineHeight: 14
    }),
    titleContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    titleTextStyle: Object.assign({}, _text.boldFontStyle, {
      fontSize: 16,
      lineHeight: 20
    }),
    totalPriceTextStyle: Object.assign({}, _text.boldFontStyle),
    contentTextStyle: Object.assign({}, _text.regularFontStyle)
  });
  var loadingStyles = exports.loadingStyles = _reactNative.StyleSheet.create({
    iconStyle: {
      height: 60,
      width: 60
    }
  });
