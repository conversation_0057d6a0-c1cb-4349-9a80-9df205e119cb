  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      marginHorizontal: 16,
      marginTop: 12,
      padding: 16
    }, _theme.shadow.primaryShadow),
    firstCardContainerStyle: {
      marginTop: 24
    }
  });
