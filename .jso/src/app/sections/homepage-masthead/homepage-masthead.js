  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var React = _react;
  var _native = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _homepageMasthead = _$$_REQUIRE(_dependencyMap[5]);
  var _adobe = _$$_REQUIRE(_dependencyMap[6]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _account = _$$_REQUIRE(_dependencyMap[8]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[9]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[10]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var sourceSystem = _constants.SOURCE_SYSTEM.ECARD;
  var HomePageMasthead = function HomePageMasthead(props) {
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "HomePageMasthead" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "HomePageMasthead" : _props$accessibilityL,
      _onChangiPayPressed = props.onChangiPayPressed,
      onReLoadUpComingEvent = props.onReLoadUpComingEvent;
    var navigation = (0, _native.useNavigation)();
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      enableSearchV2 = _useContext.enableSearchV2;
    var isEnableSearchV2 = (0, _remoteConfig.isFlagOnCondition)(enableSearchV2);
    var onChangiRewardsIconPressed = React.useCallback(function (isLoggedIn) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeECard, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeECard, "1"));
      if (isLoggedIn) {
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
        _changiEcardControler.default.showModal(navigation);
      } else {
        //@ts-ignore
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: sourceSystem,
          callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
            return _changiEcardControler.default.showModal(navigation);
          }
        });
      }
    }, []);
    var onSearchBarPressed = React.useCallback(function () {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeSearch, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeSearch, "1"));
      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.search, {
        sourcePage: _adobe.AdobeTagName.CAppHomePage,
        screen: isEnableSearchV2 ? _searchIndex.SearchIndex.flights : undefined
      });
    }, [isEnableSearchV2]);
    var onChangiRewardsPointsPressed = React.useCallback(function (isLoggedIn) {
      if (isLoggedIn) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeCRPoints, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeCRPoints, "1"));
        //@ts-ignore
        navigation.navigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
          screen: _constants.NavigationConstants.pointsTab
        });
      } else {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeLogin, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeLogin, "1"));
        //@ts-ignore
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: sourceSystem
        });
      }
    }, [sourceSystem]);
    return (0, _jsxRuntime.jsx)(_homepageMasthead.HomepageMasthead, {
      sourceSystem: sourceSystem,
      onSearchBarPressed: onSearchBarPressed,
      testID: testID,
      accessibilityLabel: accessibilityLabel,
      onChangiRewardsPointsPressed: onChangiRewardsPointsPressed,
      onChangiPayPressed: function onChangiPayPressed() {
        return _onChangiPayPressed();
      },
      onChangiRewardsIconPressed: onChangiRewardsIconPressed,
      onReLoadUpComingEvent: onReLoadUpComingEvent
    });
  };
  var _default = exports.default = React.memo(HomePageMasthead);
