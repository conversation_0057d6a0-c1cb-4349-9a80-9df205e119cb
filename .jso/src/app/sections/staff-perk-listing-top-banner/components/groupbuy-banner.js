  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _staffPerkRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[10]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactNativeSnapCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _validate = _$$_REQUIRE(_dependencyMap[18]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[19]);
  var _store = _$$_REQUIRE(_dependencyMap[20]);
  var _pageConfigSaga = _$$_REQUIRE(_dependencyMap[21]);
  var _enum = _$$_REQUIRE(_dependencyMap[22]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _account = _$$_REQUIRE(_dependencyMap[24]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[25]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[26]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var HEIGHT_WIDTH_ITEM_BANNER_RATIO = 0.****************;
  var HEIGHT_WIDTH_IMAGE_BANNER_RATIO = 0.6655948553054662;
  var HEIGHT_WIDTH_BANNER_RATIO = 1.0388059701492538;
  var WIDTH_ITEM_BANNER = screenWidth - 40;
  var HEIGHT_ITEM_BANNER = WIDTH_ITEM_BANNER * HEIGHT_WIDTH_ITEM_BANNER_RATIO;
  var HEIGHT_IMAGE_BANNER = (WIDTH_ITEM_BANNER - 24) * HEIGHT_WIDTH_IMAGE_BANNER_RATIO;
  var HEIGHT_BANNER = WIDTH_ITEM_BANNER * HEIGHT_WIDTH_BANNER_RATIO;
  var DATE_RANGE_POSITION_TOP = HEIGHT_IMAGE_BANNER - 3;
  var CURRENCY_SYMBOL = "S$";
  var UPCOMING = "upcoming";
  var formatPrice = function formatPrice(price) {
    return `${CURRENCY_SYMBOL}${price}`;
  };
  var BTN_COLOR = ["#8A2AA2", "#7A35B0"];
  var formatDateRange = function formatDateRange(createdDate, endDate) {
    var createdDateMoment = (0, _moment.default)(createdDate);
    var endDateMoment = (0, _moment.default)(endDate);
    var endDateFormat = endDateMoment.format(_dateTime.DateFormats.DayMonthYear);
    var createdDateFormat = createdDateMoment.format("DD");
    var createdYear = createdDateMoment.year();
    var createdMonth = createdDateMoment.month();
    var endDateYear = endDateMoment.year();
    var endDateMonth = endDateMoment.month();
    if (createdYear != endDateYear) {
      createdDateFormat = createdDateMoment.format(_dateTime.DateFormats.DayMonthYear);
    } else if (createdMonth != endDateMonth) {
      createdDateFormat = createdDateMoment.format(_dateTime.DateFormats.DateMonth);
    }
    return `${createdDateFormat} - ${endDateFormat}`.toUpperCase();
  };
  var GroupbuyBanner = function GroupbuyBanner() {
    var carouselRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      activeIndex = _useState2[0],
      setActiveIndex = _useState2[1];
    var groupbuyBannerPayload = (0, _reactRedux.useSelector)(_staffPerkRedux.StaffPerkSelectors.groupbuyBannerPayload);
    var navigation = (0, _native.useNavigation)();
    var flagContext = (0, _react.useContext)(_account.AccountContext);
    var onHandleBuyPress = function onHandleBuyPress(item) {
      var isFlagOn = (0, _remoteConfig.isFlagOnCondition)(flagContext.groupBuy);
      if (isFlagOn) {
        handleCSMNavigation(item);
      } else {
        openWebBuy(item);
      }
    };
    var handleCSMNavigation = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (item) {
        var _store$getState$profi;
        var ecid = yield (0, _adobe.getExperienceCloudId)();
        var payload = {
          stateCode: _constants.StateCode.GROUPBUY,
          params: "",
          input: {
            uid: (_store$getState$profi = _store.store.getState().profileReducer) == null || (_store$getState$profi = _store$getState$profi.profilePayload) == null ? undefined : _store$getState$profi.id,
            landingPage: (item == null ? undefined : item.bannerType) === UPCOMING ? "upcoming" : "active",
            ecid: ecid
          }
        };
        try {
          var response = yield (0, _pageConfigSaga.getDeepLinkV2)(payload, true);
          if (response != null && response.redirectUri) {
            //@ts-ignore
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: response == null ? undefined : response.redirectUri,
              needBackButton: true,
              needCloseButton: true,
              headerType: _enum.WebViewHeaderTypes.default,
              basicAuthCredential: response == null ? undefined : response.basicAuth
            });
          } else {
            _globalLoadingController.default.showRetryBottomRef();
            _globalLoadingController.default.setCurrentRetryLabel("Close");
          }
        } catch (error) {
          _globalLoadingController.default.showRetryBottomRef();
          _globalLoadingController.default.setCurrentRetryLabel("Close");
        }
      });
      return function handleCSMNavigation(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var openWebBuy = function openWebBuy(item) {
      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: item == null ? undefined : item.webviewUrl
      });
    };
    var trackingData = (0, _react.useCallback)(function (item) {
      var galaxyLandingTrackValue = `Group Buy | ${item == null ? undefined : item.productName}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyLanding, galaxyLandingTrackValue));
      return false;
    }, []);
    var trackingSwipe = (0, _react.useCallback)(function (index) {
      if ((0, _validate.isArray)(groupbuyBannerPayload)) {
        var _groupbuyBannerPayloa;
        var galaxyLandingTrackValue = `Swipe | ${(_groupbuyBannerPayloa = groupbuyBannerPayload[index]) == null ? undefined : _groupbuyBannerPayloa.productName}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppGalaxyLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppGalaxyLanding, galaxyLandingTrackValue));
      }
    }, [groupbuyBannerPayload]);
    var renderItem = function renderItem(_ref2) {
      var item = _ref2.item;
      return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: function onPress() {
          return onHandleBuyPress(item);
        },
        onStartShouldSetResponder: function onStartShouldSetResponder() {
          return trackingData(item);
        },
        style: styles.itemBanner,
        children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
          source: {
            uri: item.productImage
          },
          style: styles.imageBanner
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.dateRangeContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.dateRangeTxt,
            text: formatDateRange(item == null ? undefined : item.startDate, item == null ? undefined : item.endDate)
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.inforBanner,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.contentBanner,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.productName,
              text: item == null ? undefined : item.productName,
              numberOfLines: 2
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.priceContainer,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.groupBuyPrice,
                text: formatPrice(item == null ? undefined : item.groupBuyPrice)
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.retailPrice,
                text: formatPrice(item == null ? undefined : item.retailPrice)
              })]
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: styles.btnBuyContainer,
            children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              end: {
                x: 0,
                y: 1
              },
              colors: BTN_COLOR,
              start: {
                x: 0,
                y: 0
              },
              style: styles.btnBuyBackground,
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                onPress: function onPress() {
                  return onHandleBuyPress(item);
                },
                textStyle: styles.btnBuy,
                tx: (item == null ? undefined : item.bannerType) === UPCOMING ? "staffPerkListing.groupBuyBanner.view" : "staffPerkListing.groupBuyBanner.buy"
              })
            })
          })]
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.groupbuyBannerHeaderContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.groupBuyTxt,
            tx: "staffPerkListing.groupBuyBanner.groupBuy"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.newDealsEveryMonday,
            tx: "staffPerkListing.groupBuyBanner.newDeals"
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.carousel,
          children: (0, _jsxRuntime.jsx)(_reactNativeSnapCarousel.default, {
            data: groupbuyBannerPayload || [],
            ref: carouselRef,
            renderItem: renderItem,
            sliderWidth: screenWidth,
            itemWidth: WIDTH_ITEM_BANNER // Adjust item width to create some padding
            ,
            onSnapToItem: function onSnapToItem(index) {
              setActiveIndex(index);
              trackingSwipe(index);
            },
            hasParallaxImages: true // This enables the parallax effect
            ,
            inactiveSlideScale: 0.925,
            inactiveSlideOpacity: 0.5
          })
        })]
      }), (groupbuyBannerPayload == null ? undefined : groupbuyBannerPayload.length) > 1 && (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.groupbuyIndicator,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.indicatorGroup,
          children: [0, 1].map(function (ele, index) {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: ele === activeIndex ? styles.activeIndicator : styles.inActiveIndicator
            }, index);
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.nextWeekDeal,
          tx: "staffPerkListing.groupBuyBanner.nextWeekDeal"
        })]
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 16,
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      height: HEIGHT_BANNER
    },
    imageBanner: {
      width: "100%",
      height: HEIGHT_IMAGE_BANNER,
      borderRadius: 4,
      resizeMode: "cover",
      justifyContent: "flex-end"
    },
    carousel: {
      position: "absolute",
      top: 33
    },
    groupbuyBannerHeaderContainer: {
      position: "absolute",
      top: 9,
      width: WIDTH_ITEM_BANNER,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center"
    },
    groupBuyTxt: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 18,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginLeft: 16,
      fontFamily: _reactNative.Platform.select({
        ios: "Lato-Bold",
        android: "Lato-BoldItalic"
      }),
      fontStyle: _reactNative.Platform.select({
        ios: "italic",
        android: "normal"
      }),
      letterSpacing: 1
    }),
    newDealsEveryMonday: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 14,
      fontSize: 10,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginRight: 16,
      letterSpacing: 1
    }),
    itemBanner: {
      backgroundColor: _theme.color.palette.whiteGrey,
      padding: 12,
      width: WIDTH_ITEM_BANNER,
      height: HEIGHT_ITEM_BANNER,
      borderRadius: 12,
      borderColor: _theme.color.palette.lightestPurple,
      borderWidth: 2,
      borderTopWidth: 0
    },
    inforBanner: {
      flexDirection: "row",
      marginTop: 12
    },
    contentBanner: {
      width: "75%"
    },
    productName: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      lineHeight: 20,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginBottom: 4
    }),
    priceContainer: {
      flexDirection: "row",
      alignItems: "center"
    },
    groupBuyPrice: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      lineHeight: 20,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginRight: 8
    }),
    retailPrice: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkGrey999,
      textDecorationLine: "line-through"
    }),
    btnBuyContainer: {
      flex: 1,
      alignItems: "flex-end",
      justifyContent: "center"
    },
    btnBuy: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 20,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      })
    }),
    btnBuyBackground: {
      backgroundColor: _theme.color.palette.lightPurple,
      height: 28,
      paddingHorizontal: 22,
      borderRadius: 60,
      alignItems: "center",
      justifyContent: "center"
    },
    dateRangeContainer: {
      position: "absolute",
      top: DATE_RANGE_POSITION_TOP,
      left: 12,
      borderTopRightRadius: 4,
      borderBottomLeftRadius: 4,
      backgroundColor: _theme.color.palette.almostBlackGrey,
      paddingHorizontal: 6,
      paddingVertical: 4
    },
    dateRangeTxt: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 14,
      fontSize: 11,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      })
    }),
    groupbuyIndicator: {
      marginTop: 6,
      width: "100%",
      paddingHorizontal: 42,
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center"
    },
    indicatorGroup: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center"
    },
    inActiveIndicator: {
      width: 4,
      height: 4,
      backgroundColor: _theme.color.palette.greyCCCCCC,
      borderRadius: 99,
      marginRight: 2
    },
    activeIndicator: {
      width: 12,
      height: 4,
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 99,
      marginRight: 2
    },
    nextWeekDeal: Object.assign({
      marginLeft: 6
    }, _text.presets.bodyTextBold, {
      color: _theme.color.palette.darkGrey999,
      lineHeight: 14,
      fontSize: 11,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      })
    })
  });
  var _default = exports.default = GroupbuyBanner;
