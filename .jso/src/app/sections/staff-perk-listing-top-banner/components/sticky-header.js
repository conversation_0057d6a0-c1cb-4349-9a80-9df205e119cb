  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[1]);
  var _staffPerkListingTopBanner = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _useAnimated = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var V_OFFSET_BREAKING_POINT = 130;
  var AnimatedView = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.View);
  var AnimatedText = _reactNativeReanimated.default.createAnimatedComponent(_reactNative2.Text);
  var AnimatedPath = _reactNativeReanimated.default.createAnimatedComponent(_reactNativeSvg.Path);
  var adapter = (0, _useAnimated.createSvgAnimatedPropAdapter)();
  var AnimatedBackArrow = function AnimatedBackArrow(_ref) {
    var animatedProps = _ref.animatedProps;
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
      width: 24,
      height: 24,
      viewBox: "0 0 24 24",
      children: (0, _jsxRuntime.jsx)(AnimatedPath, {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M4.29289 11.2929C4.10536 11.4804 4 11.7348 4 12C4 12.2652 4.10536 12.5196 4.29289 12.7071L9.29289 17.7071C9.68342 18.0976 10.3166 18.0976 10.7071 17.7071C11.0976 17.3166 11.0976 16.6834 10.7071 16.2929L7.41421 13L18 13C18.5523 13 19 12.5523 19 12C19 11.4477 18.5523 11 18 11L7.41421 11L10.7071 7.70711C11.0976 7.31658 11.0976 6.68342 10.7071 6.29289C10.3166 5.90237 9.68342 5.90237 9.29289 6.29289L4.29289 11.2929Z",
        animatedProps: animatedProps
      })
    });
  };
  var _worklet_16531544646304_init_data = {
    code: "function stickyHeaderTsx1(){const{interpolateColor,vOffset,V_OFFSET_BREAKING_POINT,color,interpolate,Platform,Extrapolation}=this.__closure;const backgroundColor=interpolateColor(vOffset.value,[0,V_OFFSET_BREAKING_POINT],[\"transparent\",color.palette.whiteGrey]);const shadowOpacity=interpolate(vOffset.value,[0,V_OFFSET_BREAKING_POINT],[0,Platform.OS===\"android\"?0.16:1],Extrapolation.CLAMP);const elevation=interpolate(vOffset.value,[-1,0,V_OFFSET_BREAKING_POINT],[0,0,2],Extrapolation.CLAMP);return{backgroundColor:backgroundColor,elevation:elevation,shadowOpacity:shadowOpacity};}"
  };
  var _worklet_11013404308220_init_data = {
    code: "function stickyHeaderTsx2(){const{interpolateColor,vOffset,V_OFFSET_BREAKING_POINT,color}=this.__closure;const textColor=interpolateColor(vOffset.value,[0,V_OFFSET_BREAKING_POINT],[color.palette.whiteGrey,color.palette.darkestGrey]);return{color:textColor};}"
  };
  var _worklet_3457183137519_init_data = {
    code: "function stickyHeaderTsx3(){const{interpolateColor,vOffset,V_OFFSET_BREAKING_POINT,color}=this.__closure;const fill=interpolateColor(vOffset.value,[0,V_OFFSET_BREAKING_POINT],[color.palette.whiteGrey,color.palette.darkestGrey]);return{fill:fill};}"
  };
  var StaffPerkStickyHeader = function StaffPerkStickyHeader(props) {
    var navigation = props.navigation,
      vOffset = props.vOffset;
    var callAEMData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.STAFF_PERK_LISTING_TOP_BANNER));
    var topBannerData = callAEMData == null ? undefined : callAEMData.data;
    var animatedContainerStyles = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var stickyHeaderTsx1 = function stickyHeaderTsx1() {
        var backgroundColor = (0, _reactNativeReanimated.interpolateColor)(vOffset.value, [0, V_OFFSET_BREAKING_POINT], ["transparent", _theme.color.palette.whiteGrey]);
        var shadowOpacity = (0, _reactNativeReanimated.interpolate)(vOffset.value, [0, V_OFFSET_BREAKING_POINT], [0, _reactNative.Platform.OS === "android" ? 0.16 : 1], _reactNativeReanimated.Extrapolation.CLAMP);
        var elevation = (0, _reactNativeReanimated.interpolate)(vOffset.value, [-1, 0, V_OFFSET_BREAKING_POINT], [0, 0, 2], _reactNativeReanimated.Extrapolation.CLAMP);
        return {
          backgroundColor: backgroundColor,
          elevation: elevation,
          shadowOpacity: shadowOpacity
        };
      };
      stickyHeaderTsx1.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        vOffset: vOffset,
        V_OFFSET_BREAKING_POINT: V_OFFSET_BREAKING_POINT,
        color: _theme.color,
        interpolate: _reactNativeReanimated.interpolate,
        Platform: _reactNative.Platform,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      stickyHeaderTsx1.__workletHash = 16531544646304;
      stickyHeaderTsx1.__initData = _worklet_16531544646304_init_data;
      return stickyHeaderTsx1;
    }(), [vOffset.value]);
    var animatedTitleStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var stickyHeaderTsx2 = function stickyHeaderTsx2() {
        var textColor = (0, _reactNativeReanimated.interpolateColor)(vOffset.value, [0, V_OFFSET_BREAKING_POINT], [_theme.color.palette.whiteGrey, _theme.color.palette.darkestGrey]);
        return {
          color: textColor
        };
      };
      stickyHeaderTsx2.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        vOffset: vOffset,
        V_OFFSET_BREAKING_POINT: V_OFFSET_BREAKING_POINT,
        color: _theme.color
      };
      stickyHeaderTsx2.__workletHash = 11013404308220;
      stickyHeaderTsx2.__initData = _worklet_11013404308220_init_data;
      return stickyHeaderTsx2;
    }(), [vOffset.value]);
    var backArrowProps = (0, _reactNativeReanimated.useAnimatedProps)(function () {
      var stickyHeaderTsx3 = function stickyHeaderTsx3() {
        var fill = (0, _reactNativeReanimated.interpolateColor)(vOffset.value, [0, V_OFFSET_BREAKING_POINT], [_theme.color.palette.whiteGrey, _theme.color.palette.darkestGrey]);
        return {
          fill: fill
        };
      };
      stickyHeaderTsx3.__closure = {
        interpolateColor: _reactNativeReanimated.interpolateColor,
        vOffset: vOffset,
        V_OFFSET_BREAKING_POINT: V_OFFSET_BREAKING_POINT,
        color: _theme.color
      };
      stickyHeaderTsx3.__workletHash = 3457183137519;
      stickyHeaderTsx3.__initData = _worklet_3457183137519_init_data;
      return stickyHeaderTsx3;
    }(), [vOffset.value]
    // adapter,
    );
    return (0, _jsxRuntime.jsxs)(AnimatedView, {
      style: [styles.containerStyle, animatedContainerStyles],
      children: [(0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        accessibilityLabel: `${_staffPerkListingTopBanner.COMPONENT_NAME}_BackButton`,
        onPress: function onPress() {
          return navigation == null ? undefined : navigation.goBack();
        },
        style: styles.backBtnStyle,
        testID: `${_staffPerkListingTopBanner.COMPONENT_NAME}_BackButton`,
        children: (0, _jsxRuntime.jsx)(AnimatedBackArrow, {
          animatedProps: backArrowProps
        })
      }), (0, _jsxRuntime.jsx)(AnimatedText, {
        style: [styles.titleTextStyle, animatedTitleStyle],
        children: topBannerData == null ? undefined : topBannerData.title
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.shareBtnStyle
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      height: 100,
      left: 0,
      paddingBottom: 16,
      paddingHorizontal: 16,
      paddingTop: 60,
      position: "absolute",
      top: 0,
      width: "100%",
      zIndex: 1
    }, _theme.shadow.filterHeaderShadow, {
      shadowColor: _reactNative.Platform.select({
        android: _theme.color.palette.almostBlackGrey,
        ios: "rgba(18, 18, 18, 0.08)"
      })
    }),
    backBtnStyle: {
      width: 28
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold),
    shareBtnStyle: {
      width: 28
    }
  });
  var _default = exports.default = StaffPerkStickyHeader;
