  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.lightGreyLoadingColors = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  /* eslint-disable react-native/no-color-literals */

  var _Dimensions$get = _reactNative.Dimensions.get('screen'),
    width = _Dimensions$get.width;
  var widthForTitle = width * 0.8 < 500 ? width * 0.8 : 500;
  var WIDTH_ITEM_BANNER = width - 40;
  var lightGreyLoadingColors = exports.lightGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var ratioImageBackground = 0.68;
  var ratioTopImageCurve = 0.592;
  var ratioHeightCurve = 0.17066666666666666;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    viewContainer: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    caption1TopBanner: Object.assign({}, _text.presets.h6, {
      color: _theme.color.palette.baseYellow,
      lineHeight: 28,
      marginBottom: 8,
      marginTop: 16,
      textAlign: "center",
      textAlignVertical: "center",
      width: widthForTitle
    }),
    caption2TopBanner: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.whiteGrey,
      marginBottom: 8,
      textAlign: "center",
      textAlignVertical: "center",
      width: widthForTitle
    }),
    container: {
      left: 0,
      position: "absolute",
      right: 0,
      top: 0
    },
    containerTopBar: {
      paddingBottom: 16,
      paddingHorizontal: 16,
      paddingTop: 60
    },
    imageBackground: {
      position: 'absolute',
      top: 0,
      width: "100%",
      height: ratioImageBackground * width
    },
    shimmerCaption1TopBanner: {
      height: 28,
      marginBottom: 8,
      width: 327
    },
    shimmerCaption2TopBanner: {
      height: 20,
      width: 327
    },
    shimmerTitleTopBanner: {
      height: 16,
      marginBottom: 16,
      width: 191
    },
    titleTopBanner: Object.assign({}, _text.presets.caption2Bold, {
      letterSpacing: 2.4,
      paddingHorizontal: 24,
      textAlign: "center",
      textTransform: "uppercase"
    }),
    wrapContentTopBanner: {
      width: '100%',
      alignItems: 'center',
      paddingTop: 105
    },
    viewEmpty: {
      width: '100%',
      alignItems: 'center',
      height: ratioImageBackground * width - 105
    },
    viewError: {
      paddingTop: 26,
      width: '100%',
      alignItems: 'center',
      paddingBottom: 68
    },
    txtError: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      color: _theme.color.palette.almostWhiteGrey80,
      textAlign: 'center',
      marginTop: 6
    }),
    groupbuyBannerBgContainer: {
      alignItems: "center"
    },
    groupbuyBannerBg: {
      width: WIDTH_ITEM_BANNER,
      height: 152,
      position: "absolute",
      top: 120,
      borderRadius: 12
    },
    backgroundCurveContainer: {
      width: "100%",
      height: ratioHeightCurve * width,
      position: "absolute",
      top: ratioTopImageCurve * width,
      overflow: "hidden"
    },
    backgroundCurve: {
      width: "100%"
    }
  });
