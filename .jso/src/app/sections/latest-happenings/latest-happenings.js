  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LatestHappenings = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _latestHappening = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _error = _$$_REQUIRE(_dependencyMap[8]);
  var _errorProps = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var viewContainerStyle = {
    marginLeft: 24,
    marginBottom: 16,
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var wrapContainerWithSeparator = {
    marginBottom: 50
  };
  var outerContainerStyle = {
    marginRight: 16
  };
  var nestedOuterContainerStyle = {
    justifyContent: "space-between"
  };
  var contentContainerStyle = {
    paddingLeft: 24
  };
  var errorContainerStyle = Object.assign({}, viewContainerStyle, {
    marginBottom: 0
  });
  var lastItemEmptyStyle = {
    marginBottom: -50
  };
  var renderFlatListData = function renderFlatListData(_ref) {
    var item = _ref.item,
      onSelected = _ref.onSelected,
      index = _ref.index,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel;
    var isItemArray = (0, _lodash.isArray)(item);
    return isItemArray ? (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [nestedOuterContainerStyle, outerContainerStyle],
      children: item.map(function (childItem, childItemIndex) {
        return (0, _jsxRuntime.jsx)(_latestHappening.default, Object.assign({}, childItem, {
          onPressed: function onPressed() {
            return onSelected == null ? undefined : onSelected(childItem);
          },
          testID: `${testID}__ItemLatestHappening__${childItemIndex}`,
          accessibilityLabel: `${accessibilityLabel}__ItemLatestHappening__${childItemIndex}`
        }), childItemIndex);
      })
    }) : (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: outerContainerStyle,
      children: (0, _jsxRuntime.jsx)(_latestHappening.default, Object.assign({}, item, {
        onPressed: function onPressed() {
          return onSelected == null ? undefined : onSelected(item);
        },
        testID: `${testID}__ItemLatestHappening__${index}`,
        accessibilityLabel: `${accessibilityLabel}__ItemLatestHappening__${index}`
      }))
    });
  };
  var LatestHappenings = exports.LatestHappenings = React.memo(function (props) {
    var _latestHappeningsPayl;
    var onSelected = props.onSelected,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "LatestHappenings" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "LatestHappenings" : _props$accessibilityL,
      _props$useSeparator = props.useSeparator,
      useSeparator = _props$useSeparator === undefined ? false : _props$useSeparator,
      isLastItem = props.isLastItem;
    var isLogin = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var dispatch = (0, _reactRedux.useDispatch)();
    var latestHappeningsPayload = (0, _reactRedux.useSelector)(function (data) {
      return _exploreRedux.ExploreSelectors.latestHappeningsData(data);
    });
    var loadLatestHappenings = React.useCallback(function () {
      dispatch(_exploreRedux.default.latestHappeningsRequest());
    }, []);
    React.useEffect(function () {
      loadLatestHappenings();
    }, [isLogin]);
    if (latestHappeningsPayload != null && latestHappeningsPayload.hasError) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: errorContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            children: (0, _i18n.translate)("exploreScreen.latestHappenings")
          })
        }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _errorProps.ErrorComponentType.standard,
          onPressed: loadLatestHappenings,
          testID: `${testID}__ErrorComponent`,
          accessibilityLabel: `${accessibilityLabel}__ErrorComponent`
        })]
      });
    }
    if (latestHappeningsPayload === null || latestHappeningsPayload === undefined || (latestHappeningsPayload == null || (_latestHappeningsPayl = latestHappeningsPayload.data) == null ? undefined : _latestHappeningsPayl.length) === 0) {
      if (isLastItem) return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: lastItemEmptyStyle
      });
      return null;
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: useSeparator ? wrapContainerWithSeparator : {},
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: viewContainerStyle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          children: (0, _i18n.translate)("exploreScreen.latestHappenings")
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: latestHappeningsPayload.data,
        contentContainerStyle: contentContainerStyle,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item,
            index = _ref2.index;
          return renderFlatListData({
            item: item,
            onSelected: onSelected,
            index: index,
            testID: testID,
            accessibilityLabel: accessibilityLabel
          });
        },
        horizontal: true,
        scrollEnabled: true,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return `LatestHappenings-FlatList-${index.toString()}`;
        },
        testID: `${testID}__FlatListLatestHappening`,
        accessibilityLabel: `${accessibilityLabel}__FlatListLatestHappening`,
        initialNumToRender: 3,
        windowSize: 1,
        maxToRenderPerBatch: 3
      })]
    });
  });
