  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.AboutYourPoints = AboutYourPoints;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[7]);
  var _menuOption2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _error = _$$_REQUIRE(_dependencyMap[9]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingData = Array(3).fill({
    type: _menuOption.MenuOptionType.loading
  });
  var COMPONENT_NAME = "AboutYourPoints";
  function AboutYourPoints() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var aboutYourPointState = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.ABOUT_YOUR_POINT));
    var aboutYourPointsFetching = aboutYourPointState.loading;
    var aboutYourPointsPayload = aboutYourPointState.data;
    var error = aboutYourPointState.error;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ACCOUNT_ABOUT_YOUR_POINT"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var onPress = function onPress(item) {
      // Handle external link
      var _ref = item || {},
        navigationType = _ref.navigationType,
        navigationValue = _ref.navigationValue,
        redirect = _ref.redirect;
      if (!navigationType || !navigationValue) return null;
      handleNavigation(navigationType, navigationValue, redirect);
    };
    var callBackAfterSuccess = function callBackAfterSuccess(data) {
      var _data$list;
      return (_data$list = data.list) == null ? undefined : _data$list.reduce(function (array, currentValue) {
        if (currentValue.displayScreen === _constants.DisplayScreens.forYouRewardsDetails) {
          return [].concat((0, _toConsumableArray2.default)(array), [Object.assign({}, currentValue, {
            navigationType: currentValue.navigation.type,
            navigationValue: currentValue.navigation.value,
            icon: (0, _utils.mappingUrlAem)(currentValue.icon)
          })]);
        }
        return array;
      }, []);
    };
    var fetchAboutYourPoints = function fetchAboutYourPoints() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.ABOUT_YOUR_POINT,
        pathName: "getForYouMoreOptions",
        callBackAfterSuccess: callBackAfterSuccess
      }));
    };
    (0, _react.useEffect)(function () {
      // Load more menu data
      fetchAboutYourPoints();
      return function () {
        dispatch(_aemRedux.default.clearAemConfigData(_aemRedux.AEM_PAGE_NAME.ABOUT_YOUR_POINT));
      };
    }, []);
    if (aboutYourPointsPayload === null || aboutYourPointsFetching) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.containerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.contentContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            style: styles.textStyle,
            tx: "changiRewardsDetail.about"
          }), loadingData.map(function (_, index) {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.itemStyle,
              children: (0, _jsxRuntime.jsx)(_menuOption2.default, {
                type: _menuOption.MenuOptionType.loading,
                testID: `${COMPONENT_NAME}__MenuOptionLoading__${index}`,
                accessibilityLabel: `${COMPONENT_NAME}__MenuOptionLoading__${index}`
              })
            }, index);
          })]
        })
      });
    }
    if (Array.isArray(aboutYourPointsPayload) && !(0, _isEmpty.default)(aboutYourPointsPayload)) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.containerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.contentContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            style: styles.textStyle,
            tx: "changiRewardsDetail.about"
          }), aboutYourPointsPayload.map(function (item, index) {
            return (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.itemStyle,
              children: (0, _jsxRuntime.jsx)(_menuOption2.default, {
                type: item.type,
                icon: item.icon,
                title: item.title,
                redirection: item.redirection,
                navigationType: item.navigationType,
                navigationValue: item.navigationValue,
                onPress: onPress,
                testID: `${COMPONENT_NAME}__MenuOptionAboutYourPoints__${index}`,
                accessibilityLabel: `${COMPONENT_NAME}__MenuOptionAboutYourPoints__${index}`
              })
            }, index);
          })]
        })
      });
    }
    if (!(0, _isEmpty.default)(error)) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.contentContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            style: styles.textStyle,
            tx: "changiRewardsDetail.about"
          })
        }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          onPressed: fetchAboutYourPoints,
          testID: `${COMPONENT_NAME}__ErrorComponentFetchAboutYourPoints`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorComponentFetchAboutYourPoints`
        })]
      });
    }
    return null;
  }
