  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.resultFilterTitles = exports.itemReservation = exports.filterState = exports.default = exports.childReservation = exports.ReservationValues = undefined;
  var _shortcutLink = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _shortcutLink2 = _$$_REQUIRE(_dependencyMap[2]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  // dineReservationRequest
  var ReservationValues = exports.ReservationValues = /*#__PURE__*/function (ReservationValues) {
    ReservationValues["reservations"] = "reservations";
    return ReservationValues;
  }({});
  var COMPONENT_NAME = "DineReservation";
  var styleWithOrderIsSecond = {
    marginBottom: 24
  };
  var filterState = exports.filterState = [{
    main: "availability",
    child: ["reservations"]
  }];
  var resultFilterTitles = exports.resultFilterTitles = [{
    main: "availability",
    tagName: "reservations",
    tagTitle: "Reservations",
    type: "withIcon",
    text: "Reservations"
  }];
  var itemReservation = exports.itemReservation = "availability";
  var childReservation = exports.childReservation = {
    tagName: "reservations",
    tagTitle: "Reservations"
  };
  var DineReservation = function DineReservation() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("DINE_RESERVATION"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        dispatch(_dineRedux.default.dineReservationRequest());
      });
    }, []);
    var listReservation = (0, _reactRedux.useSelector)(_dineRedux.DineSelectors.dineReservationPayload);
    var shortcutLinksOnPressed = function shortcutLinksOnPressed(item) {
      var _ref = (item == null ? undefined : item.navigation) || "",
        type = _ref.type,
        value = _ref.value;
      var _ref2 = item || {},
        redirect = _ref2.redirect;
      handleNavigation(type, value, redirect);
    };
    if ((0, _lodash.isEmpty)(listReservation)) return null;
    return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      horizontal: true,
      showsHorizontalScrollIndicator: false,
      data: listReservation,
      style: styleWithOrderIsSecond,
      renderItem: function renderItem(_ref3) {
        var item = _ref3.item;
        return (0, _jsxRuntime.jsx)(_shortcutLink.default, {
          title: item.title,
          label: item.label,
          icon: (0, _mediaHelper.handleImageUrl)(item.icon),
          type: _shortcutLink2.ShortcutLinkType.default,
          onPressed: function onPressed() {
            return shortcutLinksOnPressed(item);
          },
          testID: `${COMPONENT_NAME}__ItemShortCutLinkDefault`,
          accessibilityLabel: `${COMPONENT_NAME}__ItemShortCutLinkDefault`
        });
      }
    });
  };
  var _default = exports.default = DineReservation;
