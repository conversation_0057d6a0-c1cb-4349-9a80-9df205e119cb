  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Cuisine = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _cuisineCatogory = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _color = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _error = _$$_REQUIRE(_dependencyMap[9]);
  var _errorProps = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _shopRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var parentContainerStyle = {
    backgroundColor: _color.color.transparent
  };
  var itemStyle = {
    marginRight: 12
  };
  var titleContainer = {
    marginLeft: 24,
    marginBottom: 16
  };
  var container = {
    paddingHorizontal: 24
  };
  var errorContainer = {
    marginBottom: 15
  };
  var errorContainerTitle = {
    marginLeft: 24
  };
  var Cuisine = exports.Cuisine = function Cuisine(props) {
    var _cuisinesCategoriesPa;
    var bottomMarginStyle = {
      marginBottom: 50
    };
    var title = props.title,
      screen = props.screen;
    var isDineScreen = screen === "DINE";
    var cuisinesCategoriesPayload = isDineScreen ? (0, _reactRedux.useSelector)(function (data) {
      return _dineRedux.DineSelectors.dineCuisinesData(data);
    }) : (0, _reactRedux.useSelector)(function (data) {
      return _shopRedux.ShopSelectors.shopCategoriesData(data);
    });
    var filterItems = isDineScreen ? (0, _reactRedux.useSelector)(function (data) {
      return _dineRedux.DineSelectors.filterItems(data);
    }) : (0, _reactRedux.useSelector)(function (data) {
      return _shopRedux.ShopSelectors.filterItems(data);
    });
    if (!filterItems) {
      filterItems = [];
    }
    var filterTiles = isDineScreen ? (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterTitles(state);
    }) : (0, _reactRedux.useSelector)(function (state) {
      return _shopRedux.ShopSelectors.filterTitles(state);
    });
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var loadCuisinesCategories = function loadCuisinesCategories() {
      isDineScreen ? dispatch(_dineRedux.default.dineCuisinesRequest()) : dispatch(_shopRedux.default.shopCategoriesRequest());
    };
    _react.default.useEffect(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        loadCuisinesCategories();
      });
    }, []);
    var getCuisine = function getCuisine(_ref) {
      var item = _ref.item;
      item.onPressed = function () {
        var filterData = {
          tagTitle: item.labelCopy,
          tagName: item.tagName,
          filterType: item.filterType
        };
        if (isDineScreen) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineExploreMoreFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineExploreMoreFilters, item == null ? undefined : item.labelCopy));
          dispatch(_dineRedux.default.dineHandleFilterItems(filterData));
          dispatch(_dineRedux.default.dineSetFilterTitles(filterTiles));
          dispatch(_dineRedux.default.startRequestFilter(true));
          navigation.navigate("dineFilterResults", {
            filteredData: filterItems
          });
        } else {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopExploreMoreFilters, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopExploreMoreFilters, item == null ? undefined : item.labelCopy));
          dispatch(_shopRedux.default.shopHandleFilterItems(filterData));
          dispatch(_shopRedux.default.shopSetFilterTitles(filterTiles));
          dispatch(_shopRedux.default.startRequestFilter(true));
          navigation.navigate("shopFilterResults", {
            filteredData: filterItems
          });
        }
      };
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: itemStyle,
        accessible: false,
        children: (0, _jsxRuntime.jsx)(_cuisineCatogory.CuisineCategory, Object.assign({}, item))
      }, Math.random() * 20);
    };
    if (cuisinesCategoriesPayload != null && cuisinesCategoriesPayload.errorFlag) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: errorContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: errorContainerTitle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            text: title
          })
        }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _errorProps.ErrorComponentType.standard,
          onPressed: function onPressed() {
            loadCuisinesCategories();
          }
        })]
      });
    }
    if (!cuisinesCategoriesPayload || (cuisinesCategoriesPayload == null || (_cuisinesCategoriesPa = cuisinesCategoriesPayload.data) == null ? undefined : _cuisinesCategoriesPa.length) === 0) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: Object.assign({}, parentContainerStyle, bottomMarginStyle),
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          text: title
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        contentContainerStyle: container,
        data: cuisinesCategoriesPayload == null ? undefined : cuisinesCategoriesPayload.data,
        renderItem: function renderItem(item) {
          return getCuisine(item);
        },
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        nestedScrollEnabled: true
      })]
    });
  };
