  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _searchRecentSection = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _searchRecentSection2 = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _menuOption = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _menuOption2 = _$$_REQUIRE(_dependencyMap[12]);
  var _recentSearchHook = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _native = _$$_REQUIRE(_dependencyMap[16]);
  var _theme = _$$_REQUIRE(_dependencyMap[17]);
  var _constants = _$$_REQUIRE(_dependencyMap[18]);
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "SearchRecentSectionDefault__";
  var SearchRecentSectionLoading = function SearchRecentSectionLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _searchRecentSection2.styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _searchRecentSection2.styles.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _searchRecentSection2.styles.title,
          text: (0, _i18n.translate)("search.recent.title")
        })
      }), (0, _jsxRuntime.jsx)(_menuOption.default, {
        type: _menuOption2.MenuOptionType.loading
      })]
    });
  };
  var SearchRecentSectionDefault = function SearchRecentSectionDefault(_ref) {
    var onClearAll = _ref.onClearAll,
      itemOnPress = _ref.itemOnPress;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isDisplayed = _useState2[0],
      setIsDisplayed = _useState2[1];
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var _useRecentSearch = (0, _recentSearchHook.useRecentSearch)(),
      recentSearch = _useRecentSearch.recentSearch,
      removeAll = _useRecentSearch.removeAll,
      removeItemKeySearch = _useRecentSearch.removeItemKeySearch,
      addNewRecentSearchKeyword = _useRecentSearch.addNewRecentSearchKeyword,
      refreshRecentSearch = _useRecentSearch.refreshRecentSearch;
    (0, _react.useEffect)(function () {
      refreshRecentSearch();
    }, [isFocused]);
    var onPressClearAll = function onPressClearAll() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppPopularSearches, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppPopularSearches, `${(0, _i18n.translate)("searchV2.recent.title")} | ${_adobe.AdobeValueByTagName.CAppPopularSearchesClearAll}`));
      setIsDisplayed(!isDisplayed);
      onClearAll == null || onClearAll();
      removeAll();
    };
    var onRemoveItem = function onRemoveItem(keyword) {
      removeItemKeySearch(keyword);
    };
    var handleOnItemPress = function handleOnItemPress(item) {
      itemOnPress == null || itemOnPress(item);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppPopularSearches, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppPopularSearches, `${(0, _i18n.translate)("searchV2.recent.title")} | ${item}`));
      addNewRecentSearchKeyword(item);
      dispatch(_searchRedux.default.setSearchKeyword(item));
      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.searchResult);
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      children: isDisplayed && (recentSearch == null ? undefined : recentSearch.length) > 0 && (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _searchRecentSection2.styles.containerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _searchRecentSection2.styles.titleContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _searchRecentSection2.styles.title,
              text: (0, _i18n.translate)("searchV2.recent.title")
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onPressClearAll,
              testID: `${COMPONENT_NAME}ButtonClearAll`,
              accessibilityLabel: `${COMPONENT_NAME}ButtonClearAll`,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _searchRecentSection2.styles.clearAll,
                text: (0, _i18n.translate)("searchV2.recent.clearAllLabel")
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _searchRecentSection2.styles.wrapCard,
            children: isDisplayed && (recentSearch == null ? undefined : recentSearch.map(function (item, index) {
              return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                onPress: function onPress() {
                  return handleOnItemPress(item);
                },
                style: _searchRecentSection2.styles.optionContainer,
                testID: `${COMPONENT_NAME}ItemRecentSearch__${index}`,
                accessibilityLabel: `${COMPONENT_NAME}ItemRecentSearch__${index}`,
                children: [(0, _jsxRuntime.jsx)(_icons.RecentIcon, {
                  color: _theme.color.palette.darkestGrey
                }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: _searchRecentSection2.styles.containerTitleStyle,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    numberOfLines: 1,
                    style: _searchRecentSection2.styles.titleStyle,
                    text: item
                  })
                }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  hitSlop: {
                    top: 10,
                    left: 2,
                    bottom: 10,
                    right: 10
                  },
                  onPress: function onPress() {
                    return onRemoveItem(item);
                  },
                  style: _searchRecentSection2.styles.removeIconStyles,
                  testID: `${COMPONENT_NAME}ButtonDeleteItemRecentSearch__${index}`,
                  accessibilityLabel: `${COMPONENT_NAME}ButtonDeleteItemRecentSearch__${index}`,
                  children: (0, _jsxRuntime.jsx)(_icons.CloseIconV2, {
                    color: _theme.color.palette.darkestGrey
                  })
                })]
              }, index);
            }))
          })]
        })
      })
    });
  };
  var SearchRecentSection = function SearchRecentSection(props) {
    return props.type === _searchRecentSection.SearchRecentSectionType.loading ? (0, _jsxRuntime.jsx)(SearchRecentSectionLoading, {}) : (0, _jsxRuntime.jsx)(SearchRecentSectionDefault, Object.assign({}, props));
  };
  var _default = exports.default = SearchRecentSection;
