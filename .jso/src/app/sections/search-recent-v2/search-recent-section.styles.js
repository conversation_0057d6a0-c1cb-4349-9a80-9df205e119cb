  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapCard: {
      flexWrap: "wrap",
      flexDirection: "row",
      gap: 8
    },
    clearAll: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.lightPurple
    }),
    containerStyle: {
      marginTop: 40,
      paddingHorizontal: 20
    },
    containerTitleStyle: {
      flexShrink: 1
    },
    optionContainer: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderRadius: 99,
      flexDirection: "row",
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      gap: 2,
      flexShrink: 1
    },
    removeIconStyles: {
      alignItems: "center",
      justifyContent: "center"
    },
    title: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    titleContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 16
    },
    titleStyle: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.darkestGrey
    })
  });
