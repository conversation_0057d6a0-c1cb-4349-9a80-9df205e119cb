  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useStaffPerkListingRequests = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _staffPerkSaga = _$$_REQUIRE(_dependencyMap[4]);
  var _size = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _staffPerkCategoryListing = _$$_REQUIRE(_dependencyMap[6]);
  var _explore = _$$_REQUIRE(_dependencyMap[7]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[8]);
  var _set2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _filterBottomSheet = _$$_REQUIRE(_dependencyMap[10]);
  var DEFAULT_PAGE_SIZE = 10;
  var useStaffPerkListingRequests = exports.useStaffPerkListingRequests = function useStaffPerkListingRequests() {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      loadingStaffPerkList = _useState2[0],
      setLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      hasError = _useState4[0],
      setError = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      hasNextPageStaffPerk = _useState6[0],
      setHasNextPageStaffPerk = _useState6[1];
    var _useState7 = (0, _react.useState)([]),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      listData = _useState8[0],
      setListData = _useState8[1];
    var _useState9 = (0, _react.useState)(true),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isFirstRequest = _useState0[0],
      setisFirstRequest = _useState0[1];
    var _useState1 = (0, _react.useState)({}),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      currentFilterParams = _useState10[0],
      setCurrentFilterParams = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      refresh = _useState12[0],
      setRefresh = _useState12[1];
    var isLoadMoreRef = (0, _react.useRef)(false);
    var pageNumber = (0, _react.useRef)(1);
    var _useContext = (0, _react.useContext)(_explore.ExploreContext),
      staffPerksTilesV2Flag = _useContext.staffPerksTilesV2Flag;
    var isStaffPerksTilesV2 = (0, _remoteConfig.isFlagOnCondition)(staffPerksTilesV2Flag);
    var getGraphQLInput = function getGraphQLInput() {
      var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var areaFilters = params.areaFilters,
        categoryFilters = params.categoryFilters,
        sortBy = params.sortBy,
        terminalFilters = params.terminalFilters;
      var isSortByAZ = sortBy === _filterBottomSheet.SortBy.AZ;
      var convertedParams = {
        input: {
          sort_by_a_to_z: isSortByAZ,
          staff_perks_tiles_v2: isStaffPerksTilesV2
        }
      };
      if (!isSortByAZ) {
        (0, _set2.default)(convertedParams, "input.page_number", pageNumber.current);
        (0, _set2.default)(convertedParams, "input.page_size", DEFAULT_PAGE_SIZE);
      }
      if (areaFilters != null && areaFilters.length) {
        (0, _set2.default)(convertedParams, "input.filter_by_locations.area", areaFilters.map(function (area) {
          return area == null ? undefined : area.tagTitle;
        }));
      }
      if (terminalFilters != null && terminalFilters.length) {
        (0, _set2.default)(convertedParams, "input.filter_by_locations.terminal", terminalFilters.map(function (terminal) {
          var _terminal$tagCode;
          if ((terminal == null ? undefined : terminal.tagCode) === "tj") {
            return "J1";
          }
          return terminal == null || (_terminal$tagCode = terminal.tagCode) == null || _terminal$tagCode.toUpperCase == null ? undefined : _terminal$tagCode.toUpperCase();
        }));
      }
      if (categoryFilters != null && categoryFilters.length) {
        (0, _set2.default)(convertedParams, "input.filter_by_categories", categoryFilters.map(function (val) {
          return val;
        }));
      }
      return convertedParams;
    };
    var fetchData = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var moreParams = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
        setCurrentFilterParams(moreParams);
        pageNumber.current = 1;
        var params = getGraphQLInput(moreParams);
        setLoading(true);
        setError(null);
        var response = yield (0, _staffPerkSaga.getStaffPerkListingReq)(params);
        if (response) {
          setLoading(false);
          setRefresh(false);
          if (isFirstRequest) {
            setisFirstRequest(false);
          }
          if (response != null && response.errors) {
            setError(response == null ? undefined : response.errors);
          } else {
            var _params$input;
            var hasNextPage = (0, _size.default)(response == null ? undefined : response.promos) < (response == null ? undefined : response.totalPromosCount);
            setHasNextPageStaffPerk(!(params != null && (_params$input = params.input) != null && _params$input.sort_by_a_to_z) && hasNextPage);
            setListData((0, _staffPerkCategoryListing.transformStaffPerkData)(response == null ? undefined : response.promos));
          }
        } else {
          setLoading(false);
          setRefresh(false);
          setError(true);
        }
      });
      return function fetchData() {
        return _ref.apply(this, arguments);
      };
    }();
    var fetchPaginationData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        isLoadMoreRef.current = true;
        pageNumber.current += 1;
        var params = getGraphQLInput(currentFilterParams);
        setError(null);
        var response = yield (0, _staffPerkSaga.getStaffPerkListingReq)(params);
        if (response) {
          var newRecord = listData.concat((0, _staffPerkCategoryListing.transformStaffPerkData)(response == null ? undefined : response.promos));
          var hasNextPage = (0, _size.default)(newRecord) < (response == null ? undefined : response.totalPromosCount);
          setHasNextPageStaffPerk(hasNextPage);
          setListData(newRecord);
        } else {
          setError(true);
        }
        isLoadMoreRef.current = false;
      });
      return function fetchPaginationData() {
        return _ref2.apply(this, arguments);
      };
    }();
    var loadMoreData = function loadMoreData() {
      if (isLoadMoreRef != null && isLoadMoreRef.current) {
        return;
      }
      if (loadingStaffPerkList) {
        return;
      }
      if (hasNextPageStaffPerk) {
        fetchPaginationData();
      }
    };
    return {
      currentFilterParams: currentFilterParams,
      fetchData: fetchData,
      hasError: hasError,
      hasNextPageStaffPerk: hasNextPageStaffPerk,
      isFirstRequest: isFirstRequest,
      listData: listData,
      loadingStaffPerkList: loadingStaffPerkList,
      loadMoreData: loadMoreData,
      refresh: refresh,
      setRefresh: setRefresh
    };
  };
