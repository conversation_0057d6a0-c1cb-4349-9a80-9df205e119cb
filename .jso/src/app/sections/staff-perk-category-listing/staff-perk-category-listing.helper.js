  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.transformStaffPerkData = undefined;
  var transformStaffPerkData = exports.transformStaffPerkData = function transformStaffPerkData(rawList) {
    return (rawList == null || rawList.map == null ? undefined : rawList.map(function (item) {
      var _item$categories;
      return Object.assign({}, item, {
        categories: item == null || (_item$categories = item.categories) == null || _item$categories.replace == null || (_item$categories = _item$categories.replace(" ", "")) == null || _item$categories.split == null ? undefined : _item$categories.split(",")
      });
    })) || [];
  };
