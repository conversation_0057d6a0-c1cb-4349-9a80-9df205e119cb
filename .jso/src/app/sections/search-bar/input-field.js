  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.InputField = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _textField = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var closeIconStyles = {
    color: _theme.color.palette.darkGrey999
  };
  var InputField = exports.InputField = function InputField(props) {
    // Props
    var highlightOnFocused = props.highlightOnFocused,
      trim = props.trim,
      isInvalid = props.isInvalid,
      isDisabled = props.isDisabled,
      isFocused = props.isFocused,
      _onFocus = props.onFocus,
      _onBlur = props.onBlur,
      _onChangeText = props.onChangeText,
      onSearchClear = props.onSearchClear,
      placeHolderValue = props.placeHolderValue,
      _props$isShowClearAll = props.isShowClearAll,
      isShowClearAll = _props$isShowClearAll === undefined ? false : _props$isShowClearAll,
      iconLeft = props.iconLeft;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      focused = _useState2[0],
      setFocused = _useState2[1];
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: Object.assign({}, styles.inputShadowStyles, focused ? styles.inputShadowFocusedStyles : {}),
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, styles.inputGroupStyle, highlightOnFocused && (focused || isFocused) ? styles.inputGroupFocusedStyle : {}, isInvalid ? styles.inputGroupErrorStyle : {}, isDisabled ? styles.inputGroupDisabledStyle : {}),
        children: [iconLeft != null ? iconLeft : (0, _jsxRuntime.jsx)(_icons.SearchIconInActive, {
          style: styles.searchIconStyle,
          width: "18",
          height: "18"
        }), (0, _jsxRuntime.jsx)(_textField.TextField, Object.assign({
          style: styles.inputFieldStyle,
          inputStyle: Object.assign({}, styles.inputControlStyle, isDisabled ? styles.inputControlDisabledStyle : {}),
          placeholderTextColor: _theme.color.palette.midGrey,
          secureTextEntry: false,
          preset: "noMargin",
          editable: !isDisabled,
          allowFontScaling: false,
          placeholder: focused ? "" : placeHolderValue || null
        }, props, {
          onBlur: function onBlur(e) {
            setFocused(false);
            _onBlur && _onBlur(e);
          },
          onFocus: function onFocus(e) {
            setFocused(true);
            _onFocus && _onFocus(e);
          },
          onChangeText: function onChangeText(e) {
            var val = trim && e ? e.trim() : e;
            _onChangeText && _onChangeText(val);
          }
        })), isShowClearAll && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.searchIconContainer,
          onPress: onSearchClear,
          children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
            fill: "currentColor",
            style: closeIconStyles
          })
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    inputControlDisabledStyle: {
      backgroundColor: _theme.color.palette.transparent,
      color: _theme.color.palette.midGrey
    },
    inputControlStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      minHeight: 0,
      paddingBottom: 0,
      paddingTop: 11
    }),
    inputFieldStyle: {
      flex: 1
    },
    inputGroupDisabledStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderColor: _theme.color.palette.lighterGrey
    },
    inputGroupErrorStyle: {
      borderColor: _theme.color.palette.baseRed
    },
    inputGroupFocusedStyle: {
      borderColor: _theme.color.palette.lightPurple
    },
    inputGroupStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 8,
      borderWidth: 1,
      flexDirection: "row",
      height: 44,
      paddingHorizontal: 12
    },
    inputShadowFocusedStyles: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 10,
      zIndex: -1
    },
    inputShadowStyles: {
      padding: 4,
      zIndex: -1
    },
    searchIconContainer: {
      alignItems: "center",
      height: 44,
      justifyContent: "center",
      position: "relative"
    },
    searchIconStyle: {
      marginLeft: 5,
      marginRight: 10,
      marginTop: 12
    }
  });
  var _default = exports.default = InputField;
