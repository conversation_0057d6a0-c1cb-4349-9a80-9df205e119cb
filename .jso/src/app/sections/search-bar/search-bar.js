  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _inputField = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _inputFieldV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _recentSearchHook = _$$_REQUIRE(_dependencyMap[14]);
  var _theme = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var HelperText = /*#__PURE__*/function (HelperText) {
    HelperText["all"] = "search.placeHolder.all";
    HelperText["dine"] = "search.placeHolder.dine";
    HelperText["shop"] = "search.placeHolder.shop";
    HelperText["flight"] = "search.placeHolder.flights";
    HelperText["airport"] = "search.placeHolder.airport";
    HelperText["attractions"] = "search.placeHolder.attractions";
    HelperText["events"] = "search.placeHolder.events";
    return HelperText;
  }(HelperText || {});
  var SearchBar = function SearchBar(_ref, ref) {
    var tab = _ref.tab,
      keyword = _ref.keyword,
      onPressBack = _ref.onPressBack,
      onChangeKeyword = _ref.onChangeKeyword,
      onSearchClear = _ref.onSearchClear,
      onSubmitEditing = _ref.onSubmitEditing,
      searchAutoCompleteFlag = _ref.searchAutoCompleteFlag,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "SearchBar" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "SearchBar" : _ref$accessibilityLab,
      _ref$isShowBack = _ref.isShowBack,
      isShowBack = _ref$isShowBack === undefined ? true : _ref$isShowBack,
      inputProps = _ref.inputProps,
      arrowLeftColor = _ref.arrowLeftColor,
      _ref$containerStyle = _ref.containerStyle,
      containerStyle = _ref$containerStyle === undefined ? {} : _ref$containerStyle,
      _ref$inputContainerSt = _ref.inputContainerStyle,
      inputContainerStyle = _ref$inputContainerSt === undefined ? {} : _ref$inputContainerSt,
      _ref$useInputFieldV = _ref.useInputFieldV2,
      useInputFieldV2 = _ref$useInputFieldV === undefined ? false : _ref$useInputFieldV,
      _ref$shouldFocusAfter = _ref.shouldFocusAfterClear,
      shouldFocusAfterClear = _ref$shouldFocusAfter === undefined ? false : _ref$shouldFocusAfter,
      _ref$returnKeyType = _ref.returnKeyType,
      returnKeyType = _ref$returnKeyType === undefined ? "search" : _ref$returnKeyType,
      onSubmitLocal = _ref.onSubmitLocal,
      autoFocusTextInput = _ref.autoFocusTextInput;
    var dispatch = (0, _reactRedux.useDispatch)();
    var inputElement = (0, _react.useRef)(null);
    var _React$useState = _react.default.useState(""),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      value = _React$useState2[0],
      setValue = _React$useState2[1];
    var timeout = (0, _react.useRef)(null);
    var timeoutSubmitEditing = (0, _react.useRef)(null);
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      setIsPressSearchKey = _useContext.setIsPressSearchKey;
    var _useRecentSearch = (0, _recentSearchHook.useRecentSearch)(),
      addNewRecentSearchKeyword = _useRecentSearch.addNewRecentSearchKeyword;
    var InputFieldComponent = useInputFieldV2 ? _inputFieldV.default : _inputField.default;
    var onChangeHandler = function onChangeHandler(newValue) {
      setValue(newValue);
      onChangeKeyword(newValue);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        setInputValue: setValue,
        focusInput: function focusInput() {
          var _inputElement$current;
          inputElement == null || (_inputElement$current = inputElement.current) == null || _inputElement$current.focus == null || _inputElement$current.focus();
        }
      };
    });
    var handleClearSearch = function handleClearSearch() {
      var _inputElement$current2;
      clearTimeout(timeout.current);
      setValue("");
      onSearchClear();
      shouldFocusAfterClear && (inputElement == null || (_inputElement$current2 = inputElement.current) == null || _inputElement$current2.focus == null ? undefined : _inputElement$current2.focus());
    };
    (0, _react.useEffect)(function () {
      if (autoFocusTextInput) {
        var _inputElement$current3;
        (_inputElement$current3 = inputElement.current) == null || _inputElement$current3.focus();
      }
    }, [autoFocusTextInput]);
    (0, _react.useEffect)(function () {
      return function () {
        clearTimeout(timeoutSubmitEditing.current);
        clearTimeout(timeout.current);
      };
    }, []);
    (0, _react.useEffect)(function () {
      !(0, _isEmpty.default)(keyword) && setValue(keyword);
    }, [keyword]);
    var getPlacehoderValue = function getPlacehoderValue() {
      var placeholder = (0, _i18n.translate)(HelperText[tab.toLowerCase()]);
      if (placeholder.length >= 30 && _reactNative2.Platform.OS === "android") {
        return placeholder.substring(0, 27) + "...";
      }
      return placeholder;
    };
    var handleOnSubmitEditing = function handleOnSubmitEditing() {
      _reactNative2.Keyboard.dismiss();
      var timeout = setTimeout(function () {
        if (setIsPressSearchKey && value.length > 1) {
          dispatch(_searchRedux.default.setSearchKeyword(value));
          addNewRecentSearchKeyword(value);
          setIsPressSearchKey(true);
          if (onSubmitEditing) {
            onSubmitEditing();
          }
        } else if (setIsPressSearchKey) {
          value.length > 1 && addNewRecentSearchKeyword(value);
          setIsPressSearchKey(true);
        }
      }, 800);
      timeoutSubmitEditing.current = timeout;
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
      onPress: _reactNative2.Keyboard.dismiss,
      hitSlop: {
        top: 30,
        bottom: 10
      },
      style: [styles.containerStyle, containerStyle],
      testID: `${testID}PressableContainer`,
      accessibilityLabel: `${accessibilityLabel}PressableContainer`,
      children: [isShowBack && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.iconContainer,
        onPress: onPressBack,
        testID: `${testID}TouchableBack`,
        accessibilityLabel: `${accessibilityLabel}TouchableBack`,
        children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
          color: arrowLeftColor != null ? arrowLeftColor : _theme.color.palette.lightPurple
        })
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [styles.inputContainer, inputContainerStyle],
        children: (0, _jsxRuntime.jsx)(InputFieldComponent, Object.assign({
          isShowClearAll: !(0, _isEmpty.default)(value),
          value: value,
          returnKeyType: returnKeyType,
          highlightOnFocused: true,
          autoCapitalize: "none",
          autoCorrect: false,
          maxLength: 50,
          onChangeText: onChangeHandler,
          onSearchClear: handleClearSearch,
          forwardedRef: inputElement,
          onSubmitEditing: onSubmitLocal != null ? onSubmitLocal : handleOnSubmitEditing,
          placeHolderValue: getPlacehoderValue(),
          testID: `${testID}InputSearch`,
          accessibilityLabel: `${accessibilityLabel}InputSearch`,
          autoFocus: autoFocusTextInput
        }, inputProps))
      })]
    });
  };
  var _default = exports.default = (0, _react.forwardRef)(SearchBar);
