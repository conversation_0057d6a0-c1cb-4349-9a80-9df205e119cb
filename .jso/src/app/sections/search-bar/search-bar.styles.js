  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.inputContainer = exports.iconContainer = exports.helperStyle = exports.helperContainer = exports.containerStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var containerStyle = exports.containerStyle = {
    flexDirection: "row",
    backgroundColor: _theme.color.palette.whiteGrey,
    paddingLeft: 10,
    paddingRight: 24,
    alignItems: "center",
    marginTop: _reactNative.Platform.OS === "ios" ? 16 : _reactNative.StatusBar.currentHeight + 16
  };
  var iconContainer = exports.iconContainer = {
    marginHorizontal: 10
  };
  var inputContainer = exports.inputContainer = {
    flex: 1,
    backgroundColor: _theme.color.palette.whiteGrey
  };
  var helperContainer = exports.helperContainer = {
    marginTop: -30,
    marginHorizontal: 50,
    backgroundColor: _theme.color.transparent
  };
  var helperStyle = exports.helperStyle = {
    backgroundColor: _theme.color.transparent,
    color: _theme.color.palette.midGrey,
    height: 40,
    paddingLeft: 6
  };
