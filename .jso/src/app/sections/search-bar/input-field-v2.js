  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.InputFieldV2 = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _textField = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _placeholderCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var _excluded = ["highlightOnFocused", "trim", "isInvalid", "isDisabled", "isFocused", "onFocus", "onBlur", "onChangeText", "onSearchClear", "isShowClearAll", "value", "inputFieldStyle", "placeholderList"];
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var iconProps = {
    color: _theme.color.palette.darkGrey999,
    width: 20,
    height: 20
  };
  var InputFieldV2 = exports.InputFieldV2 = function InputFieldV2(props) {
    // Props
    var highlightOnFocused = props.highlightOnFocused,
      trim = props.trim,
      isInvalid = props.isInvalid,
      isDisabled = props.isDisabled,
      isFocused = props.isFocused,
      _onFocus = props.onFocus,
      _onBlur = props.onBlur,
      onChangeText = props.onChangeText,
      onSearchClear = props.onSearchClear,
      _props$isShowClearAll = props.isShowClearAll,
      isShowClearAll = _props$isShowClearAll === undefined ? false : _props$isShowClearAll,
      value = props.value,
      _props$inputFieldStyl = props.inputFieldStyle,
      inputFieldStyle = _props$inputFieldStyl === undefined ? {
        inputGroupStyle: {}
      } : _props$inputFieldStyl,
      placeholderList = props.placeholderList,
      restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      focused = _useState2[0],
      setFocused = _useState2[1];
    var placeholderRef = (0, _react.useRef)(null);
    var currentPlaceholderIndex = (0, _react.useRef)(0);
    var placeholderListProps = {};
    if (placeholderList != null && placeholderList.length) {
      placeholderListProps = {
        placeholder: focused ? placeholderList[currentPlaceholderIndex.current] : ""
      };
    }
    var inputGroupStyle = Object.assign({}, styles.inputGroupStyle, inputFieldStyle.inputGroupStyle, highlightOnFocused && (focused || isFocused) ? styles.inputGroupFocusedStyle : {}, isInvalid ? styles.inputGroupErrorStyle : {}, isDisabled ? styles.inputGroupDisabledStyle : {});
    var inputHeight = typeof inputGroupStyle.height === "number" ? inputGroupStyle.height : undefined;
    var handleChangeText = function handleChangeText(e) {
      var val = trim && e ? e.trim() : e;
      onChangeText && onChangeText(val);
      if (e != null && e.length) {
        currentPlaceholderIndex.current = 0;
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: Object.assign({}, styles.inputShadowStyles, focused ? styles.inputShadowFocusedStyles : {}),
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: inputGroupStyle,
        children: [(0, _jsxRuntime.jsx)(_icons.SearchBox, Object.assign({}, iconProps, {
          style: styles.searchIconStyle
        })), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: {
            flex: 1
          },
          children: [(0, _jsxRuntime.jsx)(_textField.TextField, Object.assign({
            style: styles.inputFieldStyle,
            inputStyle: Object.assign({}, styles.inputControlStyle, isDisabled ? styles.inputControlDisabledStyle : {}),
            placeholderTextColor: _theme.color.palette.midGrey,
            secureTextEntry: false,
            preset: "noMargin",
            editable: !isDisabled,
            allowFontScaling: false,
            onBlur: function onBlur(e) {
              setFocused(false);
              _onBlur && _onBlur(e);
            },
            onFocus: function onFocus(e) {
              setFocused(true);
              _onFocus && _onFocus(e);
            },
            onChangeText: handleChangeText,
            value: value
          }, restProps, placeholderListProps)), !(value != null && value.length) && !!(placeholderList != null && placeholderList.length) && (0, _jsxRuntime.jsx)(_placeholderCarousel.default, {
            ref: placeholderRef,
            isStop: focused,
            placeholderList: placeholderList,
            itemHeight: inputHeight,
            currentPlaceholderIndex: currentPlaceholderIndex
          })]
        }), isShowClearAll && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          hitSlop: {
            top: 5,
            left: 5,
            bottom: 5,
            right: 5
          },
          style: styles.closeIconContainer,
          onPress: onSearchClear,
          children: (0, _jsxRuntime.jsx)(_icons.CloseIconV2, Object.assign({}, iconProps))
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    inputControlDisabledStyle: {
      backgroundColor: _theme.color.palette.transparent,
      color: _theme.color.palette.midGrey
    },
    inputControlStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      minHeight: 0,
      paddingBottom: 0,
      paddingTop: 0,
      paddingHorizontal: 0,
      backgroundColor: "transparent",
      textAlignVertical: "center"
    }),
    inputFieldStyle: {
      flex: 1,
      justifyContent: "center"
    },
    inputGroupDisabledStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderColor: _theme.color.palette.lighterGrey
    },
    inputGroupErrorStyle: {
      borderColor: _theme.color.palette.baseRed
    },
    inputGroupFocusedStyle: {
      borderColor: _theme.color.palette.lightPurple
    },
    inputGroupStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.greyCCCCCC,
      borderRadius: 8,
      borderWidth: 1,
      flexDirection: "row",
      height: 36,
      alignItems: "center",
      paddingHorizontal: 12
    },
    inputShadowFocusedStyles: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 10,
      zIndex: -1
    },
    inputShadowStyles: {
      padding: 4,
      zIndex: -1
    },
    closeIconContainer: {
      alignItems: "center",
      justifyContent: "center"
    },
    searchIconStyle: {
      marginLeft: 5,
      marginRight: 10
    }
  });
  var _default = exports.default = InputFieldV2;
