  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimatedCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ITEM_HEIGHT = 44;
  var PlaceholderCarousel = function PlaceholderCarousel(props, ref) {
    var _props$isStop = props.isStop,
      isStop = _props$isStop === undefined ? false : _props$isStop,
      _props$placeholderLis = props.placeholderList,
      placeholderList = _props$placeholderLis === undefined ? [] : _props$placeholderLis,
      _props$itemHeight = props.itemHeight,
      itemHeight = _props$itemHeight === undefined ? ITEM_HEIGHT : _props$itemHeight,
      currentPlaceholderIndex = props.currentPlaceholderIndex,
      _props$containerStyle = props.containerStyle,
      containerStyle = _props$containerStyle === undefined ? {} : _props$containerStyle,
      _props$textStyle = props.textStyle,
      textStyle = _props$textStyle === undefined ? {} : _props$textStyle;
    var isFocused = (0, _native.useIsFocused)();
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isAutoPlay = _useState2[0],
      setIsAutoPlay = _useState2[1];
    var prevIndex = (0, _react.useRef)(0);
    (0, _react.useEffect)(function () {
      if (isStop || !isFocused) {
        setIsAutoPlay(false);
      } else {
        setIsAutoPlay(true);
      }
    }, [isStop, isFocused]);
    var TextItem = function TextItem(_ref) {
      var item = _ref.item;
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.wrapText,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: [styles.text, textStyle],
          children: isStop ? "" : item
        })
      });
    };
    var onProgressChange = function onProgressChange(_, absoluteProgress) {
      var newIndex = Math.ceil(absoluteProgress) % placeholderList.length;
      if (newIndex !== prevIndex.current) {
        prevIndex.current = newIndex;
        currentPlaceholderIndex.current = newIndex;
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: (0, _jsxRuntime.jsx)(_reactNativeReanimatedCarousel.default, {
        scrollAnimationDuration: _reactNative.Platform.OS === "ios" ? 500 : 700,
        ref: ref,
        height: itemHeight,
        data: placeholderList,
        vertical: true,
        renderItem: TextItem,
        autoPlay: isAutoPlay,
        loop: true,
        snapEnabled: false,
        enabled: false,
        onProgressChange: onProgressChange,
        autoPlayInterval: 4000
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      position: "absolute",
      left: 0,
      top: 0,
      bottom: 0,
      right: 0,
      justifyContent: "center",
      pointerEvents: "none",
      overflow: "hidden"
    },
    wrapText: {
      justifyContent: "center",
      flex: 1,
      top: _reactNative.Platform.OS === "ios" ? 0.5 : 0
    },
    text: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.midGrey
    })
  });
  var _default = exports.default = (0, _react.forwardRef)(PlaceholderCarousel);
