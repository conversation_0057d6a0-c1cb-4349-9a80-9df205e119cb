  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _timelineFlyTiles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _timelineFlyTiles2 = _$$_REQUIRE(_dependencyMap[9]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _verticalLine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _bottomSheetVerifyEmail = _$$_REQUIRE(_dependencyMap[15]);
  var _airportLandingRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _utils = _$$_REQUIRE(_dependencyMap[17]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[18]);
  var _theme = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightTimelineSection = function FlightTimelineSection(props) {
    var sectionTitle = props.sectionTitle,
      sectionKey = props.sectionKey,
      tileItems = props.tileItems,
      isOpen = props.isOpen,
      isLastSection = props.isLastSection,
      onPressSection = props.onPressSection,
      onTrackingTimeline = props.onTrackingTimeline,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showResend = _useState2[0],
      setShowResend = _useState2[1];
    var navigation = (0, _native.useNavigation)();
    var getChangiGameUrlPathData = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getChangiGameUrlPathData);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("TIMELINES_FLIGHT_DETAIL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    (0, _react.useEffect)(function () {
      var _getChangiGameUrlPath;
      if (getChangiGameUrlPathData != null && (_getChangiGameUrlPath = getChangiGameUrlPathData.data) != null && (_getChangiGameUrlPath = _getChangiGameUrlPath.getChangiGames) != null && _getChangiGameUrlPath.url) {
        var _getChangiGameUrlPath2;
        var url = getChangiGameUrlPathData == null || (_getChangiGameUrlPath2 = getChangiGameUrlPathData.data) == null || (_getChangiGameUrlPath2 = _getChangiGameUrlPath2.getChangiGames) == null ? undefined : _getChangiGameUrlPath2.url;
        navigation.navigate(_constants.NavigationConstants.webview, {
          uri: url
        });
      }
    }, [getChangiGameUrlPathData]);
    var onPress = function onPress(item) {
      handleNavigation(item.navigationType, item.navigationValue, item == null ? undefined : item.redirect);
    };
    var handlePressTitle = function handlePressTitle(newTitleItem, value) {
      if (onTrackingTimeline) {
        onTrackingTimeline(`${sectionTitle}|${newTitleItem == null ? undefined : newTitleItem.title}|${newTitleItem == null ? undefined : newTitleItem.tileID}|${value == null ? undefined : value.navigationValue}`, sectionKey);
      }
      onPress(value);
    };
    var renderVariation2Tile = function renderVariation2Tile(tileItem, index) {
      return (0, _jsxRuntime.jsx)(_timelineFlyTiles.default, {
        type: _timelineFlyTiles2.TimelineFlyTilesVariations.variation2,
        title: tileItem == null ? undefined : tileItem.title,
        description: tileItem == null ? undefined : tileItem.description,
        firstLink: tileItem == null ? undefined : tileItem.firstLink,
        secondLink: tileItem == null ? undefined : tileItem.secondLink,
        imageUrl: tileItem == null ? undefined : tileItem.image,
        onPress: function onPress(value) {
          return handlePressTitle(tileItem, value);
        },
        testID: `${testID}__${tileItem == null ? undefined : tileItem.type}`,
        accessibilityLabel: `${accessibilityLabel}__${tileItem == null ? undefined : tileItem.type}`
      }, `${index}-${tileItem.title}`);
    };
    var renderVariation3Tile = function renderVariation3Tile(tileItem, index) {
      return (0, _jsxRuntime.jsx)(_timelineFlyTiles.default, {
        type: _timelineFlyTiles2.TimelineFlyTilesVariations.variation3,
        title: tileItem == null ? undefined : tileItem.title,
        data: tileItem == null ? undefined : tileItem.contents,
        onPress: function onPress(value) {
          return handlePressTitle(tileItem, value);
        },
        testID: `${testID}__${tileItem == null ? undefined : tileItem.type}`,
        accessibilityLabel: `${accessibilityLabel}__${tileItem == null ? undefined : tileItem.type}`
      }, `${index}-${tileItem.title}`);
    };
    var renderTimelineTile = function renderTimelineTile(tileItem, index) {
      var variation = tileItem == null ? undefined : tileItem.type;
      if (variation === _timelineFlyTiles2.TimelineFlyTilesVariations.variation1 || variation === _timelineFlyTiles2.TimelineFlyTilesVariations.variation2) {
        return renderVariation2Tile(tileItem, index);
      }
      if (variation === _timelineFlyTiles2.TimelineFlyTilesVariations.variation3) {
        return renderVariation3Tile(tileItem, index);
      }
      return null;
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.containerStyle,
      children: [isLastSection && !isOpen ? null : (0, _jsxRuntime.jsx)(_verticalLine.default, {}), (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: onPressSection,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.sectionHeadStyle,
          children: [(0, _jsxRuntime.jsx)(_icons.DotWithoutBg, {
            width: "16",
            height: "16"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "subTitleBold",
            text: sectionTitle,
            style: styles.sectionTitleStyle
          }), isOpen ? (0, _jsxRuntime.jsx)(_icons.TopArrow, {}) : (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
            color: _theme.color.palette.lightPurple
          })]
        })
      }), isOpen ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_verticalLine.default, {}), tileItems == null ? undefined : tileItems.map(function (tileItem, index) {
          return renderTimelineTile(tileItem, index);
        }), (0, _utils.handleCondition)(isLastSection, null, (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.sectionSeparator
        }))]
      }) : null, (0, _jsxRuntime.jsx)(_bottomSheetVerifyEmail.BottomSheetVerifyEmail, {
        visible: showResend,
        email: profilePayload == null ? undefined : profilePayload.email,
        onHide: function onHide() {
          return setShowResend(false);
        },
        testID: `FlightDetail_BottomSheetVerifyEmailVerifyEmail`
      })]
    });
  };
  var _default = exports.default = FlightTimelineSection;
