  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleTextStyle = exports.switchNotificationType = exports.restAroundContent = exports.modalStyle = exports.messageContainerStyle = exports.mainSwitchLeftStyle = exports.mainSwitchContainerStyle = exports.listStyle = exports.headerTextStyle = exports.dividerTransparentStyle = exports.dividerStyle = exports.containerStyle = exports.closeIconContainerStyle = exports.channelItemStyle = exports.channelContainerStyle = exports.buttonTextDisabledStyle = exports.buttonStyle = exports.buttonDisabledStyle = exports.buttonColors = exports.bodyTextBold = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var modalStyle = exports.modalStyle = {
    justifyContent: "flex-end",
    margin: 0
  };
  var HEIGHT = _reactNative.Dimensions.get("window").height;
  var containerStyle = exports.containerStyle = {
    paddingTop: 22,
    paddingHorizontal: 24,
    backgroundColor: _theme.color.palette.whiteGrey,
    borderTopLeftRadius: 16,
    borderTopEndRadius: 16,
    flex: 1,
    maxHeight: HEIGHT >= 812 ? 93 - HEIGHT / 812 + "%" : "95%",
    overflow: "hidden"
  };
  var closeIconContainerStyle = exports.closeIconContainerStyle = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between"
    // marginBottom: 24
  };
  var headerTextStyle = exports.headerTextStyle = Object.assign({}, _text.presets.h3, {
    textAlign: "center",
    color: _theme.color.palette.almostBlackGrey
  });
  var listStyle = exports.listStyle = {};
  var restAroundContent = exports.restAroundContent = {
    width: "10%"
  };
  var mainSwitchContainerStyle = exports.mainSwitchContainerStyle = {
    flexDirection: "row",
    justifyContent: "space-between"
  };
  var switchNotificationType = exports.switchNotificationType = {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24
  };
  var bodyTextBold = exports.bodyTextBold = {
    color: _theme.color.palette.almostBlackGrey
  };
  var mainSwitchLeftStyle = exports.mainSwitchLeftStyle = {
    // flexGrow: 1,
    width: "70%"
  };
  var dividerTransparentStyle = exports.dividerTransparentStyle = {
    height: 1,
    // backgroundColor: color.palette.lighterGrey,
    marginVertical: 8
  };
  var dividerStyle = exports.dividerStyle = {
    height: 1,
    backgroundColor: _theme.color.palette.lighterGrey,
    marginVertical: 24
  };
  var titleTextStyle = exports.titleTextStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostBlackGrey,
    marginBottom: 16
  });
  var messageContainerStyle = exports.messageContainerStyle = {
    marginBottom: 24
  };
  var channelContainerStyle = exports.channelContainerStyle = {
    flexDirection: "row"
  };
  var channelItemStyle = exports.channelItemStyle = {
    width: "50%"
  };
  var buttonStyle = exports.buttonStyle = {
    width: "100%",
    borderRadius: 60,
    marginTop: 40
  };
  var buttonColors = exports.buttonColors = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var buttonDisabledStyle = exports.buttonDisabledStyle = {
    borderColor: _theme.color.palette.lightGrey,
    backgroundColor: _theme.color.palette.lightGrey
  };
  var buttonTextDisabledStyle = exports.buttonTextDisabledStyle = {
    color: _theme.color.palette.darkGrey
  };
