  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _button = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var testID = "VceaErrorPage";
  var ErrorPage = function ErrorPage(_ref) {
    var onPressRetry = _ref.onPressRetry;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_icons.InfoRed, {
        style: _styles.default.errorIcon
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        testID: `${testID}__ErrorTitle`,
        style: _styles.default.errorTitle,
        tx: "popupError.somethingWrongOneline"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        testID: `${testID}__ErrorMessage`,
        style: _styles.default.errorMessage,
        tx: "popupError.networkErrorMessage"
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        style: _styles.default.errorButtonStyle,
        start: {
          x: 1,
          y: 0
        },
        end: {
          x: 0,
          y: 1
        },
        colors: [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start],
        children: (0, _jsxRuntime.jsx)(_button.Button, {
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "secondary",
          tx: "common.retry",
          statePreset: "default",
          backgroundPreset: "light",
          onPress: onPressRetry,
          testID: `${testID}__ButtonExecuteAndClose`,
          accessibilityLabel: `${testID}__ButtonExecuteAndClose`
        })
      })]
    });
  };
  var _default = exports.default = ErrorPage;
