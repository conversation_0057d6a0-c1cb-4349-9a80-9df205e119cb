  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactNativeGeolocationService = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactNativeDeviceInfo = _$$_REQUIRE(_dependencyMap[15]);
  var _reactNativePermissions = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _text = _$$_REQUIRE(_dependencyMap[18]);
  var _storage = _$$_REQUIRE(_dependencyMap[19]);
  var _useAppStateChange2 = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[20]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[22]);
  var _envParams = _$$_REQUIRE(_dependencyMap[23]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[25]));
  var _systemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[26]));
  var _queries = _$$_REQUIRE(_dependencyMap[27]);
  var _theme = _$$_REQUIRE(_dependencyMap[28]);
  var _constants = _$$_REQUIRE(_dependencyMap[29]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[30]);
  var _icons = _$$_REQUIRE(_dependencyMap[31]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[32]);
  var _icons2 = _$$_REQUIRE(_dependencyMap[33]);
  var _loadingAnimation = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _styles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[36]);
  var _account = _$$_REQUIRE(_dependencyMap[37]);
  var _validate = _$$_REQUIRE(_dependencyMap[38]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[39]);
  var _adobe = _$$_REQUIRE(_dependencyMap[40]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[41]);
  var _useModal4 = _$$_REQUIRE(_dependencyMap[42]);
  var _errorPage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[43]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[44]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[45]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ANIMATION_TIMING = 500;
  var SCREEN_NAME = "VCEA_SCREEN";
  var contactUsTx = "vECA.contactUs";
  var ContactUsEntryPoints = function ContactUsEntryPoints(_ref) {
    var setHeaderTitleTx = _ref.setHeaderTitleTx,
      setCurrentPage = _ref.setCurrentPage;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useModal = (0, _useModal4.useModal)("bottomSheetVcea"),
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var bottomSheetVceaData = (0, _reactRedux.useSelector)(_systemRedux.SystemSelectors.bottomSheetVceaData);
    var _ref2 = bottomSheetVceaData || {},
      adobeTagName = _ref2.adobeTagName;
    var moreOptionsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.moreOptionsData);
    var moreOptionsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.moreOptionsError);
    var navigateToSubmitSuggestionsAndFeedback = function navigateToSubmitSuggestionsAndFeedback() {
      // @ts-ignore
      navigation.navigate(_constants2.NavigationConstants.submitSuggestionsAndFeedBack, {
        onGoBack: openModal
      });
    };
    var mainCtasData = [{
      icon: (0, _jsxRuntime.jsx)(_icons2.OnlineForm, {}),
      labelTx: "vECA.onlineForm",
      onPress: function onPress() {
        (0, _adobe.trackAction)(adobeTagName, (0, _defineProperty2.default)({}, adobeTagName, `${(0, _i18n.translate)(contactUsTx)} | ${(0, _i18n.translate)("vECA.onlineForm")}`));
        if (isLoggedIn) {
          closeModal();
          navigateToSubmitSuggestionsAndFeedback();
        } else {
          closeModal();
          // @ts-ignore
          navigation.navigate(_constants2.NavigationConstants.authScreen, {
            callBackAfterLoginSuccess: navigateToSubmitSuggestionsAndFeedback,
            callBackAfterLoginCancel: openModal
          });
        }
      }
    }, {
      icon: (0, _jsxRuntime.jsx)(_icons2.VceaCall, {
        style: _styles.default.ctaItemIcon
      }),
      labelTx: "vECA.call",
      onPress: function onPress() {
        (0, _adobe.trackAction)(adobeTagName, (0, _defineProperty2.default)({}, adobeTagName, `${(0, _i18n.translate)(contactUsTx)} | ${(0, _i18n.translate)("vECA.call")}`));
        _reactNative2.Linking.openURL("tel:+6565956868");
      }
    }, {
      icon: (0, _jsxRuntime.jsx)(_icons2.VceaLiveAgent, {
        style: _styles.default.ctaItemIcon
      }),
      labelTx: "vECA.liveAgent",
      onPress: function onPress() {
        (0, _adobe.trackAction)(adobeTagName, (0, _defineProperty2.default)({}, adobeTagName, `${(0, _i18n.translate)(contactUsTx)} | ${(0, _i18n.translate)("vECA.liveAgent")}`));
        setHeaderTitleTx("vECA.contactLiveAgent");
        setCurrentPage(_constants.VECA_PAGES.CONTACT_LIVE_AGENT);
      }
    }];
    var onGoBackFromExternalLink = function onGoBackFromExternalLink() {
      navigation.goBack();
      openModal();
    };
    var fullLineCatas = [{
      icon: (0, _jsxRuntime.jsx)(_icons2.VceaAskMax, {}),
      labelTx: "vECA.askMax",
      onPress: function onPress() {
        (0, _adobe.trackAction)(adobeTagName, (0, _defineProperty2.default)({}, adobeTagName, `${(0, _i18n.translate)(contactUsTx)} | ${(0, _i18n.translate)("vECA.askMax")}`));
        closeModal();
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.chatBot, {
          onCloseWebviewBtnPress: onGoBackFromExternalLink
        });
      }
    }, {
      icon: (0, _jsxRuntime.jsx)(_icons2.VceaMoreOptions, {}),
      labelTx: "vECA.moreOptions",
      onPress: function onPress() {
        (0, _adobe.trackAction)(adobeTagName, (0, _defineProperty2.default)({}, adobeTagName, `${(0, _i18n.translate)(contactUsTx)} | ${(0, _i18n.translate)("vECA.moreOptions")}`));
        // prettier-ignore
        var contactUsInfo = moreOptionsData == null || moreOptionsData.find == null ? undefined : moreOptionsData.find(function (ele) {
          var _ele$fragmentTags;
          return !!(ele != null && (_ele$fragmentTags = ele.fragmentTags) != null && _ele$fragmentTags.find(function (i) {
            return (i == null ? undefined : i.filterType) === _constants2.AEM_FILTER_TYPES.CONTACT_US_VARIATION;
          }));
        });
        if (!contactUsInfo || moreOptionsError) {
          setHeaderTitleTx("");
          setCurrentPage(_constants.VECA_PAGES.ERROR_PAGE);
          return;
        }
        if (contactUsInfo != null && contactUsInfo.navigationValue && contactUsInfo != null && contactUsInfo.navigationType) {
          closeModal();
          handleNavigation(contactUsInfo == null ? undefined : contactUsInfo.navigationType, contactUsInfo == null ? undefined : contactUsInfo.navigationValue, {
            onGoBack: onGoBackFromExternalLink
          });
        }
      }
    }];
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.default.bodyWrapper,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.default.ctasWrapper,
        children: mainCtasData.map(function (item, i) {
          return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: _styles.default.ctaItem,
            onPress: item == null ? undefined : item.onPress,
            children: [item == null ? undefined : item.icon, (0, _jsxRuntime.jsx)(_text.Text, {
              tx: item == null ? undefined : item.labelTx,
              style: _styles.default.ctaItemLabel
            })]
          }, i);
        })
      }), fullLineCatas.map(function (item, i) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.default.fullLineCtaItem,
          children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            style: _styles.default.fullLineCtaButton,
            onPress: item == null ? undefined : item.onPress,
            children: [item == null ? undefined : item.icon, (0, _jsxRuntime.jsx)(_text.Text, {
              tx: item == null ? undefined : item.labelTx,
              style: _styles.default.fullLineCtaItemLabel
            })]
          })
        }, i);
      })]
    });
  };
  var ContactLiveAgent = function ContactLiveAgent(_ref3) {
    var onGoBackContactUsPage = _ref3.onGoBackContactUsPage;
    var navigation = (0, _native.useNavigation)();
    var _useAppStateChange = (0, _useAppStateChange2.default)(),
      appStateVisible = _useAppStateChange.appStateVisible;
    var _useModal2 = (0, _useModal4.useModal)("bottomSheetVcea"),
      openModal = _useModal2.openModal,
      closeModal = _useModal2.closeModal;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      checkingLocation = _useState2[0],
      setCheckingLocation = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isEnabledLocation = _useState4[0],
      setIsEnabledLocation = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isServiceUnavailable = _useState6[0],
      setIsServiceUnavailable = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      shouldNotCheckLocationPermission = _useState8[0],
      setShouldNotCheckLocationPermission = _useState8[1];
    var isVceaLiveChat = (0, _remoteConfig.isFlagOnCondition)((0, _react.useContext)(_account.AccountContext).vceaLiveChatFlag);
    var isBackFromSettingsRef = (0, _react.useRef)(false);
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isOpeningSettings = _useState0[0],
      setIsOpeningSettings = _useState0[1];
    var pageIcon = isServiceUnavailable || !isVceaLiveChat ? (0, _jsxRuntime.jsx)(_icons2.ServiceUnavailable, {
      style: _styles.default.contactLiveAgentIcon
    }) : (0, _jsxRuntime.jsx)(_icons2.ContactLiveAgent, {
      style: _styles.default.contactLiveAgentIcon
    });
    var handleGlobalNavigate = function handleGlobalNavigate() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      navigation.navigate.apply(navigation, (0, _toConsumableArray2.default)(args));
    };
    var onGoToAppSettings = function onGoToAppSettings() {
      _reactNative2.Alert.alert((0, _i18n.translate)("vECA.enableLocationAlertTitle"), (0, _i18n.translate)("vECA.enableLocationAlertMessage"), [{
        text: (0, _i18n.translate)("vECA.enableLocationAlertSettings"),
        isPreferred: true,
        onPress: function onPress() {
          (0, _reactNativePermissions.openSettings)();
          setCheckingLocation(true);
          setShouldNotCheckLocationPermission(false);
        }
      }, {
        text: (0, _i18n.translate)("vECA.enableLocationAlertOk"),
        onPress: function onPress() {
          return null;
        }
      }]);
    };
    var messageTx = (0, _react.useMemo)(function () {
      if (isServiceUnavailable || !isVceaLiveChat) {
        return "vECA.serviceUnavailableMessage";
      }
      if (!isEnabledLocation) {
        return "vECA.enableLocationMessage";
      }
      return "vECA.notInTheChangiAirportTerminalsMessage";
    }, [isEnabledLocation, isServiceUnavailable, isVceaLiveChat]);
    var renderFirstCtaButton = (0, _react.useMemo)(function () {
      var labelTx = "vECA.viewOtherChannels";
      var onPressButton = onGoBackContactUsPage;
      if (!isEnabledLocation && isVceaLiveChat) {
        labelTx = "vECA.enableLocation";
        onPressButton = onGoToAppSettings;
      }
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onPressButton,
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          style: _styles.default.contactLiveAgentFirstButton,
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: labelTx,
            style: _styles.default.contactLiveAgentFirstButtonText
          })
        })
      });
    }, [isEnabledLocation, isVceaLiveChat]);
    var renderSecondCtaButton = (0, _react.useMemo)(function () {
      if (!isEnabledLocation && isVceaLiveChat) {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onGoBackContactUsPage,
          hitSlop: {
            top: 10,
            bottom: 10
          },
          style: _styles.default.contactLiveAgentSecondButton,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "vECA.notNow",
            style: _styles.default.contactLiveAgentSecondButtonText
          })
        });
      }
    }, [isEnabledLocation, isVceaLiveChat]);
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg61 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG61";
    });
    var msg62 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG62";
    });
    var msg95 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG95";
    });
    var msg96 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG96";
    });
    var cameraRationale = {
      title: (msg61 == null ? undefined : msg61.title) || (0, _i18n.translate)("requestPermission.camera.title"),
      message: (msg61 == null ? undefined : msg61.message) || (0, _i18n.translate)("requestPermission.camera.message"),
      buttonPositive: (msg61 == null ? undefined : msg61.secondButton) || (0, _i18n.translate)("requestPermission.camera.buttonPositive"),
      buttonNegative: (msg61 == null ? undefined : msg61.firstButton) || (0, _i18n.translate)("requestPermission.camera.buttonNegative")
    };
    var microphoneRationale = {
      title: (msg95 == null ? undefined : msg95.title) || (0, _i18n.translate)("requestPermission.microphone.title"),
      message: (msg95 == null ? undefined : msg95.message) || (0, _i18n.translate)("requestPermission.microphone.message"),
      buttonPositive: (msg95 == null ? undefined : msg95.secondButton) || (0, _i18n.translate)("requestPermission.microphone.buttonPositive"),
      buttonNegative: (msg95 == null ? undefined : msg95.firstButton) || (0, _i18n.translate)("requestPermission.microphone.buttonNegative")
    };
    var isIOSDevice = _reactNative2.Platform.OS === "ios";
    var cameraPermission = isIOSDevice ? _reactNativePermissions.PERMISSIONS.IOS.CAMERA : _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA;
    var microPermission = isIOSDevice ? _reactNativePermissions.PERMISSIONS.IOS.MICROPHONE : _reactNativePermissions.PERMISSIONS.ANDROID.RECORD_AUDIO;
    var onGoBackFromLiveChat = function onGoBackFromLiveChat() {
      navigation.goBack();
      openModal();
    };
    var requestMicrophonePermission = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (vCEALandingPage) {
        var isRequestedMicroPermission = yield (0, _storage.load)(_storage.StorageKey.isRequestedMicroPermission);
        var permissionStatus = yield _reactNativePermissions.default.check(microPermission);
        if (permissionStatus !== _reactNativePermissions.RESULTS.GRANTED && !isRequestedMicroPermission) {
          (0, _reactNativePermissions.request)(microPermission, microphoneRationale).then(function (microphoneResult) {
            if (microphoneResult === _reactNativePermissions.RESULTS.GRANTED) {
              if (vCEALandingPage) {
                closeModal();
                handleGlobalNavigate(_constants2.NavigationConstants.webview, {
                  uri: vCEALandingPage,
                  originWhitelist: ["*"],
                  allowsInlineMediaPlayback: true,
                  mediaPlaybackRequiresUserAction: false,
                  mediaCapturePermissionGrantType: "grant",
                  onGoBack: onGoBackFromLiveChat,
                  showHeaderLeftButton: false,
                  useForward: false
                });
                setTimeout(function () {
                  onGoBackContactUsPage();
                }, 100);
              }
            } else {
              setCheckingLocation(false);
            }
          });
          (0, _storage.save)(_storage.StorageKey.isRequestedMicroPermission, true);
        } else if (permissionStatus === _reactNativePermissions.RESULTS.GRANTED) {
          if (vCEALandingPage) {
            closeModal();
            handleGlobalNavigate(_constants2.NavigationConstants.webview, {
              uri: vCEALandingPage,
              originWhitelist: ["*"],
              allowsInlineMediaPlayback: true,
              mediaPlaybackRequiresUserAction: false,
              mediaCapturePermissionGrantType: "grant",
              onGoBack: onGoBackFromLiveChat,
              showHeaderLeftButton: false,
              useForward: false
            });
            setTimeout(function () {
              onGoBackContactUsPage();
            }, 100);
          }
        } else {
          setCheckingLocation(false);
          _reactNative2.Alert.alert((msg96 == null ? undefined : msg96.title) || (0, _i18n.translate)("vECA.microphonePermission.title"), (msg96 == null ? undefined : msg96.message) || (0, _i18n.translate)("vECA.microphonePermission.description"), [{
            text: (msg96 == null ? undefined : msg96.firstButton) || (0, _i18n.translate)("vECA.microphonePermission.firstButton"),
            isPreferred: true,
            onPress: function onPress() {
              (0, _reactNativePermissions.openSettings)();
              setCheckingLocation(true);
              setIsOpeningSettings(true);
            }
          }, {
            text: (msg96 == null ? undefined : msg96.secondButton) || (0, _i18n.translate)("vECA.microphonePermission.secondButton"),
            onPress: function onPress() {
              return null;
            }
          }]);
        }
      });
      return function requestMicrophonePermission(_x) {
        return _ref4.apply(this, arguments);
      };
    }();
    var checkCameraAndMicroPermissionAndNavigate = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (vCEALandingPage) {
        var isRequestedCameraPermission = yield (0, _storage.load)(_storage.StorageKey.isRequestedCameraPermission);
        var permissionStatus = yield _reactNativePermissions.default.check(cameraPermission);
        if (permissionStatus !== _reactNativePermissions.RESULTS.GRANTED && !isRequestedCameraPermission) {
          (0, _reactNativePermissions.request)(cameraPermission, cameraRationale).then(function (cameraResult) {
            if (cameraResult === _reactNativePermissions.RESULTS.GRANTED) {
              requestMicrophonePermission(vCEALandingPage);
            } else {
              setCheckingLocation(false);
            }
          });
          (0, _storage.save)(_storage.StorageKey.isRequestedCameraPermission, true);
        } else if (permissionStatus === _reactNativePermissions.RESULTS.GRANTED) {
          requestMicrophonePermission(vCEALandingPage);
        } else {
          setCheckingLocation(false);
          _reactNative2.Alert.alert((msg62 == null ? undefined : msg62.title) || (0, _i18n.translate)("retroClaims.needAccessPermission.title"), (msg62 == null ? undefined : msg62.message) || (0, _i18n.translate)("retroClaims.needAccessPermission.description"), [{
            text: (msg62 == null ? undefined : msg62.firstButton) || (0, _i18n.translate)("retroClaims.needAccessPermission.firstButton"),
            isPreferred: true,
            onPress: function onPress() {
              (0, _reactNativePermissions.openSettings)();
              setCheckingLocation(true);
              setIsOpeningSettings(true);
            }
          }, {
            text: (msg62 == null ? undefined : msg62.secondButton) || (0, _i18n.translate)("retroClaims.needAccessPermission.secondButton"),
            onPress: function onPress() {
              return null;
            }
          }]);
        }
      });
      return function checkCameraAndMicroPermissionAndNavigate(_x2) {
        return _ref5.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      var checkIsInTheChangiAirportTerminals = /*#__PURE__*/function () {
        var _ref6 = (0, _asyncToGenerator2.default)(function* (locationData) {
          var _locationData$coords, _locationData$coords2;
          var input = {
            lat: (locationData == null || (_locationData$coords = locationData.coords) == null ? undefined : _locationData$coords.latitude) || "",
            long: (locationData == null || (_locationData$coords2 = locationData.coords) == null ? undefined : _locationData$coords2.longitude) || ""
          };
          try {
            var _env, _env2, _response$data, _response$data2;
            var response = yield (0, _request.default)({
              url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
              method: "post",
              data: (0, _awsAmplify.graphqlOperation)(_queries.vCEACheckAirportPremise, {
                input: input
              }),
              parameters: {},
              headers: {
                "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
              }
            });
            var _ref7 = (response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.vCEACheckAirportPremise) || {},
              isInAirportPremise = _ref7.isInAirportPremise,
              vCEALandingPage = _ref7.vCEALandingPage;
            if (isInAirportPremise && vCEALandingPage) {
              setShouldNotCheckLocationPermission(true);
              checkCameraAndMicroPermissionAndNavigate(vCEALandingPage);
            } else {
              setCheckingLocation(false);
            }
            if ((response == null || (_response$data2 = response.data) == null || (_response$data2 = _response$data2.data) == null ? undefined : _response$data2.vCEACheckAirportPremise) === null) {
              setIsServiceUnavailable(true);
            }
          } catch (error) {
            setCheckingLocation(false);
            setIsServiceUnavailable(true);
          }
        });
        return function checkIsInTheChangiAirportTerminals(_x3) {
          return _ref6.apply(this, arguments);
        };
      }();
      var checkLocationPermission = /*#__PURE__*/function () {
        var _ref8 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setCheckingLocation(false);
            setIsServiceUnavailable(true);
            return;
          }
          var isLocationEnabled = (0, _reactNativeDeviceInfo.isLocationEnabledSync)();
          if (isLocationEnabled) {
            var iOSLocationPermissions = [_reactNativePermissions.PERMISSIONS.IOS.LOCATION_ALWAYS, _reactNativePermissions.PERMISSIONS.IOS.LOCATION_WHEN_IN_USE];
            var androidLocationPermissions = [_reactNativePermissions.PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION, _reactNativePermissions.PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION];
            var permissionsToCheck = _reactNative2.Platform.OS === "ios" ? iOSLocationPermissions : androidLocationPermissions;
            var permissionStatus = yield _reactNativePermissions.default.checkMultiple(permissionsToCheck);
            var isGranted = !!Object.values(permissionStatus || {}).find(function (i) {
              return i === _reactNativePermissions.RESULTS.GRANTED;
            });
            setIsEnabledLocation(isGranted);
            if (isGranted) {
              if (!isEnabledLocation) {
                setCheckingLocation(true);
              }
              _reactNativeGeolocationService.default.getCurrentPosition(function (position) {
                return checkIsInTheChangiAirportTerminals(position);
              }, function (error) {
                return undefined;
              }, {
                enableHighAccuracy: true,
                timeout: 15000,
                maximumAge: 10000
              });
            }
            if (!isGranted) {
              setCheckingLocation(true);
              setIsEnabledLocation(false);
              setTimeout(function () {
                setCheckingLocation(false);
              }, 1000);
            }
          } else {
            setCheckingLocation(false);
            setIsEnabledLocation(false);
          }
        });
        return function checkLocationPermission() {
          return _ref8.apply(this, arguments);
        };
      }();
      var shouldCheckLocationPermission = !shouldNotCheckLocationPermission || isBackFromSettingsRef.current;
      if (appStateVisible === _useAppStateChange2.APP_STATES.ACTIVE && isVceaLiveChat && shouldCheckLocationPermission) {
        checkLocationPermission();
        if (isBackFromSettingsRef.current) {
          isBackFromSettingsRef.current = false;
        }
      }
      if (!isVceaLiveChat) {
        setCheckingLocation(false);
      }
    }, [appStateVisible, isVceaLiveChat, isBackFromSettingsRef.current, shouldNotCheckLocationPermission]);
    (0, _react.useEffect)(function () {
      if (appStateVisible.match(/inactive|background/) && isOpeningSettings) {
        setIsOpeningSettings(false);
        isBackFromSettingsRef.current = true;
      }
    }, [appStateVisible, isOpeningSettings]);
    if (checkingLocation) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.default.checkingLocation,
        children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
          style: _styles.default.lottieStyle,
          source: _loadingAnimation.default,
          autoPlay: true,
          loop: true
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: Object.assign({}, _styles.default.contactLiveAgentWrapper, {
        paddingBottom: !isVceaLiveChat || isServiceUnavailable ? 5 : 0
      }),
      children: [pageIcon, (isServiceUnavailable || !isVceaLiveChat) && (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "vECA.serviceUnavailable",
        style: _styles.default.serviceUnavailable
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: messageTx,
        style: _styles.default.contactLiveAgentMessage
      }), renderFirstCtaButton, renderSecondCtaButton]
    });
  };
  var BottomSheetVcea = function BottomSheetVcea() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var isPressRetryRef = (0, _react.useRef)(false);
    var isInternetErrorRef = (0, _react.useRef)(false);
    var isMoreOptionsErrorRef = (0, _react.useRef)(false);
    var _useModal3 = (0, _useModal4.useModal)("bottomSheetVcea"),
      isModalVisible = _useModal3.isModalVisible,
      closeModal = _useModal3.closeModal;
    var _useState1 = (0, _react.useState)(contactUsTx),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      headerTitleTx = _useState10[0],
      setHeaderTitleTx = _useState10[1];
    var _useState11 = (0, _react.useState)(_constants.VECA_PAGES.CONTACT_US),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      currentPage = _useState12[0],
      setCurrentPage = _useState12[1];
    var moreOptionsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.moreOptionsData);
    var moreOptionsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.moreOptionsError);
    var moreOptionsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.moreOptionsFetching);
    var checkConnection = /*#__PURE__*/function () {
      var _ref9 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected || moreOptionsError) {
          setHeaderTitleTx("");
          isInternetErrorRef.current = !isConnected;
          isMoreOptionsErrorRef.current = moreOptionsError;
          setCurrentPage(_constants.VECA_PAGES.ERROR_PAGE);
        }
      });
      return function checkConnection() {
        return _ref9.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (isModalVisible) {
        checkConnection();
      }
    }, [isModalVisible]);
    (0, _react.useEffect)(function () {
      if (!(0, _validate.isEmpty)(moreOptionsData) && isPressRetryRef.current) {
        isPressRetryRef.current = false;
        isMoreOptionsErrorRef.current = false;
        var contactUsInfo = moreOptionsData == null || moreOptionsData.find == null ? undefined : moreOptionsData.find(function (ele) {
          var _ele$fragmentTags2;
          return !!(ele != null && (_ele$fragmentTags2 = ele.fragmentTags) != null && _ele$fragmentTags2.find(function (i) {
            return (i == null ? undefined : i.filterType) === _constants2.AEM_FILTER_TYPES.CONTACT_US_VARIATION;
          }));
        });
        if (contactUsInfo != null && contactUsInfo.navigationValue && contactUsInfo != null && contactUsInfo.navigationType) {
          setHeaderTitleTx(contactUsTx);
          setCurrentPage(_constants.VECA_PAGES.CONTACT_US);
        }
      }
    }, [moreOptionsData]);
    var onGoBackContactUsPage = function onGoBackContactUsPage() {
      setHeaderTitleTx(contactUsTx);
      setCurrentPage(_constants.VECA_PAGES.CONTACT_US);
    };
    var onPressRetryBottomSheetError = /*#__PURE__*/function () {
      var _ref0 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (!isConnected) {
          return;
        }
        var contactUsInfo = moreOptionsData == null || moreOptionsData.find == null ? undefined : moreOptionsData.find(function (ele) {
          var _ele$fragmentTags3;
          return !!(ele != null && (_ele$fragmentTags3 = ele.fragmentTags) != null && _ele$fragmentTags3.find(function (i) {
            return (i == null ? undefined : i.filterType) === _constants2.AEM_FILTER_TYPES.CONTACT_US_VARIATION;
          }));
        });
        var shouldShowContactUsPage = isInternetErrorRef.current || (contactUsInfo == null ? undefined : contactUsInfo.navigationValue) && (contactUsInfo == null ? undefined : contactUsInfo.navigationType);
        if (shouldShowContactUsPage) {
          setHeaderTitleTx(contactUsTx);
          setCurrentPage(_constants.VECA_PAGES.CONTACT_US);
          if (isInternetErrorRef.current) isInternetErrorRef.current = false;
          return;
        }
        isPressRetryRef.current = true;
        if (isMoreOptionsErrorRef.current) {
          dispatch(_forYouRedux.default.moreOptionsRequest());
        }
      });
      return function onPressRetryBottomSheetError() {
        return _ref0.apply(this, arguments);
      };
    }();
    var mappingBodyContent = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _constants.VECA_PAGES.CONTACT_US, (0, _jsxRuntime.jsx)(ContactUsEntryPoints, {
      setCurrentPage: setCurrentPage,
      setHeaderTitleTx: setHeaderTitleTx
    })), _constants.VECA_PAGES.CONTACT_LIVE_AGENT, (0, _jsxRuntime.jsx)(ContactLiveAgent, {
      onGoBackContactUsPage: onGoBackContactUsPage
    })), _constants.VECA_PAGES.ERROR_PAGE, (0, _jsxRuntime.jsx)(_errorPage.default, {
      onPressRetry: onPressRetryBottomSheetError
    }));
    var onCloseSheet = function onCloseSheet() {
      if ([_constants.VECA_PAGES.CONTACT_US, _constants.VECA_PAGES.ERROR_PAGE].includes(currentPage)) {
        closeModal();
        setCurrentPage(_constants.VECA_PAGES.CONTACT_US);
        dispatch(_systemRedux.default.resetBottomSheetVceaData());
      }
      if (currentPage === _constants.VECA_PAGES.CONTACT_LIVE_AGENT) {
        onGoBackContactUsPage();
      }
    };
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      stopDragCollapse: true,
      onBackPressHandle: true,
      onClosedSheet: onCloseSheet,
      isModalVisible: isModalVisible,
      animationInTiming: ANIMATION_TIMING,
      animationOutTiming: ANIMATION_TIMING,
      containerStyle: _styles.default.wrapperContainer,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.default.headerWrapper,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: headerTitleTx,
          style: _styles.default.headerTitle
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onCloseSheet,
          style: _styles.default.closeButton,
          hitSlop: {
            top: 10,
            left: 10,
            right: 10,
            bottom: 10
          },
          children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
            style: _styles.default.closeIcon
          })
        })]
      }), mappingBodyContent[currentPage], (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
        visible: isModalVisible && moreOptionsFetching
      })]
    });
  };
  var _default = exports.default = BottomSheetVcea;
