  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var ctaItemWidth = (_reactNative.Dimensions.get("window").width - 88) / 3;
  var styles = _reactNative.StyleSheet.create({
    wrapperContainer: {
      height: "auto",
      paddingBottom: 40,
      alignItems: "center",
      borderTopEndRadius: 16,
      borderTopLeftRadius: 16,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    headerWrapper: {
      width: "100%",
      paddingVertical: 20,
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      justifyContent: "space-between"
    },
    headerTitle: Object.assign({
      flex: 1
    }, _text.newPresets.bodyTextBold, {
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    }),
    closeButton: {
      right: 16,
      position: "absolute"
    },
    closeIcon: {
      color: _theme.color.palette.lightPurple
    },
    bodyWrapper: {
      width: "100%",
      paddingHorizontal: 24
    },
    ctasWrapper: {
      paddingVertical: 8,
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    ctaItem: {
      borderWidth: 1,
      borderRadius: 8,
      width: ctaItemWidth,
      paddingVertical: 12,
      alignItems: "center",
      justifyContent: "center",
      borderColor: _theme.color.palette.lighterGrey
    },
    ctaItemIcon: {
      color: _theme.color.palette.almostBlackGrey
    },
    ctaItemLabel: Object.assign({
      marginTop: 8
    }, _text.newPresets.bodyTextBold, {
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    }),
    fullLineCtaItem: {
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    fullLineCtaButton: {
      alignItems: "center",
      flexDirection: "row"
    },
    fullLineCtaItemLabel: Object.assign({
      marginLeft: 16
    }, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    contactLiveAgentWrapper: {
      paddingHorizontal: 24
    },
    checkingLocation: {
      height: 273,
      alignItems: "center",
      justifyContent: "center"
    },
    lottieStyle: {
      width: 157,
      height: "100%",
      marginTop: 10,
      alignSelf: "center"
    },
    contactLiveAgentIcon: {
      marginVertical: 16,
      alignSelf: "center"
    },
    serviceUnavailable: Object.assign({}, _text.newPresets.h2, {
      lineHeight: 28,
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    }),
    contactLiveAgentMessage: Object.assign({}, _text.newPresets.bodyTextRegular, {
      textAlign: "center",
      marginTop: 16,
      marginBottom: 24,
      color: _theme.color.palette.almostBlackGrey
    }),
    contactLiveAgentFirstButton: {
      borderRadius: 60,
      paddingVertical: 12,
      alignItems: "center",
      paddingHorizontal: 18
    },
    contactLiveAgentFirstButtonText: Object.assign({}, _text.newPresets.bodyTextBold),
    contactLiveAgentSecondButton: {
      marginTop: 16,
      alignSelf: "center"
    },
    contactLiveAgentSecondButtonText: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.lightPurple
    }),
    errorIcon: {
      top: -35,
      position: "absolute"
    },
    errorTitle: Object.assign({}, _text.newPresets.h2, {
      lineHeight: 28,
      textAlign: "center",
      marginBottom: 16
    }),
    errorMessage: Object.assign({}, _text.newPresets.bodyTextRegular, {
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    }),
    errorButtonStyle: {
      marginTop: 24,
      borderRadius: 60,
      width: _reactNative.Dimensions.get("window").width - 48
    }
  });
  var _default = exports.default = styles;
