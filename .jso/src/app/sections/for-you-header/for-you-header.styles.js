  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = _reactNative.StyleSheet.create({
    backgroundStyle: {
      position: "absolute",
      width: "100%",
      height: 500,
      top: 0,
      left: 0,
      zIndex: -2
    },
    backgroundImageStyle: {
      width: "100%",
      height: 500
    },
    nameContainerStyle: {
      flexDirection: "row",
      alignItems: "center",
      paddingTop: 54,
      paddingBottom: 14,
      paddingLeft: 24
    },
    settingStyle: {
      marginLeft: "auto",
      marginRight: 16
    },
    notifyContainer: {
      position: "relative",
      width: "11%"
    },
    badgeStyle: {
      backgroundColor: _theme.color.palette.lightRed,
      position: "absolute",
      borderRadius: 10,
      alignItems: "center",
      justifyContent: "center",
      left: 7,
      top: -6,
      paddingHorizontal: 5,
      paddingVertical: 1
    },
    badgeTextStyle: {
      color: _theme.color.palette.almostWhiteGrey
    },
    placeholderContainerStyle: {
      height: 232,
      marginHorizontal: 16,
      alignItems: "center"
    },
    placeholderBgStyle: {
      position: "absolute",
      width: "100%",
      height: 500,
      top: 0,
      left: 0,
      zIndex: -2
    },
    placeholderBar1Style: {
      width: 204,
      height: 16,
      borderRadius: 8,
      marginBottom: 8
    },
    placeholderBar2Style: {
      width: 120,
      height: 12,
      borderRadius: 8
    },
    refreshIndicatorContainerStyle: {
      alignItems: 'center',
      height: 0,
      opacity: 0,
      overflow: 'hidden'
    }
  });
  var _default = exports.default = styles;
