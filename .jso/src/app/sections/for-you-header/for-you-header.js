  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _isNumber2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _text = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _forYouHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _bottomSheetVerifyEmail = _$$_REQUIRE(_dependencyMap[17]);
  var _type = _$$_REQUIRE(_dependencyMap[18]);
  var _noInternetModalError = _$$_REQUIRE(_dependencyMap[19]);
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[20]));
  var _adobe = _$$_REQUIRE(_dependencyMap[21]);
  var _utils = _$$_REQUIRE(_dependencyMap[22]);
  var _errorAccount = _$$_REQUIRE(_dependencyMap[23]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[25]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _headerRewards = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _authStateSection = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[28]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _theme = _$$_REQUIRE(_dependencyMap[31]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[32]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[33]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[34]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[35]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ForYou__HeaderForYou";
  var deviceId = _reactNativeDeviceInfo.default.getUniqueIdSync();
  var THRESH_HOLD = 80;
  var HeaderPlaceholder = function HeaderPlaceholder() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        colors: ["#F0F0F0", "#EEEEEE", "#FCFCFC"],
        end: {
          x: 0,
          y: 1
        },
        start: {
          x: 0,
          y: 0
        },
        style: _forYouHeader.default.placeholderBgStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _forYouHeader.default.placeholderContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: _forYouHeader.default.placeholderBar1Style
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _theme.color.shimmerPlacholderColor,
          shimmerStyle: _forYouHeader.default.placeholderBar2Style
        })]
      })]
    });
  };
  var _worklet_*************_init_data = {
    code: "function forYouHeaderTsx1(){const{interpolate,positionY,THRESH_HOLD}=this.__closure;const polateHeight=interpolate(positionY.value,[0,THRESH_HOLD],[0,48]);const polateOpacity=interpolate(positionY.value,[0,THRESH_HOLD],[0,1]);return{height:polateHeight,opacity:polateOpacity};}"
  };
  var ForYouHeader = function ForYouHeader(props) {
    var positionY = props.positionY;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var profileLandingError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileLandingError);
    var profileCardNoMissing = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileCardNoMissing);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var isUserVerified = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.isAccountVerified);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var notificationsCountPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.notificationsCountPayload);
    var eventAndPromotionNotificationCount = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.eventAndPromotionNotificationCount);
    var allL1AdvisoriesPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.allL1AdvisoriesPayload);
    var allAnnouncementsPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.allAnnouncementsPayload);
    var unReadAnnouncementCount = (0, _screenHelper.countUnreadAnnouncements)([].concat((0, _toConsumableArray2.default)(allL1AdvisoriesPayload || []), (0, _toConsumableArray2.default)(allAnnouncementsPayload || [])));
    var rewardsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsFetching);
    var countNotification = notificationsCountPayload + eventAndPromotionNotificationCount + unReadAnnouncementCount;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showResend = _useState2[0],
      setShowResend = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      showNoInternetError = _useState4[0],
      setShowNoInternetError = _useState4[1];
    var notificationsCountLoading = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.notificationsCountLoading);
    var aemCommonData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var overallLoading = notificationsCountLoading && !(0, _isNumber2.default)(notificationsCountPayload) || isLoggedIn && profileFetching && !profilePayload || isLoggedIn && rewardsFetching && !rewardsData || (aemCommonData == null ? undefined : aemCommonData.loading) && !(aemCommonData != null && aemCommonData.data);
    var isJustLoggedOut = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isJustLoggedOut);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      var interactionHandle = _reactNative2.InteractionManager.runAfterInteractions(function () {
        dispatch(_notificationRedux.default.notificationsCountRequest(deviceId));
      });
      return function () {
        if (interactionHandle) {
          interactionHandle.cancel();
        }
      };
    }, []));
    var backgroundTheme = function backgroundTheme() {
      var _rewardsData$reward;
      var tier = !isLoggedIn ? "" : rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null || (_rewardsData$reward = _rewardsData$reward.currentTierInfo) == null ? undefined : _rewardsData$reward.replace(" ", "");
      var BackgroundGradient = _backgrounds.GradientBGAccountPageMember;
      switch (tier) {
        case _type.Tier[_type.Tier.Member]:
        case _type.Tier[_type.Tier.StaffMember]:
          break;
        case _type.Tier[_type.Tier.Gold]:
        case _type.Tier[_type.Tier.StaffGold]:
          BackgroundGradient = _backgrounds.GradientBGAccountPageGold;
          break;
        case _type.Tier[_type.Tier.Platinum]:
        case _type.Tier[_type.Tier.StaffPlatinum]:
          BackgroundGradient = _backgrounds.GradientBGAccountPagePlatinum;
          break;
        case _type.Tier[_type.Tier.Monarch]:
        case _type.Tier[_type.Tier.StaffMonarch]:
          BackgroundGradient = _backgrounds.GradientBGAccountPageMonarch;
          break;
        default:
          break;
      }
      var windowWidth = _reactNative2.Dimensions.get("window").width;
      var bgHeight = windowWidth / 3 * 4;
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          width: windowWidth,
          position: 'absolute',
          zIndex: -1
        },
        children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
          source: BackgroundGradient,
          imageStyle: [_forYouHeader.default.backgroundImageStyle, {
            height: bgHeight
          }],
          resizeMode: "cover"
        })
      });
    };
    var onPressAccountSettings = function onPressAccountSettings() {
      var trackingDataToBeSent = `${_adobe.AdobeValueByTagName.CAppAccountTopNavigation} | ${_adobe.AdobeValueByTagName.CAppAccountSettings}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, trackingDataToBeSent));
      navigation.navigate(_constants.NavigationConstants.settingScreen);
    };
    var onPressToViewNotificans = function onPressToViewNotificans() {
      var trackingDataToBeSent = `${_adobe.AdobeValueByTagName.CAppAccountTopNavigation} | ${_adobe.AdobeValueByTagName.CAppAccountNotifications}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, trackingDataToBeSent));
      navigation.navigate(_constants.NavigationConstants.notificationsScreen);
    };
    var HeaderForYou = (0, _react.useMemo)(function () {
      if (isLoggedIn && !!profileLandingError && !profileFetching || profileCardNoMissing) {
        return (0, _jsxRuntime.jsx)(_errorAccount.ErrorAccountComponent, {});
      } else if (isLoggedIn && rewardsError && !profileFetching) {
        return (0, _jsxRuntime.jsx)(_authStateSection.default, {
          authStateType: _authStateSection.AUTH_STATE_TYPE.LOGIN_FAILURE
        });
      } else if (isLoggedIn && (isUserVerified || profileFetching)) {
        return (0, _jsxRuntime.jsx)(_headerRewards.default, {
          profilePayload: profilePayload,
          rewardsData: rewardsData
        });
      }
      return (0, _jsxRuntime.jsx)(_authStateSection.default, {
        isJustLoggedOut: isJustLoggedOut,
        authStateType: _authStateSection.AUTH_STATE_TYPE.NON_LOGIN
      });
    }, [isLoggedIn, isUserVerified, profileFetching, profileLandingError, profilePayload, rewardsData, rewardsError, isJustLoggedOut]);
    var refreshIndicatorAnimatedStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var forYouHeaderTsx1 = function forYouHeaderTsx1() {
        var polateHeight = (0, _reactNativeReanimated.interpolate)(positionY.value, [0, THRESH_HOLD], [0, 48]);
        var polateOpacity = (0, _reactNativeReanimated.interpolate)(positionY.value, [0, THRESH_HOLD], [0, 1]);
        return {
          height: polateHeight,
          opacity: polateOpacity
        };
      };
      forYouHeaderTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        positionY: positionY,
        THRESH_HOLD: THRESH_HOLD
      };
      forYouHeaderTsx1.__workletHash = *************;
      forYouHeaderTsx1.__initData = _worklet_*************_init_data;
      return forYouHeaderTsx1;
    }());
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _forYouHeader.default.nameContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _forYouHeader.default.settingStyle,
          onPress: onPressAccountSettings,
          testID: `${COMPONENT_NAME}__TouchableSetting`,
          accessibilityLabel: `${COMPONENT_NAME}__TouchableSetting`,
          children: (0, _jsxRuntime.jsx)(_icons.AccountSetting, {
            width: "24",
            height: "24"
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _forYouHeader.default.notifyContainer,
          onPress: onPressToViewNotificans,
          testID: `${COMPONENT_NAME}__TouchableNotifications`,
          accessibilityLabel: `${COMPONENT_NAME}__TouchableNotifications`,
          children: [(0, _jsxRuntime.jsx)(_icons.AccountBell, {}), (0, _utils.handleCondition)(countNotification > 0, (0, _utils.handleCondition)(countNotification < 100, (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _forYouHeader.default.badgeStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallBold",
              style: _forYouHeader.default.badgeTextStyle,
              text: countNotification
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _forYouHeader.default.badgeStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "XSmallBold",
              style: _forYouHeader.default.badgeTextStyle,
              tx: "notificationsScreen.greaterThan99"
            })
          })), (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {}))]
        })]
      }), overallLoading ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: [_forYouHeader.default.refreshIndicatorContainerStyle, refreshIndicatorAnimatedStyle],
          children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
            size: "small",
            color: _theme.color.palette.whiteGrey
          })
        }), (0, _jsxRuntime.jsx)(HeaderPlaceholder, {})]
      }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [backgroundTheme(), isLoggedIn && (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: [_forYouHeader.default.refreshIndicatorContainerStyle, refreshIndicatorAnimatedStyle],
          children: (0, _jsxRuntime.jsx)(_reactNative2.ActivityIndicator, {
            size: "small",
            color: _theme.color.palette.whiteGrey
          })
        }), HeaderForYou]
      }), (0, _jsxRuntime.jsx)(_noInternetModalError.NoInternetModalError, {
        isShowModalError: showNoInternetError,
        onCloseModalError: function onCloseModalError() {
          return setShowNoInternetError(false);
        },
        testID: `${COMPONENT_NAME}__NoInternetModalError`,
        accessibilityLabel: `${COMPONENT_NAME}__NoInternetModalError`
      }), (0, _jsxRuntime.jsx)(_bottomSheetVerifyEmail.BottomSheetVerifyEmail, {
        visible: showResend,
        email: profilePayload == null ? undefined : profilePayload.email,
        onHide: function onHide() {
          return setShowResend(false);
        },
        testID: `${COMPONENT_NAME}__BottomSheetVerifyEmail`,
        accessibilityLabel: `${COMPONENT_NAME}__BottomSheetVerifyEmail`
      })]
    });
  };
  var _default = exports.default = ForYouHeader;
