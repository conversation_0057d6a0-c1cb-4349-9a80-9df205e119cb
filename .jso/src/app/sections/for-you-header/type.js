  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TypeBenefitCard = exports.Tier = undefined;
  var TypeBenefitCard = exports.TypeBenefitCard = /*#__PURE__*/function (TypeBenefitCard) {
    TypeBenefitCard["NOT_LOGGED_IN"] = "notLoggedIn";
    TypeBenefitCard["NON_VERIFED"] = "nonVerified";
    TypeBenefitCard["LOGGED_IN"] = "loggedIn";
    TypeBenefitCard["JUST_LOGGED_OUT"] = "justLoggedOut";
    return TypeBenefitCard;
  }(TypeBenefitCard || {});
  var Tier = exports.Tier = /*#__PURE__*/function (Tier) {
    Tier[Tier["Member"] = 0] = "Member";
    Tier[Tier["Gold"] = 1] = "Gold";
    Tier[Tier["Platinum"] = 2] = "Platinum";
    Tier[Tier["Classic"] = 3] = "Classic";
    Tier[Tier["Premium"] = 4] = "Premium";
    Tier[Tier["Elite"] = 5] = "Elite";
    Tier[Tier["StaffMember"] = 6] = "StaffMember";
    Tier[Tier["StaffGold"] = 7] = "StaffGold";
    Tier[Tier["StaffPlatinum"] = 8] = "StaffPlatinum";
    Tier[Tier["Monarch"] = 9] = "Monarch";
    Tier[Tier["StaffMonarch"] = 10] = "StaffMonarch";
    return Tier;
  }(Tier || {});
