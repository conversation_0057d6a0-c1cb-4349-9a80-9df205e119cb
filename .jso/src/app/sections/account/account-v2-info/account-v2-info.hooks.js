  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFetchData = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[1]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[3]);
  var _account = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var useFetchData = exports.useFetchData = function useFetchData() {
    var _useContext, _rewardsData$reward2;
    var dispatch = (0, _reactRedux.useDispatch)();
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var isMiffyGameVPR = (0, _remoteConfig.isFlagOnCondition)((_useContext = (0, _react.useContext)(_account.AccountContext)) == null ? undefined : _useContext.miffygameVPR);
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        var _rewardsData$reward;
        dispatch(_forYouRedux.default.accountInfoValuesRequest({
          cardNo: rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null ? undefined : _rewardsData$reward.cardNo,
          isMiffyGameVPR: isMiffyGameVPR
        }));
      });
    }, [rewardsData == null || (_rewardsData$reward2 = rewardsData.reward) == null ? undefined : _rewardsData$reward2.cardNo]);
  };
