  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _accountV2Info = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeDashedLine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _accountV2Info2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _native = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[16]);
  var _react = _$$_REQUIRE(_dependencyMap[17]);
  var _i18n = _$$_REQUIRE(_dependencyMap[18]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _drive = _$$_REQUIRE(_dependencyMap[20]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[21]);
  var _accountV2Info3 = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  var InfoTile = (0, _react.memo)(function (props) {
    var content = props.content,
      error = props.error,
      errorIcon = props.errorIcon,
      loading = props.loading,
      styles = props.styles;
    if (loading) return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _theme.color.shimmerPlacholderColor,
        shimmerStyle: styles.infoIconPlaceholderStyle
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _theme.color.shimmerPlacholderColorLighter,
        shimmerStyle: styles.infoValuePlaceholderStyle
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _theme.color.shimmerPlacholderColorLighter,
        shimmerStyle: styles.infoDescriptionPlaceholderStyle
      })]
    });
    if (error) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [styles.iconContainerStyle, {
            backgroundColor: _theme.color.palette.lightestGrey
          }],
          children: errorIcon
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _accountV2Info.errorStyles.titleContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _accountV2Info.errorStyles.titleTextStyle,
            text: (0, _i18n.translate)("forYouScreen.accountSection.errorTitle")
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _accountV2Info.errorStyles.descriptionTextStyle,
          text: (0, _i18n.translate)("forYouScreen.accountSection.errorDescription")
        })]
      });
    }
    return content;
  });
  var AccountV2Info = function AccountV2Info(props) {
    var isLoadingPrivilegeCards = props.isLoadingPrivilegeCards;
    var navigation = (0, _native.useNavigation)();
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewCarPassAirPort"),
      getCommonLoginModule = _useGeneratePlayPassU.getCommonLoginModule;
    var _useStyles = (0, _accountV2Info.useStyles)({
        loading: isLoadingPrivilegeCards
      }),
      styles = _useStyles.styles,
      tierInfo = _useStyles.tierInfo;
    var accountInfoValuesFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.accountInfoValuesFetching);
    var accountInfoValuesData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.accountInfoValuesData);
    var accountInfoValuesError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.accountInfoValuesError);
    var accountInfo1stTimeFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.accountInfo1stTimeFetching);
    var totalCreditsValue = accountInfoValuesData == null ? undefined : accountInfoValuesData.totalCredits;
    var totalPromoCodesValue = accountInfoValuesData == null ? undefined : accountInfoValuesData.totalPromoCodes;
    var totalPerksValue = accountInfoValuesData == null ? undefined : accountInfoValuesData.totalPerks;
    var totalBookingsOrdersValue = accountInfoValuesData == null ? undefined : accountInfoValuesData.totalBookingsOrders;
    var isLoading = accountInfo1stTimeFetching || accountInfoValuesFetching && !accountInfoValuesData && !accountInfoValuesError;
    var isProfileCardNoMissing = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileCardNoMissing);
    var errors = {
      credits: totalCreditsValue === null,
      vpr: totalPerksValue === null || isProfileCardNoMissing,
      bo: totalBookingsOrdersValue === null,
      promos: totalPromoCodesValue === null
    };
    var _useContext = (0, _react.useContext)(_drive.DriveContext),
      driveParkingFeatureFlag = _useContext.driveParkingFeatureFlag;
    var isDriveParking = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.DRIVE_PARKING, driveParkingFeatureFlag);
    var isAccountPromoCodes = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.ACCOUNT_PROMOCODES);
    var tmpParkingContentTx = "forYouScreen.accountSection.tmpParkingContent";
    var rebatesPromosTx = "forYouScreen.accountSection.rebatesPromos";
    var carnivalCreditsTx = "forYouScreen.accountSection.carnivalCredits";
    var promoCodesTx = "forYouScreen.accountSection.promoCodes";
    var vouchersPrizesRedemptionsTx = "forYouScreen.accountSection.vouchersPrizesRedemptions";
    var bookingOrdersTx = "forYouScreen.accountSection.bookingOrders";
    var goToParkingScreen = function goToParkingScreen() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppCarparkAvailabilityParkingPriviledges, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppCarparkAvailabilityParkingPriviledges, "1"));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, `${_adobe.AdobeValueByTagName.CAppAccountQuicklinks}${(0, _i18n.translate)(tmpParkingContentTx)} ${(0, _i18n.translate)(rebatesPromosTx)}`));
      if (isDriveParking) {
        navigation.navigate(_constants.NavigationConstants.parkingLanding);
      } else {
        getCommonLoginModule(_constants.StateCode.CARPASS);
      }
    };
    var handleGoToScreen = (0, _react.useCallback)(function (screen, buttonTitleTx) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccount, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccount, `${_adobe.AdobeValueByTagName.CAppAccountQuicklinks}${(0, _i18n.translate)(buttonTitleTx)}`));
      navigation.navigate(screen);
    }, []);
    (0, _accountV2Info2.useFetchData)();
    var onPressGoToCreditsScreen = function onPressGoToCreditsScreen() {
      handleGoToScreen(_constants.NavigationConstants.creditsScreen, carnivalCreditsTx);
    };
    var onPressGoToVouchersPrizesRedemptionsScreen = function onPressGoToVouchersPrizesRedemptionsScreen() {
      handleGoToScreen(_constants.NavigationConstants.vouchersPrizesRedemptionsScreen, vouchersPrizesRedemptionsTx);
    };
    var onPressGoToBookingsOrdersScreen = function onPressGoToBookingsOrdersScreen() {
      handleGoToScreen(_constants.NavigationConstants.bookingsOrdersScreen, bookingOrdersTx);
    };
    var onPressGoToPromoCodesScreen = function onPressGoToPromoCodesScreen() {
      handleGoToScreen(_constants.NavigationConstants.promoCodes, promoCodesTx);
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.subShadowContainerStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.titleContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.titleTextStyle,
            tx: "forYouScreen.accountSection.title"
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.rowStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              accessibilityLabel: `${_accountV2Info3.COMPONENT_NAME}__ParkingTile`,
              onPress: goToParkingScreen,
              style: styles.infoAreaStyle,
              testID: `${_accountV2Info3.COMPONENT_NAME}__ParkingTile`,
              children: (0, _jsxRuntime.jsx)(InfoTile, {
                content: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.iconContainerStyle,
                    children: (0, _jsxRuntime.jsx)(_icons.CarIcon, {
                      color: tierInfo.iconColor
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoValueTextStyle,
                    tx: tmpParkingContentTx
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoDescriptionTextStyle,
                    tx: rebatesPromosTx
                  })]
                }),
                loading: isLoading,
                styles: styles
              })
            }), (0, _jsxRuntime.jsx)(_reactNativeDashedLine.default, {
              axis: "vertical",
              dashThickness: 1,
              dashLength: 3,
              dashGap: 2,
              dashColor: tierInfo == null ? undefined : tierInfo.dashedLineColor,
              style: styles.verticalDashedLine
            }), isAccountPromoCodes ? (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              accessibilityLabel: `${_accountV2Info3.COMPONENT_NAME}__PromoCodesTile`,
              disabled: isLoading || errors.promos,
              onPress: onPressGoToPromoCodesScreen,
              style: styles.infoAreaStyle,
              testID: `${_accountV2Info3.COMPONENT_NAME}__PromoCodesTile`,
              children: (0, _jsxRuntime.jsx)(InfoTile, {
                content: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.iconContainerStyle,
                    children: (0, _jsxRuntime.jsx)(_icons.SaleTagFilled, {
                      color: tierInfo.iconColor
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoValueTextStyle,
                    children: totalPromoCodesValue
                  }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                    style: styles.infoDescriptionContainerStyle,
                    children: [(0, _jsxRuntime.jsx)(_text.Text, {
                      style: styles.infoDescriptionTextStyle,
                      tx: promoCodesTx
                    }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                      style: styles.newTagContainerStyle,
                      children: (0, _jsxRuntime.jsx)(_text.Text, {
                        style: styles.newTagLabelTextStyle,
                        tx: "forYouScreen.accountSection.newTag"
                      })
                    })]
                  })]
                }),
                error: errors.promos,
                errorIcon: (0, _jsxRuntime.jsx)(_icons.SaleTagFilled, {
                  color: _theme.color.palette.midGrey
                }),
                loading: isLoading,
                styles: styles
              })
            }) : (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              accessibilityLabel: `${_accountV2Info3.COMPONENT_NAME}__CreditsTile`,
              disabled: isLoading || errors.credits,
              onPress: onPressGoToCreditsScreen,
              style: styles.infoAreaStyle,
              testID: `${_accountV2Info3.COMPONENT_NAME}__CreditsTile`,
              children: (0, _jsxRuntime.jsx)(InfoTile, {
                content: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.iconContainerStyle,
                    children: (0, _jsxRuntime.jsx)(_icons.CoinIcon, {
                      color: tierInfo.iconColor
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoValueTextStyle,
                    children: totalCreditsValue
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoDescriptionTextStyle,
                    tx: carnivalCreditsTx
                  })]
                }),
                error: errors.credits,
                errorIcon: (0, _jsxRuntime.jsx)(_icons.CoinIcon, {
                  color: _theme.color.palette.midGrey
                }),
                loading: isLoading,
                styles: styles
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNativeDashedLine.default, {
            axis: "horizontal",
            dashThickness: 1,
            dashLength: 3,
            dashGap: 2,
            dashColor: tierInfo == null ? undefined : tierInfo.dashedLineColor
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.rowStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              accessibilityLabel: `${_accountV2Info3.COMPONENT_NAME}__VPRTile`,
              disabled: isLoading || errors.vpr,
              onPress: onPressGoToVouchersPrizesRedemptionsScreen,
              style: styles.infoArea2Style,
              testID: `${_accountV2Info3.COMPONENT_NAME}__VPRTile`,
              children: (0, _jsxRuntime.jsx)(InfoTile, {
                content: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.iconContainerStyle,
                    children: (0, _jsxRuntime.jsx)(_icons.GiftIcon, {
                      color: tierInfo.iconColor
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoValueTextStyle,
                    children: totalPerksValue
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoDescriptionTextStyle,
                    tx: vouchersPrizesRedemptionsTx
                  })]
                }),
                error: errors.vpr,
                errorIcon: (0, _jsxRuntime.jsx)(_icons.GiftIcon, {
                  color: _theme.color.palette.midGrey
                }),
                loading: isLoading,
                styles: styles
              })
            }), (0, _jsxRuntime.jsx)(_reactNativeDashedLine.default, {
              axis: "vertical",
              dashThickness: 1,
              dashLength: 3,
              dashGap: 2,
              dashColor: tierInfo == null ? undefined : tierInfo.dashedLineColor,
              style: styles.verticalDashedLine
            }), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              accessibilityLabel: `${_accountV2Info3.COMPONENT_NAME}__BookingOrdersTile`,
              disabled: isLoading || errors.bo,
              onPress: onPressGoToBookingsOrdersScreen,
              style: styles.infoArea2Style,
              testID: `${_accountV2Info3.COMPONENT_NAME}__BookingOrdersTile`,
              children: (0, _jsxRuntime.jsx)(InfoTile, {
                content: (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.iconContainerStyle,
                    children: (0, _jsxRuntime.jsx)(_icons.TicketIcon, {
                      color: tierInfo.iconColor
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoValueTextStyle,
                    children: totalBookingsOrdersValue
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.infoDescriptionTextStyle,
                    tx: bookingOrdersTx
                  })]
                }),
                error: errors.bo,
                errorIcon: (0, _jsxRuntime.jsx)(_icons.TicketIcon, {
                  color: _theme.color.palette.midGrey
                }),
                loading: isLoading,
                styles: styles
              })
            })]
          })]
        })]
      })
    });
  };
  var _default = exports.default = AccountV2Info;
