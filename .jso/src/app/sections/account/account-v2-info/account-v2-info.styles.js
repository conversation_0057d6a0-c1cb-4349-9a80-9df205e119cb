  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useStyles = exports.errorStyles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var useStyles = exports.useStyles = function useStyles(params) {
    var loading = params.loading;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var styles = (0, _react.useMemo)(function () {
      return _reactNative.StyleSheet.create({
        containerStyle: Object.assign({
          backgroundColor: _theme.color.palette.whiteGrey,
          borderRadius: 12
        }, _reactNative.Platform.select({
          android: Object.assign({
            elevation: 2
          }, loading ? {
            borderColor: _theme.color.palette.lighterGrey,
            borderTopWidth: 2
          } : {}),
          ios: {
            shadowColor: _theme.color.palette.almostBlackGrey,
            shadowOffset: {
              width: 0,
              height: -6
            },
            shadowOpacity: 0.2,
            shadowRadius: 5
          }
        })),
        subShadowContainerStyle: Object.assign({}, _reactNative.Platform.select({
          android: {
            elevation: 2
          },
          ios: {
            shadowColor: "rgba(0, 0, 0, 0.12)",
            shadowOffset: {
              width: 0,
              height: 6
            },
            shadowOpacity: 0.08,
            shadowRadius: 5
          }
        })),
        titleContainerStyle: {
          height: 24,
          position: "relative"
        },
        titleTextStyle: Object.assign({}, _text.newPresets.caption2Bold, {
          left: 16,
          position: "absolute",
          top: 10
        }),
        rowStyle: {
          display: "flex",
          flexDirection: "row"
        },
        infoAreaStyle: {
          padding: 16,
          width: "50%"
        },
        infoArea2Style: {
          padding: 16,
          width: "50%"
        },
        iconContainerStyle: {
          alignItems: "center",
          alignSelf: "flex-start",
          backgroundColor: memberIconInfo == null ? undefined : memberIconInfo.iconBgColor,
          borderRadius: 8,
          justifyContent: "center",
          height: 32,
          width: 32
        },
        infoIconStyle: {
          color: memberIconInfo == null ? undefined : memberIconInfo.iconColor
        },
        infoValueTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
          color: _theme.color.palette.almostBlackGrey,
          marginTop: 8
        }),
        infoDescriptionTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
          color: _theme.color.palette.darkestGrey,
          marginTop: 2,
          textTransform: "none"
        }),
        infoIconPlaceholderStyle: {
          borderRadius: 8,
          height: 32,
          width: 32
        },
        infoValuePlaceholderStyle: {
          borderRadius: 4,
          height: 12,
          marginTop: 8,
          width: 74
        },
        infoDescriptionPlaceholderStyle: {
          borderRadius: 4,
          height: 12,
          marginTop: 4,
          width: 40
        },
        verticalDashedLine: {
          top: 0,
          left: "50%",
          height: "98%",
          position: "absolute"
        },
        infoDescriptionContainerStyle: {
          alignItems: "baseline",
          flexDirection: "row",
          gap: 4
        },
        newTagContainerStyle: {
          alignItems: "center",
          backgroundColor: _theme.color.palette.lighterOrange,
          borderColor: _theme.color.palette.orangeWarning300,
          borderRadius: 4,
          borderWidth: 1,
          height: 16,
          justifyContent: "center",
          paddingHorizontal: 4
        },
        newTagLabelTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
          color: _theme.color.palette.orangeWarning600,
          fontSize: 8,
          fontWeight: _reactNative.Platform.select({
            ios: "900",
            android: "normal"
          })
        })
      });
    }, [loading, memberIconInfo]);
    return {
      styles: styles,
      tierInfo: memberIconInfo
    };
  };
  var errorStyles = exports.errorStyles = _reactNative.StyleSheet.create({
    titleContainerStyle: {
      display: "flex",
      height: 20,
      justifyContent: "flex-end",
      marginTop: 8
    },
    titleTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.almostBlackGrey,
      textAlignVertical: "bottom",
      textTransform: "none"
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.darkGrey999,
      marginTop: 2,
      textTransform: "none"
    })
  });
