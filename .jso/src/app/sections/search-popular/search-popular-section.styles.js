  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    cardViewContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      marginBottom: 12
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    })),
    clearAll: Object.assign({}, _text.presets.textLink, {
      color: _theme.color.palette.lightPurple,
      flex: 1,
      lineHeight: 22,
      marginTop: 4,
      paddingLeft: 12,
      textAlign: "right"
    }),
    containerStyle: {
      marginTop: 40,
      paddingHorizontal: 24
    },
    containerTitleStyle: {
      flex: 1,
      marginHorizontal: 10
    },
    contentStyle: {
      display: 'flex',
      flexDirection: "row",
      flexWrap: 'wrap',
      marginRight: -24
    },
    iconStyle: {
      height: 20,
      resizeMode: "contain",
      width: 20
    },
    optionContainer: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      flexDirection: "row",
      paddingLeft: 12
    },
    removeIconStyles: {
      alignItems: "center",
      height: 40,
      justifyContent: "center",
      paddingHorizontal: 14
    },
    rightItemContainer: {
      flexDirection: "row",
      flex: 1,
      marginVertical: 14,
      alignItems: "center"
    },
    tagStyle: {
      marginBottom: 12,
      marginRight: 12
    },
    title: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1,
      lineHeight: 22,
      marginBottom: 16
    }),
    titleContainer: {
      flexDirection: "row"
    },
    titleStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
