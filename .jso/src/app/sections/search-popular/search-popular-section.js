  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _searchPopularSection = _$$_REQUIRE(_dependencyMap[8]);
  var _chip = _$$_REQUIRE(_dependencyMap[9]);
  var _chip2 = _$$_REQUIRE(_dependencyMap[10]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _searchIndex = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var SearchPopularSectionDefault = function SearchPopularSectionDefault(props) {
    var _props$data = props.data,
      data = _props$data === undefined ? [] : _props$data,
      searchIndex = props.searchIndex;
    var dispatch = (0, _reactRedux.useDispatch)();
    var getSearchTabName = function getSearchTabName(searchIndex) {
      switch (searchIndex) {
        case _searchIndex.SearchIndex.all:
          return (0, _i18n.translate)("search.tabTitles.all");
        case _searchIndex.SearchIndex.dine:
          return (0, _i18n.translate)("search.tabTitles.dine");
        case _searchIndex.SearchIndex.shop:
          return (0, _i18n.translate)("search.tabTitles.shop");
        case _searchIndex.SearchIndex.flights:
          return (0, _i18n.translate)("search.tabTitles.flights");
        case _searchIndex.SearchIndex.airport:
          return (0, _i18n.translate)("search.tabTitles.airport");
        case _searchIndex.SearchIndex.attractions:
          return (0, _i18n.translate)("search.tabTitles.attractions");
        case _searchIndex.SearchIndex.events:
          return (0, _i18n.translate)("search.tabTitles.events");
        default:
          return "";
      }
    };
    var handleOnItemPress = function handleOnItemPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppPopularSearches, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppPopularSearches, `${getSearchTabName(searchIndex)} | ${item}`));
      dispatch(_searchRedux.default.setSearchKeyword(item));
    };
    var getTags = function getTags() {
      return data.map(function (tagName, index) {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _searchPopularSection.styles.tagStyle,
          children: (0, _jsxRuntime.jsx)(_chip.Chip, {
            onPressed: function onPressed() {
              return handleOnItemPress(tagName);
            },
            text: tagName,
            type: _chip2.ChipType.unSelected
          })
        }, "tag-" + index);
      });
    };
    return data && !!(data != null && data.length) ? (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _reactNative2.Keyboard.dismiss();
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _searchPopularSection.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _searchPopularSection.styles.titleContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _searchPopularSection.styles.title,
            text: (0, _i18n.translate)("search.popular.title")
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _searchPopularSection.styles.contentStyle,
          children: getTags()
        })]
      })
    }) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
  };
  var SearchPopularSection = function SearchPopularSection(props) {
    return (0, _jsxRuntime.jsx)(SearchPopularSectionDefault, Object.assign({}, props));
  };
  var _default = exports.default = SearchPopularSection;
