  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    contentContainerStyle: {
      paddingBottom: 120,
      paddingLeft: 24,
      paddingRight: 12
    },
    error: {
      paddingHorizontal: 24
    },
    errorComponent: {
      marginHorizontal: 0
    },
    errorContainer: {
      flexDirection: "row",
      justifyContent: "flex-start",
      marginBottom: 0
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      marginTop: 50
    },
    viewContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 16,
      marginHorizontal: 24
    }
  });
