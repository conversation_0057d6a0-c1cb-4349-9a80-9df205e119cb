  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _redemptionCatalogueCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _privilegesRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _redemptionCatalogueCard2 = _$$_REQUIRE(_dependencyMap[7]);
  var _error = _$$_REQUIRE(_dependencyMap[8]);
  var _errorProps = _$$_REQUIRE(_dependencyMap[9]);
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _redeemMoreRewards = _$$_REQUIRE(_dependencyMap[11]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var loadingData = Array(5).fill({
    type: _redemptionCatalogueCard2.RedemptionCatalogueType.loading
  });
  var COMPONENT_NAME = "RedeemMoreRewards";
  var RedeemMoreRewards = function RedeemMoreRewards(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFetching = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.exploreMoreRewardsFetching);
    var error = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.exploreMoreRewardsError);
    var exploreMoreRewards = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.exploreMoreRewards);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var label = props.label;
    var renderItem = function renderItem(_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsx)(_redemptionCatalogueCard.default, Object.assign({}, item, {
        testID: `${COMPONENT_NAME}__RedeemMoreRewardsCard__${index}`,
        accessibilityLabel: `${COMPONENT_NAME}__RedeemMoreRewardsButtonClosePopUp__${index}`
      }));
    };
    var data = (0, _react.useMemo)(function () {
      if (!isFetching && !(0, _isEmpty.default)(exploreMoreRewards)) {
        return exploreMoreRewards == null ? undefined : exploreMoreRewards.map(function (item) {
          return Object.assign({}, item, {
            type: _redemptionCatalogueCard2.RedemptionCatalogueType.default
          });
        });
      }
      return loadingData;
    }, [isFetching, exploreMoreRewards]);
    var getExploreMoreRewards = (0, _react.useCallback)(function () {
      dispatch(_privilegesRedux.default.getExploreMoreRewards((profilePayload == null ? undefined : profilePayload.cardNo) || ""));
    }, []);
    (0, _react.useEffect)(function () {
      getExploreMoreRewards();
      return function () {
        dispatch(_privilegesRedux.default.resetExploreMoreRewards());
      };
    }, [getExploreMoreRewards]);
    if (!(0, _isEmpty.default)(exploreMoreRewards) || isFetching) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _redeemMoreRewards.styles.parentContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "h4",
          text: label,
          style: _redeemMoreRewards.styles.viewContainer
        }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          contentContainerStyle: _redeemMoreRewards.styles.contentContainerStyle,
          data: data,
          renderItem: renderItem,
          horizontal: true,
          showsHorizontalScrollIndicator: false,
          keyExtractor: function keyExtractor(_, index) {
            return index.toString();
          },
          testID: `${COMPONENT_NAME}__FlatListRedeemMoreRewards`,
          accessibilityLabel: `${COMPONENT_NAME}__FlatListRedeemMoreRewards`
        })]
      });
    }
    if (!(0, _isEmpty.default)(error)) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [_redeemMoreRewards.styles.parentContainer, _redeemMoreRewards.styles.error],
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _redeemMoreRewards.styles.errorContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            text: label
          })
        }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _errorProps.ErrorComponentType.standard,
          onPressed: getExploreMoreRewards,
          testID: `${COMPONENT_NAME}__ErrorComponent`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorComponent`,
          style: _redeemMoreRewards.styles.errorComponent
        })]
      });
    }
    return null;
  };
  var _default = exports.default = RedeemMoreRewards;
