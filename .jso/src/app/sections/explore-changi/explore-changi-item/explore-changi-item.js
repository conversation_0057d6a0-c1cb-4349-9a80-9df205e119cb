  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreChangiItem = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _eventCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _attractionsFacilitiesServices = _$$_REQUIRE(_dependencyMap[4]);
  var _exploreChangiItemProps = _$$_REQUIRE(_dependencyMap[5]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativePlugin = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var widthDevice = _reactNative.Dimensions.get("window").width;
  var ExploreChangiItem = exports.ExploreChangiItem = function ExploreChangiItem(props) {
    var _item$aemItem;
    var item = props.item,
      onSelected = props.onSelected,
      index = props.index,
      _onLayout = props.onLayout,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ExploreChangiItem" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ExploreChangiItem" : _props$accessibilityL,
      viewRefs = props.viewRefs;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      ref: function ref(_ref) {
        return viewRefs.current[index] = _ref;
      },
      onLayout: function onLayout() {
        return _onLayout(index);
      },
      children: (item == null ? undefined : item.source) === _exploreChangiItemProps.ExploreChangiItemType.PlayPass ? (0, _jsxRuntime.jsx)(_eventCard.default, Object.assign({}, item, {
        imageUrl: item == null ? undefined : item.imageListing,
        categoryType: _exploreItemType.ExploreItemCategoryEnum.event,
        location: [item == null ? undefined : item.eventLocation],
        eventStart: item == null ? undefined : item.eventStart,
        eventEnd: item == null ? undefined : item.eventEnd,
        ticketPrices: item == null ? undefined : item.price,
        tokenType: item == null ? undefined : item.tokenType,
        onPressed: function onPressed() {
          return onSelected == null ? undefined : onSelected(item, index + 1);
        },
        testID: testID,
        accessibilityLabel: accessibilityLabel
      })) : (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.aemItemContainer,
        children: item == null || (_item$aemItem = item.aemItem) == null ? undefined : _item$aemItem.map(function (aemItem, aemIndex) {
          return (0, _reactNativePlugin.createElement)(_attractionsFacilitiesServices.AttractionsFacilitiesServices, Object.assign({}, aemItem, {
            title: aemItem == null ? undefined : aemItem.title,
            imageUrl: aemItem == null ? undefined : aemItem.imageUrl,
            onPressed: function onPressed() {
              return onSelected == null ? undefined : onSelected(aemItem, index + aemIndex + 1);
            },
            testID: testID,
            key: aemIndex,
            accessibilityLabel: accessibilityLabel,
            locationDisplayText: aemItem == null ? undefined : aemItem.eventLocation
          }));
        })
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    aemItemContainer: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      paddingHorizontal: 12,
      width: widthDevice
    }
  });
