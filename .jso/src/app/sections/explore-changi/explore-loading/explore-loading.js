  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreLoading = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _attractionsFacilitiesServices = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _exploreLoadingProps = _$$_REQUIRE(_dependencyMap[3]);
  var _exploreChangiStyles = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var renderFlatListData = function renderFlatListData(_ref) {
    var item = _ref.item,
      index = _ref.index,
      onSelected = _ref.onSelected;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      children: (0, _jsxRuntime.jsx)(_attractionsFacilitiesServices.AttractionsFacilitiesServices, Object.assign({}, item, {
        onPressed: function onPressed() {
          return onSelected == null ? undefined : onSelected(item);
        }
      }))
    }, index);
  };
  var ExploreLoading = exports.ExploreLoading = function ExploreLoading(props) {
    var onLayout = props.onLayout,
      onSelected = props.onSelected,
      style = props.style;
    var exploreLoadingData = _exploreLoadingProps.EXPLORE_LOADING_DATA.data;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      onLayout: onLayout,
      children: (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        style: [_exploreChangiStyles.styles.flatListStyle, style],
        contentContainerStyle: _exploreChangiStyles.styles.contentContainerStyle,
        numColumns: _exploreLoadingProps.EXPLORE_LOADING_NUMBER_OF_COLUMNS,
        data: exploreLoadingData,
        renderItem: function renderItem(_ref2) {
          var item = _ref2.item,
            index = _ref2.index;
          return renderFlatListData({
            item: item,
            index: index,
            onSelected: onSelected
          });
        },
        scrollEnabled: false,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return `ExploreLoading-${index.toString()}`;
        }
      })
    });
  };
