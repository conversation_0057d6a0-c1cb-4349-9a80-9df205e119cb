  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ExploreChangiSection = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _exploreChangiItem = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _customToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _exploreChangiStyles = _$$_REQUIRE(_dependencyMap[14]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _icons = _$$_REQUIRE(_dependencyMap[16]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _core = _$$_REQUIRE(_dependencyMap[18]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[20]);
  var _button = _$$_REQUIRE(_dependencyMap[21]);
  var _theme = _$$_REQUIRE(_dependencyMap[22]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _adobe = _$$_REQUIRE(_dependencyMap[26]);
  var _exploreHelper = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ExploreChangiSection = exports.ExploreChangiSection = function ExploreChangiSection(props) {
    var _dataCommonAEM$data;
    var onLayout = props.onLayout,
      _onSelected = props.onSelected,
      onLoginPress = props.onLoginPress,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "ExploreChangiSection" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "ExploreChangiSection" : _props$accessibilityL,
      showFilterModal = props.showFilterModal;
    var dispatch = (0, _reactRedux.useDispatch)();
    var toast = React.useRef(null);
    var viewRefs = React.useRef([]);
    var navigation = (0, _core.useNavigation)();
    var exploreDataPayload = (0, _reactRedux.useSelector)(function (data) {
      return _exploreRedux.ExploreSelectors.exploreData(data);
    });
    var exploreData = exploreDataPayload == null ? undefined : exploreDataPayload.exploreCategoriesData;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var tabSelectData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.selectedExploreCategoryItem);
    var hasMoreExploreData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.hasMoreExploreData);
    var indexItemExploreChangi = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.indexItemExploreChangi);
    var _ref = exploreDataPayload || {},
      isLoading = _ref.isLoading,
      hasError = _ref.hasError,
      paging = _ref.paging;
    var _paging = Object.assign({}, paging),
      _paging$pageNumber = _paging.pageNumber,
      exploreDataPageNumber = _paging$pageNumber === undefined ? 0 : _paging$pageNumber;
    var isDataEmpty = !exploreData || (exploreData == null ? undefined : exploreData.length) === 0;
    var pageNumber = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreDataPageNumber);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var exploreChangiFilter = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreChangiFilter);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig("AEM_COMMON_DATA"));
    var faqPage = dataCommonAEM == null || (_dataCommonAEM$data = dataCommonAEM.data) == null || (_dataCommonAEM$data = _dataCommonAEM$data.faqs) == null ? undefined : _dataCommonAEM$data.find(function (e) {
      return (e == null ? undefined : e.type) === "playPass";
    });
    var handleGlobalNavigate = function handleGlobalNavigate() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      navigation.navigate.apply(navigation, (0, _toConsumableArray2.default)(args));
    };
    var pressFAQ = function pressFAQ() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassLandingTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassLandingTiles, "FAQ"));
      handleGlobalNavigate(_constants.NavigationConstants.webview, {
        uri: (0, _mediaHelper.handleImageUrl)(faqPage == null ? undefined : faqPage.page),
        fromFAQ: true,
        showFooter: false,
        useForward: false,
        useClose: false
      });
    };
    var getExploreData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        dispatch(_exploreRedux.default.setNoInternetFilter(isConnected));
        if (isConnected) {
          var _exploreChangiFilter$;
          dispatch(_exploreRedux.default.exploreDataRequest({
            pageNumber: pageNumber,
            category: tabSelectData == null ? undefined : tabSelectData.text,
            categoryCode: tabSelectData == null ? undefined : tabSelectData.value,
            email: profilePayload == null ? undefined : profilePayload.email,
            date: exploreChangiFilter == null ? undefined : exploreChangiFilter.date,
            locations: exploreChangiFilter == null || (_exploreChangiFilter$ = exploreChangiFilter.locations) == null ? undefined : _exploreChangiFilter$.map(function (newItem) {
              return newItem.toUpperCase();
            })
          }));
        }
      });
      return function getExploreData() {
        return _ref2.apply(this, arguments);
      };
    }();
    var loadExploreData = function loadExploreData() {
      var shouldLoadData = hasMoreExploreData && pageNumber > exploreDataPageNumber;
      if (shouldLoadData) getExploreData();
    };
    React.useEffect(function () {
      loadExploreData();
    }, [hasMoreExploreData, pageNumber, exploreDataPageNumber]);
    var onRetry = React.useCallback(function () {
      var _toast$current;
      toast == null || (_toast$current = toast.current) == null || _toast$current.close(1);
      dispatch(_exploreRedux.default.exploreDataSetPageNumber());
    }, []);
    React.useEffect(function () {
      if (!isLoading && hasError && hasMoreExploreData) {
        var _toast$current2;
        toast == null || (_toast$current2 = toast.current) == null || _toast$current2.show((0, _i18n.translate)("exploreScreen.events.errorToastMessage"), _constants.TOAST_MESSAGE_DURATION, (0, _i18n.translate)("exploreScreen.events.retry"), onRetry, null);
      }
    }, [isLoading, hasError, hasMoreExploreData]);
    var handleItemLayout = function handleItemLayout(index) {
      viewRefs.current[index].measure(function (_x, _y, _width, _height, _pageX, pageY) {
        if (indexItemExploreChangi === index) {
          dispatch(_exploreRedux.default.setOffsetItemExploreChangi(pageY));
        }
      });
    };
    var renderChangiExploreItem = function renderChangiExploreItem(item, index) {
      var previousIds = (listExploreChangi != null ? listExploreChangi : []).slice(0, index).filter(function (i) {
        return i.packageCode;
      }).map(function (i) {
        return i.packageCode;
      });
      return (0, _jsxRuntime.jsx)(_exploreChangiItem.ExploreChangiItem, {
        item: item,
        index: index,
        onSelected: function onSelected(e, itemPosition) {
          return _onSelected(e, previousIds, itemPosition);
        },
        testID: `${testID}__ExploreChangiItem`,
        accessibilityLabel: `${accessibilityLabel}__ExploreChangiItem`,
        onLayout: handleItemLayout,
        viewRefs: viewRefs
      }, index);
    };
    var renderPerkView = function renderPerkView() {
      if (!isLoggedIn) {
        return (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _exploreChangiStyles.styles.perkTextStyles,
          children: [`Book or redeem event & premiums. `, (0, _jsxRuntime.jsxs)(_text.Text, {
            style: _exploreChangiStyles.styles.viewButonContainer,
            onPress: onLoginPress,
            testID: `${testID}__TextLogin`,
            accessibilityLabel: `${testID}__TextLogin`,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _exploreChangiStyles.styles.viewTextStyles,
              children: " Login"
            }), (0, _jsxRuntime.jsx)(_icons.CaretRight, {
              width: "24",
              height: "12",
              style: _exploreChangiStyles.styles.iconStyles
            })]
          })]
        });
      }
      if (exploreDataPayload != null && exploreDataPayload.isPerksAvailable) {
        return (0, _jsxRuntime.jsxs)(_text.Text, {
          style: _exploreChangiStyles.styles.perkTextStyles,
          children: [exploreDataPayload == null ? undefined : exploreDataPayload.perkTexts, (0, _jsxRuntime.jsxs)(_text.Text, {
            style: _exploreChangiStyles.styles.viewButonContainer,
            onPress: function onPress() {
              handleGlobalNavigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
                tabActive: _constants.NavigationConstants.perksTab
              });
            },
            testID: `${testID}__TextView`,
            accessibilityLabel: `${testID}__TextView`,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _exploreChangiStyles.styles.viewTextStyles,
              children: " View"
            }), (0, _jsxRuntime.jsx)(_icons.CaretRight, {
              width: "18",
              height: "12",
              style: _exploreChangiStyles.styles.iconStyles
            })]
          })]
        });
      }
      return (0, _jsxRuntime.jsx)(_text.Text, {
        style: _exploreChangiStyles.styles.perkTextStyles,
        text: (0, _i18n.translate)("exploreScreen.perksTextV2")
      });
    };
    var listExploreChangi = React.useMemo(function () {
      var convertedExploreChangiData = (0, _exploreHelper.convertExploreChangiData)(exploreData);
      return convertedExploreChangiData;
    }, [exploreData]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      onLayout: onLayout,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _exploreChangiStyles.styles.headerListContainer,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: exploreDataPayload == null ? undefined : exploreDataPayload.heroImage
          },
          style: _exploreChangiStyles.styles.perkHeroImageStyles,
          resizeMode: "cover"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _exploreChangiStyles.styles.perkContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _exploreChangiStyles.styles.perkStarContainer,
            children: (0, _jsxRuntime.jsx)(_icons.StarPerk, {
              color: _theme.color.palette.whiteGrey,
              height: 18,
              width: 18
            })
          }), exploreDataPayload != null && exploreDataPayload.isLoading ? (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: _exploreChangiStyles.styles.viewTextStyles
          }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _exploreChangiStyles.styles.rightPerkView,
            children: renderPerkView()
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _exploreChangiStyles.styles.faqButton,
            onPress: pressFAQ,
            testID: `${testID}__ButtonFAQ`,
            accessibilityLabel: `${testID}__ButtonFAQ`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              text: "FAQ",
              style: _exploreChangiStyles.styles.faqTextStyles
            })
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        style: _exploreChangiStyles.styles.flatListStyle,
        contentContainerStyle: _exploreChangiStyles.styles.contentContainerStyle,
        data: listExploreChangi || [],
        renderItem: function renderItem(_ref3) {
          var item = _ref3.item,
            index = _ref3.index;
          return renderChangiExploreItem(item, index);
        },
        scrollEnabled: true,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        },
        testID: `${testID}__FlatList`,
        accessibilityLabel: `${accessibilityLabel}__FlatList`
      }), isDataEmpty && !isLoading && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _exploreChangiStyles.styles.viewContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: _exploreChangiStyles.styles.emptyTextStateAbove,
          children: (0, _i18n.translate)("exploreScreen.events.noItems")
        }), (tabSelectData == null ? undefined : tabSelectData.value) === "all" ? (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: _exploreChangiStyles.styles.emptyTextStateBellow,
          children: (0, _i18n.translate)("exploreScreen.events.noItemsAction")
        }) : (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          style: _exploreChangiStyles.styles.emptyTextStateBellow,
          children: (0, _i18n.translate)("exploreScreen.events.noItemsActionCategory")
        }), (0, _jsxRuntime.jsx)(_button.Button, {
          tx: "exploreScreen.events.reSelectFilter",
          sizePreset: "large",
          textPreset: "buttonLarge",
          typePreset: "secondary",
          statePreset: "default",
          backgroundPreset: "light",
          textStyle: {
            color: _theme.color.palette.lightPurple
          },
          onPress: showFilterModal,
          style: _exploreChangiStyles.styles.btnReselectFilters,
          testID: `${testID}__ButtonReselectFilters`,
          accessibilityLabel: `${accessibilityLabel}__ButtonReselectFilters`
        })]
      }), (0, _jsxRuntime.jsx)(_customToast.default, {
        ref: toast,
        style: _exploreChangiStyles.styles.toastStyle,
        textButtonStyle: _exploreChangiStyles.styles.toastButtonStyle,
        positionValue: _exploreChangiStyles.styles.toastPositionStyle,
        fadeInDuration: 750,
        fadeOutDuration: 1000,
        opacity: 1,
        position: "bottom",
        textStyle: _exploreChangiStyles.styles.toastTextStyle
      })]
    });
  };
