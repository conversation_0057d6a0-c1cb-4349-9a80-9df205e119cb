  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var withScreen = _reactNative.Dimensions.get("screen").width;
  var heightPerkImage = 0.37333333333333335 * withScreen;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    btnReselectFilters: {
      marginTop: 24
    },
    contentContainerStyle: {
      alignItems: "center"
    },
    emptyTextStateAbove: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 18
    },
    emptyTextStateBellow: {
      color: _theme.color.palette.almostBlackGrey
    },
    faqButton: {
      height: "100%",
      justifyContent: "center"
    },
    faqTextStyles: Object.assign({
      paddingHorizontal: 24
    }, _text.presets.textLink, {
      color: _theme.color.palette.lightPurple
    }),
    flatListStyle: {
      paddingVertical: 20,
      width: "100%"
    },
    footerLoading: {
      height: 70
    },
    headerListContainer: {
      alignItems: "center",
      height: "auto",
      marginBottom: 24,
      marginTop: 12,
      width: "100%"
    },
    iconStyles: {
      marginTop: -1
    },
    illustrationStyle: {
      height: 400,
      width: 400
    },
    loadingFullScreenContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteColorOpacity,
      height: "100%",
      justifyContent: "center",
      position: "absolute",
      width: "100%"
    },
    perkContainer: Object.assign({
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      bottom: -36,
      flexDirection: "row",
      height: 72,
      justifyContent: "center",
      position: "absolute",
      width: _reactNative.Dimensions.get("window").width - 48
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.12,
        shadowOffset: {
          width: 0,
          height: 3
        },
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    perkHeroImageStyles: {
      height: heightPerkImage,
      width: "100%"
    },
    perkStarContainer: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.baseBlue,
      borderRadius: 14,
      justifyContent: "center",
      marginLeft: 18,
      marginRight: 10,
      padding: 5
    },
    perkTextStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    rightPerkView: {
      alignItems: "flex-start",
      borderRightColor: _theme.color.palette.lighterGrey,
      borderRightWidth: 1,
      flex: 1,
      height: "100%",
      justifyContent: "center",
      paddingRight: 16
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastPositionStyle: {
      bottom: 30
    },
    toastStyle: Object.assign({}, _reactNative.Platform.OS === "ios" && {
      position: "absolute",
      bottom: 0
    }, {
      alignItems: "flex-start",
      backgroundColor: _theme.color.palette.black,
      borderRadius: 8,
      height: 60,
      marginBottom: 40,
      width: "95%"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    viewButonContainer: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      textAlignVertical: "center"
    },
    viewContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      height: 216,
      marginBottom: _reactNative.Platform.OS === "ios" ? 60 : 48,
      paddingHorizontal: 24
    },
    viewTextStyles: Object.assign({}, _text.presets.textLink, {
      color: _theme.color.palette.lightPurple
    })
  });
