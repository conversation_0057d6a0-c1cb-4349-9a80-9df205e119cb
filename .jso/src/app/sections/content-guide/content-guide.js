  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.GuideContent = undefined;
  var _error = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _contentGuide = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _dineShopContext = _$$_REQUIRE(_dependencyMap[8]);
  var _color = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ContentGuide";
  var parentContainer = {
    backgroundColor: _color.color.palette.lightestGrey
  };
  var viewContainer = {
    marginLeft: 24,
    marginBottom: 16
  };
  var contentContainerStyle = {
    paddingHorizontal: 24,
    paddingBottom: 49
  };
  var contentGuideView = {
    marginRight: 12
  };
  var errorContainerTitle = {
    marginLeft: 24
  };
  var errorContainer = {
    marginBottom: 3
  };
  var getContentGuide = function getContentGuide(_ref, handleDineComponentClick, type) {
    var item = _ref.item;
    item.onPressed = function () {
      return handleDineComponentClick(item);
    };
    item.type = type;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: contentGuideView,
      accessible: false,
      children: (0, _jsxRuntime.jsx)(_contentGuide.ContentGuide, Object.assign({}, item))
    });
  };
  var GuideContent = exports.GuideContent = function GuideContent(props) {
    var _contentGuidePayload$;
    var title = props.title;
    var dispatch = (0, _reactRedux.useDispatch)();
    var contentGuidePayload = (0, _reactRedux.useSelector)(function (data) {
      return _dineRedux.DineSelectors.contentGuideData(data);
    });
    var loadContentGuide = function loadContentGuide() {
      dispatch(_dineRedux.default.dineContentGuideRequest());
    };
    _react.default.useEffect(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        loadContentGuide();
      });
    }, []);
    if (contentGuidePayload != null && contentGuidePayload.hasError) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: errorContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: errorContainerTitle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            text: title,
            testID: `${COMPONENT_NAME}__TitleSection`,
            accessibilityLabel: title
          })
        }), (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _error.ErrorComponentType.standard,
          onPressed: function onPressed() {
            loadContentGuide();
          }
        })]
      });
    }
    if (contentGuidePayload === null || contentGuidePayload === undefined || (contentGuidePayload == null || (_contentGuidePayload$ = contentGuidePayload.data) == null ? undefined : _contentGuidePayload$.length) === 0) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
    return (0, _jsxRuntime.jsx)(_dineShopContext.DineShopContext.Consumer, {
      children: function children(value) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: parentContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
            style: viewContainer,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              preset: "h4",
              testID: `${COMPONENT_NAME}__TitleSection`,
              accessibilityLabel: title,
              children: title
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
            contentContainerStyle: contentContainerStyle,
            data: contentGuidePayload.data,
            renderItem: function renderItem(item) {
              return getContentGuide(item, value.Handlers.dine.handleDineComponentClick, contentGuidePayload.type);
            },
            horizontal: true,
            showsHorizontalScrollIndicator: false,
            keyExtractor: function keyExtractor(_, index) {
              return index.toString();
            }
          })]
        });
      }
    });
  };
