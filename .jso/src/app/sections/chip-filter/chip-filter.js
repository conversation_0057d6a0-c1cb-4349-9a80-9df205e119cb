  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ChipFilterSection = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _chip = _$$_REQUIRE(_dependencyMap[2]);
  var _chip2 = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _dineRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _shopRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _color = _$$_REQUIRE(_dependencyMap[9]);
  var _core = _$$_REQUIRE(_dependencyMap[10]);
  var _chipFilter = _$$_REQUIRE(_dependencyMap[11]);
  var _error = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _native = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ChipFilterSection";
  var viewPaddingStyle = {
    paddingLeft: 5,
    backgroundColor: _color.color.palette.lightestGrey
  };
  var containerStyle = {
    backgroundColor: _color.color.palette.lightestGrey
  };
  var scrollContainerStyle = {
    paddingHorizontal: 21,
    marginTop: 12,
    marginBottom: 24
  };
  var errorContainerStyle = {
    backgroundColor: _color.color.palette.lightestGrey,
    paddingHorizontal: 21,
    paddingBottom: 24,
    marginTop: 12
  };
  var ChipFilterSection = exports.ChipFilterSection = function ChipFilterSection(props) {
    var _filterPillsPayload$d, _filterPillsPayload$d2, _filterPillsPayload$d3;
    var screen = props.screen;
    var isDineScreen = screen === "DINE";
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _core.useNavigation)();
    var filterItems = isDineScreen ? (0, _reactRedux.useSelector)(function (data) {
      return _dineRedux.DineSelectors.filterItems(data);
    }) : (0, _reactRedux.useSelector)(function (data) {
      return _shopRedux.ShopSelectors.filterItems(data);
    });
    if (!filterItems) {
      filterItems = [];
    }
    var _ref = isDineScreen ? (0, _reactRedux.useSelector)(function (data) {
        return _dineRedux.DineSelectors.filterPillsData(data);
      }) || {} : (0, _reactRedux.useSelector)(function (data) {
        return _shopRedux.ShopSelectors.filterPillsData(data);
      }) || {},
      filterPillsPayload = _ref.filterPillsPayload;
    var filterTiles = isDineScreen ? (0, _reactRedux.useSelector)(function (state) {
      return _dineRedux.DineSelectors.filterTitles(state);
    }) : (0, _reactRedux.useSelector)(function (state) {
      return _shopRedux.ShopSelectors.filterTitles(state);
    });
    var loadFilterPills = function loadFilterPills() {
      isDineScreen ? dispatch(_dineRedux.default.dineFilterPillsRequest()) : dispatch(_shopRedux.default.shopFilterPillsRequest());
    };
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        loadFilterPills();
      });
    }, []);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var interactionHandle = _reactNative.InteractionManager.runAfterInteractions(function () {
        isDineScreen ? dispatch(_dineRedux.default.dineResetFilterItems()) : dispatch(_shopRedux.default.shopResetFilterItems());
      });
      return function () {
        if (interactionHandle) {
          interactionHandle.cancel();
        }
      };
    }, [isDineScreen]));
    var handleFilter = function handleFilter(item) {
      isDineScreen ? dispatch(_dineRedux.default.dineHandleFilterItems(item)) : dispatch(_shopRedux.default.shopHandleFilterItems(item));
    };
    if (!filterPillsPayload || (filterPillsPayload == null || (_filterPillsPayload$d = filterPillsPayload.data) == null ? undefined : _filterPillsPayload$d.length) === 0 && filterPillsPayload != null && filterPillsPayload.errorFlag && (filterPillsPayload == null ? undefined : filterPillsPayload.type) === _chipFilter.ChipFilterType.default) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: errorContainerStyle,
        children: (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _error.ErrorComponentType.filter,
          onPressed: function onPressed() {
            loadFilterPills();
          }
        })
      });
    } else if (!filterPillsPayload || !(filterPillsPayload != null && filterPillsPayload.data) || (filterPillsPayload == null || (_filterPillsPayload$d2 = filterPillsPayload.data) == null ? undefined : _filterPillsPayload$d2.length) === 0) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
    var isLoading = (filterPillsPayload == null ? undefined : filterPillsPayload.type) === _chip2.ChipType.loading;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: containerStyle,
      children: (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        contentContainerStyle: scrollContainerStyle,
        nestedScrollEnabled: true,
        children: filterPillsPayload == null || (_filterPillsPayload$d3 = filterPillsPayload.data) == null ? undefined : _filterPillsPayload$d3.map(function (item, key) {
          return (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: viewPaddingStyle,
            children: (0, _jsxRuntime.jsx)(_chip.Chip, {
              onPressed: function onPressed() {
                handleFilter(item);
                if (isDineScreen) {
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppDineSearchFilterDirectory, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppDineSearchFilterDirectory, item == null ? undefined : item.tagTitle));
                  dispatch(_dineRedux.default.dineSetFilterTitles(filterTiles));
                  dispatch(_dineRedux.default.startRequestFilter(true));
                  navigation.navigate("dineFilterResults", {
                    filteredData: filterItems
                  });
                  return;
                }
                (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppShopSearchFilterDirectory, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppShopSearchFilterDirectory, item == null ? undefined : item.tagTitle));
                dispatch(_shopRedux.default.shopSetFilterTitles(filterTiles));
                dispatch(_shopRedux.default.startRequestFilter(true));
                navigation.navigate("shopFilterResults", {
                  filteredData: filterItems
                });
              },
              text: item.tagTitle,
              type: isLoading ? _chip2.ChipType.loading : _chip2.ChipType.unSelected
            })
          }, key);
        })
      })
    });
  };
