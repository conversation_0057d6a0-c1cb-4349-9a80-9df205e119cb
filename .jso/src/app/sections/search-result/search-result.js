  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _isEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _tenantListingHorizontal = _$$_REQUIRE(_dependencyMap[11]);
  var _searchResult = _$$_REQUIRE(_dependencyMap[12]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _searchYouMayAlsoLike = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchResult = function SearchResult(_ref) {
    var data = _ref.data,
      searchIndex = _ref.searchIndex,
      _onEndReached = _ref.onEndReached,
      _ref$testID = _ref.testID,
      testID = _ref$testID === undefined ? "SearchResult" : _ref$testID,
      _ref$accessibilityLab = _ref.accessibilityLabel,
      accessibilityLabel = _ref$accessibilityLab === undefined ? "SearchResult" : _ref$accessibilityLab,
      onRefresh = _ref.onRefresh,
      _ref$useTitle = _ref.useTitle,
      useTitle = _ref$useTitle === undefined ? false : _ref$useTitle,
      _ref$textTitle = _ref.textTitle,
      textTitle = _ref$textTitle === undefined ? "" : _ref$textTitle,
      itemYmalOnPress = _ref.itemYmalOnPress;
    var navigation = (0, _native.useNavigation)();
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      trackingSearchResultClick = _useContext.trackingSearchResultClick;
    var renderFlatListData = function renderFlatListData(_ref2, newNavigation) {
      var item = _ref2.item,
        index = _ref2.index;
      item.onPressed = function () {
        if (trackingSearchResultClick) {
          trackingSearchResultClick(item == null ? undefined : item.tenantName, searchIndex, index);
        }
        newNavigation.navigate(_searchResult.SearchResultDetailType[searchIndex], {
          tenantId: item == null ? undefined : item.id
        });
      };
      var getLocationContent = function getLocationContent() {
        return [item == null ? undefined : item.locationDisplay, item == null ? undefined : item.areaDisplay].filter(function (value) {
          return !(0, _isEmpty2.default)(value);
        }).join(` ${(0, _constants.getDotUnicode)()} `);
      };
      var getCategoryContent = function getCategoryContent() {
        var _item$categories;
        if (!(item != null && (_item$categories = item.categories) != null && _item$categories.length)) return '';
        return item.categories.slice(0, 3).map(function (ctg) {
          return ctg == null ? undefined : ctg.tagTitle;
        }).join(', ');
      };
      var getDietaryData = function getDietaryData() {
        var _item$dietary, _item$dietary2;
        if (!(item != null && (_item$dietary = item.dietary) != null && _item$dietary.length)) return undefined;
        var dietaryItem = item == null || (_item$dietary2 = item.dietary) == null ? undefined : _item$dietary2[0];
        var result = {
          content: dietaryItem == null ? undefined : dietaryItem.tagTitle,
          icon: undefined
        };
        switch (dietaryItem == null ? undefined : dietaryItem.tagTitle) {
          case _constants.DINE_DIETARY_TYPE.GLUTEN_FREE:
          case _constants.DINE_DIETARY_TYPE.GLUTEN_FREE_OPTIONS:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryGlutenFreeOptionsIcon, {});
            break;
          case _constants.DINE_DIETARY_TYPE.HALAL:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryHalalIcon, {});
            break;
          case _constants.DINE_DIETARY_TYPE.VEGAN:
          case _constants.DINE_DIETARY_TYPE.VEGAN_OPTIONS:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryVeganOptionsIcon, {});
            break;
          case _constants.DINE_DIETARY_TYPE.VEGETARIAN:
          case _constants.DINE_DIETARY_TYPE.VEGETARIAN_FRIENDLY:
            result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryVegetarianFriendlyIcon, {});
            break;
          default:
            break;
        }
        return result;
      };
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.flatListItemsStyle,
        children: (0, _jsxRuntime.jsx)(_tenantListingHorizontal.TenantListingHorizontal, Object.assign({}, item, {
          accessibilityLabel: `${accessibilityLabel}__FlatListSearchResult`,
          categoryContent: getCategoryContent(),
          dietaryData: getDietaryData(),
          location: getLocationContent(),
          testID: `${testID}__FlatListSearchResult`
        }))
      }, Math.random() * 12);
    };
    var renderSeparator = function renderSeparator() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.dividerStyle
      });
    };
    var titleContainer = {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingTop: 40
    };
    var titleStyle = Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 0.96,
      lineHeight: 22
    });
    var ymalContainer = {
      paddingHorizontal: 0,
      marginTop: 40
    };
    var ymalEmptyStyles = {
      height: 0
    };
    var ymalFlatListStyles = {
      paddingBottom: 0
    };
    var headerComponent = function headerComponent() {
      if (!useTitle) return null;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: titleContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: titleStyle,
            text: textTitle
          })
        })
      });
    };
    var footerComponent = function footerComponent() {
      return (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
        containerStyles: ymalContainer,
        flatListStyles: ymalFlatListStyles,
        emptyScreenStyles: ymalEmptyStyles,
        itemOnPress: itemYmalOnPress
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _reactNative2.Keyboard.dismiss();
      },
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.mainContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: onRefresh
          }),
          keyboardShouldPersistTaps: "always",
          onScroll: _reactNative2.Keyboard.dismiss,
          data: data,
          renderItem: function renderItem(item) {
            return renderFlatListData(item, navigation);
          },
          ItemSeparatorComponent: renderSeparator,
          showsVerticalScrollIndicator: false,
          onEndReachedThreshold: 0.3,
          onEndReached: function onEndReached(_ref3) {
            var distanceFromEnd = _ref3.distanceFromEnd;
            if (distanceFromEnd >= 0) {
              _onEndReached();
            }
          },
          keyExtractor: function keyExtractor(_, index) {
            return index.toString();
          },
          style: styles.flatListStyle,
          testID: `${testID}__FlatListSearchResult`,
          accessibilityLabel: `${accessibilityLabel}__FlatListSearchResult`,
          contentContainerStyle: styles.paddingContentList,
          ListHeaderComponent: headerComponent,
          ListFooterComponent: footerComponent
        })
      })
    });
  };
  var _default = exports.default = SearchResult;
