  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _searchPopularSection = _$$_REQUIRE(_dependencyMap[8]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _adobe = _$$_REQUIRE(_dependencyMap[10]);
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  var SearchPopularSectionDefault = function SearchPopularSectionDefault(props) {
    var _props$data = props.data,
      data = _props$data === undefined ? [] : _props$data;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var handleOnItemPress = function handleOnItemPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppPopularSearches, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppPopularSearches, `${(0, _i18n.translate)("searchV2.popular.title")} | ${item}`));
      dispatch(_searchRedux.default.setSearchKeyword(item));
      //@ts-ignore
      navigation.navigate(_constants.NavigationConstants.searchResult);
    };
    var getTags = function getTags() {
      return data.map(function (item, index) {
        return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return handleOnItemPress(item);
          },
          style: _searchPopularSection.styles.tagStyle,
          children: [(0, _jsxRuntime.jsx)(_icons.TrendUpIcon, {
            color: _theme.color.palette.darkestGrey
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            style: _searchPopularSection.styles.titleStyle,
            text: item
          })]
        }, "tag-" + index);
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _reactNative2.Keyboard.dismiss();
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _searchPopularSection.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _searchPopularSection.styles.title,
          text: (0, _i18n.translate)("searchV2.popular.title")
        }), data && !!(data != null && data.length) ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _searchPopularSection.styles.contentStyle,
          children: getTags()
        }) : null]
      })
    });
  };
  var SearchPopularSection = function SearchPopularSection(props) {
    return (0, _jsxRuntime.jsx)(SearchPopularSectionDefault, Object.assign({}, props));
  };
  var _default = exports.default = SearchPopularSection;
