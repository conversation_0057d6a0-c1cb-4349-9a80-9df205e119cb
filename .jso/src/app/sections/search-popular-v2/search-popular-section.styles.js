  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      marginTop: 40,
      paddingHorizontal: 20
    },
    titleStyle: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.darkestGrey,
      flexShrink: 1
    }),
    contentStyle: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: 8
    },
    iconStyle: {
      height: 20,
      resizeMode: "contain",
      width: 20
    },
    tagStyle: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      gap: 2,
      flexShrink: 1
    },
    title: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.darkestGrey,
      marginBottom: 16
    })
  });
