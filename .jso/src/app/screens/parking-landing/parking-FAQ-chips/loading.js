  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FAQLoading = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[2]);
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var FAQLoading = exports.FAQLoading = _react.default.memo(function () {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.viewRow,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.loadingBarStyleTopFirst
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.loadingBarStyleTopSecond
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.viewRow,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.loadingBarStyleBottomFirst
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.loadingBarStyleBottomSecond
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.loadingBarStyleTopSecond
        })]
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 45,
      gap: 12
    },
    viewRow: {
      width: '100%',
      flexDirection: 'row',
      gap: 8
    },
    loadingBarStyleTopFirst: {
      width: 240,
      height: 32,
      borderTopRightRadius: 99,
      borderBottomRightRadius: 99
    },
    loadingBarStyleTopSecond: {
      width: '100%',
      height: 32,
      borderTopLeftRadius: 99,
      borderBottomLeftRadius: 99
    },
    loadingBarStyleBottomFirst: {
      width: 50,
      height: 32,
      borderTopRightRadius: 99,
      borderBottomRightRadius: 99
    },
    loadingBarStyleBottomSecond: {
      width: 154,
      height: 32,
      borderRadius: 99
    }
  });
