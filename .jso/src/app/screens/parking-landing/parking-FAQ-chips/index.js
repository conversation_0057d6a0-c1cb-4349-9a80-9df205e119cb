  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ParkingFAQChips = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _marqueeViewRevert = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _marqueeView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _utils = _$$_REQUIRE(_dependencyMap[9]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[10]);
  var _loading = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  var timeDefault = 40000;
  var timeEachItem = 8000;
  var ParkingFAQChips = exports.ParkingFAQChips = _react.default.memo(function (props) {
    var _chipsPayload$dataTop, _chipsPayload$dataTop2, _chipsPayload$dataBot, _chipsPayload$dataBot2;
    var chipsError = props.chipsError,
      listChipsFetching = props.listChipsFetching,
      chipsPayload = props.chipsPayload,
      navigation = props.navigation,
      parkingLandingTrackValue = props.parkingLandingTrackValue;
    if (listChipsFetching) {
      return (0, _jsxRuntime.jsx)(_loading.FAQLoading, {});
    }
    var handleClickItem = function handleClickItem(item) {
      var _item$cta;
      var dataToBeSent = `${parkingLandingTrackValue} | FAQs | ${item == null ? undefined : item.question}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppParkingLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppParkingLanding, dataToBeSent));
      navigation.navigate(_constants.NavigationConstants.FAQLanding, {
        url: `${item == null || (_item$cta = item.cta) == null || (_item$cta = _item$cta.navigation) == null ? undefined : _item$cta.value}`
      });
    };
    var renderTime = function renderTime(data) {
      if (data) {
        return (data == null ? undefined : data.length) * timeEachItem;
      } else {
        return timeDefault;
      }
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: chipsError || !chipsPayload ? null : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.container,
        children: [(chipsPayload == null || (_chipsPayload$dataTop = chipsPayload.dataTop) == null ? undefined : _chipsPayload$dataTop.length) > 0 && (0, _jsxRuntime.jsx)(_marqueeViewRevert.default, {
          duration: renderTime(chipsPayload == null ? undefined : chipsPayload.dataTop),
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.viewContentMarquee,
            children: chipsPayload == null || (_chipsPayload$dataTop2 = chipsPayload.dataTop) == null ? undefined : _chipsPayload$dataTop2.map(function (item) {
              return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: styles.itemMarquee,
                onPress: function onPress() {
                  return handleClickItem(item);
                },
                children: [(item == null ? undefined : item.icon) && (0, _jsxRuntime.jsx)(_reactNative2.Image, {
                  accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__MarqueeItem___Icon`,
                  source: {
                    uri: (0, _utils.mappingUrlAem)(item == null ? undefined : item.icon)
                  },
                  style: styles.itemIconStyle,
                  testID: `${_parkingLanding.COMPONENT_NAME}__MarqueeItem___Icon`,
                  resizeMode: "contain"
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.txtQuestion,
                  testID: `${_parkingLanding.COMPONENT_NAME}__MarqueeItem___Question`,
                  children: item == null ? undefined : item.question
                })]
              }, `MarqueeItem_${item == null ? undefined : item.sequenceNumber}`);
            })
          })
        }), (chipsPayload == null || (_chipsPayload$dataBot = chipsPayload.dataBottom) == null ? undefined : _chipsPayload$dataBot.length) > 0 && (0, _jsxRuntime.jsx)(_marqueeView.default, {
          duration: renderTime(chipsPayload == null ? undefined : chipsPayload.dataBottom),
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.viewContentMarquee,
            children: chipsPayload == null || (_chipsPayload$dataBot2 = chipsPayload.dataBottom) == null ? undefined : _chipsPayload$dataBot2.map(function (item) {
              return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                style: styles.itemMarquee,
                onPress: function onPress() {
                  return handleClickItem(item);
                },
                children: [(item == null ? undefined : item.icon) && (0, _jsxRuntime.jsx)(_reactNative2.Image, {
                  accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__MarqueeItem___Icon`,
                  source: {
                    uri: (0, _utils.mappingUrlAem)(item == null ? undefined : item.icon)
                  },
                  style: styles.itemIconStyle,
                  testID: `${_parkingLanding.COMPONENT_NAME}__MarqueeItem___Icon`,
                  resizeMode: "contain"
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.txtQuestion,
                  testID: `${_parkingLanding.COMPONENT_NAME}__MarqueeItem___Question`,
                  children: item == null ? undefined : item.question
                })]
              }, `MarqueeItem_${item == null ? undefined : item.sequenceNumber}`);
            })
          })
        })]
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginTop: 50,
      gap: 12
    },
    viewContentMarquee: {
      height: 32,
      flexDirection: 'row'
    },
    itemMarquee: {
      flexDirection: 'row',
      height: 32,
      borderWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      borderRadius: 99,
      marginHorizontal: 4,
      alignItems: 'center',
      paddingHorizontal: 16
    },
    txtQuestion: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16
    },
    itemIconStyle: {
      width: 16,
      height: 16,
      marginRight: 8
    }
  });
