  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ParkingEcoupon = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _titleCopy = _$$_REQUIRE(_dependencyMap[3]);
  var _eCouponsLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _eCouponsItem = _$$_REQUIRE(_dependencyMap[5]);
  var _eCouponsError = _$$_REQUIRE(_dependencyMap[6]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var ParkingEcoupon = exports.ParkingEcoupon = _react.default.memo(function (props) {
    var _eCouponsPayload$data, _eCouponsPayload$data2;
    var refreshData = props.refreshData,
      eCouponsFetching = props.eCouponsFetching,
      eCouponsPayload = props.eCouponsPayload,
      eCouponsPayloadError = props.eCouponsPayloadError,
      fetchEcoupons = props.fetchEcoupons,
      eCouponStateCodeRef = props.eCouponStateCodeRef,
      parkingECouponSectionOffsetY = props.parkingECouponSectionOffsetY,
      parkingLandingTrackValue = props.parkingLandingTrackValue;
    var reTryCallApi = function reTryCallApi() {
      _globalLoadingController.default.showLoading(true);
      fetchEcoupons(_globalLoadingController.default.hideLoading, true);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      onLayout: function onLayout(evt) {
        parkingECouponSectionOffsetY.value = evt.nativeEvent.layout.y - _constants.PARKING_HEADER_HEIGHT;
      },
      style: styles.container,
      children: [((eCouponsPayload == null || (_eCouponsPayload$data = eCouponsPayload.data) == null ? undefined : _eCouponsPayload$data.length) > 0 || eCouponsFetching || eCouponsPayloadError) && (0, _jsxRuntime.jsx)(_titleCopy.TitleCopy, {
        isError: eCouponsPayloadError
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: eCouponsPayloadError ? styles.viewContent : styles.viewContentMargin,
        children: [eCouponsFetching && (0, _jsxRuntime.jsx)(_eCouponsLoading.default, {}), !eCouponsFetching && (eCouponsPayload == null || (_eCouponsPayload$data2 = eCouponsPayload.data) == null ? undefined : _eCouponsPayload$data2.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_eCouponsItem.EcouponsItem, {
            item: item,
            index: index,
            refreshData: refreshData,
            list: eCouponsPayload == null ? undefined : eCouponsPayload.data,
            eCouponStateCodeRef: eCouponStateCodeRef,
            parkingLandingTrackValue: parkingLandingTrackValue
          }, `eCouponsPayload_${index}`);
        })), !eCouponsFetching && eCouponsPayloadError && (0, _jsxRuntime.jsx)(_eCouponsError.ECouponsError, {
          reTryCallApi: reTryCallApi
        })]
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 50
    },
    viewContent: {
      paddingHorizontal: 16
    },
    viewContentMargin: {
      paddingHorizontal: 16,
      marginTop: 16
    }
  });
