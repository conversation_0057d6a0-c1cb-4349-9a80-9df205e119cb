  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EcouponsItem = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _useModal2 = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var EcouponsItem = exports.EcouponsItem = _react.default.memo(function (props) {
    var item = props.item,
      index = props.index,
      list = props.list,
      refreshData = props.refreshData,
      eCouponStateCodeRef = props.eCouponStateCodeRef,
      parkingLandingTrackValue = props.parkingLandingTrackValue;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useModal = (0, _useModal2.useModal)("parkingEntitlements"),
      openEditVehicleIUModal = _useModal.openModal;
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var onPressEcouponItem = function onPressEcouponItem() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppParkingLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppParkingLanding, `${parkingLandingTrackValue} | Parking eCoupon | ${item == null ? undefined : item.title}`));
      if (profilePayload && !(profilePayload != null && profilePayload.vehicleIU)) {
        eCouponStateCodeRef.current = item == null ? undefined : item.state_code;
        openEditVehicleIUModal();
        return;
      }
      handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.carpass, {
        refreshData: refreshData,
        stateCode: item == null ? undefined : item.state_code,
        noVehicleIUcallback: function noVehicleIUcallback() {
          eCouponStateCodeRef.current = item == null ? undefined : item.state_code;
          openEditVehicleIUModal();
        }
      });
    };
    (0, _react.useEffect)(function () {
      if (profilePayload != null && profilePayload.vehicleIU && eCouponStateCodeRef.current) {
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.carpass, {
          refreshData: refreshData,
          stateCode: eCouponStateCodeRef.current
        });
        eCouponStateCodeRef.current = "";
      }
    }, [profilePayload == null ? undefined : profilePayload.vehicleIU]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        onPress: onPressEcouponItem,
        style: styles.container,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__Icon`,
          resizeMode: "contain",
          source: {
            uri: item == null ? undefined : item.image
          },
          style: styles.iconStyle,
          testID: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__Icon`
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewContent,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy1`,
            style: styles.titleTextStyle,
            testID: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy1`,
            text: item == null ? undefined : item.highlightCopy1
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__Title`,
            style: styles.highlightCopy1,
            testID: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__Title`,
            text: item == null ? undefined : item.title
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewRow,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__current_price`,
              style: styles.current_price,
              testID: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__current_price`,
              text: item == null ? undefined : item.current_price
            }), (item == null ? undefined : item.actual_price) && (0, _jsxRuntime.jsx)(_text.Text, {
              accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__actual_price`,
              style: styles.actual_price,
              testID: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__actual_price`,
              text: item == null ? undefined : item.actual_price
            })]
          }), (item == null ? undefined : item.highlightCopy2) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [styles.viewRow, {
              marginTop: 4
            }],
            children: [(0, _jsxRuntime.jsx)(_icons.ECouponIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
              accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy2`,
              style: styles.highlightCopy2,
              testID: `${_parkingLanding.COMPONENT_NAME}__ECoupons_Item__${index}__highlightCopy2`,
              text: item == null ? undefined : item.highlightCopy2
            })]
          })]
        })]
      }), (list == null ? undefined : list.length) - 1 !== index && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.viewBottom
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      flexDirection: 'row',
      gap: 16
    },
    iconStyle: {
      width: 80,
      height: 80
    },
    viewContent: {
      flex: 1
    },
    titleTextStyle: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16
    },
    highlightCopy1: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      marginTop: 4,
      marginBottom: 16
    },
    viewRow: {
      flexDirection: 'row',
      alignItems: 'flex-end'
    },
    current_price: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18,
      marginRight: 4
    },
    actual_price: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      textDecorationLine: 'line-through',
      marginBottom: 1
    },
    highlightCopy2: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.lightOrange,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 14,
      marginLeft: 4
    },
    viewBottom: {
      width: '100%',
      height: 1,
      backgroundColor: _theme.color.palette.lighterGrey,
      marginVertical: 16
    }
  });
