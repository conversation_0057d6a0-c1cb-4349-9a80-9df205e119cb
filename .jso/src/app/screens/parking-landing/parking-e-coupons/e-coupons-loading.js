  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[2]);
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var ECouponsLoadingSkeleton = function ECouponsLoadingSkeleton() {
    return new Array(3).fill(null).map(function () {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.imgLoadingContainerStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.rightSideContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.firstLoadingBarStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.secondLoadingBarStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.thirdLoadingBarStyle
          })]
        })]
      });
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var SCREEN_SIZE_RATE = screenWidth / 375;
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      flexDirection: "row",
      gap: 16,
      marginBottom: 28
    },
    imgLoadingContainerStyle: {
      height: 80,
      width: 120
    },
    rightSideContainerStyle: {
      flex: 1,
      gap: 12
    },
    firstLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: "100%"
    },
    secondLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: 148 * SCREEN_SIZE_RATE
    },
    thirdLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: 80 * SCREEN_SIZE_RATE
    }
  });
  var _default = exports.default = ECouponsLoadingSkeleton;
