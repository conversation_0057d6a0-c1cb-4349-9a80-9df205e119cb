  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.PARK_LOCATION_HEIGHT = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[7]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var ParkLocation = function ParkLocation(props) {
    var parkingLandingCardPayload = props.parkingLandingCardPayload;
    var _useState = (0, _react.useState)(MAX_FONT_SIZE),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      fontSize = _useState2[0],
      setFontSize = _useState2[1];
    var textRef = (0, _react.useRef)(null);
    var containerRef = (0, _react.useRef)(null);
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      isMonarch = _useRewardTier.isMonarch;
    var carparkLocationName = parkingLandingCardPayload == null ? undefined : parkingLandingCardPayload.parkingLocation;

    // Dynamically adjust font size based on container width
    (0, _react.useEffect)(function () {
      if (containerRef != null && containerRef.current && textRef != null && textRef.current) {
        var currentSize = fontSize;
        var measureAndAdjust = function measureAndAdjust() {
          if (currentSize <= MIN_FONT_SIZE) return;
          textRef.current.measure(function (_fx, _fy, width, _height, _px, _py) {
            containerRef.current.measure(function (_cfx, _cfy, containerWidth, _ch, _cpx, _cpy) {
              if (width > containerWidth - CONTAINER_PADDING_HORIZONTAL * 2) {
                currentSize = Math.max(currentSize - 1, MIN_FONT_SIZE);
                setFontSize(currentSize);
              } else if (width < (containerWidth - CONTAINER_PADDING_HORIZONTAL * 2) * 0.8) {
                currentSize = Math.min(currentSize + 1, MAX_FONT_SIZE);
                setFontSize(currentSize);
              }
            });
          });
        };

        // Measure after layout
        setTimeout(measureAndAdjust, 0);
      }
    }, [carparkLocationName, fontSize]);
    if (!carparkLocationName) return null;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ParkLocation`,
      ref: containerRef,
      style: [styles.wrapperStyle, isMonarch && styles.monarchWrapperStyle],
      testID: `${_parkingLanding.COMPONENT_NAME}__ParkLocation`,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        ref: textRef,
        style: styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_icons.LocationFill, {
          color: _theme.color.palette.whiteGrey,
          height: 16,
          width: 16
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: [styles.locationPrefixTextStyle, {
            fontSize: fontSize
          }],
          tx: "parkingLanding.parkLocationPrefix"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__ParkLocation__LocationValue`,
          numberOfLines: 1,
          style: [styles.locationValueTextStyle, {
            fontSize: fontSize
          }],
          testID: `${_parkingLanding.COMPONENT_NAME}__ParkLocation__LocationValue`,
          text: carparkLocationName
        })]
      })
    });
  };
  var MAX_FONT_SIZE = 14;
  var MIN_FONT_SIZE = 1;
  var CONTAINER_PADDING_HORIZONTAL = 16;
  var CONTAINER_HEIGHT = 42;
  var CONTAINER_MARGIN_BOTTOM = 12;
  var PARK_LOCATION_HEIGHT = exports.PARK_LOCATION_HEIGHT = 54;
  var styles = _reactNative.StyleSheet.create({
    wrapperStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.shadowColor,
      borderRadius: 8,
      justifyContent: "center",
      height: CONTAINER_HEIGHT,
      marginBottom: CONTAINER_MARGIN_BOTTOM,
      marginHorizontal: 16
    },
    monarchWrapperStyle: {
      backgroundColor: "rgba(18, 18, 18, 0.8)"
    },
    containerStyle: {
      alignItems: "center",
      flex: 0,
      flexDirection: "row",
      justifyContent: "center"
    },
    locationPrefixTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 8,
      marginRight: 4
    }),
    locationValueTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.whiteGrey
    })
  });
  var _default = exports.default = ParkLocation;
