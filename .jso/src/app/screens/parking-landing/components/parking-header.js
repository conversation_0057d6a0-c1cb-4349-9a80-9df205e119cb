  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_14434177196032_init_data = {
    code: "function parkingHeaderTsx1(){const{pageOffsetY,parkingEntitlementOffsetY,animatedOpacity,withTiming,animatedZIndex,styles}=this.__closure;var _pageOffsetY,_parkingEntitlementOf,_pageOffsetY2,_parkingEntitlementOf2;if(((_pageOffsetY=pageOffsetY)===null||_pageOffsetY===void 0?void 0:_pageOffsetY.value)>((_parkingEntitlementOf=parkingEntitlementOffsetY)===null||_parkingEntitlementOf===void 0?void 0:_parkingEntitlementOf.value)&&animatedOpacity.value===0){animatedOpacity.value=withTiming(1,{duration:200});animatedZIndex.value=1;}else if(((_pageOffsetY2=pageOffsetY)===null||_pageOffsetY2===void 0?void 0:_pageOffsetY2.value)<=((_parkingEntitlementOf2=parkingEntitlementOffsetY)===null||_parkingEntitlementOf2===void 0?void 0:_parkingEntitlementOf2.value)&&animatedOpacity.value===1){animatedOpacity.value=withTiming(0,{duration:200},function(isFinished){if(isFinished){animatedZIndex.value=-1;}});}return{...styles.stickyContainerStyle,opacity:animatedOpacity.value,zIndex:animatedZIndex.value};}"
  };
  var _worklet_10518505261107_init_data = {
    code: "function parkingHeaderTsx2(isFinished){const{animatedZIndex}=this.__closure;if(isFinished){animatedZIndex.value=-1;}}"
  };
  var ParkingHeader = function ParkingHeader(props) {
    var isStickyHeader = props.isStickyHeader,
      navigation = props.navigation,
      pageOffsetY = props.pageOffsetY,
      parkingEntitlementOffsetY = props.parkingEntitlementOffsetY;
    var animatedOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var animatedZIndex = (0, _reactNativeReanimated.useSharedValue)(-1);
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var stickyHeaderStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var parkingHeaderTsx1 = function parkingHeaderTsx1() {
        if ((pageOffsetY == null ? undefined : pageOffsetY.value) > (parkingEntitlementOffsetY == null ? undefined : parkingEntitlementOffsetY.value) && animatedOpacity.value === 0) {
          animatedOpacity.value = (0, _reactNativeReanimated.withTiming)(1, {
            duration: 200
          });
          animatedZIndex.value = 1;
        } else if ((pageOffsetY == null ? undefined : pageOffsetY.value) <= (parkingEntitlementOffsetY == null ? undefined : parkingEntitlementOffsetY.value) && animatedOpacity.value === 1) {
          animatedOpacity.value = (0, _reactNativeReanimated.withTiming)(0, {
            duration: 200
          }, function () {
            var parkingHeaderTsx2 = function parkingHeaderTsx2(isFinished) {
              if (isFinished) {
                animatedZIndex.value = -1;
              }
            };
            parkingHeaderTsx2.__closure = {
              animatedZIndex: animatedZIndex
            };
            parkingHeaderTsx2.__workletHash = 10518505261107;
            parkingHeaderTsx2.__initData = _worklet_10518505261107_init_data;
            return parkingHeaderTsx2;
          }());
        }
        return Object.assign({}, styles.stickyContainerStyle, {
          opacity: animatedOpacity.value,
          zIndex: animatedZIndex.value
        });
      };
      parkingHeaderTsx1.__closure = {
        pageOffsetY: pageOffsetY,
        parkingEntitlementOffsetY: parkingEntitlementOffsetY,
        animatedOpacity: animatedOpacity,
        withTiming: _reactNativeReanimated.withTiming,
        animatedZIndex: animatedZIndex,
        styles: styles
      };
      parkingHeaderTsx1.__workletHash = 14434177196032;
      parkingHeaderTsx1.__initData = _worklet_14434177196032_init_data;
      return parkingHeaderTsx1;
    }());
    var handlePressBackBtn = function handlePressBackBtn() {
      navigation.goBack();
    };
    return (0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
      style: [styles.containerStyle, insets.top && {
        paddingTop: insets.top
      }, isStickyHeader && stickyHeaderStyle],
      children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__BackBtn`,
        androidRippleColor: "transparent",
        onPress: handlePressBackBtn,
        testID: `${_parkingLanding.COMPONENT_NAME}__BackBtn`,
        children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
          color: isStickyHeader ? _theme.color.palette.darkestGrey : _theme.color.palette.whiteGrey
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: [styles.titleTextStyle, isStickyHeader && styles.stickyTitleTextStyle],
        text: "Your Parking Benefits"
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: {
          width: 24
        }
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      flexDirection: "row",
      height: _constants.PARKING_HEADER_HEIGHT,
      justifyContent: "space-between",
      marginBottom: 4,
      paddingBottom: 16,
      paddingHorizontal: 16
    },
    stickyContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      left: 0,
      position: "absolute",
      right: 0,
      top: 0
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey
    }),
    stickyTitleTextStyle: {
      color: _theme.color.palette.almostBlackGrey
    }
  });
  var _default = exports.default = ParkingHeader;
