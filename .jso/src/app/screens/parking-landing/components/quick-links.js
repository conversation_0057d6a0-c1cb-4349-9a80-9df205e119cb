  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[2]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _react = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _adobe = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  var QuickLinkItem = function QuickLinkItem(props) {
    var handlePressQuickLinkItem = props.handlePressQuickLinkItem,
      item = props.item;
    if (!item) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.itemContainerStyle
      });
    }
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__${item == null ? undefined : item.title}`,
      onPress: function onPress() {
        return handlePressQuickLinkItem == null ? undefined : handlePressQuickLinkItem(item);
      },
      style: styles.itemContainerStyle,
      testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__${item == null ? undefined : item.title}`,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.itemIconContainerStyle,
        children: [item != null && item.icon ? (0, _jsxRuntime.jsx)(_baseImage.default, {
          accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Icon`,
          source: {
            uri: (0, _utils.mappingUrlAem)(item == null ? undefined : item.icon)
          },
          style: styles.itemIconStyle,
          testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Icon`
        }) : (0, _jsxRuntime.jsx)(_reactNative.View, {
          accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Icon__Default`,
          style: styles.itemDefaultIconStyle,
          testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Icon__Default`
        }), (item == null ? undefined : item.highlightRibbon) && (0, _jsxRuntime.jsx)(_reactNative.View, {
          accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Highlight`,
          style: styles.itemHighlightContainerStyle,
          testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Highlight`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 1,
            style: styles.itemHighlightLabelTextStyle,
            text: item == null ? undefined : item.highlightRibbon
          })
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Title`,
        numberOfLines: 2,
        style: styles.itemTitleTextStyle,
        testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__Item__Title`,
        text: item.title
      })]
    });
  };
  var LoadingSkeleton = function LoadingSkeleton() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.rowContainerStyle,
        children: Array.from({
          length: 4
        }).map(function (_, index) {
          return (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.itemContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: LOADING_COLORS,
              shimmerStyle: styles.loadingIconStyle
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: LOADING_COLORS,
              shimmerStyle: styles.loadingTitleStyle
            })]
          }, `loading_${index}`);
        })
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.rowContainerStyle,
        children: Array.from({
          length: 4
        }).map(function (_, index) {
          return (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.itemContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: LOADING_COLORS,
              shimmerStyle: styles.loadingIconStyle
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: LOADING_COLORS,
              shimmerStyle: styles.loadingTitleStyle
            })]
          }, `loading_${index}`);
        })
      })]
    });
  };
  var ErrorOverlay = function ErrorOverlay(props) {
    var fetchParkingQuickLinks = props.fetchParkingQuickLinks;
    var handlePressRetry = function handlePressRetry() {
      fetchParkingQuickLinks == null || fetchParkingQuickLinks();
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.errorContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__ErrorOverlay__Description`,
        style: styles.errorDescriptionTextStyle,
        testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__ErrorOverlay__Description`,
        tx: "parkingLanding.quickLinks.error.description"
      }), (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__QuickLink__ErrorOverlay__Btn__Retry`,
        testID: `${_parkingLanding.COMPONENT_NAME}__QuickLink__ErrorOverlay__Btn__Retry`,
        onPress: handlePressRetry,
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          end: {
            x: 0,
            y: 1
          },
          start: {
            x: 0,
            y: 0
          },
          style: styles.errorRetryBtnStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.errorRetryBtnLabelStyle,
            tx: "common.retry"
          })
        })
      })]
    });
  };

  // === Main Component ===
  var QuickLinks = function QuickLinks(props) {
    var fetchParkingQuickLinks = props.fetchParkingQuickLinks,
      openEditVehicleIUModal = props.openEditVehicleIUModal,
      parkingECouponSectionOffsetY = props.parkingECouponSectionOffsetY,
      parkingMoreServicesSectionOffsetY = props.parkingMoreServicesSectionOffsetY,
      parkingPromotionsSectionOffsetY = props.parkingPromotionsSectionOffsetY,
      quickLinksError = props.quickLinksError,
      quickLinksFetching = props.quickLinksFetching,
      quickLinksPayload = props.quickLinksPayload,
      scrollRef = props.scrollRef,
      parkingLandingTrackValue = props.parkingLandingTrackValue;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("PARKING_QUICKLINKS"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var upperLinks = (0, _react.useMemo)(function () {
      return Array.from({
        length: 4
      }).map(function (_item, index) {
        return quickLinksPayload == null ? undefined : quickLinksPayload[index];
      });
    }, [JSON.stringify(quickLinksPayload)]);
    var lowerLinks = (0, _react.useMemo)(function () {
      return Array.from({
        length: 4
      }).map(function (_item, index) {
        return quickLinksPayload == null ? undefined : quickLinksPayload[index + 4];
      });
    }, [JSON.stringify(quickLinksPayload)]);
    var handlePressQuickLinkItem = function handlePressQuickLinkItem(item) {
      var _item$cta, _item$cta2;
      var navigationData = item == null || (_item$cta = item.cta) == null ? undefined : _item$cta.navigation;
      var redirectData = item == null || (_item$cta2 = item.cta) == null ? undefined : _item$cta2.redirect;
      if (!(navigationData != null && navigationData.type) || !(navigationData != null && navigationData.value)) {
        return;
      }
      var dataToBeSent = `${parkingLandingTrackValue} | Quicklinks | ${item == null ? undefined : item.title}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppParkingLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppParkingLanding, dataToBeSent));
      handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, redirectData, {
        handleParkingCoupons: function handleParkingCoupons() {
          var _scrollRef$current;
          scrollRef == null || (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollTo({
            y: parkingECouponSectionOffsetY.value,
            animated: true
          });
        },
        handleParkingMyIU: function handleParkingMyIU() {
          openEditVehicleIUModal == null || openEditVehicleIUModal();
        },
        handleParkingPromotions: function handleParkingPromotions() {
          var _scrollRef$current2;
          scrollRef == null || (_scrollRef$current2 = scrollRef.current) == null || _scrollRef$current2.scrollTo({
            y: parkingPromotionsSectionOffsetY.value,
            animated: true
          });
        },
        handleParkingServices: function handleParkingServices() {
          var _scrollRef$current3;
          scrollRef == null || (_scrollRef$current3 = scrollRef.current) == null || _scrollRef$current3.scrollTo({
            y: parkingMoreServicesSectionOffsetY.value,
            animated: true
          });
        }
      });
    };
    if (quickLinksFetching) {
      return (0, _jsxRuntime.jsx)(LoadingSkeleton, {});
    }
    if (quickLinksError) {
      return (0, _jsxRuntime.jsx)(ErrorOverlay, {
        fetchParkingQuickLinks: fetchParkingQuickLinks
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [!!(upperLinks != null && upperLinks.length) && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.rowContainerStyle,
        children: upperLinks == null ? undefined : upperLinks.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(QuickLinkItem, {
            handlePressQuickLinkItem: handlePressQuickLinkItem,
            item: item
          }, `${index}_${item == null ? undefined : item.title}`);
        })
      }), !!(lowerLinks != null && lowerLinks.length) && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.rowContainerStyle,
        children: lowerLinks == null ? undefined : lowerLinks.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(QuickLinkItem, {
            handlePressQuickLinkItem: handlePressQuickLinkItem,
            item: item
          }, `${index}_${item == null ? undefined : item.title}`);
        })
      })]
    });
  };
  // ======================

  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      gap: 24,
      marginTop: 12
    },
    rowContainerStyle: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 16
    },
    lowerRowContainerStyle: {
      marginTop: 24
    },
    itemContainerStyle: {
      alignItems: "center",
      gap: 8,
      width: screenWidth / 375 * 72
    },
    itemIconContainerStyle: {
      width: 40
    },
    itemIconStyle: {
      borderRadius: 16,
      height: 40,
      width: 40
    },
    itemDefaultIconStyle: {
      backgroundColor: "#DEE6F6",
      borderRadius: 16,
      height: 40,
      width: 40
    },
    itemHighlightContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lighterOrange,
      borderColor: _theme.color.palette.orangeWarning300,
      borderRadius: 4,
      borderWidth: 1,
      height: 16,
      left: -3,
      paddingHorizontal: 3,
      position: "absolute",
      top: 30,
      width: 46
    },
    itemHighlightLabelTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: "#F2721B",
      fontSize: 8,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    }),
    itemTitleTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      textTransform: "none"
    }),
    loadingIconStyle: {
      borderRadius: 16,
      height: 40,
      width: 40
    },
    loadingTitleStyle: {
      borderRadius: 4,
      height: 12,
      width: 48
    },
    errorContainerStyle: {
      alignSelf: "center",
      gap: 9,
      marginTop: 24
    },
    errorDescriptionTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    errorRetryBtnStyle: {
      alignItems: "center",
      alignSelf: "center",
      borderRadius: 60,
      height: 28,
      justifyContent: "center",
      paddingHorizontal: 11
    },
    errorRetryBtnLabelStyle: Object.assign({}, _text.newPresets.caption1Bold)
  });
  var _default = exports.default = QuickLinks;
