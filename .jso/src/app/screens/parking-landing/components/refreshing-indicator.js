  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _worklet_1344500691771_init_data = {
    code: "function refreshingIndicatorTsx1(){const{indicatorHeight}=this.__closure;return{height:indicatorHeight.value};}"
  };
  var _worklet_9060847914087_init_data = {
    code: "function refreshingIndicatorTsx2(){const{indicatorHeight,REFRESHING_DIMENSIONS}=this.__closure;const scale=indicatorHeight.value>=REFRESHING_DIMENSIONS.TRIGGER_POINT?indicatorHeight.value/REFRESHING_DIMENSIONS.MAX_INDICATOR_HEIGHT:0;return{top:-3,transform:[{scale:scale}]};}"
  };
  var RefreshingIndicator = function RefreshingIndicator(_ref) {
    var indicatorHeight = _ref.indicatorHeight;
    var containerAnimationStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var refreshingIndicatorTsx1 = function refreshingIndicatorTsx1() {
        return {
          height: indicatorHeight.value
        };
      };
      refreshingIndicatorTsx1.__closure = {
        indicatorHeight: indicatorHeight
      };
      refreshingIndicatorTsx1.__workletHash = 1344500691771;
      refreshingIndicatorTsx1.__initData = _worklet_1344500691771_init_data;
      return refreshingIndicatorTsx1;
    }());
    var indicatorAnimationStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var refreshingIndicatorTsx2 = function refreshingIndicatorTsx2() {
        var scale = indicatorHeight.value >= _constants.REFRESHING_DIMENSIONS.TRIGGER_POINT ? indicatorHeight.value / _constants.REFRESHING_DIMENSIONS.MAX_INDICATOR_HEIGHT : 0;
        return {
          top: -3,
          transform: [{
            scale: scale
          }]
        };
      };
      refreshingIndicatorTsx2.__closure = {
        indicatorHeight: indicatorHeight,
        REFRESHING_DIMENSIONS: _constants.REFRESHING_DIMENSIONS
      };
      refreshingIndicatorTsx2.__workletHash = 9060847914087;
      refreshingIndicatorTsx2.__initData = _worklet_9060847914087_init_data;
      return refreshingIndicatorTsx2;
    }());
    return (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
      style: [styles.container, containerAnimationStyle],
      children: (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: indicatorAnimationStyle,
        children: (0, _jsxRuntime.jsx)(_reactNative.ActivityIndicator, {
          size: "small",
          color: _theme.color.palette.whiteGrey
        })
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center"
    }
  });
  var _default = exports.default = RefreshingIndicator;
