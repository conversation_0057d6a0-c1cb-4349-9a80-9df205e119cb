  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TitleCopy = undefined;
  var _aemRedux = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[7]);
  var _translate = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var TitleCopy = exports.TitleCopy = _react.default.memo(function (props) {
    var _aemCommonData$data;
    var isError = props.isError;
    var aemCommonData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var inf30 = aemCommonData == null || (_aemCommonData$data = aemCommonData.data) == null || (_aemCommonData$data = _aemCommonData$data.informatives) == null ? undefined : _aemCommonData$data.find(function (e) {
      return (e == null ? undefined : e.code) === "INF30";
    });
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.txtTitle,
        testID: `${_parkingLanding.COMPONENT_NAME}__moreServices_Title`,
        children: isError ? (0, _translate.translate)("parkingLanding.moreServices.titleError") : (inf30 == null ? undefined : inf30.title) || (0, _translate.translate)("parkingLanding.moreServices.titleAEM")
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.txtContent,
        testID: `${_parkingLanding.COMPONENT_NAME}__eCoupons_Content`,
        children: isError ? (0, _translate.translate)("parkingLanding.moreServices.contentError") : (inf30 == null ? undefined : inf30.informativeText) || (0, _translate.translate)("parkingLanding.moreServices.contentAEM")
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      paddingHorizontal: 16,
      marginBottom: 4,
      marginHorizontal: 4
    },
    txtTitle: {
      marginBottom: 4,
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20
    },
    txtContent: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18
    }
  });
