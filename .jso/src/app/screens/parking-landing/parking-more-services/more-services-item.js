  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MoreServicesItem = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _parkingLanding = _$$_REQUIRE(_dependencyMap[7]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _native = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var MoreServicesItem = exports.MoreServicesItem = _react.default.memo(function (props) {
    var _item$locationDisplay, _item$area, _item$openingStatus;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var item = props.item,
      index = props.index,
      parkingLandingTrackValue = props.parkingLandingTrackValue;
    var handleOnclick = function handleOnclick() {
      var _item$navigation, _item$navigation2;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppParkingLanding, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppParkingLanding, `${parkingLandingTrackValue} | More Services | ${item == null ? undefined : item.title}`));
      switch (item == null ? undefined : item.type) {
        case _parkingLanding.MoreServicesType.AEM:
          handleNavigation(item == null || (_item$navigation = item.navigation) == null ? undefined : _item$navigation.type, item == null || (_item$navigation2 = item.navigation) == null ? undefined : _item$navigation2.value);
          break;
        case _parkingLanding.MoreServicesType.SHOP:
          navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
            tenantId: item == null ? undefined : item.id,
            name: ""
          });
          break;
        case _parkingLanding.MoreServicesType.DINE:
          navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
            tenantId: item == null ? undefined : item.id,
            name: ""
          });
          break;
      }
    };
    var renderStatus = function renderStatus() {
      if ((item == null ? undefined : item.openingStatus) === _parkingLanding.MoreServicesStatus.Open) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.txtStatusGreen,
          testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Status_Open`,
          children: item == null ? undefined : item.openingStatus
        });
      } else if ((item == null ? undefined : item.openingStatus) === _parkingLanding.MoreServicesStatus.OpenSomeOutlets) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewSomeOutLet,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtStatusGreen,
            children: "Open"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: [styles.txtStatusGray, {
              color: _theme.color.palette.darkGrey999
            }],
            children: "Some Outlets"
          })]
        });
      } else {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.txtStatusGray,
          testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Status_Closed`,
          children: item == null ? undefined : item.openingStatus
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.container,
      onPress: handleOnclick,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        accessibilityLabel: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Icon`,
        source: {
          uri: item == null ? undefined : item.image
        },
        style: styles.image,
        testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Icon`
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContent,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewText,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtTitle,
            testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Title`,
            children: item == null ? undefined : item.title
          }), (item == null || (_item$locationDisplay = item.locationDisplayText) == null ? undefined : _item$locationDisplay.length) > 0 && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewRow,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.txtGate,
              testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Gate`,
              children: item == null ? undefined : item.locationDisplayText
            }), (item == null || (_item$area = item.area) == null ? undefined : _item$area.length) > 0 && (0, _jsxRuntime.jsxs)(_text.Text, {
              style: styles.txtStatusGate,
              testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__GateStatus`,
              children: [" \xB7 ", item == null ? undefined : item.area]
            })]
          }), (item == null ? undefined : item.category) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.txtService,
            testID: `${_parkingLanding.COMPONENT_NAME}__MoreServices_Item__${index}__Service`,
            children: item == null ? undefined : item.category
          })]
        }), (item == null || (_item$openingStatus = item.openingStatus) == null ? undefined : _item$openingStatus.length) > 0 && renderStatus()]
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      flexDirection: 'row',
      marginTop: 12
    },
    image: {
      width: 48,
      height: 48,
      borderRadius: 8,
      marginRight: 16
    },
    viewContent: {
      flex: 1,
      flexDirection: 'row',
      paddingBottom: 24,
      borderColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1
    },
    viewText: {
      flex: 1
    },
    txtStatusGreen: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.basegreen,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 15,
      marginLeft: 16
    },
    txtStatusGray: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.darkestGrey,
      fontSize: 11,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 15,
      marginLeft: 16
    },
    txtTitle: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20
    },
    viewRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 4
    },
    txtGate: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtStatusGate: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtService: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16,
      marginTop: 12
    },
    viewSomeOutLet: {
      alignItems: 'flex-end',
      gap: 2
    }
  });
