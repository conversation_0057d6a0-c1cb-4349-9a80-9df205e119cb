  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MoreServicesError = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var MoreServicesError = exports.MoreServicesError = _react.default.memo(function (props) {
    var reTryCallApi = props.reTryCallApi;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.txtTitle,
        tx: "parkingLanding.moreServices.pleaseTryAgain"
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: reTryCallApi,
        children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          end: {
            x: 0,
            y: 1
          },
          start: {
            x: 0,
            y: 0
          },
          style: styles.retryBtnContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.retryBtnLabelTextStyle,
            tx: "common.retry"
          })
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginHorizontal: 4
    },
    txtTitle: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18,
      marginBottom: 16,
      marginTop: -2
    },
    retryBtnLabelTextStyle: Object.assign({}, _text.newPresets.caption1Bold),
    retryBtnContainerStyle: {
      alignItems: "center",
      borderRadius: 99,
      justifyContent: "center",
      height: 28,
      paddingHorizontal: 10,
      paddingVertical: 5,
      width: 60
    }
  });
