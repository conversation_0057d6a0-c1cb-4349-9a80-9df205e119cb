  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ParkingMoreServices = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _titleCopy = _$$_REQUIRE(_dependencyMap[3]);
  var _moreServicesError = _$$_REQUIRE(_dependencyMap[4]);
  var _moreServicesItem = _$$_REQUIRE(_dependencyMap[5]);
  var _moreServicesLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _constants = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var ParkingMoreServices = exports.ParkingMoreServices = _react.default.memo(function (props) {
    var _moreServicesPayload$, _moreServicesPayload$2;
    var moreServicesFetching = props.moreServicesFetching,
      moreServicesPayload = props.moreServicesPayload,
      moreServicesError = props.moreServicesError,
      fetchMoreServices = props.fetchMoreServices,
      parkingMoreServicesSectionOffsetY = props.parkingMoreServicesSectionOffsetY,
      parkingLandingTrackValue = props.parkingLandingTrackValue;
    var reTryCallApi = function reTryCallApi() {
      _globalLoadingController.default.showLoading(true);
      fetchMoreServices(_globalLoadingController.default.hideLoading, true);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      onLayout: function onLayout(evt) {
        parkingMoreServicesSectionOffsetY.value = evt.nativeEvent.layout.y - _constants.PARKING_HEADER_HEIGHT;
      },
      style: styles.container,
      children: [((moreServicesPayload == null || (_moreServicesPayload$ = moreServicesPayload.data) == null ? undefined : _moreServicesPayload$.length) > 0 || moreServicesFetching || moreServicesError) && (0, _jsxRuntime.jsx)(_titleCopy.TitleCopy, {
        isError: moreServicesError
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: moreServicesError ? styles.viewContent : styles.viewContentMargin,
        children: [moreServicesFetching && (0, _jsxRuntime.jsx)(_moreServicesLoading.default, {}), !moreServicesFetching && (moreServicesPayload == null || (_moreServicesPayload$2 = moreServicesPayload.data) == null ? undefined : _moreServicesPayload$2.map(function (item, index) {
          return (0, _jsxRuntime.jsx)(_moreServicesItem.MoreServicesItem, {
            item: item,
            index: index,
            parkingLandingTrackValue: parkingLandingTrackValue
          }, `moreServicesPayload_${index}`);
        })), moreServicesError && !moreServicesFetching && (0, _jsxRuntime.jsx)(_moreServicesError.MoreServicesError, {
          reTryCallApi: reTryCallApi
        })]
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 50
    },
    viewContent: {
      paddingHorizontal: 16
    },
    viewContentMargin: {
      paddingHorizontal: 16,
      marginTop: 16
    }
  });
