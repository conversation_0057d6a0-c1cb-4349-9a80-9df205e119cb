  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var MoreServicesSkeleton = function MoreServicesSkeleton() {
    return [1, 2, 3].map(function (index) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
          shimmerStyle: styles.imgLoadingContainerStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: [styles.rightSideContainerStyle, {
            borderBottomWidth: index === 3 ? 0 : 1
          }],
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.firstLoadingBarStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.secondLoadingBarStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
            shimmerStyle: styles.thirdLoadingBarStyle
          })]
        })]
      }, `moreServicesLoading_${index}`);
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var SCREEN_SIZE_RATE = screenWidth / 375;
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      flexDirection: "row",
      gap: 16,
      marginTop: 12
    },
    imgLoadingContainerStyle: {
      height: 48,
      width: 48,
      borderRadius: 8
    },
    rightSideContainerStyle: {
      flex: 1,
      gap: 12,
      paddingBottom: 24,
      borderColor: _theme.color.palette.lighterGrey
    },
    firstLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: "100%"
    },
    secondLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: 148 * SCREEN_SIZE_RATE
    },
    thirdLoadingBarStyle: {
      borderRadius: 4,
      height: 12,
      width: 80 * SCREEN_SIZE_RATE
    }
  });
  var _default = exports.default = MoreServicesSkeleton;
