  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeVersionInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[11]);
  var _drive = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SECTION_NAME = "CSATSurvey";
  var CSATSurvey = function CSATSurvey(_ref) {
    var navigation = _ref.navigation;
    var appVersion = _reactNativeVersionInfo.default.appVersion;
    var _useContext = (0, _react.useContext)(_drive.DriveContext),
      driveCSAT = _useContext.driveCSAT;
    var isDriveCSATOn = !!(0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.DRIVE_CSAT, driveCSAT);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var vehicleIU = profilePayload != null && profilePayload.vehicleIU ? profilePayload == null ? undefined : profilePayload.vehicleIU : "null";
    var userId = (profilePayload == null ? undefined : profilePayload.id) || "";
    var onNavigateToCSAT = (0, _react.useCallback)(function () {
      navigation == null || navigation.navigate == null || navigation.navigate(_constants.NavigationConstants.webview, {
        uri: `https://survey.changiairport.com/jfe/form/SV_3ymMZsfolli0nmm?uid=${userId}&appVersion=${appVersion}&vehicle=${vehicleIU}`
      });
    }, [navigation, userId, appVersion, vehicleIU]);
    if (!isDriveCSATOn) {
      return null;
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
      style: styles.container,
      onPress: onNavigateToCSAT,
      testID: `${SECTION_NAME}__ButtonCSATSurvey`,
      accessibilityLabel: `${SECTION_NAME}__ButtonCSATSurvey`,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.text,
        preset: "caption2Bold",
        tx: "parkingLanding.csatSurvey",
        testID: `${SECTION_NAME}__ButtonCSATSurveyText`,
        accessibilityLabel: `${SECTION_NAME}__ButtonCSATSurveyText`
      }), (0, _jsxRuntime.jsx)(_icons.FeedbackChat, {
        color: _theme.color.palette.lightPurple
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      marginTop: -4,
      marginBottom: 20,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center"
    },
    text: {
      marginRight: 3,
      color: _theme.color.palette.lightPurple
    }
  });
  var _default = exports.default = CSATSurvey;
