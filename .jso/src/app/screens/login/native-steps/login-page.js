  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _modalTopBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _nativeAuthRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _button = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[15]);
  var _emailInput = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _passwordInput = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _resetPassword = _$$_REQUIRE(_dependencyMap[19]);
  var _nativeAuthSaga = _$$_REQUIRE(_dependencyMap[20]);
  var _icons = _$$_REQUIRE(_dependencyMap[21]);
  var _bottomErrorCommon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _socialProvider = _$$_REQUIRE(_dependencyMap[24]);
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[25]);
  var _envParams = _$$_REQUIRE(_dependencyMap[26]);
  var _confirmCaptcha = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _adobe = _$$_REQUIRE(_dependencyMap[28]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[29]);
  var _i18n = _$$_REQUIRE(_dependencyMap[30]);
  var _storage = _$$_REQUIRE(_dependencyMap[31]);
  var _types = _$$_REQUIRE(_dependencyMap[32]);
  var _errorConnection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[34]);
  var _emailLogin = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _loginHelper = _$$_REQUIRE(_dependencyMap[36]);
  var _reactNativeBiometrics = _$$_REQUIRE(_dependencyMap[37]);
  var _pageConfigRedux = _$$_REQUIRE(_dependencyMap[38]);
  var _account = _$$_REQUIRE(_dependencyMap[39]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[40]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[41]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var AA_TRACKING_LOGIN_FAILED_VALUE = `${_adobe.AdobeValueByTagName.CAppLoginPage}Login | Fail`;
  var BiometricsIcon = function BiometricsIcon() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: _nativeLoginStyle.default.biometricsIcon,
      children: _reactNative.Platform.OS === "android" ? (0, _jsxRuntime.jsx)(_icons.FingerprintPurple, {
        width: 20,
        height: 20
      }) : (0, _jsxRuntime.jsx)(_icons.FaceIDPurple, {
        width: 20,
        height: 20
      })
    });
  };
  /**
   * @description Component for the Login screen, handling email/password login, social login,
   * biometric authentication, and password reset.
   *
   * @param {object} props - The component's props.
   * @param {string} props.emailProfile - The user's email profile (if available).
   * @param {string} props.socialError - Error message from social login (if any).
   * @param {boolean} props.justEntered - Flag indicating if the user just entered the screen.
   * @param {boolean} props.savedEmail - Flag indicating if a saved email is being used.
   * @param {function} props.setJustEntered - Setter function for the `justEntered` flag.
   * @param {function} props.setSavedEmail - Setter function for the `savedEmail` flag.
   * @returns {JSX.Element} The LoginPage component.
   */
  var LoginPage = function LoginPage(_ref) {
    var _env, _env2;
    var emailProfile = _ref.emailProfile,
      socialError = _ref.socialError,
      justEntered = _ref.justEntered,
      setJustEntered = _ref.setJustEntered,
      savedEmail = _ref.savedEmail,
      setSavedEmail = _ref.setSavedEmail;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      setLoading = _useContext.setLoading,
      sourceSystem = _useContext.sourceSystem,
      setCurrentStep = _useContext.setCurrentStep,
      setSocialError = _useContext.setSocialError,
      setCurrentFlow = _useContext.setCurrentFlow,
      networkConnected = _useContext.networkConnected,
      checkNetwork = _useContext.checkNetwork,
      setNetworkConnected = _useContext.setNetworkConnected,
      currentFlow = _useContext.currentFlow,
      socialProviderValue = _useContext.socialProviderValue,
      password = _useContext.password,
      setPassword = _useContext.setPassword,
      showBiometrics = _useContext.showBiometrics,
      setShowBiometrics = _useContext.setShowBiometrics;
    // Remote Config - Flag for CIAM Biometrics
    var _useContext2 = (0, _react.useContext)(_account.AccountContext),
      ciamBiometrics = _useContext2.ciamBiometrics;
    var isCiamBiometricsFlagOn = (0, _remoteConfig.isFlagOnCondition)(ciamBiometrics);
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      inputEmail = _useState2[0],
      setInputEmail = _useState2[1];
    var loadingLogin = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.loadingLogin);
    var loginStatus = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.loginStatus);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var isMissingField = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isMissingField);
    var successSendEmail = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.successSendEmail);
    var sendEmailResponseStatus = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.sendEmailResponseStatus);
    var isBiometricsOn = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.isBiometricsOn);
    var _useState3 = (0, _react.useState)(""),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      errorLogin = _useState4[0],
      setErrorLogin = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isVisible = _useState6[0],
      setIsVisible = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      justPressForgetPass = _useState8[0],
      setJustPressForgetPass = _useState8[1];
    var emailInputRef = (0, _react.useRef)(null);
    var passwordInputRef = (0, _react.useRef)(null);
    var socialProviderRef = (0, _react.useRef)(null);
    var captchaRef = (0, _react.useRef)(null);
    var timeoutRef = (0, _react.useRef)(null);
    var emailRegister = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.emailRegister);
    var disabledLogin = savedEmail ? password.length < 8 : !(0, _emailInput.isValidEmail)(inputEmail) || password.length < 8 || !inputEmail;
    var textContent = `nativeLoginScreen.loginPage.${savedEmail ? "titleSavedEmail" : "title"}`;
    var textDescription = `nativeLoginScreen.loginPage.${savedEmail ? "txDescriptionSavedEmail" : "txDescription"}`;

    /**
     * @description Handles the "Not You?" button press. Clears saved email and password,
     * resets error messages, and focuses the email input.
     */
    var pressNotYou = function pressNotYou() {
      var _passwordInputRef$cur;
      setSavedEmail(false);
      setShowBiometrics(false);
      setPassword("");
      clearError();
      setInputEmail("");
      (_passwordInputRef$cur = passwordInputRef.current) == null || _passwordInputRef$cur.blur();
    };
    var maskedMail = (0, _loginHelper.maskEmail)(emailProfile);

    /**
     * @description Handles the login button press. Validates network connectivity,
     * clears errors, and dispatches the login request. Shows Captcha if needed.
     *
     * @async
     */
    var handleLogin = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _emailInputRef$curren, _passwordInputRef$cur2;
        var connected = yield checkNetwork();
        if (!connected) return;
        clearError();
        (_emailInputRef$curren = emailInputRef.current) == null || _emailInputRef$curren.blur();
        (_passwordInputRef$cur2 = passwordInputRef.current) == null || _passwordInputRef$cur2.blur();
        var email = savedEmail ? emailProfile : inputEmail;
        var lockedEmails = yield (0, _storage.load)(_storage.StorageKey.lockedEmails);
        if (lockedEmails != null && lockedEmails.includes(email)) {
          showCaptchaView();
        } else {
          setLoading(true);
          dispatch(_nativeAuthRedux.default.sendLoginRequest(email, sourceSystem, password));
        }
      });
      return function handleLogin() {
        return _ref2.apply(this, arguments);
      };
    }();

    /**
     * @description Handles the "Forgot Password?" button press. Navigates to the reset password
     * screen or initiates the password reset request.
     *
     * @async
     */
    var pressForgetPass = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        _reactNative.Keyboard.dismiss();
        if (savedEmail) {
          setJustPressForgetPass(true);
          var connected = yield checkNetwork();
          if (!connected) return;
          dispatch(_nativeAuthRedux.default.sendEmailResetRequest(emailProfile, sourceSystem));
          setLoading(true);
        } else {
          setCurrentStep(_types.STEP.RESET_PASSWORD);
          setSocialError("");
        }
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, `${_adobe.AdobeValueByTagName.CAppLoginPage}${(0, _i18n.translate)("nativeLoginScreen.loginPage.forgotPassword")}`));
      });
      return function pressForgetPass() {
        return _ref3.apply(this, arguments);
      };
    }();
    var clearError = function clearError() {
      setErrorLogin("");
      setSocialError("");
    };

    /**
     * @description Handles biometric authentication. Prompts the user for biometric input
     * and dispatches the login request if successful.
     * if failed, it checks if biometric login is available and handles showing the biometric button.
     *
     * @async
     */
    var handleBiometrics = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        if (showBiometrics) {
          var _yield$rnBiometrics$i = yield _loginHelper.rnBiometrics.isSensorAvailable(),
            available = _yield$rnBiometrics$i.available,
            biometryType = _yield$rnBiometrics$i.biometryType;
          if (available && biometryType !== _reactNativeBiometrics.BiometryTypes.TouchID) {
            (0, _loginHelper.showAndroidBiometricsPrompt)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
              var _yield$rnBiometrics$s = yield _loginHelper.rnBiometrics.simplePrompt(_loginHelper.PROMPT_OPTIONS),
                success = _yield$rnBiometrics$s.success;
              if (success) {
                setLoading(true);
                var userPassword = (0, _mmkvStorage.getUserPassword)();
                dispatch(_nativeAuthRedux.default.sendLoginRequest(emailProfile, sourceSystem, userPassword));
              } else {
                _reactNative.InteractionManager.runAfterInteractions(function () {
                  setTimeout(function () {
                    var _passwordInputRef$cur3;
                    return passwordInputRef == null || (_passwordInputRef$cur3 = passwordInputRef.current) == null ? undefined : _passwordInputRef$cur3.focus();
                  }, 200);
                });
                var _yield$rnBiometrics$i2 = yield _loginHelper.rnBiometrics.isSensorAvailable(),
                  _available = _yield$rnBiometrics$i2.available;
                if (!_available) {
                  setShowBiometrics(false);
                }
              }
            }), /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
              var _yield$rnBiometrics$i3 = yield _loginHelper.rnBiometrics.isSensorAvailable(),
                available = _yield$rnBiometrics$i3.available;
              if (!available) {
                setShowBiometrics(false);
              }
            }));
          } else {
            _reactNative.InteractionManager.runAfterInteractions(function () {
              setTimeout(function () {
                var _passwordInputRef$cur4;
                return passwordInputRef == null || (_passwordInputRef$cur4 = passwordInputRef.current) == null ? undefined : _passwordInputRef$cur4.focus();
              }, 200);
            });
            setShowBiometrics(false);
          }
        }
        setJustEntered(false);
      });
      return function handleBiometrics() {
        return _ref4.apply(this, arguments);
      };
    }();

    /**
     * @description useEffect hook to handle the send email response status.
     */
    (0, _react.useEffect)(function () {
      if (sendEmailResponseStatus) {
        if (successSendEmail || sendEmailResponseStatus === _nativeAuthSaga.RES_CODE.NO_USER_ERROR) {
          setCurrentStep(_types.STEP.SENT_EMAIL);
          setSocialError("");
        } else {
          setIsVisible(true);
        }
        setLoading(false);
        dispatch(_nativeAuthRedux.default.clearEmailStatusCode());
      }
    }, [sendEmailResponseStatus]);

    /**
     * @description useEffect hook to focus the appropriate input field when the component mounts
     * or when the `savedEmail` state changes.
     */
    (0, _react.useEffect)(function () {
      if (!socialError && !loginStatus && (!showBiometrics || !justEntered)) {
        if (savedEmail) {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            setTimeout(function () {
              var _passwordInputRef$cur5;
              return passwordInputRef == null || (_passwordInputRef$cur5 = passwordInputRef.current) == null ? undefined : _passwordInputRef$cur5.focus();
            }, 100);
          });
        } else {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            setTimeout(function () {
              var _emailInputRef$curren2;
              return emailInputRef == null || (_emailInputRef$curren2 = emailInputRef.current) == null ? undefined : _emailInputRef$curren2.focus();
            }, 100);
          });
        }
      }
    }, [savedEmail]);

    /**
     * @description useEffect hook to set the current flow, clear the password, and handle
     * biometric login if the user just entered the screen.
     */
    (0, _react.useEffect)(function () {
      setCurrentFlow(_types.USER_FLOW.SIGNIN_NATIVE);
      password && setPassword("");
      justEntered && handleBiometrics();
    }, []);

    /**
     * @description useEffect hook to update the login error message if there's a social error.
     */
    (0, _react.useEffect)(function () {
      if (socialError) {
        setErrorLogin(socialError);
      }
    }, [socialError]);
    var trackingForLoginSuccess = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* () {
        var viewerUID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, `${_adobe.AdobeValueByTagName.CAppLoginPage}Login | Success | ${viewerUID}`));
        (0, _adobe.adobeSyncIdentifier)({
          identifierValue: `${viewerUID}`,
          identifierType: _adobe.AdobeIdentifierType.uid,
          authenticationState: _adobe.AdobeAuthenticationState.authenticated
        });
      });
      return function trackingForLoginSuccess() {
        return _ref7.apply(this, arguments);
      };
    }();
    var removeLockedEmail = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* () {
        var email = savedEmail ? emailProfile : inputEmail;
        var lockedEmails = yield (0, _storage.load)(_storage.StorageKey.lockedEmails);
        if (lockedEmails != null && lockedEmails.includes(email)) {
          var updatedLockedEmails = lockedEmails.filter(function (lockedEmail) {
            return lockedEmail !== email;
          });
          yield (0, _storage.save)(_storage.StorageKey.lockedEmails, updatedLockedEmails);
        }
      });
      return function removeLockedEmail() {
        return _ref8.apply(this, arguments);
      };
    }();
    var addLockedEmail = /*#__PURE__*/function () {
      var _ref9 = (0, _asyncToGenerator2.default)(function* () {
        var email = savedEmail ? emailProfile : inputEmail;
        var lockedEmails = (yield (0, _storage.load)(_storage.StorageKey.lockedEmails)) || [];
        if (!(lockedEmails != null && lockedEmails.includes(email))) {
          yield (0, _storage.save)(_storage.StorageKey.lockedEmails, [].concat((0, _toConsumableArray2.default)(lockedEmails), [email]));
        }
      });
      return function addLockedEmail() {
        return _ref9.apply(this, arguments);
      };
    }();
    /**
     * @description useEffect hook to handle the login status and recaptcha.
     * we currently saved locked emails in local and remove it when login success
     * since we have to handle captcha in FE side
     */
    (0, _react.useEffect)(function () {
      if (loginStatus) {
        var loginStatusTemp = loginStatus;
        dispatch(_nativeAuthRedux.default.clearLoginStatus());
        setLoading(false);
        if (loginStatusTemp === _nativeAuthSaga.RES_CODE.COMMON_SUCCESS) {
          trackingForLoginSuccess();
          removeLockedEmail();
          if (isMissingField) {
            setCurrentStep(_types.STEP.SUPPLEMENT_DATA);
          }
        } else if (loginStatusTemp === _nativeAuthSaga.RES_CODE.INVALID_EMAIL_PASSWORD) {
          setErrorLogin("nativeLoginScreen.invalidEmailPassword");
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, AA_TRACKING_LOGIN_FAILED_VALUE));
        } else if (loginStatusTemp === _nativeAuthSaga.RES_CODE.DISABLED_ACCOUNT) {
          setErrorLogin("nativeLoginScreen.disabledAccount");
        } else if (loginStatusTemp === _nativeAuthSaga.RES_CODE.ACCOUNT_LOCKED_OUT || loginStatusTemp === String(_nativeAuthSaga.RES_CODE.ACCOUNT_LOCKED_OUT)) {
          setErrorLogin("nativeLoginScreen.invalidEmailPassword");
          addLockedEmail();
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, AA_TRACKING_LOGIN_FAILED_VALUE));
        } else if (loginStatusTemp === _nativeAuthSaga.RES_CODE.PENDING_VERIFY) {
          dispatch(_nativeAuthRedux.default.clearSendOtpCodeStatus());
          dispatch(_nativeAuthRedux.default.sendOtpCode(inputEmail || emailRegister, sourceSystem));
          setCurrentStep(_types.STEP.VERIFY_EMAIL);
        } else {
          setIsVisible(true);
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, AA_TRACKING_LOGIN_FAILED_VALUE));
        }
        if (loginStatusTemp !== _nativeAuthSaga.RES_CODE.COMMON_SUCCESS) {
          (0, _adobe.adobeSyncIdentifier)({
            authenticationState: _adobe.AdobeAuthenticationState.unknown
          });
        }
      }
    }, [loginStatus, isMissingField]);
    (0, _react.useEffect)(function () {
      if (loadingLogin || profileFetching) {
        setLoading(true);
      } else {
        setLoading(false);
      }
    }, [loadingLogin, profileFetching]);
    (0, _react.useEffect)(function () {
      return clearTiming;
    }, []);

    /**
     * @description Handles the back button press.
     * Resets the saved email state or navigates to the get started screen if we are in no-saved email state.
     *
     * @async
     */
    var onPressBack = /*#__PURE__*/function () {
      var _ref0 = (0, _asyncToGenerator2.default)(function* () {
        clearError();
        if (!!emailProfile && !savedEmail) {
          setSavedEmail(true);
          setPassword("");
          var _yield$rnBiometrics$i4 = yield _loginHelper.rnBiometrics.isSensorAvailable(),
            available = _yield$rnBiometrics$i4.available,
            biometryType = _yield$rnBiometrics$i4.biometryType;
          if (isCiamBiometricsFlagOn && isBiometricsOn && available && biometryType !== _reactNativeBiometrics.BiometryTypes.TouchID) {
            setShowBiometrics(true);
          }
        } else setCurrentStep(_types.STEP.GET_STARTED);
      });
      return function onPressBack() {
        return _ref0.apply(this, arguments);
      };
    }();
    var submitEditing = function submitEditing() {
      var _passwordInputRef$cur6;
      (_passwordInputRef$cur6 = passwordInputRef.current) == null || _passwordInputRef$cur6.focus();
    };
    var clearTiming = function clearTiming() {
      clearTimeout(timeoutRef.current);
    };

    /**
     * @description Handles the back button press.
     * Resets the saved email state or navigates to the get started screen if we are in no-saved email state.
     * startTiming is called to start the countdown timer for the Captcha.
     */
    var startTiming = function startTiming() {
      clearTiming();
      timeoutRef.current = setTimeout(function () {
        var data = {
          nativeEvent: {
            data: "expired"
          }
        };
        onMessage(data);
      }, 120000);
    };
    var hideCaptcha = function hideCaptcha() {
      var _captchaRef$current;
      return (_captchaRef$current = captchaRef.current) == null ? undefined : _captchaRef$current.hide();
    };

    /**
     * @description Shows the Captcha view and starts the countdown timer.
     */
    var showCaptchaView = function showCaptchaView() {
      captchaRef == null || captchaRef.current.show();
      startTiming();
    };

    /**
     * @description This handles the message callback from the Captcha view.
     * if we have data and it's not cancel, error, or expired, we send the login request.
     */
    var onMessage = function onMessage(event) {
      var _ref1 = (event == null ? undefined : event.nativeEvent) || {},
        data = _ref1.data;
      if (!data) {
        hideCaptcha();
        return;
      }
      switch (data) {
        case "cancel":
        case "error":
        case "expired":
          {
            setErrorLogin("nativeLoginScreen.captchaFailed");
            hideCaptcha();
            break;
          }
        default:
          {
            var email = savedEmail ? emailProfile : inputEmail;
            dispatch(_nativeAuthRedux.default.sendLoginRequest(email, sourceSystem, password, data));
            hideCaptcha();
            break;
          }
      }
    };

    /**
     * @description This handles retrying the network request.
     * If justPressForgetPass is true, we send the password reset request
     * since the user just pressed the "Forgot Password?" button.
     * If not, we handle the login request or social login request.
     */
    var retryNetwork = /*#__PURE__*/function () {
      var _ref10 = (0, _asyncToGenerator2.default)(function* () {
        var status = yield checkNetwork();
        if (status) {
          if (justPressForgetPass) {
            setJustPressForgetPass(false);
            dispatch(_nativeAuthRedux.default.sendEmailResetRequest(emailProfile, sourceSystem));
            setLoading(true);
          } else {
            if (currentFlow === _types.USER_FLOW.SIGNIN_NATIVE) handleLogin();else if (currentFlow === _types.USER_FLOW.SIGNIN_FROM_SOCIAL) {
              var _socialProviderRef$cu;
              var provider = (0, _socialProvider.getProvider)(socialProviderValue);
              socialProviderRef == null || (_socialProviderRef$cu = socialProviderRef.current) == null || _socialProviderRef$cu.handleSocialLogin(provider);
            }
          }
        }
      });
      return function retryNetwork() {
        return _ref10.apply(this, arguments);
      };
    }();

    /**
     * @description This handles closing the network error message.
     * justPressForgetPass is used to check if the user just pressed the "Forgot Password?" button.
     */
    var onClose = function onClose() {
      setNetworkConnected(true);
      if (justPressForgetPass) setJustPressForgetPass(false);
      setCurrentFlow(_types.USER_FLOW.SIGNIN_NATIVE);
      if (!socialError) {
        if (savedEmail) {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            setTimeout(function () {
              var _passwordInputRef$cur7;
              return passwordInputRef == null || (_passwordInputRef$cur7 = passwordInputRef.current) == null ? undefined : _passwordInputRef$cur7.focus();
            }, 100);
          });
        } else {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            setTimeout(function () {
              var _emailInputRef$curren3;
              return emailInputRef == null || (_emailInputRef$curren3 = emailInputRef.current) == null ? undefined : _emailInputRef$curren3.focus();
            }, 100);
          });
        }
      }
    };

    /**
     * @description Return ErrorConnection component when no network connection
     */
    if (!networkConnected) {
      return (0, _jsxRuntime.jsx)(_errorConnection.default, {
        retryNetwork: retryNetwork,
        onClose: onClose
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
      style: innerStyles.viewContainer,
      extraScrollHeight: 30,
      showsVerticalScrollIndicator: false,
      scrollEnabled: !savedEmail && !!errorLogin,
      keyboardDismissMode: "on-drag",
      keyboardShouldPersistTaps: "handled",
      scrollEventThrottle: 16,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: innerStyles.scrollViewContainer,
        children: [(0, _jsxRuntime.jsx)(_modalTopBar.default, {
          onPressBack: onPressBack
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: Object.assign({}, _nativeLoginStyle.default.content),
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: Object.assign({}, _nativeLoginStyle.default.textContent, {
              color: _theme.color.palette.almostBlackGrey
            }),
            tx: textContent
          }), savedEmail ? (0, _jsxRuntime.jsx)(_emailLogin.default, {
            pressNotYou: pressNotYou,
            maskedMail: maskedMail,
            textDescription: textDescription
          }) : (0, _jsxRuntime.jsx)(_text.Text, {
            style: _nativeLoginStyle.default.textResetLink,
            tx: textDescription
          }), savedEmail && (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _nativeLoginStyle.default.dividerTop
          }), !savedEmail ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_emailInput.default, {
              email: inputEmail,
              setEmail: setInputEmail,
              ref: emailInputRef,
              onSubmitEdit: submitEditing
            }), (0, _jsxRuntime.jsx)(_passwordInput.default, {
              password: password,
              setPassword: setPassword,
              ref: passwordInputRef
            })]
          }) : (0, _jsxRuntime.jsx)(_passwordInput.default, {
            password: password,
            setPassword: setPassword,
            ref: passwordInputRef
          }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            end: {
              x: 0,
              y: 1
            },
            colors: disabledLogin ? _resetPassword.BTN_COLOR.INACTIVE : _resetPassword.BTN_COLOR.ACTIVE,
            start: {
              x: 0,
              y: 0
            },
            style: Object.assign({}, _nativeLoginStyle.default.btnLogin, {
              marginTop: 0
            }),
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              disabled: disabledLogin,
              onPress: handleLogin,
              tx: "nativeLoginScreen.login",
              textStyle: Object.assign({}, _nativeLoginStyle.default.textBtn, {
                color: disabledLogin ? _theme.color.palette.darkGrey : "white"
              })
            })
          }), showBiometrics && savedEmail ? (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.subFuncsContainer,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
              style: _nativeLoginStyle.default.biometricsContainer,
              onPress: function onPress() {
                return (0, _loginHelper.loginWithBiometrics)(handleBiometrics);
              },
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                tx: "nativeLoginScreen.logInWith",
                style: _nativeLoginStyle.default.txForgetPass
              }), (0, _jsxRuntime.jsx)(BiometricsIcon, {})]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "nativeLoginScreen.loginPage.forgotPassword",
              onPress: pressForgetPass,
              style: _nativeLoginStyle.default.txForgetPass
            })]
          }) : (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "nativeLoginScreen.loginPage.forgotPassword",
            onPress: pressForgetPass,
            style: _nativeLoginStyle.default.txForgetPass
          }), !!errorLogin && (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.errorLoginArea,
            children: [(0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
              style: _nativeLoginStyle.default.errorIcon
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: errorLogin,
              style: _nativeLoginStyle.default.txErrorLogin
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.dividerContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _nativeLoginStyle.default.divider
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _nativeLoginStyle.default.textOr,
              children: "Or"
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _nativeLoginStyle.default.divider
            })]
          }), (0, _jsxRuntime.jsx)(_socialProvider.SocialProvider, {
            ref: socialProviderRef,
            flow: "LOGIN"
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_confirmCaptcha.default, {
        ref: captchaRef,
        clearTimeout: clearTiming,
        siteKey: (_env = (0, _envParams.env)()) == null ? undefined : _env.RECAPTCHA_SITEKEY,
        baseUrl: (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.RECAPTCHA_BASEURL,
        onMessage: onMessage
      }), (0, _jsxRuntime.jsx)(_bottomErrorCommon.default, {
        isVisible: isVisible,
        close: function close() {
          return setIsVisible(false);
        }
      })]
    });
  };
  var _default = exports.default = LoginPage;
  var innerStyles = _reactNative.StyleSheet.create({
    scrollViewContainer: {
      height: "140%"
    },
    viewContainer: {
      backgroundColor: "white"
    }
  });
