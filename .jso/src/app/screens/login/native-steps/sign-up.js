  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = SignUp;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _modalTopBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _$$_REQUIRE(_dependencyMap[10]);
  var _passwordInput = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _button = _$$_REQUIRE(_dependencyMap[13]);
  var _resetPassword = _$$_REQUIRE(_dependencyMap[14]);
  var _icons = _$$_REQUIRE(_dependencyMap[15]);
  var _bottomErrorCommon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[19]);
  var _nativeAuthSaga = _$$_REQUIRE(_dependencyMap[20]);
  var _socialProvider = _$$_REQUIRE(_dependencyMap[21]);
  var _types = _$$_REQUIRE(_dependencyMap[22]);
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _errorConnection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _signUp = _$$_REQUIRE(_dependencyMap[25]);
  var _emailInputSignup = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _analytics = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  /**
   * @description Component for the Sign Up screen, allowing users to create a new account.
   * Handles email/password sign-up, social login, input validation, and error handling.
   *
   * @returns {JSX.Element} The SignUp component.
   */function SignUp() {
    var dispatch = (0, _reactRedux.useDispatch)();
    // Context
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      setCurrentStep = _useContext.setCurrentStep,
      sourceSystem = _useContext.sourceSystem,
      currentFlow = _useContext.currentFlow,
      setCurrentFlow = _useContext.setCurrentFlow,
      checkNetwork = _useContext.checkNetwork,
      networkConnected = _useContext.networkConnected,
      setNetworkConnected = _useContext.setNetworkConnected,
      socialProviderValue = _useContext.socialProviderValue,
      password = _useContext.password,
      setPassword = _useContext.setPassword;
    // Refs
    var emailInputRef = (0, _react.useRef)(null);
    var passwordInputRef = (0, _react.useRef)(null);
    var socialProviderRef = (0, _react.useRef)(null);
    var _useSignUp = (0, _signUp.useSignUp)(),
      email = _useSignUp.email,
      setEmail = _useSignUp.setEmail,
      emailError = _useSignUp.emailError,
      setEmailError = _useSignUp.setEmailError,
      isShowError = _useSignUp.isShowError,
      setIsShowError = _useSignUp.setIsShowError,
      setEmailBlocklistChecked = _useSignUp.setEmailBlocklistChecked,
      accountExists = _useSignUp.accountExists,
      setAccountExists = _useSignUp.setAccountExists,
      disabledSubmit = _useSignUp.disabledSubmit,
      loadingSignUp = _useSignUp.loadingSignUp,
      signUpStatus = _useSignUp.signUpStatus,
      onCheckBlockEmail = _useSignUp.onCheckBlockEmail;
    var txValidate = [{
      tx: "nativeLoginScreen.signUpPage.verifyPass.passwordErrorMinAndNoSpace",
      validated: password.length >= 8 && !/\s/.test(password)
    }, {
      tx: "nativeLoginScreen.signUpPage.verifyPass.passwordErrorHasNumeral",
      validated: /\d/.test(password)
    }, {
      tx: "nativeLoginScreen.signUpPage.verifyPass.passwordErrorHasUppercase",
      validated: /[A-Z]/.test(password)
    }];

    /**
     * @description Handles the back button press. Navigates to the previous step
     * based on the current flow.
     */
    var onPressBack = function onPressBack() {
      if (currentFlow === _types.USER_FLOW.SIGNIN_NATIVE) {
        setCurrentStep(_types.STEP.RESET_PASSWORD);
        return;
      }
      setCurrentStep(_types.STEP.GET_STARTED);
    };
    var onCloseErrorModal = function onCloseErrorModal() {
      setIsShowError(false);
    };

    /**
     * @description useEffect hook to focus the email input on mount and set the current flow.
     */
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        setTimeout(function () {
          var _emailInputRef$curren;
          return emailInputRef == null || (_emailInputRef$curren = emailInputRef.current) == null ? undefined : _emailInputRef$curren.focus();
        }, 100);
      });
      setCurrentFlow(_types.USER_FLOW.SIGNUP_NATIVE);
      !!password && setPassword("");
    }, []);

    /**
     * @description useEffect hook to handle the sign-up status from Redux.
     * Navigates to the verification screen on success, sets accountExists error to true if the account exists,
     * or shows an error message on failure.
     */
    (0, _react.useEffect)(function () {
      if (signUpStatus) {
        if (signUpStatus === _nativeAuthSaga.RES_CODE.COMMON_SUCCESS) {
          setCurrentStep(_types.STEP.VERIFY_EMAIL);
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSignupFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSignupFlow, `${_adobe.AdobeValueByTagName.CAppSignUpPage}Success`));
        } else if (signUpStatus === _nativeAuthSaga.RES_CODE.ACCOUNT_EXIST) {
          setAccountExists(true);
        } else {
          setIsShowError(true);
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSignupFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSignupFlow, `${_adobe.AdobeValueByTagName.CAppSignUpPage}Fail`));
        }
        dispatch(_nativeAuthRedux.default.clearSignUpStatus());
      }
    }, [signUpStatus]);

    /**
     * @description Handles the "Continue" button press. Validates network connectivity and dispatches the sign-up request.
     *
     * @async
     */
    var onPressContinue = function onPressContinue() {
      var dtAction = (0, _analytics.dtManualActionEvent)(`${_analytics.FE_LOG_PREFIX}onPress_Signup_Continue`);
      dtAction.leaveAction();
      handleContinue();
    };
    var handleContinue = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _emailInputRef$curren2, _passwordInputRef$cur;
        var connected = yield checkNetwork();
        if (!connected) return;
        setAccountExists(false);
        (_emailInputRef$curren2 = emailInputRef.current) == null || _emailInputRef$curren2.blur();
        (_passwordInputRef$cur = passwordInputRef.current) == null || _passwordInputRef$cur.blur();
        dispatch(_nativeAuthRedux.default.sendSignUpRequest(email, sourceSystem, password));
      });
      return function handleContinue() {
        return _ref.apply(this, arguments);
      };
    }();

    /**
     * @description Handles the submit editing event for the email input (moves focus to the password input).
     */
    var submitEditing = function submitEditing() {
      var _passwordInputRef$cur2;
      (_passwordInputRef$cur2 = passwordInputRef.current) == null || _passwordInputRef$cur2.focus();
    };

    /**
     * @description Retries the network request if the connection fails.
     *
     * @async
     */
    var retryNetwork = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var status = yield checkNetwork();
        if (status) {
          if (currentFlow === _types.USER_FLOW.SIGNUP_NATIVE) handleContinue();else if (currentFlow === _types.USER_FLOW.SIGNIN_FROM_SOCIAL) {
            var _socialProviderRef$cu;
            var provider = (0, _socialProvider.getProvider)(socialProviderValue);
            socialProviderRef == null || (_socialProviderRef$cu = socialProviderRef.current) == null || _socialProviderRef$cu.handleSocialLogin(provider);
          }
        }
      });
      return function retryNetwork() {
        return _ref2.apply(this, arguments);
      };
    }();

    /**
     * @description Handles closing the network error component and refocuses the email input.
     */
    var onClose = function onClose() {
      setNetworkConnected(true);
      setCurrentFlow(_types.USER_FLOW.SIGNUP_NATIVE);
      _reactNative.InteractionManager.runAfterInteractions(function () {
        setTimeout(function () {
          var _emailInputRef$curren3;
          return emailInputRef == null || (_emailInputRef$curren3 = emailInputRef.current) == null ? undefined : _emailInputRef$curren3.focus();
        }, 100);
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: _nativeLoginStyle.default.containerPage,
      children: !networkConnected ? (0, _jsxRuntime.jsx)(_errorConnection.default, {
        retryNetwork: retryNetwork,
        onClose: onClose
      }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_modalTopBar.default, {
          onPressBack: onPressBack
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _nativeLoginStyle.default.content,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: Object.assign({}, _nativeLoginStyle.default.textContent, {
              color: _theme.color.palette.almostBlackGrey
            }),
            tx: "nativeLoginScreen.signUpPage.title"
          }), (0, _jsxRuntime.jsx)(_emailInputSignup.default, {
            email: email,
            setEmail: setEmail,
            emailError: emailError,
            setEmailError: setEmailError,
            setEmailBlocklistChecked: setEmailBlocklistChecked,
            ref: emailInputRef,
            onCheckBlockEmail: onCheckBlockEmail,
            onSubmitEdit: submitEditing
          }), (0, _jsxRuntime.jsx)(_passwordInput.default, {
            password: password,
            setPassword: setPassword,
            ref: passwordInputRef
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _nativeLoginStyle.default.txValidateArea,
            children: txValidate.map(function (item, index) {
              return (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: _nativeLoginStyle.default.txValidatePass,
                children: [!item.validated ? (0, _jsxRuntime.jsx)(_reactNative.View, {
                  style: _nativeLoginStyle.default.containDot,
                  children: (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: _nativeLoginStyle.default.dot
                  })
                }) : (0, _jsxRuntime.jsx)(_icons.GreenTick, {}), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: Object.assign({}, _nativeLoginStyle.default.txErrorLogin, {
                    paddingStart: 6,
                    color: item.validated ? _theme.color.palette.basegreen : _theme.color.palette.darkGrey999
                  }),
                  tx: item.tx
                })]
              }, index);
            })
          }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            end: {
              x: 0,
              y: 1
            },
            colors: disabledSubmit ? _resetPassword.BTN_COLOR.INACTIVE : _resetPassword.BTN_COLOR.ACTIVE,
            start: {
              x: 0,
              y: 0
            },
            style: Object.assign({}, _nativeLoginStyle.default.btnLogin, {
              marginTop: 24
            }),
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              disabled: disabledSubmit,
              onPress: onPressContinue,
              tx: "nativeLoginScreen.signUpPage.btnContinue",
              textStyle: Object.assign({}, _nativeLoginStyle.default.textBtn, {
                color: disabledSubmit ? _theme.color.palette.darkGrey : "white"
              })
            })
          }), accountExists && (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.errorLoginArea,
            children: [(0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
              style: Object.assign({}, _nativeLoginStyle.default.errorIcon, {
                alignSelf: "auto"
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "nativeLoginScreen.signUpPage.error.accountExists",
              style: _nativeLoginStyle.default.txErrorLogin
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.dividerContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _nativeLoginStyle.default.divider
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _nativeLoginStyle.default.textOr,
              children: "Or"
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _nativeLoginStyle.default.divider
            })]
          }), (0, _jsxRuntime.jsx)(_socialProvider.SocialProvider, {
            ref: socialProviderRef,
            flow: "SIGNUP"
          })]
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: loadingSignUp
        }), (0, _jsxRuntime.jsx)(_bottomErrorCommon.default, {
          isVisible: isShowError,
          close: onCloseErrorModal
        })]
      })
    });
  }
