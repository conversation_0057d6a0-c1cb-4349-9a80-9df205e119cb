  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.BTN_COLOR = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _modalTopBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _react = _$$_REQUIRE(_dependencyMap[11]);
  var _button = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _nativeAuthRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[16]);
  var _emailInput = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _bottomErrorCommon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _errorConnection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _nativeAuthSaga = _$$_REQUIRE(_dependencyMap[22]);
  var _types = _$$_REQUIRE(_dependencyMap[23]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[24]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BTN_COLOR = exports.BTN_COLOR = {
    ACTIVE: ["#8A2AA2", "#7A35B0"],
    INACTIVE: ["#CCCCCC", "#CCCCCC"]
  };

  /**
   * @description This component implements the "Reset Password" screen for native authentication.
   * It allows users to request a password reset link by entering their email address.
   *
   * @returns {JSX.Element} The ResetPassword component.
   */
  var ResetPassword = function ResetPassword() {
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      sourceSystem = _useContext.sourceSystem,
      setCurrentStep = _useContext.setCurrentStep,
      checkNetwork = _useContext.checkNetwork,
      networkConnected = _useContext.networkConnected,
      setNetworkConnected = _useContext.setNetworkConnected;
    var dispatch = (0, _reactRedux.useDispatch)();
    var loadingSendEmail = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.loadingSendEmail);
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      email = _useState2[0],
      setEmail = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isShowError = _useState4[0],
      setIsShowError = _useState4[1];
    var disabledSubmit = !(0, _emailInput.isValidEmail)(email) || !email;
    var emailInputRef = (0, _react.useRef)(null);
    var successSendEmail = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.successSendEmail);
    var sendEmailResponseStatus = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.sendEmailResponseStatus);

    /**
     * @description Focuses the email input field when the component mounts.
     */
    (0, _react.useEffect)(function () {
      var _emailInputRef$curren;
      (_emailInputRef$curren = emailInputRef.current) == null || _emailInputRef$curren.focus();
    }, []);

    /**
     * @description Handles the submit button press.
     * Validates the network connection and dispatches the send email request.
     */
    var submit = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _emailInputRef$curren2;
        var connected = yield checkNetwork();
        if (!connected) return;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, `${_adobe.AdobeValueByTagName.CAppLoginPage}Reset Password`));
        (_emailInputRef$curren2 = emailInputRef.current) == null || _emailInputRef$curren2.blur();
        dispatch(_nativeAuthRedux.default.sendEmailResetRequest(email, sourceSystem));
      });
      return function submit() {
        return _ref.apply(this, arguments);
      };
    }();

    /**
     * @description Handles the back button press.
     * Navigates back to the LOGIN step and clears the email status code.
     */
    var onPressBackResetPass = function onPressBackResetPass() {
      setCurrentStep(_types.STEP.LOGIN);
      dispatch(_nativeAuthRedux.default.clearEmailStatusCode());
    };

    /**
     * @description Handles the response from the send email request.
     * If successful or the user is not found, navigates to the SENT_EMAIL step.
     * Otherwise, displays an error message.
     */
    (0, _react.useEffect)(function () {
      if (sendEmailResponseStatus) {
        if (successSendEmail || sendEmailResponseStatus === _nativeAuthSaga.RES_CODE.NO_USER_ERROR) {
          setCurrentStep(_types.STEP.SENT_EMAIL);
        } else {
          setIsShowError(true);
        }
        dispatch(_nativeAuthRedux.default.clearEmailStatusCode());
      }
    }, [sendEmailResponseStatus]);

    /**
     * @description Closes the error modal.
     */
    var onCloseErrorModal = function onCloseErrorModal() {
      setIsShowError(false);
    };

    /**
     * @description Navigates to the LOGIN step.
     */
    var handlePressLogin = function handlePressLogin() {
      setCurrentStep(_types.STEP.LOGIN);
    };

    /**
     * @description Navigates to the SIGNUP step.
     */
    var handlePressSignUp = function handlePressSignUp() {
      setCurrentStep(_types.STEP.SIGN_UP);
    };
    var retryNetwork = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var status = yield checkNetwork();
        if (status) {
          submit();
        }
      });
      return function retryNetwork() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onClose = function onClose() {
      setNetworkConnected(true);
      _reactNative.InteractionManager.runAfterInteractions(function () {
        setTimeout(function () {
          var _emailInputRef$curren3;
          return emailInputRef == null || (_emailInputRef$curren3 = emailInputRef.current) == null ? undefined : _emailInputRef$curren3.focus();
        }, 100);
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: _nativeLoginStyle.default.containerPage,
      children: !networkConnected ? (0, _jsxRuntime.jsx)(_errorConnection.default, {
        retryNetwork: retryNetwork,
        onClose: onClose
      }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_modalTopBar.default, {
          onPressBack: onPressBackResetPass
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: Object.assign({}, _nativeLoginStyle.default.content),
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: Object.assign({}, _nativeLoginStyle.default.textContent, {
              color: _theme.color.palette.almostBlackGrey
            }),
            text: (0, _i18n.translate)("nativeLoginScreen.resetPassword.title")
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _nativeLoginStyle.default.textEnterMail,
            text: (0, _i18n.translate)("nativeLoginScreen.resetPassword.descriptionPrimary")
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _nativeLoginStyle.default.textResetLink,
            text: (0, _i18n.translate)("nativeLoginScreen.resetPassword.descriptionSecondary")
          }), (0, _jsxRuntime.jsx)(_emailInput.default, {
            email: email,
            setEmail: setEmail,
            ref: emailInputRef
          }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            end: {
              x: 0,
              y: 1
            },
            colors: disabledSubmit ? BTN_COLOR.INACTIVE : BTN_COLOR.ACTIVE,
            start: {
              x: 0,
              y: 0
            },
            style: Object.assign({}, _nativeLoginStyle.default.btnLogin, {
              marginTop: 8
            }),
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              disabled: disabledSubmit,
              onPress: submit,
              text: (0, _i18n.translate)("nativeLoginScreen.submit"),
              textStyle: Object.assign({}, _nativeLoginStyle.default.textBtn, {
                color: "white"
              })
            })
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.txGroupResetPageContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _nativeLoginStyle.default.txLoginContainer,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: (0, _i18n.translate)("nativeLoginScreen.login"),
                style: _nativeLoginStyle.default.textLogin,
                onPress: handlePressLogin
              })
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _nativeLoginStyle.default.txSignUpContainer,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: (0, _i18n.translate)("nativeLoginScreen.signUp"),
                style: _nativeLoginStyle.default.textLogin,
                onPress: handlePressSignUp
              })
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: loadingSendEmail
        }), (0, _jsxRuntime.jsx)(_bottomErrorCommon.default, {
          isVisible: isShowError,
          close: onCloseErrorModal
        })]
      })
    });
  };
  var _default = exports.default = ResetPassword;
