  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = SentEmail;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _modalTopBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[8]);
  var _nativeAuthRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _react = _$$_REQUIRE(_dependencyMap[10]);
  var _types = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function SentEmail() {
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      setCurrentStep = _useContext.setCurrentStep,
      currentFlow = _useContext.currentFlow;
    var dispatch = (0, _reactRedux.useDispatch)();

    /**
     * @description Handles the back button press. Navigates to the previous step
     * based on the current flow.
     */
    var onPressBackSentEmail = function onPressBackSentEmail() {
      dispatch(_nativeAuthRedux.default.clearEmailStatusCode());
      if (currentFlow === _types.USER_FLOW.SOCIAL_LINKING) {
        setCurrentStep(_types.STEP.LINK_ACCOUNT);
      } else setCurrentStep(_types.STEP.LOGIN);
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _nativeLoginStyle.default.containerPage,
      children: [(0, _jsxRuntime.jsx)(_modalTopBar.default, {
        onPressBack: onPressBackSentEmail
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: Object.assign({}, _nativeLoginStyle.default.content),
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: Object.assign({}, _nativeLoginStyle.default.textContent, {
            color: _theme.color.palette.almostBlackGrey
          }),
          text: (0, _i18n.translate)("nativeLoginScreen.sentEmail.title")
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _nativeLoginStyle.default.textEnterMail,
          text: (0, _i18n.translate)("nativeLoginScreen.sentEmail.descriptionPrimary")
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _nativeLoginStyle.default.textResetLink,
          text: (0, _i18n.translate)("nativeLoginScreen.sentEmail.descriptionSecondary")
        })]
      })]
    });
  }
