  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useSignUp = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _envParams = _$$_REQUIRE(_dependencyMap[4]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[9]);
  var _passwordInput = _$$_REQUIRE(_dependencyMap[10]);
  var useSignUp = exports.useSignUp = function useSignUp() {
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      password = _useContext.password;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      email = _useState2[0],
      setEmail = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isShowError = _useState4[0],
      setIsShowError = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      accountExists = _useState6[0],
      setAccountExists = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      emailBlocklistChecked = _useState8[0],
      setEmailBlocklistChecked = _useState8[1];
    var _useState9 = (0, _react.useState)(""),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      emailError = _useState0[0],
      setEmailError = _useState0[1];
    var disabledSubmit = !!emailError || !email || !password || !(0, _passwordInput.validatePassword)(password) || !emailBlocklistChecked;
    var loadingSignUp = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.loadingSignUp);
    var signUpStatus = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.signUpStatus);
    var validateEmailBlockList = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (email) {
        var _env, _env2, _response$data;
        var paramsArray = _apis.default.checkblocklist.split(" ");
        var method = paramsArray[0] || "GET";
        var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.MW_AUTH_URL) + paramsArray[1];
        var headers = {
          "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.MW_AUTH_API_KEY
        };
        var response = yield (0, _request.default)({
          url: url,
          method: method,
          headers: headers,
          data: {
            email_address: email
          },
          timeout: 3000
        });
        var isBlock = false;
        if (response != null && response.success && !!(response != null && (_response$data = response.data) != null && _response$data.in_blocklist)) {
          isBlock = true;
        }
        return isBlock;
      });
      return function validateEmailBlockList(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var onCheckBlockEmail = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var isBlock = yield validateEmailBlockList(email);
        return isBlock;
      });
      return function onCheckBlockEmail() {
        return _ref2.apply(this, arguments);
      };
    }();
    return {
      email: email,
      setEmail: setEmail,
      emailError: emailError,
      setEmailError: setEmailError,
      isShowError: isShowError,
      setIsShowError: setIsShowError,
      emailBlocklistChecked: emailBlocklistChecked,
      setEmailBlocklistChecked: setEmailBlocklistChecked,
      accountExists: accountExists,
      setAccountExists: setAccountExists,
      disabledSubmit: disabledSubmit,
      loadingSignUp: loadingSignUp,
      signUpStatus: signUpStatus,
      onCheckBlockEmail: onCheckBlockEmail
    };
  };
