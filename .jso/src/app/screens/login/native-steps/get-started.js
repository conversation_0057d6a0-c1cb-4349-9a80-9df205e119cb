  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = GetStarted;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _getStartedSourceSystem = _$$_REQUIRE(_dependencyMap[9]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _modalTopBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _react = _$$_REQUIRE(_dependencyMap[12]);
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[13]);
  var _socialProvider = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _errorConnection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _icons = _$$_REQUIRE(_dependencyMap[17]);
  var _reactNativeBiometrics = _$$_REQUIRE(_dependencyMap[18]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[19]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _nativeAuthRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[21]));
  var _nativeAuthSaga = _$$_REQUIRE(_dependencyMap[22]);
  var _loginHelper = _$$_REQUIRE(_dependencyMap[23]);
  var _types = _$$_REQUIRE(_dependencyMap[24]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[25]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var BiometricsIcon = function BiometricsIcon() {
    return _reactNative.Platform.OS === "android" ? (0, _jsxRuntime.jsx)(_icons.FingerprintWhite, {
      width: 24,
      height: 24
    }) : (0, _jsxRuntime.jsx)(_icons.FaceIDWhite, {
      width: 24,
      height: 24
    });
  };
  function GetStarted(_ref) {
    var _MAPPING_SOURCE$sourc, _MAPPING_SOURCE$sourc2;
    var onClose = _ref.onClose,
      emailProfile = _ref.emailProfile;
    //hooks
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      sourceSystem = _useContext.sourceSystem,
      setCurrentStep = _useContext.setCurrentStep,
      networkConnected = _useContext.networkConnected,
      setNetworkConnected = _useContext.setNetworkConnected,
      checkNetwork = _useContext.checkNetwork,
      socialProviderValue = _useContext.socialProviderValue,
      showBiometrics = _useContext.showBiometrics,
      setLoading = _useContext.setLoading,
      setShowBiometrics = _useContext.setShowBiometrics;
    var dispatch = (0, _reactRedux.useDispatch)();
    var loginStatus = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.loginStatus);
    var socialProviderRef = (0, _react.useRef)(null);

    /**
     * @description useEffect hook to handle login status changes when login with biometrics. If login fails,
     * it navigates the user to the login screen.
     */
    (0, _react.useEffect)(function () {
      if (loginStatus && loginStatus !== _nativeAuthSaga.RES_CODE.COMMON_SUCCESS) {
        setCurrentStep(_types.STEP.LOGIN);
      }
    }, [loginStatus]);

    /**
     * @description Handles the "Login" button press. Navigates to the login screen.
     */
    var pressLogin = function pressLogin() {
      setCurrentStep(_types.STEP.LOGIN);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, "Sign Up & Login Page | Login"));
    };

    /**
     * @description Handles the "Sign Up" button press. Navigates to the sign-up screen.
     */
    var pressSignUp = function pressSignUp() {
      setCurrentStep(_types.STEP.SIGN_UP);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSignupFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSignupFlow, "Sign Up & Login Page | Sign up"));
    };

    /**
     * @description Retries the social login request if the network connection is restored.
     *
     * @async
     */
    var retryNetwork = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var connected = yield checkNetwork();
        if (connected) {
          var _socialProviderRef$cu;
          var provider = (0, _socialProvider.getProvider)(socialProviderValue);
          socialProviderRef == null || (_socialProviderRef$cu = socialProviderRef.current) == null || _socialProviderRef$cu.handleSocialLogin(provider);
        }
      });
      return function retryNetwork() {
        return _ref2.apply(this, arguments);
      };
    }();

    /**
     * @description Handles biometric authentication. Prompts the user for biometric input
     * and dispatches the login request if successful.
     * if failed, it checks if biometric login is available and handles showing the biometric button.
     *
     * @async
     */
    var handleBiometrics = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        if (showBiometrics) {
          var _yield$rnBiometrics$i = yield _loginHelper.rnBiometrics.isSensorAvailable(),
            available = _yield$rnBiometrics$i.available,
            biometryType = _yield$rnBiometrics$i.biometryType;
          if (available && biometryType !== _reactNativeBiometrics.BiometryTypes.TouchID) {
            (0, _loginHelper.showAndroidBiometricsPrompt)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
              var _yield$rnBiometrics$s = yield _loginHelper.rnBiometrics.simplePrompt(_loginHelper.PROMPT_OPTIONS),
                success = _yield$rnBiometrics$s.success;
              if (success) {
                setLoading(true);
                var userPassword = (0, _mmkvStorage.getUserPassword)();
                dispatch(_nativeAuthRedux.default.sendLoginRequest(emailProfile, sourceSystem, userPassword));
              } else {
                var _yield$rnBiometrics$i2 = yield _loginHelper.rnBiometrics.isSensorAvailable(),
                  _available = _yield$rnBiometrics$i2.available;
                if (!_available) {
                  setShowBiometrics(false);
                }
              }
            }), /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
              var _yield$rnBiometrics$i3 = yield _loginHelper.rnBiometrics.isSensorAvailable(),
                available = _yield$rnBiometrics$i3.available;
              if (!available) {
                setShowBiometrics(false);
              }
            }));
          } else {
            setShowBiometrics(false);
          }
        }
      });
      return function handleBiometrics() {
        return _ref3.apply(this, arguments);
      };
    }();

    /**
     * @description Closes the network error message.
     */
    var closeNetworkError = function closeNetworkError() {
      setNetworkConnected(true);
    };

    /**
     * @description Return ErrorConnection component when no network connection
     *
     */
    if (!networkConnected) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _nativeLoginStyle.default.imgBackground,
        children: (0, _jsxRuntime.jsx)(_errorConnection.default, {
          retryNetwork: retryNetwork,
          onClose: closeNetworkError
        })
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.ImageBackground, {
      source: ((_MAPPING_SOURCE$sourc = _loginHelper.MAPPING_SOURCE[sourceSystem]) == null ? undefined : _MAPPING_SOURCE$sourc.background) || _getStartedSourceSystem.General,
      style: _nativeLoginStyle.default.imgBackground,
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _nativeLoginStyle.default.shadowView,
        children: [(0, _jsxRuntime.jsx)(_modalTopBar.default, {
          onClose: onClose
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _nativeLoginStyle.default.content,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
            source: _getStartedSourceSystem.LogoSecondary,
            style: _nativeLoginStyle.default.logo
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: Object.assign({}, _nativeLoginStyle.default.textContent, _nativeLoginStyle.default.dividerTop),
            text: (0, _i18n.translate)(((_MAPPING_SOURCE$sourc2 = _loginHelper.MAPPING_SOURCE[sourceSystem]) == null ? undefined : _MAPPING_SOURCE$sourc2.textContent) || "nativeLoginScreen.getStarted.general")
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _nativeLoginStyle.default.getStartedButtonContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: (0, _i18n.translate)("nativeLoginScreen.getStarted.loginOrCreate"),
              style: _nativeLoginStyle.default.loginOrCreate
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: showBiometrics && _nativeLoginStyle.default.biometricsContainer,
              children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                end: {
                  x: 0,
                  y: 1
                },
                colors: ["#8A2AA2", "#7A35B0"],
                start: {
                  x: 0,
                  y: 0
                },
                style: Object.assign({}, _nativeLoginStyle.default.btnLogin, {
                  marginTop: 16
                }, showBiometrics && {
                  width: "80%"
                }),
                children: (0, _jsxRuntime.jsx)(_button.Button, {
                  text: (0, _i18n.translate)("nativeLoginScreen.login"),
                  textStyle: Object.assign({}, _nativeLoginStyle.default.textBtn, {
                    color: "white"
                  }),
                  onPress: pressLogin
                })
              }), showBiometrics && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
                style: Object.assign({}, _nativeLoginStyle.default.btnLogin, {
                  marginTop: 16
                }, _nativeLoginStyle.default.btnBiometrics),
                onPress: function onPress() {
                  return (0, _loginHelper.loginWithBiometrics)(handleBiometrics);
                },
                children: (0, _jsxRuntime.jsx)(BiometricsIcon, {})
              })]
            }), (0, _jsxRuntime.jsx)(_button.Button, {
              style: _nativeLoginStyle.default.btnSignUp,
              text: (0, _i18n.translate)("nativeLoginScreen.signUp"),
              textStyle: _nativeLoginStyle.default.textBtn,
              onPress: pressSignUp
            }), (0, _jsxRuntime.jsx)(_socialProvider.SocialProvider, {
              ref: socialProviderRef,
              isWhiteIcon: true,
              hideTitle: true,
              flow: "LOGIN"
            })]
          })]
        })]
      })
    });
  }
