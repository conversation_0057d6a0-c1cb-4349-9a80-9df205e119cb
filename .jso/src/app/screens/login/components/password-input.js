  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.validatePassword = exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _inputField = _$$_REQUIRE(_dependencyMap[3]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[4]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _viewShadowWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var validatePassword = exports.validatePassword = function validatePassword(password) {
    var hasMinLength = password.length >= 8 && !/\s/.test(password);
    var hasNumber = /\d/.test(password);
    var hasUpperCase = /[A-Z]/.test(password);
    return hasMinLength && hasNumber && hasUpperCase;
  };
  var PasswordInput = (0, _react.forwardRef)(function (_ref, ref) {
    var setPassword = _ref.setPassword,
      password = _ref.password,
      isInvalid = _ref.isInvalid,
      setIsInvalid = _ref.setIsInvalid;
    var internalRef = (0, _react.useRef)(null);
    var inputRef = ref || internalRef;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      errorPass = _useState2[0],
      setErrorPass = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      firstFocus = _useState4[0],
      setFirstFocus = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isFocused = _useState6[0],
      setIsFocused = _useState6[1];
    var onBlurInput = function onBlurInput() {
      setIsFocused(false);
      handlePassError();
      if (firstFocus) setFirstFocus(false);
    };
    var handlePassError = function handlePassError() {
      if (password.length === 0) {
        setErrorPass("nativeLoginScreen.emptyPassword");
      } else {
        setErrorPass("");
      }
    };
    (0, _react.useEffect)(function () {
      if (!firstFocus) {
        handlePassError();
      }
    }, [password]);
    var onFocusField = function onFocusField() {
      setIsFocused(true);
      setIsInvalid == null || setIsInvalid(false);
    };
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      isInvalid: firstFocus ? false : !password || isInvalid,
      labelTx: "loginScreen.others.passwordLabel",
      style: _nativeLoginStyle.default.inputPassword,
      helpTextTx: errorPass,
      numberOfLinesError: 1,
      children: (0, _jsxRuntime.jsx)(_viewShadowWrap.default, {
        isFocused: isFocused,
        isInvalid: firstFocus ? false : !password || isInvalid,
        children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
          forwardedRef: inputRef,
          value: password,
          onChangeText: setPassword,
          textContentType: "password",
          highlightOnFocused: true,
          passwordReveal: true,
          isInvalid: firstFocus ? false : !password || isInvalid,
          onBlur: onBlurInput,
          autoComplete: "password",
          autoCapitalize: "none",
          autoCorrect: false,
          enterKeyHint: "next",
          cursorColor: _theme.color.palette.overlayColor,
          selectionColor: _reactNative.Platform.select({
            ios: _theme.color.palette.overlayColor,
            android: _theme.color.palette.lightestIrisBlue
          }),
          onFocus: onFocusField,
          placeHolderValue: (0, _i18n.translate)("nativeLoginScreen.enterPassword"),
          alwaysShowIconPassword: true,
          iconEyeHide: (0, _jsxRuntime.jsx)(_icons.Eye, {}),
          iconEyeShow: (0, _jsxRuntime.jsx)(_icons.EyeHideGray, {})
        })
      })
    });
  });
  var _default = exports.default = PasswordInput;
