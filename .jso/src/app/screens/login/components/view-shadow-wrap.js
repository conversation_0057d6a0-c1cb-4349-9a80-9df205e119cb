  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[2]);
  var ViewShadowWrap = function ViewShadowWrap(_ref) {
    var children = _ref.children,
      isFocused = _ref.isFocused,
      isInvalid = _ref.isInvalid;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: Object.assign({}, styles.container, isFocused && styles.inputShadowFocused, isInvalid && styles.inputShadowInvalid),
      children: children
    });
  };
  var _default = exports.default = ViewShadowWrap;
  var styles = _reactNative.StyleSheet.create({
    container: {
      padding: 4,
      zIndex: -1,
      borderRadius: 14
    },
    inputShadowFocused: {
      backgroundColor: _theme.color.palette.lightestPurple
    },
    inputShadowInvalid: {
      backgroundColor: _theme.color.palette.lightestRed
    }
  });
