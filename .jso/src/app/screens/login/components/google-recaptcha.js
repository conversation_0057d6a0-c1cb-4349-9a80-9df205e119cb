  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeWebview = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var patchPostMessageJsCode = `(${String(function () {
    //@ts-ignore
    var originalPostMessage = window.ReactNativeWebView.postMessage;
    var patchedPostMessage = function patchedPostMessage(message, targetOrigin, transfer) {
      originalPostMessage(message, targetOrigin, transfer);
    };
    patchedPostMessage.toString = function () {
      return String(Object.hasOwnProperty).replace("hasOwnProperty", "postMessage");
    };
    //@ts-ignore
    window.ReactNativeWebView.postMessage = patchedPostMessage;
  })})();`;
  var GoogleReCaptcha = function GoogleReCaptcha(_ref) {
    var onClose = _ref.onClose,
      onMessage = _ref.onMessage,
      siteKey = _ref.siteKey,
      style = _ref.style,
      url = _ref.url;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var generateTheWebViewContent = function generateTheWebViewContent(siteKey) {
      var originalForm = `<!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0">
                <meta http-equiv="X-UA-Compatible" content="ie=edge">
                <script src="https://www.google.com/recaptcha/enterprise.js" async defer></script>
                <script type="text/javascript">
                var onloadCallback = function() { };  
                var onDataCallback = function(response) {
                    window.ReactNativeWebView.postMessage(response);  
                    setTimeout(function () {
                        document.getElementById('captcha').style.display = 'none';
                    }, 500);
                };  
                var onCancel = function() {  
                    window.ReactNativeWebView.postMessage("cancel");
                    document.getElementById('captcha').style.display = 'none';
                }
                var onDataExpiredCallback = function(error) {  window.ReactNativeWebView.postMessage("expired"); };  
                var onDataErrorCallback = function(error) {  window.ReactNativeWebView.postMessage("error"); }
                </script>
                <style>
                    .btn {
                        background-color: #c60710;
                        color: #ffffff; padding: 8px 32px; margin-top: 8px;
                        border: none; border-radius: 25px; font-weight: bold;
                    }
                    .btn:active {
                        outline: none;
                    }
                    .btn:focus {
                        outline: none;
                    }
                </style>
            </head>
            <body>
                <div id="captcha">
                    <div style="text-align: center; padding-top: 30vh;">
                    <div class="g-recaptcha" style="display: inline-block; height: auto;"
                        data-sitekey=${siteKey} data-callback="onDataCallback"  
                        data-expired-callback="onDataExpiredCallback"  
                        data-error-callback="onDataErrorCallback">
                    </div>
                    </div>
                </div>
            </body>
            </html>`;
      return originalForm;
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.container,
        children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          style: Object.assign({}, styles.closeButton, {
            top: _reactNative.Platform.OS === "ios" ? insets.top + 27 : 16
          }),
          onPress: function onPress() {
            onClose();
            onMessage({
              nativeEvent: {
                data: "cancel"
              }
            });
          },
          children: (0, _jsxRuntime.jsx)(_reactNative.Image, {
            resizeMode: "contain",
            style: styles.closeIcon,
            source: _$$_REQUIRE(_dependencyMap[7])
          })
        })
      }), (0, _jsxRuntime.jsx)(_reactNativeWebview.default, {
        originWhitelist: ["*"],
        mixedContentMode: "always",
        onMessage: onMessage,
        javaScriptEnabled: true,
        injectedJavaScript: patchPostMessageJsCode,
        automaticallyAdjustContentInsets: true,
        style: [styles.webView, style],
        source: {
          html: generateTheWebViewContent(siteKey),
          baseUrl: `${url}`
        }
      })]
    });
  };
  var _default = exports.default = GoogleReCaptcha;
  var styles = _reactNative.StyleSheet.create({
    container: {
      height: _reactNative.Dimensions.get("window").height * 0.2 - (_reactNative.Platform.OS === "android" ? 34 : 0),
      width: "100%"
    },
    closeButton: {
      position: "absolute",
      right: 16,
      backgroundColor: "#0096FF",
      borderRadius: 20,
      width: 40,
      height: 40,
      alignItems: "center",
      justifyContent: "center"
    },
    closeIcon: {
      width: 12,
      height: 12,
      tintColor: "white"
    },
    webView: {
      backgroundColor: "transparent",
      width: "100%"
    }
  });
