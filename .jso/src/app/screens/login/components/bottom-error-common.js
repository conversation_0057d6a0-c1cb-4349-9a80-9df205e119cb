  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = BottomErrorCommon;
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[0]);
  var _i18n = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  function BottomErrorCommon(_ref) {
    var isVisible = _ref.isVisible,
      close = _ref.close;
    return (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
      visible: isVisible,
      title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
      icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
      colorMsg: _theme.color.palette.almostBlack<PERSON>rey,
      errorMessage: (0, _i18n.translate)("nativeLoginScreen.sendEmailError"),
      onClose: close,
      buttonText: (0, _i18n.translate)("common.okay"),
      onButtonPressed: close
    });
  }
