  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ModalTopBar;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function ModalTopBar(_ref) {
    var onClose = _ref.onClose,
      onPressBack = _ref.onPressBack,
      _ref$closeIcon = _ref.closeIcon,
      closeIcon = _ref$closeIcon === undefined ? (0, _jsxRuntime.jsx)(_icons.CloseIcon, {}) : _ref$closeIcon;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _nativeLoginStyle.default.topAppBar,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        disabled: !onPressBack,
        style: {
          opacity: onPressBack ? 1 : 0
        },
        onPress: onPressBack,
        children: (0, _jsxRuntime.jsx)(_icons.BackArrow, {})
      }), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        disabled: !onClose,
        style: {
          opacity: onClose ? 1 : 0
        },
        onPress: onClose,
        children: closeIcon
      })]
    });
  }
