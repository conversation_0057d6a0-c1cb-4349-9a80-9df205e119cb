  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.getProvider = exports.SocialProvider = exports.SOCIAL_PROVIDERS = exports.SOCIAL_CHANNEL_VALUES = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _envParams = _$$_REQUIRE(_dependencyMap[8]);
  var _react = _$$_REQUIRE(_dependencyMap[9]);
  var _axios = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativeInappbrowserReborn = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _urlParse = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _nativeLoginScreen = _$$_REQUIRE(_dependencyMap[14]);
  var _types = _$$_REQUIRE(_dependencyMap[15]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _nativeAuthRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _nativeAuthSaga = _$$_REQUIRE(_dependencyMap[18]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _bottomErrorCommon = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _mmkvEncryptionStorage = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var extractUrl = function extractUrl(html) {
    var firstIndex = html.indexOf("redirect('");
    var restString = html.substring(firstIndex + "redirect('".length);
    var lastIndex = restString.indexOf("')");
    var finalUrl = restString.substring(0, lastIndex);
    return finalUrl;
  };
  var SOCIAL_PROVIDERS = exports.SOCIAL_PROVIDERS = [_constants.SOCIAL_PROVIDER.GOOGLE, _constants.SOCIAL_PROVIDER.FACEBOOK, _constants.SOCIAL_PROVIDER.APPLE];
  var SOCIAL_CHANNEL_VALUES = exports.SOCIAL_CHANNEL_VALUES = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _constants.SOCIAL_PROVIDER.GOOGLE, "Google"), _constants.SOCIAL_PROVIDER.FACEBOOK, "Facebook"), _constants.SOCIAL_PROVIDER.APPLE, "Apple");
  var getProvider = exports.getProvider = function getProvider(socialProviderValue) {
    return SOCIAL_PROVIDERS.find(function (provider) {
      return SOCIAL_CHANNEL_VALUES[provider] === socialProviderValue;
    });
  };

  /**
   * @description Component for handling social login (Google, Facebook, Apple).
   * Manages the social login flow, including opening the InAppBrowser, handling
   * authorization responses, and dispatching Redux actions.
   *
   * @param {object} props - The component's props.
   * @param {string} [props.titleTx] - The title text key to display above the social login buttons.
   * @param {TextStyle} [props.titleStyle] - Style for the title text.
   * @param {ViewStyle} [props.socialProviderContainer] - Style for the container of social login buttons.
   * @param {"LOGIN" | "SIGNUP"} props.flow - The current flow ("LOGIN" or "SIGNUP").
   * @param {boolean} [props.hideTitle] - Whether to hide the title. (used in socialtosocial flow)
   * @param {boolean} [props.isWhiteIcon] - Whether to use white icons for Apple login. (used in socialtosocial flow)
   * @param {boolean} [props.showFacebook] - Whether to show the Facebook login button. (used in socialtosocial flow)
   * @param {boolean} [props.showGoogle] - Whether to show the Google login button. (used in socialtosocial flow)
   * @param {boolean} [props.showApple] - Whether to show the Apple login button. (used in socialtosocial flow)
   * @param {boolean} [props.showProviderName] - Whether to show the provider name next to the icon. (used in socialtosocial flow)
   * @param {function} [props.setShowTitleLinkAccountPage] - Function to control visibility of title on Link Account Page. (used in socialtosocial flow)
   * @param {object} [props.socialToSocialParams] - Parameters for social-to-social linking. (used in socialtosocial flow)
   * @returns {JSX.Element} The SocialProvider component.
   */
  var SocialProvider = exports.SocialProvider = (0, _react.forwardRef)(function (props, ref) {
    var hideTitle = props.hideTitle,
      titleStyle = props.titleStyle,
      socialProviderContainer = props.socialProviderContainer,
      flow = props.flow,
      isWhiteIcon = props.isWhiteIcon,
      titleTx = props.titleTx,
      _props$showApple = props.showApple,
      showApple = _props$showApple === undefined ? true : _props$showApple,
      _props$showFacebook = props.showFacebook,
      showFacebook = _props$showFacebook === undefined ? true : _props$showFacebook,
      _props$showGoogle = props.showGoogle,
      showGoogle = _props$showGoogle === undefined ? true : _props$showGoogle,
      _props$showProviderNa = props.showProviderName,
      showProviderName = _props$showProviderNa === undefined ? false : _props$showProviderNa,
      socialToSocialParams = props.socialToSocialParams,
      setShowTitleLinkAccountPage = props.setShowTitleLinkAccountPage;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showError = _useState2[0],
      setShowError = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      getLinkFetching = _useState4[0],
      setGetLinkFetching = _useState4[1];
    var _useContext = (0, _react.useContext)(_nativeLoginScreen.NativeLoginContext),
      setCurrentFlow = _useContext.setCurrentFlow,
      setOauthData = _useContext.setOauthData,
      setCurrentStep = _useContext.setCurrentStep,
      currentFlow = _useContext.currentFlow,
      currentStep = _useContext.currentStep,
      setLoading = _useContext.setLoading,
      setSocialError = _useContext.setSocialError,
      setEmailProfile = _useContext.setEmailProfile,
      setSocialProviderValue = _useContext.setSocialProviderValue,
      checkNetwork = _useContext.checkNetwork,
      setSocialFlow = _useContext.setSocialFlow;
    //selectors
    var getAccountInfoData = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.getAccountInfoData);
    var getAccountInfoError = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.getAccountInfoError);
    var getAccountInfoFetching = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.getAccountInfoFetching);
    var getFieldsFetching = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.getFieldsFetching);
    var getFieldsAccountStatus = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.getFieldsAccountStatus);
    var isMissingField = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isMissingField);
    var isLoginFlow = flow === "LOGIN";
    var isSignupFlow = flow === "SIGNUP";
    var pageFlowAAValue = isLoginFlow ? "Log in" : "Sign up";

    /**
     * @description Handle loading state based on fetching status.
     *
     */
    (0, _react.useEffect)(function () {
      if (getAccountInfoFetching || getFieldsFetching || getLinkFetching) {
        setLoading(true);
      } else {
        setLoading(false);
      }
    }, [getAccountInfoFetching, getFieldsFetching, getLinkFetching]);

    /**
     * @description Callback function to handle successful social authentication.
     * Saves the OAuth data and dispatches Redux actions to fetch account information.
     *
     * @async
     * @param {object} params - The OAuth parameters received from the provider.
     * @param {string} socialProviderValue - The name of the social provider.
     */
    var onAuthSuccess = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (params, socialProviderValue) {
        setOauthData(params);
        (0, _mmkvEncryptionStorage.setAuthTokenPayload)({
          kind: "ok",
          postMethod: {
            access_token: params == null ? undefined : params.access_token,
            expires_in: params == null ? undefined : params.expires_in,
            member_id_cr: params == null ? undefined : params.uid
          }
        });
        var isExistingId = params == null ? undefined : params.existingLoginID;
        if (isExistingId) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, `Verify it's you | ${socialProviderValue} | Login | Require Link`));
          dispatch(_nativeAuthRedux.default.setSocialResponse({
            email: isExistingId,
            password: ""
          }));
          setLoading(false);
          setCurrentFlow(_types.USER_FLOW.SOCIAL_LINKING);
          setCurrentStep(_types.STEP.LINK_ACCOUNT);
        } else {
          var status = yield checkNetwork();
          if (!status) return;
          dispatch(_nativeAuthRedux.default.getAccountInfoRequest(params.uid, params.access_token, params.exp));
        }
      });
      return function (_x, _x2) {
        return _ref.apply(this, arguments);
      };
    }(), []);

    /**
     * @description useEffect hook to handle the `getFieldsAccountStatus`.
     * in social login flow and missing fields, navigates to the `Supplement Data` step.
     * in social login flow and not missing fields, dispatches the `setLogin` action.
     * not in social login flow, navigates to the `Supplement Data` step.
     */
    (0, _react.useEffect)(function () {
      if (getFieldsAccountStatus && currentStep !== _types.STEP.LINK_ACCOUNT) {
        var getFieldsStatus = getFieldsAccountStatus;
        dispatch(_nativeAuthRedux.default.getFieldsReset());
        if (getFieldsStatus === _nativeAuthSaga.RES_CODE.COMMON_SUCCESS) {
          if (getAccountInfoData != null && getAccountInfoData.socialProviders.includes("site") && currentFlow === _types.USER_FLOW.SIGNIN_FROM_SOCIAL) {
            if (isMissingField) {
              setLoading(false);
              setCurrentStep(_types.STEP.SUPPLEMENT_DATA);
              dispatch(_nativeAuthRedux.default.getAccountInfoReset());
            } else {
              setLoading(true);
              dispatch(_nativeAuthRedux.default.setLogin());
            }
          } else {
            setCurrentFlow(_types.USER_FLOW.SIGNUP_FROM_SOCIAL);
            setCurrentStep(_types.STEP.SUPPLEMENT_DATA);
          }
          setSocialFlow(flow);
        } else {
          setShowError(true);
        }
      }
    }, [getFieldsAccountStatus]);

    /**
     * @description useEffect hook to handle error from `getAccountInfoError`
     */
    (0, _react.useEffect)(function () {
      if (getAccountInfoError) {
        setShowError(true);
        dispatch(_nativeAuthRedux.default.getAccountInfoReset());
      }
    }, [getAccountInfoError]);

    /**
     * @description Opens the InAppBrowser to initiate the social login flow.
     * and handles response from the InAppBrowser
     * which should be return when user finish the social login process in provider page.
     * @async
     * @param {string} url - The URL to open in the InAppBrowser.
     * @param {SOCIAL_PROVIDER} socialProvider - The social provider.
     */
    var openLink = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (url, socialProvider) {
        try {
          if (yield _reactNativeInappbrowserReborn.default.isAvailable()) {
            var result = yield _reactNativeInappbrowserReborn.default.openAuth(`${url}&prompt=select_account`, "cagichangi://provider-login/", {
              // iOS Properties
              dismissButtonStyle: "cancel",
              preferredBarTintColor: "#453AA4",
              preferredControlTintColor: "white",
              readerMode: false,
              animated: true,
              modalPresentationStyle: "fullScreen",
              modalTransitionStyle: "coverVertical",
              modalEnabled: true,
              enableBarCollapsing: false,
              ephemeralWebSession: true,
              // Android Properties
              showTitle: true,
              toolbarColor: "#6200EE",
              secondaryToolbarColor: "black",
              navigationBarColor: "black",
              navigationBarDividerColor: "white",
              enableUrlBarHiding: true,
              enableDefaultShare: true,
              forceCloseOnRedirection: false,
              // Specify full animation resource identifier(package:anim/name)
              // or only resource name(in case of animation bundled with app).
              animations: {
                startEnter: "slide_in_right",
                startExit: "slide_out_left",
                endEnter: "slide_in_left",
                endExit: "slide_out_right"
              }
            });
            var aaTagNameValue = isLoginFlow ? _adobe.AdobeTagName.CAppLoginFlow : _adobe.AdobeTagName.CAppSignupFlow;
            if (result.type === "success") {
              var _parsedUrl$query, _parsedUrl$query2;
              var parsedUrl = (0, _urlParse.default)(result.url, true);
              var errorCode = (parsedUrl == null || (_parsedUrl$query = parsedUrl.query) == null ? undefined : _parsedUrl$query.error_code) || "";
              if (errorCode) {
                var errorDataToBeSent = `${pageFlowAAValue} | ${SOCIAL_CHANNEL_VALUES[socialProvider]} | ${pageFlowAAValue} | Fail`;
                (0, _adobe.trackAction)(aaTagNameValue, (0, _defineProperty2.default)({}, aaTagNameValue, errorDataToBeSent));
                if (Number(errorCode) === _nativeAuthSaga.RES_CODE.DISABLED_ACCOUNT) {
                  setSocialError("nativeLoginScreen.disabledAccount");
                  if (currentStep !== _types.STEP.LOGIN) {
                    setEmailProfile("");
                    setCurrentStep(_types.STEP.LOGIN);
                  }
                } else {
                  setShowError(true);
                }
              } else if (parsedUrl != null && (_parsedUrl$query2 = parsedUrl.query) != null && _parsedUrl$query2.access_token) {
                if (socialToSocialParams) {
                  setLoading(true);
                  setShowTitleLinkAccountPage == null || setShowTitleLinkAccountPage(false);
                }
                var successDataToBeSent = `${pageFlowAAValue} | ${SOCIAL_CHANNEL_VALUES[socialProvider]} | ${pageFlowAAValue} | Success`;
                (0, _adobe.trackAction)(aaTagNameValue, (0, _defineProperty2.default)({}, aaTagNameValue, successDataToBeSent));
                onAuthSuccess(parsedUrl == null ? undefined : parsedUrl.query, SOCIAL_CHANNEL_VALUES[socialProvider]);
              }
            }
          } else _reactNative.Linking.openURL(url);
        } catch (error) {
          _reactNative.Alert.alert(error.message);
        }
      });
      return function openLink(_x3, _x4) {
        return _ref2.apply(this, arguments);
      };
    }();

    /**
     * @description Handles the social login button press. Initiates the social login flow
     * by fetching the authorization URL and opening the InAppBrowser.
     *
     * @async
     * @param {SOCIAL_PROVIDER} provider - The social provider to login with.
     */
    var handleSocialLogin = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (provider) {
        _reactNative.Keyboard.dismiss();
        setSocialProviderValue(SOCIAL_CHANNEL_VALUES[provider]);
        setCurrentFlow(_types.USER_FLOW.SIGNIN_FROM_SOCIAL);
        var connected = yield checkNetwork();
        if (!connected) return;
        setSocialError("");
        setGetLinkFetching(true);
        if (isLoginFlow) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppLoginFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppLoginFlow, `Log in | ${SOCIAL_CHANNEL_VALUES[provider]} | General | Entry`));
        }
        if (isSignupFlow) {
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSignupFlow, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSignupFlow, `Sign up | ${SOCIAL_CHANNEL_VALUES[provider]} | General | Entry`));
        }
        try {
          var _env, _env2, _env3, _env4, _response$data;
          var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.MW_AUTH_URL) + "/social";
          var body = JSON.stringify(Object.assign({
            provider: provider,
            apikey: (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.SOCIAL_LOGIN_API_KEY,
            frontend_url: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.SOCIAL_LOGIN_FE_LINK
          }, socialToSocialParams && socialToSocialParams));
          var response = yield _axios.default.post(url, body, {
            params: {
              provider: provider
            },
            headers: {
              "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.MW_AUTH_API_KEY
            }
          });
          if ((_response$data = response.data) != null && _response$data.url) {
            var _response$data2;
            var formatted = decodeURIComponent(decodeURI((_response$data2 = response.data) == null ? undefined : _response$data2.url)).replace(/\\u0026/g, "&");
            openLink(formatted, provider);
          } else {
            var redirectURL = extractUrl(response.data);
            var decoded = decodeURI(redirectURL);
            var decoded2 = decodeURIComponent(decoded);
            var replaced = decoded2.replace(/\\u0026/g, "&");
            openLink(replaced, provider);
          }
        } catch (error) {
          setShowError(true);
        } finally {
          setGetLinkFetching(false);
        }
      });
      return function (_x5) {
        return _ref3.apply(this, arguments);
      };
    }(), [setCurrentFlow]);
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        handleSocialLogin: handleSocialLogin
      };
    }, []);
    var onBottomSheetClose = (0, _react.useCallback)(function () {
      setShowError(false);
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: Object.assign({}, styles.container, {
        marginBottom: insets.bottom || 24
      }),
      children: [hideTitle ? null : (0, _jsxRuntime.jsx)(_text.Text, {
        style: [styles.signinWithText, titleStyle],
        tx: titleTx,
        children: isLoginFlow ? "Sign in with" : "Sign up with"
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [styles.socialProviderContainer, socialProviderContainer],
        children: [showGoogle && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          testID: `Button_Login_Google`,
          accessibilityLabel: `Button_Login_Google`,
          onPress: function onPress() {
            return handleSocialLogin(_constants.SOCIAL_PROVIDER.GOOGLE);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.iconContainer,
            children: [(0, _jsxRuntime.jsx)(_icons.SocialGoogle, {}), showProviderName && (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.providerName,
              tx: "loginManagement.google"
            })]
          })
        }), showFacebook && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          testID: `Button_Login_Facebook`,
          accessibilityLabel: `Button_Login_Facebook`,
          onPress: function onPress() {
            return handleSocialLogin(_constants.SOCIAL_PROVIDER.FACEBOOK);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.iconContainer,
            children: [(0, _jsxRuntime.jsx)(_icons.SocialFacebook, {}), showProviderName && (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.providerName,
              tx: "loginManagement.facebook"
            })]
          })
        }), showApple && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          testID: `Button_Login_Apple`,
          accessibilityLabel: `Button_Login_Apple`,
          onPress: function onPress() {
            return handleSocialLogin(_constants.SOCIAL_PROVIDER.APPLE);
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.iconContainer,
            children: [isWhiteIcon ? (0, _jsxRuntime.jsx)(_icons.SocialAppleWhite, {}) : (0, _jsxRuntime.jsx)(_icons.SocialApple, {}), showProviderName && (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.providerName,
              tx: "loginManagement.apple"
            })]
          })
        })]
      }), (0, _jsxRuntime.jsx)(_bottomErrorCommon.default, {
        isVisible: showError,
        close: onBottomSheetClose
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: 24
    },
    providerName: Object.assign({}, _text.presets.textLink, {
      marginStart: 4
    }),
    iconContainer: {
      flexDirection: "row",
      alignItems: "center",
      gap: 0
    },
    signinWithText: Object.assign({}, _text.newPresets.bodyTextRegular, {
      fontSize: 14,
      lineHeight: 18,
      color: "#454545",
      marginBottom: 12,
      textAlign: "center"
    }),
    socialProviderContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      gap: 16
    }
  });
