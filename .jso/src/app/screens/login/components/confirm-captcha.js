  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _googleRecaptcha = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var ConfirmCaptcha = (0, _react.forwardRef)(function (_ref, ref) {
    var siteKey = _ref.siteKey,
      baseUrl = _ref.baseUrl,
      onMessage = _ref.onMessage,
      clearTimeout = _ref.clearTimeout;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showCaptcha = _useState2[0],
      setShowCaptcha = _useState2[1];
    var show = (0, _react.useCallback)(function () {
      setShowCaptcha(true);
    }, []);
    var hide = (0, _react.useCallback)(function () {
      setShowCaptcha(false);
      if (clearTimeout) {
        clearTimeout();
      }
    }, [clearTimeout]);
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        show: show,
        hide: hide
      };
    }, []);
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      useNativeDriver: true,
      hideModalContentWhileAnimating: true,
      deviceHeight: height,
      deviceWidth: width,
      style: styles.modal,
      animationIn: "fadeIn",
      animationOut: "fadeOut",
      isVisible: showCaptcha,
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.wrapper,
        children: (0, _jsxRuntime.jsx)(_googleRecaptcha.default, {
          onClose: hide,
          url: baseUrl,
          siteKey: siteKey,
          onMessage: onMessage
        })
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    text: {
      fontSize: 15,
      fontWeight: "bold",
      color: "#fff",
      textAlign: "center",
      marginTop: 10
    },
    modal: {
      margin: 0
    },
    wrapper: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.01)",
      justifyContent: "center",
      overflow: "hidden"
    }
  });
  var _default = exports.default = ConfirmCaptcha;
