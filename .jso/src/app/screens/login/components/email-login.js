  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = EmailLogin;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function EmailLogin(_ref) {
    var pressNotYou = _ref.pressNotYou,
      textDescription = _ref.textDescription,
      maskedMail = _ref.maskedMail;
    var length = maskedMail.length;
    if (length <= 17) return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          flexDirection: "row"
        },
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _nativeLoginStyle.default.textResetLink,
          tx: textDescription
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: Object.assign({}, _nativeLoginStyle.default.textEnterMail),
          text: maskedMail
        })]
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "nativeLoginScreen.loginPage.notYou",
        style: Object.assign({}, _nativeLoginStyle.default.textEnterMail, {
          paddingTop: 0,
          color: _theme.color.palette.gradientColor1Start
        }),
        onPress: pressNotYou
      })]
    });
    if (length > 17 && length <= 30) return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: _nativeLoginStyle.default.textResetLink,
        tx: textDescription
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: {
          flexDirection: "row"
        },
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: Object.assign({}, _nativeLoginStyle.default.textEnterMail, {
            paddingTop: 0
          }),
          text: `${maskedMail} `
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "nativeLoginScreen.loginPage.notYou",
          style: Object.assign({}, _nativeLoginStyle.default.textEnterMail, {
            paddingTop: 0,
            color: _theme.color.palette.gradientColor1Start
          }),
          onPress: pressNotYou
        })]
      })]
    });
    if (length > 30) return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        style: _nativeLoginStyle.default.textResetLink,
        tx: textDescription
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: Object.assign({}, _nativeLoginStyle.default.textEnterMail, {
          paddingTop: 0
        }),
        text: `${maskedMail}`
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "nativeLoginScreen.loginPage.notYou",
        style: Object.assign({}, _nativeLoginStyle.default.textEnterMail, {
          paddingTop: 0,
          color: _theme.color.palette.gradientColor1Start
        }),
        onPress: pressNotYou
      })]
    });
  }
