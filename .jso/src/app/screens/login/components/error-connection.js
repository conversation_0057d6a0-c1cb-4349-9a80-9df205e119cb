  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = ErrorConnection;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function ErrorConnection(_ref) {
    var retryNetwork = _ref.retryNetwork,
      onClose = _ref.onClose;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _nativeLoginStyle.default.dismissIcon,
        children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          onPress: onClose,
          children: (0, _jsxRuntime.jsx)(_icons.CrossBlue, {})
        })
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        hideScreenHeader: false,
        visible: true,
        onReload: retryNetwork,
        noInternetOverlayStyle: _nativeLoginStyle.default.overlayStyle
      })]
    });
  }
