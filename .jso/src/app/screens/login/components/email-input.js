  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.isValidEmail = exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[3]);
  var _inputField = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _$$_REQUIRE(_dependencyMap[5]);
  var _nativeLoginStyle = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _viewShadowWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var isValidEmail = exports.isValidEmail = function isValidEmail(email) {
    var regex = /^(?!.*@(moosbay\.com|my2ducks\.com)$)(?=(.{1,64}@.{1,255}))([!#$%&\+\'\-\/=?\^_{|}~a-zA-Z0-9]+(\.[!#$%&\'\-\/=?\^_{|}~a-zA-Z0-9]+){0,})@((\[(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}\])|([a-zA-Z0-9-]{1,63}(\.[a-zA-Z0-9-]{2,63}){1,}))$/;
    return regex.test(email);
  };
  var EmailInput = (0, _react.forwardRef)(function (_ref, ref) {
    var email = _ref.email,
      setEmail = _ref.setEmail,
      onSubmitEdit = _ref.onSubmitEdit;
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      errorEmail = _useState2[0],
      setErrorEmail = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFirstFocus = _useState4[0],
      setIsFirstFocus = _useState4[1];
    var internalRef = (0, _react.useRef)(null);
    var inputRef = ref || internalRef;
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isFocused = _useState6[0],
      setIsFocused = _useState6[1];
    var onBlurInput = function onBlurInput() {
      if (email != null && email.length) {
        setEmail(email.toLowerCase());
      }
      setIsFocused(false);
      handleMailError();
      if (isFirstFocus) setIsFirstFocus(false);
    };
    var handleMailError = (0, _react.useCallback)(function () {
      if (email.length === 0) {
        setErrorEmail("nativeLoginScreen.emptyEmailError");
      } else if (!isValidEmail(email)) {
        setErrorEmail("nativeLoginScreen.invalidEmailError");
      } else {
        setErrorEmail("");
      }
    }, [email]);
    (0, _react.useEffect)(function () {
      if (!isFirstFocus) {
        handleMailError();
      }
    }, [email, isFirstFocus]);
    var submitEdit = function submitEdit() {
      onSubmitEdit == null || onSubmitEdit();
    };
    return (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
      isInvalid: isFirstFocus ? false : !isValidEmail(email) || !email,
      labelTx: "nativeLoginScreen.emailAddress",
      helpTextTx: errorEmail,
      style: _nativeLoginStyle.default.inputEmail,
      numberOfLinesError: 1,
      children: (0, _jsxRuntime.jsx)(_viewShadowWrap.default, {
        isFocused: isFocused,
        isInvalid: isFirstFocus ? false : !isValidEmail(email) || !email,
        children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
          forwardedRef: inputRef,
          isInvalid: isFirstFocus ? false : !isValidEmail(email) || !email,
          onChangeText: setEmail,
          value: email,
          onBlur: onBlurInput,
          onSubmitEditing: submitEdit,
          onFocus: function onFocus() {
            return setIsFocused(true);
          },
          textContentType: "emailAddress",
          highlightOnFocused: true,
          autoComplete: "email",
          inputMode: "email",
          autoCapitalize: "none",
          enterKeyHint: "next",
          autoCorrect: false,
          cursorColor: _theme.color.palette.overlayColor,
          selectionColor: _reactNative.Platform.select({
            ios: _theme.color.palette.overlayColor,
            android: _theme.color.palette.lightestIrisBlue
          })
        })
      })
    });
  });
  var _default = exports.default = EmailInput;
