  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: {
      flex: 1,
      backgroundColor: _theme.color.palette.lightestGrey
    },
    headerBackground: {
      top: 0,
      left: 0,
      position: "absolute"
    },
    headerBackgroundImage: {
      borderBottomLeftRadius: 35,
      height: 132,
      position: "absolute",
      width: "100%"
    },
    errorCustomBackButton: {
      marginLeft: -8
    },
    bodyContent: {
      padding: 24
    },
    cardWrapper: Object.assign({
      borderRadius: 16,
      marginBottom: 12,
      paddingBottom: 24,
      boxShadow: "rgba(50, 25, 107, 0.4)",
      backgroundColor: _theme.color.palette.whiteGrey
    }, _theme.shadow.primaryShadow),
    cardSectionTitle: Object.assign({
      marginBottom: 16,
      marginTop: 38,
      textTransform: "uppercase"
    }, _text.presets.h4, {
      lineHeight: 22,
      color: _theme.color.palette.almostBlackGrey
    }),
    cardTitle: {
      paddingTop: 16,
      paddingLeft: 12,
      paddingRight: 26,
      paddingBottom: 4,
      flexDirection: "row"
    },
    cardTitleIcon: {
      width: 20,
      height: 20,
      marginRight: 12
    },
    cardToggleIcon: {
      top: 12,
      right: 16,
      position: "absolute"
    },
    cardTitleLabel: Object.assign({}, _text.presets.bodyTextBold, {
      flex: 1,
      lineHeight: 20,
      marginRight: 24,
      color: _theme.color.palette.almostBlackGrey
    }),
    cardTitleDetail: {
      marginLeft: 44
    },
    cardTitleDetailLabel: Object.assign({}, _text.presets.caption1Bold, {
      textAlign: "left",
      color: _theme.color.palette.darkestGrey
    }),
    cardBoldText: Object.assign({}, _text.presets.caption1Bold, {
      textAlign: "left",
      color: _theme.color.palette.almostBlackGrey
    }),
    cardNormalText: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      fontSize: 14,
      lineHeight: 18
    }),
    cardOrderInfo: {
      marginBottom: 8,
      flexDirection: "row",
      justifyContent: "space-between"
    },
    cardOrderId: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    cardOrderInfoSubTotal: {
      flexDirection: "row",
      justifyContent: "space-between"
    },
    cardOrderInfoTotal: Object.assign({}, _text.presets.caption1Bold, {
      fontSize: 16,
      lineHeight: 20,
      color: _theme.color.palette.almostBlackGrey
    }),
    cardListElementWrapper: {
      marginTop: 16,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: _theme.color.palette.lighterGrey
    },
    cardContentWrapper: {
      paddingHorizontal: 24,
      borderRadius: 16
    },
    headerCardTitleWrapper: {
      marginTop: 24,
      flexDirection: "row"
    },
    headerCardTitleIcon: {
      width: 40,
      height: 40,
      marginRight: 8,
      borderRadius: 6
    },
    headerCardRight: {
      flex: 1
    },
    headerCardTitle: {
      flex: 1,
      flexDirection: "row",
      justifyContent: "space-between"
    },
    headerCardTitleText: Object.assign({}, _text.presets.bodyTextBold, {
      fontSize: 18,
      lineHeight: 22,
      color: _theme.color.palette.almostBlackGrey
    }),
    headerCardOrderId: Object.assign({
      marginTop: 4
    }, _text.presets.bodyTextBlackRegular, {
      lineHeight: 20
    }),
    headerCardOrderInfo: {
      marginTop: 18,
      flexDirection: "row"
    },
    headerCardOrderInfoItem: {
      width: "50%"
    },
    orderInfoTitle: Object.assign({}, _text.presets.caption2Bold, {
      fontSize: 12,
      color: _theme.color.palette.darkGrey999
    }),
    orderInfoDetail: Object.assign({
      marginTop: 4
    }, _text.presets.bodyTextBlackRegular, {
      lineHeight: 20
    }),
    orderedDetailItemNamePrefix: Object.assign({}, _text.presets.caption1Bold, {
      textAlign: "left",
      color: _theme.color.palette.almostBlackGrey
    }),
    flightCardTitleDetail: {
      marginTop: 16,
      marginBottom: 16,
      flexDirection: "row",
      alignItems: "center"
    },
    flightCardTitleDetailIcon: {
      width: 16,
      height: 16,
      marginRight: 8,
      color: _theme.color.palette.lightPurple
    },
    flightCardTitleDetailLocation: Object.assign({}, _text.presets.caption1Bold, {
      flex: 1,
      marginRight: 24,
      textAlign: "left",
      color: _theme.color.palette.lightPurple
    }),
    flightOrderedDetailItemUppercaseTitle: Object.assign({
      marginBottom: 8
    }, _text.presets.caption1Regular, {
      textAlign: "left",
      textTransform: "uppercase",
      color: _theme.color.palette.almostBlackGrey
    }),
    flightOrderedDetailItemTitle: Object.assign({
      marginBottom: 8
    }, _text.presets.caption1Regular, {
      textAlign: "left",
      color: _theme.color.palette.almostBlackGrey
    }),
    flightOrderedDetailItemDetailNormal: Object.assign({
      marginTop: 8
    }, _text.presets.caption1Regular, {
      fontSize: 12,
      lineHeight: 16,
      color: _theme.color.palette.almostBlackGrey
    }),
    flightOrderedDetailItemDetailBold: Object.assign({}, _text.presets.caption1Bold, {
      marginTop: 8,
      color: _theme.color.palette.almostBlackGrey
    }),
    flightOrderedDetailItemSubTotal: Object.assign({}, _text.presets.caption1Bold, {
      flexDirection: "row",
      justifyContent: "space-between"
    }),
    deliverCardTitleDetail: Object.assign({
      marginTop: 12,
      marginBottom: 16
    }, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    deliverCardSubTitleWrapper: {
      marginTop: 24,
      marginBottom: 24
    },
    deliverCardTrackShipmentWrapper: {
      flexDirection: "row",
      justifyContent: "space-between"
    },
    deliverCardTrackShipmentLabel: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    deliverCardSubTitleDetail: Object.assign({
      marginTop: 4
    }, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    orderTotalWrapper: {
      paddingHorizontal: 24,
      paddingTop: 24
    },
    orderTotalTitle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    orderTotalTitleBold: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    orderTotalTitleNormal: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    orderTotalMain: {
      marginBottom: 16,
      flexDirection: "row",
      justifyContent: "space-between"
    },
    orderTotalDetail: {
      marginTop: 8,
      flexDirection: "row",
      justifyContent: "space-between"
    },
    orderTotalDetailBold: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    orderTotalDetailNormal: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    changiRewardsPointsEarned: {
      padding: 12,
      marginTop: 12,
      marginLeft: -12,
      marginRight: -12,
      borderRadius: 12,
      flexDirection: "row",
      backgroundColor: _theme.color.palette.lightestGrey
    },
    changiRewardsPointsEarnedLeft: Object.assign({
      width: "50%"
    }, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    changiRewardsPointsEarnedRight: {
      width: "50%",
      flexDirection: "row",
      justifyContent: "flex-end"
    },
    changiRewardsPointsEarnedIcon: {
      width: 18,
      height: 18,
      marginRight: 4
    },
    helpQuestion: Object.assign({
      marginTop: 12
    }, _text.presets.textLink, {
      textAlign: "center",
      color: _theme.color.palette.black
    }),
    contactButton: {
      marginBottom: 130
    },
    contactButtonLabel: Object.assign({}, _text.presets.textLink, {
      textAlign: "center"
    })
  });
