  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeWebview = _$$_REQUIRE(_dependencyMap[8]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[10]);
  var _renderSourceWebView = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[12]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _termsOfUsePolicy = _$$_REQUIRE(_dependencyMap[14]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[15]);
  var _theme = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "TermsOfUsePolicy";
  function TermsOfUsePolicy() {
    var _renderSourceHTML;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var termsOfUsePolicy = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.TERM_OF_USE_PAGE));
    var loading = (0, _lodash.get)(termsOfUsePolicy, "loading");
    var error = (0, _lodash.get)(termsOfUsePolicy, "error");
    var title = (0, _renderSourceWebView.transformTitle)((0, _lodash.get)(termsOfUsePolicy, "data.title", ""));
    var description = (0, _lodash.get)(termsOfUsePolicy, "data.description");
    var sourceHTML = {
      html: (_renderSourceHTML = (0, _renderSourceWebView.default)(title + description)) == null || (_renderSourceHTML = _renderSourceHTML.html) == null ? undefined : _renderSourceHTML.replace("</style>", `body{background-color: ${_theme.color.palette.lightestGrey}}</style>`)
    };
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_TermsOfUsePolicy");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_TermsOfUsePolicy", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          } else {
            dispatch(_aemRedux.default.getAemConfigData({
              name: _aemRedux.AEM_PAGE_NAME.TERM_OF_USE_PAGE,
              pathName: "getTermsOfUsePolicy"
            }));
          }
        });
        return function checkInternet() {
          return _ref.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    var handleReload = function handleReload() {
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.TERM_OF_USE_PAGE,
        pathName: "getTermsOfUsePolicy"
      }));
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
        visible: loading || false
      }), (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
        style: _termsOfUsePolicy.styles.container,
        showsVerticalScrollIndicator: false,
        testID: `${SCREEN_NAME}__ScrollView`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
        contentContainerStyle: _termsOfUsePolicy.styles.contentContainerStyle,
        children: description && (0, _jsxRuntime.jsx)(_reactNativeWebview.WebView, {
          containerStyle: _termsOfUsePolicy.styles.containerStyleWebview,
          source: sourceHTML,
          originWhitelist: ["*"],
          textZoom: 200,
          style: _termsOfUsePolicy.styles.containerWebview,
          automaticallyAdjustContentInsets: false,
          showsVerticalScrollIndicator: false,
          scrollEnabled: true,
          showsHorizontalScrollIndicator: false,
          javaScriptEnabled: true
        })
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        hideScreenHeader: false,
        headerTx: "errorOverlay.header",
        headerBackgroundColor: "transparent",
        visible: error,
        onReload: handleReload,
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        headerBackgroundColor: "transparent",
        visible: isNoConnection,
        onBack: function onBack() {
          return navigation.goBack();
        },
        testID: `${SCREEN_NAME}ErrorOverlayNoConnection`,
        onReload: handleReload
      })]
    });
  }
  var _default = exports.default = TermsOfUsePolicy;
