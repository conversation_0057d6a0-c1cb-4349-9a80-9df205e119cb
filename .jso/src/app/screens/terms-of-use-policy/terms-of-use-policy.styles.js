  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingHorizontal: 24
    },
    containerStyleWebview: {
      backgroundColor: _theme.color.palette.lightestGrey,
      height: 600,
      width: "100%"
    },
    containerWebview: {
      backgroundColor: _theme.color.palette.lightestGrey,
      opacity: 0.99,
      overflow: "hidden"
    },
    contentContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      height: "100%"
    }
  });
