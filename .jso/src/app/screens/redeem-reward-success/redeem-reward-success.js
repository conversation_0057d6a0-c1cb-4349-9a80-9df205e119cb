  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.RedeemRewardSuccessScreen = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _redeemSuccess = _$$_REQUIRE(_dependencyMap[3]);
  var _exploreMoreRewards = _$$_REQUIRE(_dependencyMap[4]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "RedeemRewardSuccess";
  var RedeemRewardSuccessScreen = exports.RedeemRewardSuccessScreen = function RedeemRewardSuccessScreen(props) {
    var _props$route, _props$route2;
    var isKrisflyer = (props == null || (_props$route = props.route) == null || (_props$route = _props$route.params) == null ? undefined : _props$route.isKrisflyer) || false;
    var isPromoCode = (props == null || (_props$route2 = props.route) == null || (_props$route2 = _props$route2.params) == null ? undefined : _props$route2.isPromoCode) || false;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.containerStyle,
      children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        showsVerticalScrollIndicator: false,
        style: styles.scrollViewStyle,
        testID: `${SCREEN_NAME}__ScrollView`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
        children: [(0, _jsxRuntime.jsx)(_redeemSuccess.RedeemSuccessSection, {
          isKrisflyer: isKrisflyer,
          isPromoCode: isPromoCode,
          testID: `${SCREEN_NAME}__RedeemSuccessSection`,
          accessibilityLabel: `${SCREEN_NAME}__RedeemSuccessSection`
        }), (0, _jsxRuntime.jsx)(_exploreMoreRewards.ExploreMoreRewards, {
          title: "redeemRewardSuccess.exploreMoreRewards",
          testID: `${SCREEN_NAME}__ExploreMoreRewards`,
          accessibilityLabel: `${SCREEN_NAME}__ExploreMoreRewards`
        })]
      })
    });
  };
