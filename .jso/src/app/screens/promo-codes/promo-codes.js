  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _promoCodes = _$$_REQUIRE(_dependencyMap[2]);
  var _loadingTiles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _promoCodes2 = _$$_REQUIRE(_dependencyMap[4]);
  var _emptyContent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _errorOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _promoCodeItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _promoCodes3 = _$$_REQUIRE(_dependencyMap[8]);
  var _react = _$$_REQUIRE(_dependencyMap[9]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var PromoCodesScreen = function PromoCodesScreen(props) {
    var navigation = props.navigation;
    var _usePromoCodesData = (0, _promoCodes2.usePromoCodesData)({
        navigation: navigation
      }),
      fetchPromoCodeDetails = _usePromoCodesData.fetchPromoCodeDetails,
      fetchPromoCodeList = _usePromoCodesData.fetchPromoCodeList,
      isFetching = _usePromoCodesData.isFetching,
      isError = _usePromoCodesData.isError,
      payload = _usePromoCodesData.payload;
    var copiedToastRef = (0, _react.useRef)(null);
    var isLoading = isFetching;
    var isEmptyList = !isFetching && !isError && !(payload != null && payload.length);
    var isServiceError = !isFetching && isError;
    if (isLoading) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _promoCodes.styles.containerStyle,
        children: (0, _jsxRuntime.jsx)(_loadingTiles.default, {})
      });
    }
    if (isServiceError) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _promoCodes.styles.containerStyle,
        children: (0, _jsxRuntime.jsx)(_errorOverlay.default, {
          onReload: fetchPromoCodeList
        })
      });
    }
    if (isEmptyList) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _promoCodes.styles.containerStyle,
        children: (0, _jsxRuntime.jsx)(_emptyContent.default, {})
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: payload,
        keyExtractor: function keyExtractor(_item, index) {
          return `${_promoCodes3.COMPONENT_NAME}__PromoCodeItem__${index + 1}`;
        },
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return (0, _jsxRuntime.jsx)(_promoCodeItem.default, {
            copiedToastRef: copiedToastRef,
            dataLength: payload == null ? undefined : payload.length,
            fetchPromoCodeDetails: fetchPromoCodeDetails,
            index: index,
            item: item
          });
        },
        showsVerticalScrollIndicator: false,
        style: _promoCodes.styles.containerStyle
      }), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: copiedToastRef,
        style: _promoCodes.styles.feedBackToastStyle,
        position: "custom",
        textStyle: _promoCodes.styles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (0, _i18n.translate)("promoCodes.toast.copied")
      })]
    });
  };
  var _default = exports.default = PromoCodesScreen;
