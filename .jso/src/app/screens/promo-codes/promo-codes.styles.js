  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenHeight = _Dimensions$get.height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      minHeight: screenHeight
    },
    feedBackToastStyle: {
      bottom: 40
    },
    toastTextStyle: Object.assign({}, _text.newPresets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    })
  });
