  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _promoCodes = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeDashedLine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[9]);
  var _react = _$$_REQUIRE(_dependencyMap[10]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[11]);
  var _enum = _$$_REQUIRE(_dependencyMap[12]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[13]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[14]);
  var _clipboard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _palette = _$$_REQUIRE(_dependencyMap[16]);
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  var PromoCodeItem = function PromoCodeItem(props) {
    var copiedToastRef = props.copiedToastRef,
      dataLength = props.dataLength,
      fetchPromoCodeDetails = props.fetchPromoCodeDetails,
      index = props.index,
      item = props.item;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("PROMO_CODE_ITEM"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      currentTier = _useRewardTier.currentTier;
    var isFirstItem = index === 0;
    var isLastItem = index === dataLength - 1;
    var PromoIcon = (0, _react.useMemo)(function () {
      switch (item == null ? undefined : item.promoType) {
        case _promoCodes.PromoCodeTypeEnum.ISC:
          return _icons.ISCLogo;
        case _promoCodes.PromoCodeTypeEnum.CR:
          switch (currentTier) {
            case _enum.Tier.Member:
            case _enum.Tier.StaffMember:
              return _icons.ChangiRewardsPrivilegesMemberIcon;
            case _enum.Tier.Gold:
            case _enum.Tier.StaffGold:
              return _icons.ChangiRewardsPrivilegesGoldIcon;
            case _enum.Tier.Platinum:
            case _enum.Tier.StaffPlatinum:
              return _icons.ChangiRewardsPrivilegesPlatinumIcon;
            case _enum.Tier.Monarch:
            case _enum.Tier.StaffMonarch:
              return _icons.ChangiRewardsPrivilegesMonarchIcon;
            default:
              return _icons.ChangiRewardsPrivilegesMemberIcon;
            // Default to Member icon if tier is not recognized
          }
        default:
          return _icons.ISCLogo;
        // Default to ISCLogo if type is not recognized
      }
    }, [currentTier, JSON.stringify(item)]);
    var onPressPromoCodeItem = function onPressPromoCodeItem() {
      var campaignNameValue = (item == null ? undefined : item.campaignName) || "null";
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppPromoCodes, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppPromoCodes, `${item == null ? undefined : item.title} | ${campaignNameValue}`));
      if ((item == null ? undefined : item.promoType) === _promoCodes.PromoCodeTypeEnum.ISC) {
        handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.ishopchangiPromoCode, {
          redirect: item == null ? undefined : item.cta
        });
      } else if ((item == null ? undefined : item.promoType) === _promoCodes.PromoCodeTypeEnum.CR) {
        fetchPromoCodeDetails == null || fetchPromoCodeDetails(item);
      }
    };
    var onPressCopyPromoCode = function onPressCopyPromoCode() {
      var _copiedToastRef$curre;
      _clipboard.default.setString(item == null ? undefined : item.promoCode);
      copiedToastRef == null || (_copiedToastRef$curre = copiedToastRef.current) == null || _copiedToastRef$curre.show == null || _copiedToastRef$curre.show();
    };
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}`,
      onPress: onPressPromoCodeItem,
      style: [styles.containerStyle, isFirstItem && styles.firstItemContainerStyle, isLastItem && styles.lastItemContainerStyle],
      testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}`,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__Icon`,
        style: styles.iconContainerStyle,
        testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__Icon`,
        children: (0, _jsxRuntime.jsx)(PromoIcon, {
          height: 32,
          width: 32
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.infoSectionContainerStyle,
          children: [(item == null ? undefined : item.campaignName) && (0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__CampaignName`,
            style: styles.campaignNameTextStyle,
            testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__CampaignName`,
            text: item == null ? undefined : item.campaignName
          }), (item == null ? undefined : item.title) && (0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__Title`,
            style: styles.titleTextStyle,
            testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__Title`,
            text: item == null ? undefined : item.title
          }), (item == null ? undefined : item.validityPeriod) && (0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__ValidityPeriod`,
            style: styles.validityPeriodTextStyle,
            testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__ValidityPeriod`,
            text: item == null ? undefined : item.validityPeriod
          }), (item == null ? undefined : item.description) && (0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__Description`,
            style: styles.descriptionTextStyle,
            testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__Description`,
            text: item == null ? undefined : item.description
          })]
        }), (item == null ? undefined : item.promoCode) && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_reactNativeDashedLine.default, {
            dashGap: 4,
            dashThickness: 1,
            dashLength: 4,
            dashColor: _theme.color.palette.lighterGrey,
            style: {
              marginVertical: 12
            }
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.promoCodeContainerStyle,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.Pressable, {
              style: {
                flex: 1
              },
              onPress: function onPress() {},
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.promoCodeLabelTextStyle,
                tx: "promoCodes.promoCodeLabel"
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCode`,
                numberOfLines: 1,
                style: styles.promoCodeValueTextStyle,
                testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCode`,
                text: item == null ? undefined : item.promoCode
              })]
            }), (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
              accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCodeCopyBtn`,
              onPress: onPressCopyPromoCode,
              style: styles.promoCodeCopyBtnStyle,
              testID: `${_promoCodes.COMPONENT_NAME}__PromoCodeItem__${index + 1}__PromoCodeCopyBtn`,
              children: (0, _jsxRuntime.jsx)(_icons.Copy, {
                height: 16,
                width: 16
              })
            })]
          })]
        })]
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "stretch",
      flexDirection: "row",
      gap: 1,
      marginBottom: 12,
      marginHorizontal: 16
    },
    firstItemContainerStyle: {
      marginTop: 24
    },
    lastItemContainerStyle: {
      marginBottom: 200
    },
    iconContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 8,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 8,
      justifyContent: "center",
      paddingHorizontal: 12,
      elevation: 2,
      shadowColor: _palette.palette.almostBlackGrey,
      shadowOpacity: 0.15,
      shadowRadius: 3,
      shadowOffset: {
        width: -1.5,
        height: 1
      },
      marginRight: 0.5
    },
    contentContainerStyle: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 12,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 12,
      padding: 12,
      elevation: 2,
      shadowColor: _palette.palette.almostBlackGrey,
      shadowOpacity: 0.15,
      shadowRadius: 3,
      shadowOffset: {
        width: 1.5,
        height: 1
      }
    },
    infoSectionContainerStyle: {
      flex: 1,
      gap: 4
    },
    campaignNameTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.lightPurple,
      textTransform: "none"
    }),
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    validityPeriodTextStyle: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption2Regular),
    promoCodeContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      gap: 4,
      justifyContent: "space-between"
    },
    promoCodeLabelTextStyle: Object.assign({}, _text.newPresets.XSmallRegular, {
      color: _theme.color.palette.darkestGrey
    }),
    promoCodeValueTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 0.6
    }),
    promoCodeCopyBtnStyle: {
      alignItems: "center",
      borderColor: _theme.color.palette.purpleD5BBEA,
      borderRadius: 60,
      borderWidth: 1,
      height: 28,
      justifyContent: "center",
      paddingHorizontal: 10
    }
  });
  var _default = exports.default = PromoCodeItem;
