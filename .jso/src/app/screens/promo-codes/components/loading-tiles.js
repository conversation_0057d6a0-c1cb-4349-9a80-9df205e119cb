  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _promoCodes = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var LoadingTiles = function LoadingTiles() {
    return Array.from({
      length: 5
    }).map(function (_item, index) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: [styles.tileContainerStyle, index === 0 && styles.firstTileContainerStyle],
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.imgContainerStyle,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: styles.loadingImgStyle
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.contentContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: styles.loadingTitleStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: styles.loadingDescriptionStyle
          })]
        })]
      }, `${_promoCodes.COMPONENT_NAME}_LoadingTile_${index + 1}`);
    });
  };
  var LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.palette.lightestGrey, _theme.color.palette.lighterGrey];
  var styles = _reactNative.StyleSheet.create({
    tileContainerStyle: {
      alignItems: "stretch",
      flexDirection: "row",
      gap: 1,
      marginBottom: 12,
      marginHorizontal: 16
    },
    firstTileContainerStyle: {
      marginTop: 24
    },
    imgContainerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 8,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 15
    }, _theme.shadow.primaryShadow, {
      elevation: 2
    }),
    loadingImgStyle: {
      height: 60,
      width: 90
    },
    contentContainerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 12,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 12,
      flex: 1,
      gap: 4,
      justifyContent: "center",
      paddingHorizontal: 12
    }, _theme.shadow.primaryShadow, {
      elevation: 2
    }),
    loadingTitleStyle: {
      borderRadius: 4,
      height: 12,
      width: 74
    },
    loadingDescriptionStyle: {
      borderRadius: 4,
      height: 12,
      width: "100%"
    }
  });
  var _default = exports.default = LoadingTiles;
