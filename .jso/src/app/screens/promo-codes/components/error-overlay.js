  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[3]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[4]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _i18n = _$$_REQUIRE(_dependencyMap[7]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[12]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _promoCodes = _$$_REQUIRE(_dependencyMap[15]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[16]);
  var _react = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _errorOverlayVariant = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  var ErrorOverlay = function ErrorOverlay(props) {
    var onReload = props.onReload;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setIsConnected = _useState2[1];
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var errorPage = errorData != null && errorData.length ? errorData.find(function (el) {
      return el.code === _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL;
    }) : null;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ACCOUNT_PROMO_CODES_ERROR_OVERLAY"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var onPressCTAButton = function onPressCTAButton() {
      var _ref = (errorPage == null ? undefined : errorPage.navigationFirst) || {},
        type = _ref.type,
        value = _ref.value;
      if (type && value) {
        handleNavigation(type, value);
        return;
      }
      onReload == null || onReload();
    };
    var onPressRetryConnection = function onPressRetryConnection() {
      _netinfo.default.fetch().then(function (state) {
        setIsConnected(state == null ? undefined : state.isConnected);
        if (state != null && state.isConnected) {
          onReload == null || onReload();
        }
      });
    };
    (0, _react.useEffect)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        setIsConnected(isConnected);
      })();
    }, []);
    if (!isConnected) {
      return (0, _jsxRuntime.jsx)(_errorOverlayVariant.ErrorOverlayVariant3, {
        accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__ErrorOverlay__NoInternet`,
        containerStyle: styles.noInternetOverlayStyle,
        onReload: onPressRetryConnection,
        reload: true,
        testID: `${_promoCodes.COMPONENT_NAME}__ErrorOverlay__NoInternet`
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [errorPage != null && errorPage.icon ? (0, _jsxRuntime.jsx)(_baseImage.default, {
        resizeMode: "contain",
        source: {
          uri: (0, _utils.mappingUrlAem)(errorPage == null ? undefined : errorPage.icon)
        },
        style: styles.iconStyle
      }) : (0, _jsxRuntime.jsx)(_icons.ErrorCloud, {}), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.titleTextStyle,
        text: (errorPage == null ? undefined : errorPage.header) || (0, _i18n.translate)("screenError.oop")
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.descriptionTextStyle,
        text: (errorPage == null ? undefined : errorPage.subHeader) || (0, _i18n.translate)("screenError.somethingWrong")
      }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
        end: {
          x: 1,
          y: 0
        },
        start: {
          x: 0,
          y: 0
        },
        style: styles.ctaBtnStyle,
        children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          accessibilityLabel: `${_promoCodes.COMPONENT_NAME}__ErrorOverlay__CTABtn`,
          onPress: onPressCTAButton,
          testID: `${_promoCodes.COMPONENT_NAME}__ErrorOverlay__CTABtn`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.ctaBtnLabelTextStyle,
            text: (errorPage == null ? undefined : errorPage.buttonLabel) || (0, _i18n.translate)("screenError.reload")
          })
        })
      })]
    });
  };
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenHeight = _Dimensions$get.height,
    screenWidth = _Dimensions$get.width;
  var ERROR_OVERLAY_MARGIN_TOP = screenHeight * 80 / 2622; // Iphone 16 pro's resolute height is 2622px
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      marginTop: ERROR_OVERLAY_MARGIN_TOP,
      paddingHorizontal: 24
    },
    iconStyle: {
      height: screenWidth * 200 / 375,
      marginBottom: 24,
      width: screenWidth * 200 / 375
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bold, {
      fontSize: 24,
      letterSpacing: 0.6,
      lineHeight: 28,
      marginBottom: 16,
      textAlign: "center"
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 24,
      textAlign: "center"
    }),
    ctaBtnStyle: {
      alignItems: "center",
      borderRadius: 60,
      height: 44,
      justifyContent: "center",
      width: "100%"
    },
    ctaBtnLabelTextStyle: Object.assign({}, _text.newPresets.bold, {
      color: _theme.color.palette.almostWhiteGrey,
      fontSize: 16,
      lineHeight: 24
    }),
    noInternetOverlayStyle: {
      flex: 0,
      marginTop: 40 + ERROR_OVERLAY_MARGIN_TOP,
      position: "relative"
    }
  });
  var _default = exports.default = ErrorOverlay;
