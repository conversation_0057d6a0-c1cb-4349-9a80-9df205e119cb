  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _i18n = _$$_REQUIRE(_dependencyMap[3]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var EmptyContent = function EmptyContent() {
    var informativeCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var EHR40 = informativeCommon == null ? undefined : informativeCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR40";
    });
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        resizeMode: "contain",
        source: EHR40 != null && EHR40.icon ? {
          uri: (0, _utils.mappingUrlAem)(EHR40 == null ? undefined : EHR40.icon)
        } : _icons.NoPromoCode,
        style: styles.imageStyle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.titleTextStyle,
        text: (EHR40 == null ? undefined : EHR40.header) || (0, _i18n.translate)("promoCodes.emptyContent.title")
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.descriptionTextStyle,
        text: (EHR40 == null ? undefined : EHR40.subHeader) || (0, _i18n.translate)("promoCodes.emptyContent.description")
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      marginHorizontal: 24,
      marginTop: 60
    },
    imageStyle: {
      height: 120,
      width: 120,
      marginBottom: 16
    },
    titleTextStyle: Object.assign({}, _text.newPresets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginBottom: 8
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      textAlign: "center"
    })
  });
  var _default = exports.default = EmptyContent;
