  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.usePromoCodesData = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _services = _$$_REQUIRE(_dependencyMap[5]);
  var _queries = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[10]);
  var usePromoCodesData = exports.usePromoCodesData = function usePromoCodesData(props) {
    var navigation = props.navigation;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isFetching = _useState2[0],
      setIsFetching = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isError = _useState4[0],
      setIsError = _useState4[1];
    var _useState5 = (0, _react.useState)(null),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      payload = _useState6[0],
      setPayload = _useState6[1];
    var _useState7 = (0, _react.useState)(null),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      promoCodeDetails = _useState8[0],
      setPromoCodeDetails = _useState8[1];
    var profileData = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var fetchPromoCodeList = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _response$data;
          setIsFetching(true);
          setIsError(false);
          setPayload(null);
          var response = yield (0, _request.default)((0, _services.generateGraphQLRequestBody)({
            query: _queries.getPromoCodesQuery
          }));
          var resData = response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null ? undefined : _response$data.getPromoCodes;
          if (!resData) {
            setPayload(null);
            setIsError(true);
            return;
          } else {
            setPayload(resData == null ? undefined : resData.data);
          }
        } catch (_unused) {
          setPayload(null);
          setIsError(true);
        } finally {
          setIsFetching(false);
        }
      });
      return function fetchPromoCodeList() {
        return _ref.apply(this, arguments);
      };
    }();
    var fetchPromoCodeDetails = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (item) {
        try {
          var _response$data2;
          _globalLoadingController.default.showLoading(true);
          var response = yield (0, _request.default)((0, _services.generateGraphQLRequestBody)({
            graphqlInput: {
              input: {
                card_no: profileData == null ? undefined : profileData.cardNo,
                perk_type: _constants.PerkType.ChangiRewardPerk,
                voucher_type_code: item.voucherTypeCode,
                voucher_valid_from: item.startDate,
                voucher_valid_to: item.endDate
              }
            },
            query: _queries.getPromoCodeDetailsQuery
          }));
          var resData = response == null || (_response$data2 = response.data) == null || (_response$data2 = _response$data2.data) == null ? undefined : _response$data2.getPerkDetail_v2;
          if (resData) {
            setPromoCodeDetails(resData);
            return;
          } else {
            setPromoCodeDetails(null);
          }
        } catch (error) {
          setPromoCodeDetails(null);
        } finally {
          _globalLoadingController.default.hideLoading();
        }
      });
      return function fetchPromoCodeDetails(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      fetchPromoCodeList();
    }, []);
    (0, _react.useEffect)(function () {
      if (promoCodeDetails) {
        navigation.navigate(_constants.NavigationConstants.yourReward, {
          item: promoCodeDetails == null ? undefined : promoCodeDetails.changi_reward_detail,
          changi_reward_detail: promoCodeDetails == null ? undefined : promoCodeDetails.changi_reward_detail
        });
        setPromoCodeDetails(null); // Reset after navigation
      }
    }, [JSON.stringify(promoCodeDetails)]);
    return {
      fetchPromoCodeDetails: fetchPromoCodeDetails,
      fetchPromoCodeList: fetchPromoCodeList,
      isFetching: isFetching,
      isError: isError,
      payload: payload
    };
  };
