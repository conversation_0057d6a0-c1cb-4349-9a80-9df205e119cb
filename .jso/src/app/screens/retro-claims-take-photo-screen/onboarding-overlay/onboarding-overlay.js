  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _onboardingOverlay = _$$_REQUIRE(_dependencyMap[5]);
  var _onboardingOverlay2 = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[8]);
  var _button = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _theme = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[13]);
  var _react = _$$_REQUIRE(_dependencyMap[14]);
  var _storage = _$$_REQUIRE(_dependencyMap[15]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[18]);
  var _icons = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  var OnboardingOverlayPage = function OnboardingOverlayPage(_ref) {
    var setShowOnboarding = _ref.setShowOnboarding;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isGuideSkipped = _useState2[0],
      setIsGuideSkipped = _useState2[1];
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var handleToggleSkipGuide = (0, _react.useCallback)(/*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (value) {
        var skipGuideUsers = (yield (0, _storage.load)(_storage.StorageKey.retroClaimsSkipGuideUsers)) || [];
        var newUserList = new Set(skipGuideUsers);
        if (value) {
          newUserList.add(profilePayload == null ? undefined : profilePayload.email);
        } else {
          newUserList.delete(profilePayload == null ? undefined : profilePayload.email);
        }
        (0, _storage.save)(_storage.StorageKey.retroClaimsSkipGuideUsers, Array.from(newUserList));
        setIsGuideSkipped(value);
      });
      return function (_x) {
        return _ref2.apply(this, arguments);
      };
    }(), [profilePayload == null ? undefined : profilePayload.email]);
    var handleCloseOnboardingOverlay = function handleCloseOnboardingOverlay() {
      setShowOnboarding(false);
    };
    (0, _react.useEffect)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        var skipGuideUsers = (yield (0, _storage.load)(_storage.StorageKey.retroClaimsSkipGuideUsers)) || [];
        setIsGuideSkipped(skipGuideUsers.some(function (val) {
          return val === (profilePayload == null ? undefined : profilePayload.email);
        }));
      })();
    }, [profilePayload == null ? undefined : profilePayload.email]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      accessibilityLabel: `${_onboardingOverlay2.SCREEN_NAME}_PageContent`,
      style: _onboardingOverlay.styles.containerStyle,
      testID: `${_onboardingOverlay2.SCREEN_NAME}_PageContent`,
      children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
        onPress: handleCloseOnboardingOverlay,
        style: _onboardingOverlay.styles.closeBtnStyle,
        children: (0, _jsxRuntime.jsx)(_icons.Cross, {
          width: 30,
          height: 30,
          color: _theme.color.palette.darkestGrey
        })
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        accessibilityLabel: `${_onboardingOverlay2.SCREEN_NAME}_Title`,
        style: _onboardingOverlay.styles.titleTextStyle,
        testID: `${_onboardingOverlay2.SCREEN_NAME}_Title`,
        tx: "onboardingOverlayScreen.title"
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _onboardingOverlay.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          accessibilityLabel: `${_onboardingOverlay2.SCREEN_NAME}_Image_ReceiptExample`,
          source: _backgrounds.ReceiptExample,
          style: _onboardingOverlay.styles.receiptExampleImageStyle,
          testID: `${_onboardingOverlay2.SCREEN_NAME}_Image_ReceiptExample`
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _onboardingOverlay.styles.okBtnStyle,
          start: {
            x: 1,
            y: 0
          },
          end: {
            x: 0,
            y: 1
          },
          colors: [_theme.color.palette.lightPurple, _theme.color.palette.lightPurple],
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            accessibilityLabel: `${_onboardingOverlay2.SCREEN_NAME}__Btn_Ok`,
            backgroundPreset: "light",
            onPress: handleCloseOnboardingOverlay,
            sizePreset: "large",
            statePreset: "default",
            testID: `${_onboardingOverlay2.SCREEN_NAME}__Btn_Ok`,
            text: (0, _i18n.translate)("common.okay"),
            textPreset: "buttonLarge",
            typePreset: "secondary"
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _onboardingOverlay.styles.skipGuideInputContainerStyle,
          children: (0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
            value: isGuideSkipped,
            onToggle: handleToggleSkipGuide,
            testID: `${_onboardingOverlay2.SCREEN_NAME}__Checkbox__DoNotShowAgain`,
            accessibilityLabel: `${_onboardingOverlay2.SCREEN_NAME}__Checkbox__DoNotShowAgain`,
            text: (0, _i18n.translate)("onboardingOverlayScreen.doNotShowAgainLabel"),
            style: _onboardingOverlay.styles.skipGuideInputStyle,
            outlineStyle: _onboardingOverlay.styles.skipGuideInputOutlineStyle,
            textStyle: _onboardingOverlay.styles.skipGuideInputTextStyle
          })
        })]
      })]
    });
  };
  var _default = exports.default = OnboardingOverlayPage;
