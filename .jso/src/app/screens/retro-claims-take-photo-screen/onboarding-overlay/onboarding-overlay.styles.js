  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var SCREEN_HEIGHT_BREAKING_POINT = 812;
  var isReachBreakingPoint = function isReachBreakingPoint() {
    return _reactNative.Dimensions.get("window").height >= SCREEN_HEIGHT_BREAKING_POINT;
  };
  var getGuideImageSize = function getGuideImageSize() {
    var IMAGE_HORIZONTAL_MARGIN = 48;
    var IMAGE_WIDTH_HEIGHT_RATE = 0.7431818181818182;
    var width = _reactNative.Dimensions.get("window").width - IMAGE_HORIZONTAL_MARGIN;
    var height = width / IMAGE_WIDTH_HEIGHT_RATE;
    return {
      width: width,
      height: height
    };
  };
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      flex: 1,
      display: "flex",
      alignItems: "center"
    },
    closeBtnStyle: {
      alignSelf: "flex-end",
      marginRight: 20,
      marginVertical: isReachBreakingPoint() ? 20 : 16
    },
    titleTextStyle: Object.assign({}, _text.newPresets.h2, {
      alignSelf: "center",
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginHorizontal: 20,
      marginTop: isReachBreakingPoint() ? 8 : 0,
      textAlign: "center"
    }),
    contentContainerStyle: {
      flex: 1,
      justifyContent: "flex-end"
    },
    receiptExampleImageStyle: Object.assign({}, getGuideImageSize(), {
      marginHorizontal: 24,
      marginVertical: 16,
      resizeMode: "contain",
      justifyContent: "center",
      flex: 1
    }),
    okBtnStyle: {
      alignSelf: "center",
      borderRadius: 60,
      height: 44,
      marginBottom: 24,
      width: 327
    },
    okBtnTextStyle: Object.assign({}, _text.newPresets.bold, {
      fontSize: 16,
      lineHeight: 24
    }),
    skipGuideInputContainerStyle: {
      alignSelf: "center",
      marginBottom: isReachBreakingPoint() ? 50 : 20
    },
    skipGuideInputStyle: {
      width: 214
    },
    skipGuideInputOutlineStyle: {
      borderRadius: 6,
      borderWidth: 1,
      borderColor: _theme.color.palette.midGrey
    },
    skipGuideInputTextStyle: {
      lineHeight: 20
    }
  });
