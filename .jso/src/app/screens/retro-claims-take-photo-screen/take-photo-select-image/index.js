  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeImageCropPicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _imageEditor = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _text = _$$_REQUIRE(_dependencyMap[12]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[14]);
  var _firebase = _$$_REQUIRE(_dependencyMap[15]);
  var _retroClaimsTakePhotoScreen = _$$_REQUIRE(_dependencyMap[16]);
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _types = _$$_REQUIRE(_dependencyMap[18]);
  var _icons = _$$_REQUIRE(_dependencyMap[19]);
  var _camera = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _styles = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var windowWidth = _reactNative2.Dimensions.get("window").width;
  var windowHeight = _reactNative2.Dimensions.get("window").height;
  var screenHeight = _reactNative2.Dimensions.get("screen").height;
  var TakePhotoSelectImage = function TakePhotoSelectImage(_ref) {
    var _cameraRef$current2, _cameraRef$current3;
    var navigation = _ref.navigation;
    var _useContext = (0, _react.useContext)(_retroClaimsTakePhotoScreen.RetroClaimsTakePhotoContext),
      isCameraGranted = _useContext.isCameraGranted,
      retroClaimConfigs = _useContext.retroClaimConfigs,
      setIsCameraGranted = _useContext.setIsCameraGranted,
      setReceiptImage = _useContext.setReceiptImage,
      setCurrentScreen = _useContext.setCurrentScreen,
      setModalScreen = _useContext.setModalScreen,
      setIsFromGallery = _useContext.setIsFromGallery,
      setShowOnboarding = _useContext.setShowOnboarding,
      onOpenGalleryToSelect = _useContext.onOpenGalleryToSelect;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isFlashModeOn = _useState2[0],
      setIsFlashModeOn = _useState2[1];
    var _useState3 = (0, _react.useState)({
        headerHeight: 0,
        bottomHeight: 0
      }),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      layoutData = _useState4[0],
      setLayoutData = _useState4[1];
    var cameraRef = (0, _react.useRef)(null);
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg61 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG61";
    });
    var msg62 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG62";
    });
    var isIOS = _reactNative2.Platform.OS === "ios";
    var insetsTop = isIOS ? 0 : insets.top || 0;
    var rationale = {
      title: (msg61 == null ? undefined : msg61.title) || (0, _i18n.translate)("requestPermission.camera.title"),
      message: (msg61 == null ? undefined : msg61.message) || (0, _i18n.translate)("requestPermission.camera.message"),
      buttonPositive: (msg61 == null ? undefined : msg61.secondButton) || (0, _i18n.translate)("requestPermission.camera.buttonPositive"),
      buttonNegative: (msg61 == null ? undefined : msg61.firstButton) || (0, _i18n.translate)("requestPermission.camera.buttonNegative")
    };
    var flashIcon = isFlashModeOn ? (0, _jsxRuntime.jsx)(_icons.FlashOnOnly, {
      style: _styles.styles.bottomButtonIcon
    }) : (0, _jsxRuntime.jsx)(_icons.FlashOffOnly, {
      style: _styles.styles.bottomButtonIcon
    });
    var cropperOverlayStyle = {
      height: windowHeight + insetsTop - (((layoutData == null ? undefined : layoutData.headerHeight) || 0) + ((layoutData == null ? undefined : layoutData.bottomHeight) || 0))
    };
    var onHeaderLayout = function onHeaderLayout(e) {
      var _e$nativeEvent;
      var headerHeight = e == null || (_e$nativeEvent = e.nativeEvent) == null || (_e$nativeEvent = _e$nativeEvent.layout) == null ? undefined : _e$nativeEvent.height;
      setLayoutData(Object.assign({}, layoutData, {
        headerHeight: headerHeight
      }));
    };
    var onBottomLayout = function onBottomLayout(e) {
      var _e$nativeEvent2;
      var bottomHeight = e == null || (_e$nativeEvent2 = e.nativeEvent) == null || (_e$nativeEvent2 = _e$nativeEvent2.layout) == null ? undefined : _e$nativeEvent2.height;
      setLayoutData(Object.assign({}, layoutData, {
        bottomHeight: bottomHeight
      }));
    };
    var onPressHelpButton = function onPressHelpButton() {
      setModalScreen(_types.MODAL_SCREENS.ONBOARDING_OVERLAY);
      setShowOnboarding(true);
    };
    var onChangeFlashMode = function onChangeFlashMode() {
      setIsFlashModeOn(!isFlashModeOn);
    };
    var onPressTakePhotoButton = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _cameraRef$current;
        var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_RETRO_CLAIM_TAKE_PHOTO);
        var imageData = yield cameraRef == null || (_cameraRef$current = cameraRef.current) == null || _cameraRef$current.takePicture == null ? undefined : _cameraRef$current.takePicture();
        if (imageData != null && imageData.path) {
          dtAction.reportStringValue('take-photo', 'success');
          var imagePath = isIOS ? imageData == null ? undefined : imageData.path : `file://${imageData == null ? undefined : imageData.path}`;
          _reactNative2.Image.getSize(imagePath, function (width, height) {
            if (width && height) {
              ;
              (0, _asyncToGenerator2.default)(function* () {
                var yAxisRatioHeight = isIOS ? windowHeight : screenHeight;
                var xAxisRatio = width / windowWidth;
                var yAxisRatio = height / yAxisRatioHeight;
                dtAction.reportStringValue("image-width", `${width}`);
                dtAction.reportStringValue("image-height", `${height}`);
                var offsetX = _constants.CROPPER_OVERLAY_STYLES.LEFT_RIGHT_WIDTH * xAxisRatio;
                var offsetY = (layoutData.headerHeight + _constants.CROPPER_OVERLAY_STYLES.TOP_BOTTOM_WIDTH) * yAxisRatio;
                var newWidth = width - 2 * offsetX;
                var newHeight = height - (layoutData.headerHeight + layoutData.bottomHeight + 2 * _constants.CROPPER_OVERLAY_STYLES.TOP_BOTTOM_WIDTH) * yAxisRatio;
                var cropTopData = {
                  offset: {
                    x: offsetX,
                    y: offsetY
                  },
                  size: {
                    width: newWidth,
                    height: newHeight
                  }
                };
                var croppedTopImage = yield _imageEditor.default.cropImage(imagePath, cropTopData);
                _reactNativeImageCropPicker.default.openCropper(Object.assign({
                  mediaType: "photo",
                  path: croppedTopImage == null ? undefined : croppedTopImage.uri,
                  freeStyleCropEnabled: true
                }, isIOS && {
                  width: newWidth,
                  height: newHeight
                }, {
                  // For Android
                  cropperStatusBarColor: "#000000",
                  cropperActiveWidgetColor: "#ab76d5",
                  cropperToolbarColor: "black",
                  cropperToolbarWidgetColor: "#ffffff",
                  // For iOS
                  cropperCancelColor: "#ab76d5",
                  cropperChooseText: "Next",
                  cropperChooseColor: "#ab76d5"
                })).then(/*#__PURE__*/function () {
                  var _ref4 = (0, _asyncToGenerator2.default)(function* (croppingResult) {
                    dtAction.reportStringValue("cropping-result", "success");
                    var _ref5 = retroClaimConfigs || {},
                      retro_claim_quality = _ref5.retro_claim_quality,
                      retro_claim_max_height = _ref5.retro_claim_max_height,
                      retro_claim_max_width = _ref5.retro_claim_max_width;
                    var resizeImageConfigs = Object.assign({}, retro_claim_max_width && {
                      maxWidth: Number(retro_claim_max_width)
                    }, retro_claim_max_height && {
                      maxHeight: Number(retro_claim_max_height)
                    }, retro_claim_quality && {
                      quality: Number(retro_claim_quality)
                    });
                    dtAction.reportStringValue("configs-quality", `${retro_claim_quality}`);
                    dtAction.reportStringValue("configs-max-width", `${retro_claim_max_width}`);
                    dtAction.reportStringValue("configs-max-height", `${retro_claim_max_height}`);
                    var base64Image = yield (0, _mediaHelper.createResizedImage)(Object.assign({
                      imageData: croppingResult
                    }, resizeImageConfigs));
                    setReceiptImage(base64Image);
                    setCurrentScreen(_types.TAKE_PHOTO_STEP.CONFIRM_PAGE);
                  });
                  return function (_x) {
                    return _ref4.apply(this, arguments);
                  };
                }()).catch(function (error) {
                  dtAction.reportStringValue("cropping-result", `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
                }).finally(function () {
                  dtAction.leaveAction();
                });
              })();
            } else {
              dtAction.reportStringValue('get-size', 'failed');
              dtAction.leaveAction();
            }
          });
          setIsFromGallery(false);
        } else {
          dtAction.reportStringValue('take-photo', 'failed');
          dtAction.leaveAction();
        }
        setIsFlashModeOn(false);
      });
      return function onPressTakePhotoButton() {
        return _ref2.apply(this, arguments);
      };
    }();
    var handlePressBackButton = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        navigation == null || navigation.goBack == null || navigation.goBack();
      });
      return function handlePressBackButton() {
        return _ref6.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      setTimeout(function () {
        (0, _reactNativePermissions.request)(isIOS ? _reactNativePermissions.PERMISSIONS.IOS.CAMERA : _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA, rationale).then(/*#__PURE__*/function () {
          var _ref7 = (0, _asyncToGenerator2.default)(function* (result) {
            if ([_reactNativePermissions.RESULTS.BLOCKED, _reactNativePermissions.RESULTS.DENIED].some(function (val) {
              return val === result;
            })) {
              setIsCameraGranted(false);
              _reactNative2.Alert.alert((msg62 == null ? undefined : msg62.title) || (0, _i18n.translate)("retroClaims.needAccessPermission.title"), (msg62 == null ? undefined : msg62.message) || (0, _i18n.translate)("retroClaims.needAccessPermission.description"), [{
                text: (msg62 == null ? undefined : msg62.firstButton) || (0, _i18n.translate)("retroClaims.needAccessPermission.firstButton"),
                isPreferred: true,
                onPress: _reactNativePermissions.openSettings
              }, {
                text: (msg62 == null ? undefined : msg62.secondButton) || (0, _i18n.translate)("retroClaims.needAccessPermission.secondButton"),
                onPress: function onPress() {
                  return null;
                }
              }]);
            } else if (result === _reactNativePermissions.RESULTS.GRANTED) {
              setIsCameraGranted(true);
            }
          });
          return function (_x2) {
            return _ref7.apply(this, arguments);
          };
        }());
      }, 1000);
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.wrapper,
      children: [isCameraGranted && (0, _jsxRuntime.jsx)(_camera.default, {
        ref: cameraRef,
        flashMode: isFlashModeOn,
        photoRatio: windowHeight / windowWidth
      }), !(cameraRef != null && (_cameraRef$current2 = cameraRef.current) != null && _cameraRef$current2.isCameraReady) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.deniedStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.headerWrapper,
        onLayout: onHeaderLayout,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: handlePressBackButton,
          children: (0, _jsxRuntime.jsx)(_icons.BackArrowCircle, {
            width: 28,
            height: 28,
            style: _styles.styles.backArrowIcon
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressHelpButton,
          children: (0, _jsxRuntime.jsx)(_icons.QuestionMark, {
            width: 24,
            height: 24,
            style: _styles.styles.questionMarkIcon
          })
        })]
      }), !!(cameraRef != null && (_cameraRef$current3 = cameraRef.current) != null && _cameraRef$current3.isCameraReady) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [_styles.styles.cropperOverlay, cropperOverlayStyle],
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.cropperOverlayTop
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.cropperOverlayLeft
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.cropperOverlayRight
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.cropperOverlayBottom
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.bottomWrapper,
        onLayout: onBottomLayout,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.guideMessage,
          tx: "retroClaims.takePhotoGuideMessage"
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.bottomButtons,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onChangeFlashMode,
            style: _styles.styles.bottomButtonItem,
            children: flashIcon
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            disabled: !isCameraGranted,
            style: _styles.styles.takePhotoButton,
            onPress: onPressTakePhotoButton,
            children: (0, _jsxRuntime.jsx)(_icons.Camera, {
              width: 32,
              height: 32,
              style: _styles.styles.takePhotoIcon
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onOpenGalleryToSelect,
            style: _styles.styles.bottomButtonItem,
            children: (0, _jsxRuntime.jsx)(_icons.ImageGallery, {
              style: _styles.styles.bottomButtonIcon
            })
          })]
        })]
      })]
    });
  };
  var _default = exports.default = TakePhotoSelectImage;
