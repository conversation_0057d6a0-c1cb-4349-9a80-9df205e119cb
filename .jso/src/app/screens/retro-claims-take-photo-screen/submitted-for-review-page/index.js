  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[11]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _text = _$$_REQUIRE(_dependencyMap[13]);
  var _htmlContent = _$$_REQUIRE(_dependencyMap[14]);
  var _reactNativeRenderHtml = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _useAppStateChange2 = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _retroClaimsTakePhotoScreen = _$$_REQUIRE(_dependencyMap[17]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[18]);
  var _theme = _$$_REQUIRE(_dependencyMap[19]);
  var _types = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _icons = _$$_REQUIRE(_dependencyMap[22]);
  var _styles = _$$_REQUIRE(_dependencyMap[23]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[24]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var CONTENT_WIDTH = width - 48;
  var systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold"]);
  var tagStyle = {
    u: {
      marginBottom: 10,
      color: _theme.color.palette.lightPurple,
      textAlignVertical: "center",
      textAlign: "center",
      textDecorationLine: "none"
    },
    a: {
      marginBottom: 10,
      color: _theme.color.palette.lightPurple,
      textAlignVertical: "center",
      textAlign: "center",
      textDecorationLine: "none"
    },
    p: Object.assign({}, _text.newPresets.bodyTextRegular, {
      marginBottom: 10,
      color: _theme.color.palette.almostBlackGrey,
      textAlignVertical: "center",
      textAlign: "center"
    })
  };
  var SubmittedForReviewPage = function SubmittedForReviewPage(_ref) {
    var navigation = _ref.navigation;
    var _useContext = (0, _react.useContext)(_retroClaimsTakePhotoScreen.RetroClaimsTakePhotoContext),
      isSubmitting = _useContext.isSubmitting,
      isSubmittedSuccess = _useContext.isSubmittedSuccess,
      setCurrentScreen = _useContext.setCurrentScreen,
      setShowOnboarding = _useContext.setShowOnboarding,
      onSubmitRetroClaim = _useContext.onSubmitRetroClaim;
    var _useAppStateChange = (0, _useAppStateChange2.default)(),
      appStateVisible = _useAppStateChange.appStateVisible;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isInternetError = _useState2[0],
      setIsInternetError = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNotificationsPermissionGranted = _useState4[0],
      setIsNotificationsPermissionGranted = _useState4[1];
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg91 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG91";
    });
    var msg92 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG92";
    });
    var msg93 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG93";
    });
    var messageData = isSubmittedSuccess ? msg91 : msg92;
    var renderReceiptStatusIcon = isSubmittedSuccess ? (0, _jsxRuntime.jsx)(_icons.ReceiptSuccess, {
      style: _styles.styles.receiptStatusIcon
    }) : (0, _jsxRuntime.jsx)(_icons.ReceiptFailed, {
      style: _styles.styles.receiptStatusIcon
    });
    var renderMessageDescription = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
        systemFonts: systemFonts,
        tagsStyles: tagStyle,
        contentWidth: CONTENT_WIDTH,
        source: {
          html: (0, _htmlContent.formatHtmlContent)(messageData == null ? undefined : messageData.description)
        }
      });
    }, [messageData]);
    var onPressCloseButton = function onPressCloseButton() {
      setShowOnboarding(false);
      navigation == null || navigation.goBack == null || navigation.goBack();
    };
    var uploadReceiptImage = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setIsInternetError(true);
        } else {
          setIsInternetError(false);
          onSubmitRetroClaim();
        }
      });
      return function uploadReceiptImage() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onPressFirstButton = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        if (isSubmittedSuccess) {
          setShowOnboarding(false);
        } else {
          uploadReceiptImage();
        }
      });
      return function onPressFirstButton() {
        return _ref3.apply(this, arguments);
      };
    }();
    var onViewPointsTransactionText = function onViewPointsTransactionText() {
      setShowOnboarding(false);
      navigation == null || navigation.navigate == null || navigation.navigate(_constants.NavigationConstants.transactions, {
        screen: _constants.NavigationConstants.pointsTransaction,
        onGoBack: function onGoBack() {
          return setShowOnboarding(true);
        }
      });
    };
    var onPressAllowNotificationsButton = function onPressAllowNotificationsButton() {
      (0, _reactNativePermissions.openSettings)();
    };
    var onPressBackButton = function onPressBackButton() {
      setCurrentScreen(_types.TAKE_PHOTO_STEP.CONFIRM_PAGE);
      setShowOnboarding(false);
    };
    var renderSubmittedStatus = (0, _react.useMemo)(function () {
      var firstButtonColors = isSubmitting ? [_theme.color.palette.midGrey, _theme.color.palette.lightGrey] : [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
      var renderFirstButton = isSubmitting ? (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
        loop: true,
        autoPlay: true,
        style: _styles.styles.loadingLottieStyle,
        source: _$$_REQUIRE(_dependencyMap[25])
      }) : (0, _jsxRuntime.jsx)(_text.Text, {
        text: messageData == null ? undefined : messageData.firstButton,
        style: _styles.styles.submitAnotherClaimText
      });
      var secondButtonStyle = {
        color: isSubmitting ? _theme.color.palette.lightGrey : _theme.color.palette.lightPurple
      };
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [renderReceiptStatusIcon, (0, _jsxRuntime.jsx)(_text.Text, {
          text: messageData == null ? undefined : messageData.title,
          style: _styles.styles.submittedStatusTitle
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.submittedStatusSubcopy,
          children: renderMessageDescription
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          disabled: isSubmitting,
          onPress: onPressFirstButton,
          children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            start: {
              x: 1,
              y: 0
            },
            end: {
              x: 0,
              y: 1
            },
            colors: firstButtonColors,
            style: _styles.styles.submitAnotherClaim,
            children: renderFirstButton
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          disabled: isSubmitting,
          hitSlop: {
            top: 5,
            bottom: 5
          },
          style: _styles.styles.viewPointsTransaction,
          onPress: onViewPointsTransactionText,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: messageData == null ? undefined : messageData.secondButton,
            style: [_styles.styles.viewPointsTransactionText, secondButtonStyle]
          })
        })]
      });
    }, [messageData, isSubmitting, isSubmittedSuccess]);
    (0, _react.useEffect)(function () {
      if (appStateVisible === _useAppStateChange2.APP_STATES.ACTIVE) {
        (0, _reactNativePermissions.checkNotifications)().then(function (result) {
          if ((result == null ? undefined : result.status) === _reactNativePermissions.RESULTS.GRANTED) {
            setIsNotificationsPermissionGranted(true);
          } else {
            setIsNotificationsPermissionGranted(false);
          }
        });
      }
    }, [appStateVisible]);
    if (isInternetError) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.internetErrorWrapper,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
            reload: true,
            visible: true,
            onReload: uploadReceiptImage,
            testID: `${_constants.NavigationConstants.retroClaimsSubmittedScreen}__ErrorOverlayNoConnection`
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressBackButton,
          style: _styles.styles.internetErrorBackButton,
          children: (0, _jsxRuntime.jsx)(_icons.ArrowBack, {
            style: _styles.styles.internetErrorBackIcon
          })
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.wrapper,
      children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: _styles.styles.closeButton,
        onPress: onPressCloseButton,
        hitSlop: {
          top: 10,
          left: 10,
          right: 10,
          bottom: 10
        },
        children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
          style: _styles.styles.closeIcon
        })
      }), renderSubmittedStatus, !isNotificationsPermissionGranted && isSubmittedSuccess && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.allowNotificationsWrapper,
        children: [(0, _jsxRuntime.jsx)(_icons.AllowNotifications, {}), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.allowNotificationsRight,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.styles.allowNotificationsMessage,
            tx: "retroClaims.allowNotification.message"
          }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
            hitSlop: {
              top: 8,
              bottom: 10
            },
            style: _styles.styles.allowNotificationsCTA,
            onPress: onPressAllowNotificationsButton,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _styles.styles.allowNotificationsCTAText,
              tx: "retroClaims.allowNotification.ctaButton"
            }), (0, _jsxRuntime.jsx)(_icons.AccordionRight, {})]
          })]
        })]
      })]
    });
  };
  var _default = exports.default = SubmittedForReviewPage;
