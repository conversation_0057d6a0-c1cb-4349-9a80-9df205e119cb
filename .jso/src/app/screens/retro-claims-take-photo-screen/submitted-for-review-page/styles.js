  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: {
      flex: 1,
      paddingTop: 20,
      paddingLeft: 16,
      paddingRight: 16,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    closeButton: {
      width: 24,
      height: 24,
      alignSelf: "flex-end"
    },
    closeIcon: {
      color: _theme.color.palette.darkestGrey
    },
    receiptStatusIcon: {
      marginTop: 20,
      marginBottom: 24,
      alignSelf: "center"
    },
    submittedStatusTitle: Object.assign({}, _text.presets.h2, {
      textAlign: "center",
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    }),
    submittedStatusSubcopy: {
      marginTop: 18
    },
    loadingLottieStyle: {
      height: 24,
      width: '100%'
    },
    submitAnotherClaim: {
      padding: 10,
      marginTop: 40,
      marginBottom: 16,
      borderRadius: 60,
      alignItems: "center"
    },
    submitAnotherClaimText: Object.assign({}, _text.presets.bodyTextBold, {
      lineHeight: 24,
      color: _theme.color.palette.whiteGrey
    }),
    viewPointsTransaction: {
      alignSelf: "center"
    },
    viewPointsTransactionText: Object.assign({}, _text.presets.bodyTextBold, {
      lineHeight: 24
    }),
    internetErrorWrapper: {
      flex: 1
    },
    internetErrorBackButton: {
      top: 20,
      left: 16,
      width: 28,
      height: 28,
      zIndex: 1000,
      elevation: 1000,
      borderRadius: 99,
      position: "absolute",
      backgroundColor: "rgba(69, 69, 69, 0.6)"
    },
    internetErrorBackIcon: {
      color: _theme.color.palette.whiteGrey
    },
    allowNotificationsWrapper: {
      left: 16,
      bottom: 50,
      padding: 20,
      borderWidth: 1,
      borderRadius: 16,
      position: "absolute",
      flexDirection: "row",
      alignItems: "center",
      borderColor: _theme.color.palette.purpleD5BBEA
    },
    allowNotificationsRight: {
      flex: 1,
      marginLeft: 20
    },
    allowNotificationsMessage: Object.assign({}, _text.presets.caption1Bold, {
      fontSize: 16,
      textAlign: "left",
      color: _theme.color.palette.black
    }),
    allowNotificationsCTA: {
      marginTop: 8,
      flexDirection: "row",
      alignItems: "flex-end"
    },
    allowNotificationsCTAText: Object.assign({}, _text.presets.caption1Bold, {
      textAlign: "left",
      color: _theme.color.primary
    })
  });
