  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.RetroClaimsTakePhotoContext = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNativeImageCropPicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[11]);
  var _types = _$$_REQUIRE(_dependencyMap[12]);
  var _envParams = _$$_REQUIRE(_dependencyMap[13]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _useDeviceBackHandler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[18]);
  var _theme = _$$_REQUIRE(_dependencyMap[19]);
  var _i18n = _$$_REQUIRE(_dependencyMap[20]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _confirmImagePage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _takePhotoSelectImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[24]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[25]);
  var _onboardingOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _submittedForReviewPage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _firebase = _$$_REQUIRE(_dependencyMap[28]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[29]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var RetroClaimsTakePhotoContext = exports.RetroClaimsTakePhotoContext = (0, _react.createContext)({
    showLoading: false,
    setShowLoading: function setShowLoading(state) {},
    isCameraGranted: false,
    setIsCameraGranted: function setIsCameraGranted(state) {},
    showOnboarding: false,
    setShowOnboarding: function setShowOnboarding(state) {},
    isFromGallery: false,
    setIsFromGallery: function setIsFromGallery(state) {},
    receiptImage: null,
    setReceiptImage: function setReceiptImage(image) {},
    currentScreen: _types.TAKE_PHOTO_STEP.TAKE_PHOTO,
    setCurrentScreen: function setCurrentScreen(step) {},
    modalScreen: _types.MODAL_SCREENS.ONBOARDING_OVERLAY,
    setModalScreen: function setModalScreen(step) {},
    onSubmitRetroClaim: function onSubmitRetroClaim() {},
    isSubmittedSuccess: false,
    setIsSubmittedSuccess: function setIsSubmittedSuccess(state) {},
    isSubmitting: false,
    setIsSubmitting: function setIsSubmitting(state) {},
    retroClaimConfigs: undefined,
    setRetroClaimConfigs: function setRetroClaimConfigs(state) {},
    onOpenGalleryToSelect: function onOpenGalleryToSelect() {},
    checkCameraPermissionAgain: function checkCameraPermissionAgain() {}
  });
  var TRANSITION_TIMING = 400;
  var _worklet_12774630190874_init_data = {
    code: "function retroClaimsTakePhotoScreenTsx1(){const{interpolate,animationProgress,Extrapolation}=this.__closure;return{flex:1,overflow:\"hidden\",transform:[{scale:interpolate(animationProgress.value,[0,1],[1,0.9],Extrapolation.CLAMP)}],borderRadius:interpolate(animationProgress.value,[0,1],[1,8],Extrapolation.CLAMP)};}"
  };
  var RetroClaimsTakePhotoScreen = function RetroClaimsTakePhotoScreen(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route;
    var _ref2 = (route == null ? undefined : route.params) || {},
      originalGuideSkipped = _ref2.originalGuideSkipped;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    (0, _useDeviceBackHandler.default)({
      navigation: navigation
    });
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showLoading = _useState2[0],
      setShowLoading = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      receiptImage = _useState4[0],
      setReceiptImage = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isFromGallery = _useState6[0],
      setIsFromGallery = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      showOnboarding = _useState8[0],
      setShowOnboarding = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isSubmitting = _useState0[0],
      setIsSubmitting = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isCameraGranted = _useState10[0],
      setIsCameraGranted = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isSubmittedSuccess = _useState12[0],
      setIsSubmittedSuccess = _useState12[1];
    var _useState13 = (0, _react.useState)(undefined),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      retroClaimConfigs = _useState14[0],
      setRetroClaimConfigs = _useState14[1];
    var _useState15 = (0, _react.useState)(_types.MODAL_SCREENS.ONBOARDING_OVERLAY),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      modalScreen = _useState16[0],
      setModalScreen = _useState16[1];
    var _useState17 = (0, _react.useState)(_types.TAKE_PHOTO_STEP.TAKE_PHOTO),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      currentScreen = _useState18[0],
      setCurrentScreen = _useState18[1];
    var mappingScreens = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, _types.TAKE_PHOTO_STEP.TAKE_PHOTO, (0, _jsxRuntime.jsx)(_takePhotoSelectImage.default, {
      navigation: navigation
    })), _types.TAKE_PHOTO_STEP.CONFIRM_PAGE, (0, _jsxRuntime.jsx)(_confirmImagePage.default, {}));
    var mappingModalContent = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, _types.MODAL_SCREENS.ONBOARDING_OVERLAY, (0, _jsxRuntime.jsx)(_onboardingOverlay.default, {
      setShowOnboarding: setShowOnboarding
    })), _types.MODAL_SCREENS.SUBMITTED_PAGE, (0, _jsxRuntime.jsx)(_submittedForReviewPage.default, {
      navigation: navigation
    }));
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg62 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG62";
    });
    var msg901 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG90.1";
    });
    var msg902 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG90.2";
    });
    var isIOSDevice = _reactNative.Platform.OS === "ios";
    var rationale = {
      title: (msg901 == null ? undefined : msg901.title) || (0, _i18n.translate)("requestPermission.gallery.title"),
      message: (msg901 == null ? undefined : msg901.informativeText) || (0, _i18n.translate)("requestPermission.gallery.message"),
      buttonPositive: (msg901 == null ? undefined : msg901.secondButton) || (0, _i18n.translate)("requestPermission.gallery.buttonPositive"),
      buttonNegative: (msg901 == null ? undefined : msg901.firstButton) || (0, _i18n.translate)("requestPermission.gallery.buttonNegative")
    };
    var animationProgress = (0, _reactNativeReanimated.useSharedValue)(0);
    var wrapperStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var retroClaimsTakePhotoScreenTsx1 = function retroClaimsTakePhotoScreenTsx1() {
        return {
          flex: 1,
          overflow: "hidden",
          transform: [{
            scale: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 1], [1, 0.9], _reactNativeReanimated.Extrapolation.CLAMP)
          }],
          borderRadius: (0, _reactNativeReanimated.interpolate)(animationProgress.value, [0, 1], [1, 8], _reactNativeReanimated.Extrapolation.CLAMP)
        };
      };
      retroClaimsTakePhotoScreenTsx1.__closure = {
        interpolate: _reactNativeReanimated.interpolate,
        animationProgress: animationProgress,
        Extrapolation: _reactNativeReanimated.Extrapolation
      };
      retroClaimsTakePhotoScreenTsx1.__workletHash = 12774630190874;
      retroClaimsTakePhotoScreenTsx1.__initData = _worklet_12774630190874_init_data;
      return retroClaimsTakePhotoScreenTsx1;
    }());
    var modalStyle = {
      marginHorizontal: 0,
      marginBottom: 0,
      marginTop: _reactNative.Platform.select({
        ios: Math.min(_reactNative.Dimensions.get("window").height * 0.07, 60),
        android: Math.min(_reactNative.Dimensions.get("window").height * 0.07, inset != null && inset.top ? (inset == null ? undefined : inset.top) + 5 : 25)
      }),
      overflow: "hidden",
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10
    };
    var checkCameraPermissionAgain = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        (0, _reactNativePermissions.request)(isIOSDevice ? _reactNativePermissions.PERMISSIONS.IOS.CAMERA : _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA).then(function (result) {
          if ([_reactNativePermissions.RESULTS.BLOCKED, _reactNativePermissions.RESULTS.DENIED].some(function (val) {
            return val === result;
          })) {
            setIsCameraGranted(false);
            _reactNative.Alert.alert((msg62 == null ? undefined : msg62.title) || (0, _i18n.translate)("retroClaims.needAccessPermission.title"), (msg62 == null ? undefined : msg62.message) || (0, _i18n.translate)("retroClaims.needAccessPermission.description"), [{
              text: (msg62 == null ? undefined : msg62.firstButton) || (0, _i18n.translate)("retroClaims.needAccessPermission.firstButton"),
              isPreferred: true,
              onPress: _reactNativePermissions.openSettings
            }, {
              text: (msg62 == null ? undefined : msg62.secondButton) || (0, _i18n.translate)("retroClaims.needAccessPermission.secondButton"),
              onPress: function onPress() {
                return null;
              }
            }]);
          } else if (result === _reactNativePermissions.RESULTS.GRANTED) {
            setIsCameraGranted(true);
          }
        });
      });
      return function checkCameraPermissionAgain() {
        return _ref3.apply(this, arguments);
      };
    }();
    var handleChooseImageFromGallery = function handleChooseImageFromGallery() {
      var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_RETRO_CLAIM_OPEN_GALLERY);
      setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        setIsFromGallery(true);
        try {
          var selectedImageData;
          if (isIOSDevice) {
            selectedImageData = yield (0, _mediaHelper.selectImageByImageCropPicker)({
              multiple: false
            });
          } else {
            selectedImageData = yield (0, _mediaHelper.choosePictureFromGallery)({
              multiple: false
            });
          }
          if (selectedImageData) {
            var _selectedImageData, _selectedImageData2, _selectedImageData3, _selectedImageData4;
            dtAction.reportStringValue('selected', 'success');
            _reactNativeImageCropPicker.default.openCropper(Object.assign({
              mediaType: "photo",
              path: isIOSDevice ? (_selectedImageData = selectedImageData) == null ? undefined : _selectedImageData.path : (_selectedImageData2 = selectedImageData) == null ? undefined : _selectedImageData2.uri,
              freeStyleCropEnabled: true
            }, isIOSDevice && {
              width: (_selectedImageData3 = selectedImageData) == null ? undefined : _selectedImageData3.width,
              height: (_selectedImageData4 = selectedImageData) == null ? undefined : _selectedImageData4.height
            }, {
              // For Android
              cropperStatusBarColor: "#000000",
              cropperActiveWidgetColor: "#ab76d5",
              cropperToolbarColor: "black",
              cropperToolbarWidgetColor: "#ffffff",
              // For iOS
              cropperCancelColor: "#ab76d5",
              cropperChooseText: "Next",
              cropperChooseColor: "#ab76d5"
            })).then(/*#__PURE__*/function () {
              var _ref5 = (0, _asyncToGenerator2.default)(function* (croppingResult) {
                dtAction.reportStringValue('cropping-result', 'success');
                var _ref6 = retroClaimConfigs || {},
                  retro_claim_quality = _ref6.retro_claim_quality,
                  retro_claim_max_height = _ref6.retro_claim_max_height,
                  retro_claim_max_width = _ref6.retro_claim_max_width;
                var resizeImageConfigs = Object.assign({}, retro_claim_max_width && {
                  maxWidth: Number(retro_claim_max_width)
                }, retro_claim_max_height && {
                  maxHeight: Number(retro_claim_max_height)
                }, retro_claim_quality && {
                  quality: Number(retro_claim_quality)
                });
                dtAction.reportStringValue('configs-quality', `${retro_claim_quality}`);
                dtAction.reportStringValue('configs-max-width', `${retro_claim_max_width}`);
                dtAction.reportStringValue('configs-max-height', `${retro_claim_max_height}`);
                var base64Image = yield (0, _mediaHelper.createResizedImage)(Object.assign({
                  imageData: croppingResult
                }, resizeImageConfigs));
                setReceiptImage(base64Image);
                setCurrentScreen(_types.TAKE_PHOTO_STEP.CONFIRM_PAGE);
              });
              return function (_x) {
                return _ref5.apply(this, arguments);
              };
            }()).catch(function (error) {
              dtAction.reportStringValue('cropping-result', `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
            }).finally(function () {
              dtAction.leaveAction();
            });
          } else {
            dtAction.reportStringValue('selected', 'failed');
            dtAction.leaveAction();
          }
        } catch (error) {
          dtAction.reportStringValue('selected', `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
          dtAction.leaveAction();
        }
      }), 200);
    };
    var onOpenGalleryToSelect = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* () {
        if (_reactNative.Platform.OS === "android") {
          var _systemVersion$split;
          var systemVersion = _reactNativeDeviceInfo.default.getSystemVersion();
          var androidVersion = systemVersion == null || (_systemVersion$split = systemVersion.split(".")) == null ? undefined : _systemVersion$split[0];
          if (Number(androidVersion) < 13) {
            handleChooseImageFromGallery();
            return;
          }
        }
        (0, _reactNativePermissions.request)(isIOSDevice ? _reactNativePermissions.PERMISSIONS.IOS.PHOTO_LIBRARY : _reactNativePermissions.PERMISSIONS.ANDROID.READ_MEDIA_IMAGES, rationale).then(function (result) {
          if ([_reactNativePermissions.RESULTS.BLOCKED, _reactNativePermissions.RESULTS.DENIED].some(function (val) {
            return val === result;
          })) {
            _reactNative.Alert.alert((msg902 == null ? undefined : msg902.title) || (0, _i18n.translate)("requestPermission.gallery.cannotTitle"), (msg902 == null ? undefined : msg902.informativeText) || (0, _i18n.translate)("requestPermission.gallery.cannotMessage"), [{
              text: (msg902 == null ? undefined : msg902.firstButton) || (0, _i18n.translate)("requestPermission.gallery.firstButton"),
              isPreferred: true,
              onPress: _reactNativePermissions.openSettings
            }, {
              text: (msg902 == null ? undefined : msg902.secondButton) || (0, _i18n.translate)("requestPermission.gallery.secondButton"),
              onPress: function onPress() {
                return null;
              }
            }]);
          } else if (result === _reactNativePermissions.RESULTS.GRANTED || result === _reactNativePermissions.RESULTS.LIMITED) {
            handleChooseImageFromGallery();
          }
        });
      });
      return function onOpenGalleryToSelect() {
        return _ref7.apply(this, arguments);
      };
    }();
    var onSubmitRetroClaim = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* () {
        var receiptImageFileType = (0, _mediaHelper.detectMimeTypeOfBase64)(receiptImage);
        var inputData = {
          encodedImage: receiptImage,
          uid: profilePayload == null ? undefined : profilePayload.memberId,
          fileType: receiptImageFileType
        };
        var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_RETRO_CLAIM_SUBMIT_IMAGE);
        setIsSubmitting(true);
        try {
          var _env, _env2, _response$data;
          var paramsArray = _apis.default.submitRetroClaims.split(" ");
          var method = paramsArray[0] || "POST";
          var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.API_GATEWAY_URL) + paramsArray[1];
          var response = yield (0, _request.default)({
            url: url,
            method: method,
            data: inputData,
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.X_API_KEY
            }
          });
          setReceiptImage(null);
          setIsSubmitting(false);
          setShowOnboarding(true);
          setModalScreen(_types.MODAL_SCREENS.SUBMITTED_PAGE);
          if (response != null && (_response$data = response.data) != null && _response$data.requestId) {
            dtAction.reportStringValue('status', 'success');
            setCurrentScreen(_types.TAKE_PHOTO_STEP.TAKE_PHOTO);
            setIsSubmittedSuccess(true);
          } else {
            setIsSubmittedSuccess(false);
            dtAction.reportStringValue('status', 'failed');
          }
        } catch (error) {
          dtAction.reportStringValue('status', `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
          setReceiptImage(null);
          setIsSubmitting(false);
          setShowOnboarding(true);
          setIsSubmittedSuccess(false);
          setModalScreen(_types.MODAL_SCREENS.SUBMITTED_PAGE);
        } finally {
          dtAction.leaveAction();
        }
      });
      return function onSubmitRetroClaim() {
        return _ref8.apply(this, arguments);
      };
    }();
    var handleModalBackButtonPress = (0, _react.useCallback)(function () {
      setShowOnboarding(false);
    }, [setShowOnboarding]);
    var contextValue = (0, _react.useMemo)(function () {
      return {
        showLoading: showLoading,
        setShowLoading: setShowLoading,
        isCameraGranted: isCameraGranted,
        setIsCameraGranted: setIsCameraGranted,
        showOnboarding: showOnboarding,
        setShowOnboarding: setShowOnboarding,
        isFromGallery: isFromGallery,
        setIsFromGallery: setIsFromGallery,
        receiptImage: receiptImage,
        setReceiptImage: setReceiptImage,
        currentScreen: currentScreen,
        setCurrentScreen: setCurrentScreen,
        modalScreen: modalScreen,
        setModalScreen: setModalScreen,
        isSubmitting: isSubmitting,
        setIsSubmitting: setIsSubmitting,
        onSubmitRetroClaim: onSubmitRetroClaim,
        isSubmittedSuccess: isSubmittedSuccess,
        retroClaimConfigs: retroClaimConfigs,
        setRetroClaimConfigs: setRetroClaimConfigs,
        setIsSubmittedSuccess: setIsSubmittedSuccess,
        onOpenGalleryToSelect: onOpenGalleryToSelect,
        checkCameraPermissionAgain: checkCameraPermissionAgain
      };
    }, [showLoading, setShowLoading, isCameraGranted, setIsCameraGranted, showOnboarding, setShowOnboarding, isFromGallery, setIsFromGallery, receiptImage, setReceiptImage, currentScreen, setCurrentScreen, modalScreen, setModalScreen, isSubmitting, setIsSubmitting, isSubmittedSuccess, retroClaimConfigs, setRetroClaimConfigs, onSubmitRetroClaim, setIsSubmittedSuccess, onOpenGalleryToSelect, checkCameraPermissionAgain]);
    (0, _react.useEffect)(function () {
      if (showOnboarding) {
        animationProgress.value = (0, _reactNativeReanimated.withTiming)(1, {
          duration: TRANSITION_TIMING
        });
      } else {
        animationProgress.value = (0, _reactNativeReanimated.withTiming)(0, {
          duration: TRANSITION_TIMING
        });
      }
    }, [showOnboarding]);
    (0, _react.useEffect)(function () {
      if (!originalGuideSkipped) {
        setTimeout(function () {
          setModalScreen(_types.MODAL_SCREENS.ONBOARDING_OVERLAY);
          setShowOnboarding(true);
        }, 500);
      }
    }, [originalGuideSkipped]);
    (0, _react.useEffect)(function () {
      var getRetroClaimConfigs = /*#__PURE__*/function () {
        var _ref9 = (0, _asyncToGenerator2.default)(function* () {
          var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_RETRO_CLAIM_GET_CONFIGS);
          try {
            var _env3, _response$data2;
            var paramsArray = _apis.default.getConfigurations.split(" ");
            var method = paramsArray[0] || "POST";
            var url = ((_env3 = (0, _envParams.env)()) == null ? undefined : _env3.API_GATEWAY_URL) + paramsArray[1];
            var data = {
              keys: ["retro_claim_quality", "retro_claim_max_height", "retro_claim_max_width"]
            };
            var response = yield (0, _request.default)({
              url: url,
              method: method,
              data: data
            });
            if (response != null && (_response$data2 = response.data) != null && _response$data2.retro_claim_max_width) {
              var _response$data3, _response$data4, _response$data5;
              dtAction.reportStringValue('status', 'success');
              dtAction.reportStringValue('configs-quality', `${response == null || (_response$data3 = response.data) == null ? undefined : _response$data3.retro_claim_quality}`);
              dtAction.reportStringValue('configs-max-width', `${response == null || (_response$data4 = response.data) == null ? undefined : _response$data4.retro_claim_max_width}`);
              dtAction.reportStringValue('configs-max-height', `${response == null || (_response$data5 = response.data) == null ? undefined : _response$data5.retro_claim_max_height}`);
              setRetroClaimConfigs(response == null ? undefined : response.data);
            } else {
              dtAction.reportStringValue('status', 'failed');
            }
          } catch (error) {
            dtAction.reportStringValue('status', `Error: ${(error == null ? undefined : error.message) || "unknown"}`);
          } finally {
            dtAction.leaveAction();
          }
        });
        return function getRetroClaimConfigs() {
          return _ref9.apply(this, arguments);
        };
      }();
      getRetroClaimConfigs();
    }, []);
    return (0, _jsxRuntime.jsxs)(RetroClaimsTakePhotoContext.Provider, {
      value: contextValue,
      children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
        style: wrapperStyle,
        children: mappingScreens[currentScreen]
      }), (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
        style: modalStyle,
        backdropOpacity: 0.5,
        isVisible: showOnboarding,
        backdropColor: _theme.color.palette.black,
        animationInTiming: TRANSITION_TIMING,
        animationOutTiming: TRANSITION_TIMING,
        backdropTransitionInTiming: TRANSITION_TIMING,
        backdropTransitionOutTiming: TRANSITION_TIMING,
        onBackButtonPress: handleModalBackButtonPress,
        children: mappingModalContent[modalScreen]
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        isTransparent: true,
        visible: showLoading,
        customStyle: {
          backgroundColor: "rgba(252, 252, 252, 0.7)"
        }
      })]
    });
  };
  var _default = exports.default = RetroClaimsTakePhotoScreen;
