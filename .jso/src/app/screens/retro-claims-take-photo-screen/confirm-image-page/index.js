  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _text = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _useDeviceBackHandler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _retroClaimsTakePhotoScreen = _$$_REQUIRE(_dependencyMap[12]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[13]);
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _types = _$$_REQUIRE(_dependencyMap[15]);
  var _icons = _$$_REQUIRE(_dependencyMap[16]);
  var _styles = _$$_REQUIRE(_dependencyMap[17]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  var calculateImageWrapper = function calculateImageWrapper(_ref) {
    var width = _ref.width,
      height = _ref.height;
    var imageMaxHeight = _reactNative2.Dimensions.get("window").height - _reactNative2.Platform.select({
      ios: 60,
      android: 22
    }) - 300;
    var imageMaxWidth = _reactNative2.Dimensions.get("window").width - 116;
    var calculatedImageWidth = imageMaxHeight * width / height;
    var calculatedImageHeight = imageMaxWidth * height / width;
    var imageWidth = calculatedImageWidth > imageMaxWidth ? imageMaxWidth : calculatedImageWidth;
    var imageHeight = calculatedImageWidth > imageMaxWidth ? calculatedImageHeight : imageMaxHeight;
    return {
      width: imageWidth,
      height: imageHeight
    };
  };
  var ConfirmImagePage = function ConfirmImagePage() {
    var _useContext = (0, _react.useContext)(_retroClaimsTakePhotoScreen.RetroClaimsTakePhotoContext),
      isFromGallery = _useContext.isFromGallery,
      receiptImage = _useContext.receiptImage,
      isSubmitting = _useContext.isSubmitting,
      setIsSubmitting = _useContext.setIsSubmitting,
      setModalScreen = _useContext.setModalScreen,
      setReceiptImage = _useContext.setReceiptImage,
      setCurrentScreen = _useContext.setCurrentScreen,
      setShowOnboarding = _useContext.setShowOnboarding,
      onSubmitRetroClaim = _useContext.onSubmitRetroClaim,
      onOpenGalleryToSelect = _useContext.onOpenGalleryToSelect;
    var receiptImageUri = `data:image/png;base64,${receiptImage}`;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isInternetError = _useState2[0],
      setIsInternetError = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      receiptImageWrapperStyle = _useState4[0],
      setReceiptImageWrapper = _useState4[1];
    var cancelButtonStyle = {
      color: isSubmitting ? _theme.color.palette.darkGrey999 : _theme.color.palette.whiteGrey
    };
    var onPressBackToTakePhoto = function onPressBackToTakePhoto() {
      setReceiptImage(null);
      setIsSubmitting(false);
      setCurrentScreen(_types.TAKE_PHOTO_STEP.TAKE_PHOTO);
      if (isFromGallery) {
        onOpenGalleryToSelect();
      }
    };
    var onPressBackFromInternetError = function onPressBackFromInternetError() {
      setReceiptImage(null);
      setIsSubmitting(false);
      setCurrentScreen(_types.TAKE_PHOTO_STEP.TAKE_PHOTO);
    };
    (0, _useDeviceBackHandler.default)({
      onGoBack: onPressBackToTakePhoto
    });
    var onPressHelpButton = function onPressHelpButton() {
      setModalScreen(_types.MODAL_SCREENS.ONBOARDING_OVERLAY);
      setShowOnboarding(true);
    };
    var onPressContinueButton = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setIsInternetError(true);
        } else {
          setIsInternetError(false);
          onSubmitRetroClaim();
        }
      });
      return function onPressContinueButton() {
        return _ref2.apply(this, arguments);
      };
    }();
    var continueButtonColors = isSubmitting ? [_theme.color.palette.midGrey, _theme.color.palette.lightGrey] : [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
    var renderContinueButton = isSubmitting ? (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
      loop: true,
      autoPlay: true,
      style: _styles.styles.loadingLottieStyle,
      source: _$$_REQUIRE(_dependencyMap[19])
    }) : (0, _jsxRuntime.jsx)(_text.Text, {
      tx: "common.continue",
      style: _styles.styles.bottomButtonText
    });
    (0, _react.useEffect)(function () {
      var internetIntervalId = setInterval(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        if (isSubmitting) {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setIsSubmitting(false);
            setIsInternetError(true);
          }
        }
      }), 3000);
      return function () {
        return clearInterval(internetIntervalId);
      };
    }, [isSubmitting]);
    (0, _react.useEffect)(function () {
      _reactNative2.Image.getSize(receiptImageUri, function (width, height) {
        if (width && height) {
          var calculatedImageWrapperStyle = calculateImageWrapper({
            width: width,
            height: height
          });
          setReceiptImageWrapper(calculatedImageWrapperStyle);
        }
      });
    }, []);
    if (isInternetError) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        visible: true,
        onBack: onPressBackFromInternetError,
        onReload: onPressBackFromInternetError,
        headerBackgroundColor: "transparent",
        testID: `${_constants.NavigationConstants.retroClaimsTakePhotoScreen}__ErrorOverlayNoConnection`
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.wrapper,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.headerWrapper,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressBackToTakePhoto,
          children: (0, _jsxRuntime.jsx)(_icons.ArrowBack, {
            style: _styles.styles.arrowBackIcon
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressHelpButton,
          children: (0, _jsxRuntime.jsx)(_icons.QuestionMark, {
            style: _styles.styles.arrowBackIcon
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.previewSection,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.receiptImageWrapper,
          children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
            style: [_styles.styles.receiptImage, receiptImageWrapperStyle],
            source: {
              uri: receiptImageUri
            }
          })
        }), !!receiptImageWrapperStyle && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.footerWrapper,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            disabled: isSubmitting,
            style: _styles.styles.cancelButton,
            onPress: onPressBackToTakePhoto,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              text: "Retake",
              style: [_styles.styles.bottomButtonText, cancelButtonStyle]
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            disabled: isSubmitting,
            onPress: onPressContinueButton,
            style: _styles.styles.continueButtonWrapper,
            children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              start: {
                x: 1,
                y: 0
              },
              end: {
                x: 0,
                y: 1
              },
              style: _styles.styles.continueButton,
              colors: continueButtonColors,
              children: renderContinueButton
            })
          })]
        })]
      })]
    });
  };
  var _default = exports.default = ConfirmImagePage;
