  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: {
      flex: 1,
      backgroundColor: "rgba(18, 18, 18, 1)"
    },
    headerWrapper: {
      paddingLeft: 16,
      paddingRight: 16,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingTop: _reactNative.Platform.select({
        ios: 60,
        android: 40
      })
    },
    arrowBackIcon: {
      color: _theme.color.palette.whiteGrey
    },
    previewSection: {
      flex: 1,
      justifyContent: "flex-end"
    },
    receiptImageWrapper: {
      flex: 1,
      marginTop: 58,
      marginLeft: 58,
      marginRight: 58,
      marginBottom: 68,
      alignSelf: "center",
      justifyContent: "center"
    },
    receiptImage: {
      width: "auto",
      height: "100%"
    },
    footerWrapper: {
      marginBottom: 100,
      flexDirection: "row",
      alignItems: "center"
    },
    continueButtonWrapper: {
      flex: 1,
      marginRight: 20
    },
    continueButton: {
      padding: 12,
      width: "100%",
      borderRadius: 60,
      alignItems: "center"
    },
    loadingLottieStyle: {
      height: 18,
      width: "100%",
      alignSelf: "center"
    },
    cancelButton: {
      marginLeft: 20,
      marginRight: 12,
      borderRadius: 60,
      borderWidth: 1,
      paddingVertical: 11,
      paddingHorizontal: 18,
      borderColor: _theme.color.palette.almostWhiteGrey
    },
    bottomButtonText: Object.assign({
      width: 77
    }, _text.presets.caption1Bold, {
      fontSize: 16,
      textAlign: "center"
    })
  });
