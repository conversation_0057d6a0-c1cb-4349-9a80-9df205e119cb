  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.MIFFY_STICKER_STYLE_CONFIGS = exports.LOADING_COLORS = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[7]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var MIFFY_BG_DEFAULT_WIDTH = 343;
  var MIFFY_BG_DEFAULT_HEIGHT = 300;
  var MIFFY_BG_SIZE_RATE = 1.1433333333333333;
  var MIFFY_BG_HORIZONTAL_MARGINS = 32;
  var MIFFY_BG_ACTUAL_WIDTH = screenWidth - MIFFY_BG_HORIZONTAL_MARGINS;
  var MIFFY_BG_LAYOUT = {
    height: Math.round(MIFFY_BG_ACTUAL_WIDTH / MIFFY_BG_SIZE_RATE),
    width: MIFFY_BG_ACTUAL_WIDTH
  };
  var MIFFY_SIZE_STYLE_RATE = MIFFY_BG_ACTUAL_WIDTH / MIFFY_BG_DEFAULT_WIDTH;
  var LOGIN_TO_JOIN_QUEST_BTN_WIDTH = 154;
  var LOGIN_TO_JOIN_QUEST_BTN_HEIGHT = 35;
  var LOGIN_TO_JOIN_QUEST_BTN_SIZE_RATE = 4.4;
  var ENTER_QUEST_BTN_WIDTH = 130;
  var ENTER_QUEST_BTN_HEIGHT = 35;
  var ENTER_QUEST_BTN_SIZE_RATE = 3.7142857142857144;
  var GIFT_A_FRIEND_BTN_WIDTH = 130;
  var GIFT_A_FRIEND_BTN_HEIGHT = 35;
  var GIFT_A_FRIEND_BTN_SIZE_RATE = 3.7142857142857144;
  var MIFFY_LOADING_BG_DEFAULT_WIDTH = 343;
  var MIFFY_LOADING_BG_DEFAULT_HEIGHT = 256;
  var MIFFY_LOADING_BG_SIZE_RATE = 1.33984375;
  var MIFFY_LOADING_BG_LAYOUT = {
    height: Math.round(MIFFY_BG_ACTUAL_WIDTH / MIFFY_LOADING_BG_SIZE_RATE),
    width: MIFFY_BG_ACTUAL_WIDTH
  };
  var getActualSizeValue = function getActualSizeValue(rawValue, sizeRate) {
    return Math.ceil(rawValue * (sizeRate != null ? sizeRate : MIFFY_SIZE_STYLE_RATE));
  };
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({}, MIFFY_BG_LAYOUT),
    bgImageStyle: Object.assign({}, MIFFY_BG_LAYOUT),
    contentContainerStyle: Object.assign({}, MIFFY_BG_LAYOUT, {
      alignItems: "center"
    }),
    friendCollectionContainerStyle: {
      height: getActualSizeValue(98),
      marginBottom: getActualSizeValue(2),
      marginTop: getActualSizeValue(108),
      width: "100%"
    },
    btnGroupContainerStyle: {
      alignItems: "center",
      alignSelf: "center",
      flexDirection: "row",
      gap: getActualSizeValue(10),
      marginBottom: getActualSizeValue(18)
    },
    loginToJoinQuestBtnContainerStyle: {
      height: getActualSizeValue(LOGIN_TO_JOIN_QUEST_BTN_WIDTH) / LOGIN_TO_JOIN_QUEST_BTN_SIZE_RATE + 1,
      width: getActualSizeValue(LOGIN_TO_JOIN_QUEST_BTN_WIDTH) + 1
    },
    enterQuestBtnContainerStyle: {
      height: getActualSizeValue(ENTER_QUEST_BTN_WIDTH) / ENTER_QUEST_BTN_SIZE_RATE + 1,
      width: getActualSizeValue(ENTER_QUEST_BTN_WIDTH) + 1
    },
    giftAFriendBtnContainerStyle: {
      height: getActualSizeValue(GIFT_A_FRIEND_BTN_WIDTH) / GIFT_A_FRIEND_BTN_SIZE_RATE + 1,
      width: getActualSizeValue(GIFT_A_FRIEND_BTN_WIDTH) + 1
    },
    quantityBadgeStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.black,
      borderRadius: 99,
      height: getActualSizeValue(14),
      justifyContent: "center",
      // left: 0,
      minWidth: getActualSizeValue(14),
      paddingHorizontal: getActualSizeValue(4),
      paddingVertical: getActualSizeValue(2),
      position: "absolute"
      // top: 0,
    },
    quantityBadgeLabelStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.whiteGrey,
      fontSize: _responsive.default.getFontSize(9),
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: _responsive.default.getFontSize(10)
    }),
    loadingSkeletonContainerStyle: Object.assign({}, MIFFY_LOADING_BG_LAYOUT, {
      alignItems: "center"
    }),
    loadingSkeletonBgImageStyle: Object.assign({}, MIFFY_LOADING_BG_LAYOUT),
    longLoadingTile: {
      borderRadius: getActualSizeValue(4),
      height: getActualSizeValue(12),
      marginTop: getActualSizeValue(24),
      width: getActualSizeValue(160)
    },
    shortLoadingTile: {
      borderRadius: getActualSizeValue(4),
      height: getActualSizeValue(12),
      marginTop: getActualSizeValue(8),
      width: getActualSizeValue(80)
    }
  });
  var MIFFY_STICKER_STYLE_CONFIGS = exports.MIFFY_STICKER_STYLE_CONFIGS = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, _miffyGamificationBanner.MiffyFriend.Boris, {
    container: {
      left: getActualSizeValue(68),
      position: "absolute",
      top: getActualSizeValue(60)
    },
    iconImg: _icons.BorisIcon,
    iconStyle: {
      height: getActualSizeValue(64),
      width: getActualSizeValue(55)
    },
    placeholderImg: _icons.BorisOutlineIcon,
    quantityBadgeStyle: {
      right: getActualSizeValue(11),
      top: getActualSizeValue(18)
    }
  }), _miffyGamificationBanner.MiffyFriend.Melanie, {
    container: {
      left: getActualSizeValue(118),
      position: "absolute",
      top: getActualSizeValue(45)
    },
    iconImg: _icons.MelanieIcon,
    iconStyle: {
      height: getActualSizeValue(73),
      width: getActualSizeValue(50)
    },
    placeholderImg: _icons.MelanieOutlineIcon,
    quantityBadgeStyle: {
      right: getActualSizeValue(9),
      top: getActualSizeValue(22.5)
    }
  }), _miffyGamificationBanner.MiffyFriend.Miffy, {
    container: {
      left: getActualSizeValue(164),
      position: "absolute",
      top: getActualSizeValue(49)
    },
    iconImg: _icons.QueenMiffyIcon,
    iconStyle: {
      height: getActualSizeValue(82),
      width: getActualSizeValue(49)
    },
    placeholderImg: _icons.QueenMiffyOutlineIcon,
    quantityBadgeStyle: {
      left: getActualSizeValue(15.5),
      top: getActualSizeValue(28)
    }
  }), _miffyGamificationBanner.MiffyFriend.Barbara, {
    container: {
      left: getActualSizeValue(211),
      position: "absolute",
      top: getActualSizeValue(48)
    },
    iconImg: _icons.BarbaraIcon,
    iconStyle: {
      height: getActualSizeValue(63),
      width: getActualSizeValue(58)
    },
    placeholderImg: _icons.BarbaraOutlineIcon,
    quantityBadgeStyle: {
      left: getActualSizeValue(24),
      top: getActualSizeValue(-23.5)
    }
  }), _miffyGamificationBanner.MiffyFriend.PoppyPig, {
    container: {
      left: getActualSizeValue(279),
      position: "absolute",
      top: getActualSizeValue(53)
    },
    iconImg: _icons.PoppyPigIcon,
    iconStyle: {
      height: getActualSizeValue(78),
      width: getActualSizeValue(70)
    },
    placeholderImg: _icons.PoppyPigOutlineIcon,
    quantityBadgeStyle: {
      left: getActualSizeValue(22.07),
      top: getActualSizeValue(8)
    }
  }), _miffyGamificationBanner.MiffyFriend.Grunty, {
    container: {
      left: getActualSizeValue(253),
      position: "absolute",
      top: getActualSizeValue(72)
    },
    iconImg: _icons.GruntyIcon,
    iconStyle: {
      height: getActualSizeValue(49),
      width: getActualSizeValue(44)
    },
    placeholderImg: _icons.GruntyOutlineIcon,
    quantityBadgeStyle: {
      left: getActualSizeValue(7),
      top: getActualSizeValue(14.5)
    }
  });
  var LOADING_COLORS = exports.LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
