  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.sectionsPayloadForForYou = undefined;
  var _menuOption = _$$_REQUIRE(_dependencyMap[0]);
  var _askMax = _$$_REQUIRE(_dependencyMap[1]);
  var sectionsPayloadForForYou = exports.sectionsPayloadForForYou = {
    askMax: {
      default: {
        title: "Ask Max!",
        description: "I’m <PERSON>, your Changi virtual assistant. I can help you with:",
        highligts: "Real-time flight information;Things to do at Changi Airport;Reporting a lost item at Changi",
        imageUrl: "https://images.sutrix.com/images/04cc9c0efa.png",
        placeHolder: "Ask Max to help!",
        buttonText: "Start chat"
      },
      loading: {
        type: _askMax.AskMaxType.loading
      }
    },
    moreMenuOption: {
      default: [{
        type: _menuOption.MenuOptionType.default,
        icon: "https://images.sutrix.com/images/3cceb972d7.png",
        title: "iChangi Beta Program",
        redirection: _menuOption.RedirectType.External,
        url: "https://www.google.com"
      }, {
        type: _menuOption.MenuOptionType.default,
        icon: "https://images.sutrix.com/images/9413ae2c89.png",
        title: "Contact Us"
      }, {
        type: _menuOption.MenuOptionType.default,
        icon: "https://images.sutrix.com/images/e62bbc0bdd.png",
        title: "About iChangi"
      }],
      loading: [{}, {}, {}]
    }
  };
