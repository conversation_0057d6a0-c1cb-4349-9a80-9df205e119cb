  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.pageLayoutComponentStructureForForYou = undefined;
  var _sectionsData = _$$_REQUIRE(_dependencyMap[0]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[1]);
  var pageLayoutComponentStructureForForYou = exports.pageLayoutComponentStructureForForYou = {
    name: "view",
    _uid: "1",
    children: [{
      name: "headerForY<PERSON>",
      _uid: "2",
      title: "Header",
      props: {
        title: "Header"
      }
    }, {
      name: "shortcutLinks",
      _uid: "3",
      title: "Shortcut Links",
      props: {
        title: "Shortcut Links"
      }
    }, {
      name: "playAndWin",
      _uid: "4",
      title: "Play & Win",
      props: {
        title: "Play & Win"
      }
    }, {
      name: "askMax",
      _uid: "5",
      title: "Ask Max",
      props: {
        title: "Ask Max"
      }
    }, {
      name: "moreOptions",
      _uid: "6",
      title: "More",
      props: {
        type: _menuOption.MenuOptionType.default,
        data: _sectionsData.sectionsPayloadForForYou.moreMenuOption.default,
        title: "More"
      }
    }, {
      name: "logout",
      _uid: "8",
      props: {
        title: "Logout"
      }
    }]
  };
