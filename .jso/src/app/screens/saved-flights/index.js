  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _lodash = _$$_REQUIRE(_dependencyMap[10]);
  var _walletRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _i18n = _$$_REQUIRE(_dependencyMap[15]);
  var _savedFlightCard = _$$_REQUIRE(_dependencyMap[16]);
  var _savedFlightCard2 = _$$_REQUIRE(_dependencyMap[17]);
  var _native = _$$_REQUIRE(_dependencyMap[18]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[21]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[22]);
  var _emptyScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[25]);
  var _adobe = _$$_REQUIRE(_dependencyMap[26]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[28]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[29]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[30]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[31]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[32]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[33]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[34]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[35]);
  var _styles = _$$_REQUIRE(_dependencyMap[36]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[37]));
  var _icons = _$$_REQUIRE(_dependencyMap[38]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[39]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[40]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[41]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[42]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[43]);
  var _firebase = _$$_REQUIRE(_dependencyMap[44]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[45]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "SaveFlightsScreen";
  var timeout;
  var limit = 5;
  var dataLoading = [{
    dataFlight: {}
  }];
  var SaveFlightsScreen = function SaveFlightsScreen() {
    var toast = (0, _react.useRef)(null);
    var isFocused = (0, _native.useIsFocused)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("WALLET_MY_TRAVEL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var commonMessageAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var commonErrorAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr51 = commonErrorAEM == null ? undefined : commonErrorAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR5.1";
    });
    var ehr111 = commonErrorAEM == null ? undefined : commonErrorAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR11.1";
    });
    var msg48 = commonMessageAEM == null ? undefined : commonMessageAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var msg50 = commonMessageAEM == null ? undefined : commonMessageAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG50";
    });
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var walletMyTravel = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyTravel);
    var walletMyTravelError = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyTravelError);
    var error = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyTravelError);
    var walletMyTravelFetching = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyTravelFetching);
    var walletMyTravelFetched = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyTravelFetched);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      dataList = _useState2[0],
      setDataList = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      showSkeleton = _useState6[0],
      setShowSkeleton = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isNoConnection = _useState8[0],
      setIsNoConnection = _useState8[1];
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload);
    var rmFlag = true;
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var screenData = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.travel;
      });
      return screenData || {};
    }, [listErrorMaintenance]);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_MY_TRAVEL),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      isShowTickerband = _useTickerbandMaintan.isShowTickerband,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance,
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand,
      errorData = _useTickerbandMaintan.errorData;
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_Folio_Travel");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_Folio_Travel", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (myTravelFlightsPayload != null && myTravelFlightsPayload.loading) {
        setShowSkeleton(true);
      }
      if (!walletMyTravelFetching && !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading) && walletMyTravelFetched) {
        setShowSkeleton(false);
      }
      if (!walletMyTravelFetching && !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading) && (0, _lodash.isEmpty)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails)) {
        setShowSkeleton(false);
      }
    }, [walletMyTravelFetching, myTravelFlightsPayload, walletMyTravelFetched]);
    var fetchData = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        if (!isConnectedNetInfo) {
          setIsNoConnection(true);
        } else {
          setIsNoConnection(false);
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsRequest(profilePayload == null ? undefined : profilePayload.email));
        }
      });
      return function fetchData() {
        return _ref.apply(this, arguments);
      };
    }();
    _react.default.useEffect(function () {
      var unsubscribe = navigation.addListener("focus", function () {
        return fetchData();
      });
      return unsubscribe;
    }, [navigation]);
    _react.default.useEffect(function () {
      var unsubscribe = navigation.addListener("blur", function () {
        resetState();
        dispatch(_walletRedux.default.resetWalletMyTravel());
      });
      return unsubscribe;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) && !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading)) {
        dispatch(_walletRedux.default.walletMyTravelRequest(profilePayload == null ? undefined : profilePayload.email, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails));
      }
    }, [myTravelFlightsPayload]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(error)) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.show(_constants.TOAST_MESSAGE_DURATION);
      }
    }, [error]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(walletMyTravel) && !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading)) {
        var tempData = walletMyTravel.slice(0, limit);
        setDataList(tempData);
      }
    }, [walletMyTravel, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.loading]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      setShowSkeleton(true);
      return function () {
        dispatch(_walletRedux.default.resetWalletMyTravel());
      };
    }, []));
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
        return;
      }
      if (!(0, _lodash.isEmpty)(walletMyTravel) && isFocused && !showSkeleton) {
        (0, _screenHelper.setTriggerForShowRatingPopup)(_storageKey.StorageKey.isParticipatedInAppFolio);
      }
    }, [walletMyTravel, isFocused, showSkeleton, screenMaintenanceObj]);
    (0, _react.useEffect)(function () {
      return function () {
        clearTimeout(timeout);
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully) {
        var _toastForRemoveFlight;
        toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.show(_feedbackToastProps.DURATION.LENGTH_SHORT);
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        resetState(true);
        dispatch(_walletRedux.default.resetWalletMyTravel());
      } else {
        //
      }
    }, [removeFlightPayload]);
    var resetState = function resetState(showSkeletonState) {
      setDataList(null);
      setLoading(false);
      setShowSkeleton(!!showSkeletonState);
    };
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var actionEmptyScreenPress = function actionEmptyScreenPress() {
      navigation.navigate(_constants.NavigationConstants.search, {
        screen: _searchIndex.SearchIndex.flights,
        sourcePage: _adobe.AdobeTagName.CAppFolioTravel
      });
    };
    var _onCloseButtonPressed = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (item, index) {
        var newData = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: item == null ? undefined : item.flightNumber,
          flightScheduledDate: item == null ? undefined : item.scheduledDate,
          flightDirection: (item == null ? undefined : item.direction) || (item == null ? undefined : item.flightDirection)
        };
        var payloadForRemove = {
          item: item,
          flightNavigationType: (item == null ? undefined : item.direction) || (item == null ? undefined : item.flightDirection),
          itemIndex: index,
          sectionIndex: index
        };
        _reactNative2.Alert.alert((msg48 == null ? undefined : msg48.title) || (0, _i18n.translate)("flightLanding.areYouSure"), msg48 != null && msg48.message ? handleMessage48(msg48 == null ? undefined : msg48.message, item == null ? undefined : item.flightNumber, item == null ? undefined : item.destinationPlace) : `${(0, _i18n.translate)("flightLanding.removeMessage1")} ${item == null ? undefined : item.flightNumber} ${(0, _i18n.translate)("flightLanding.to")} ${item == null ? undefined : item.destinationPlace} ${(0, _i18n.translate)("flightLanding.removeMessage2")}`, [{
          text: (msg48 == null ? undefined : msg48.firstButton) || (0, _i18n.translate)("flightLanding.cancel")
        }, {
          text: (msg48 == null ? undefined : msg48.secondButton) || (0, _i18n.translate)("flightLanding.remove"),
          style: "cancel",
          onPress: function onPress() {
            setShowSkeleton(true);
            var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-saved-flight-unsave`);
            dtAction.reportStringValue("flight-saved-flight-unsave-press-flightNumber", `${item == null ? undefined : item.flightNumber}`);
            dtAction.reportStringValue("flight-saved-flight-unsave-press-scheduledDate", `${item == null ? undefined : item.scheduledDate}`);
            dtAction.reportStringValue("flight-saved-flight-unsave-press-direction", `${item == null ? undefined : item.direction}`);
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightRequest(newData, payloadForRemove));
            dtAction.leaveAction();
          }
        }]);
      });
      return function onCloseButtonPressed(_x, _x2) {
        return _ref2.apply(this, arguments);
      };
    }();
    var HeaderPage = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_styles.styles.headerContainer, {
          paddingTop: isShowTickerband ? 0 : inset.top
        }],
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.header,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _styles.styles.backButtonHeaderStyles,
            onPress: function onPress() {
              navigation.goBack();
            },
            hitSlop: {
              top: 5,
              left: 5,
              right: 5,
              bottom: 5
            },
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
              width: 24,
              height: 24
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "savedFlightsScreen.headerTitle",
            preset: "bodyTextBold",
            style: _styles.styles.titleHeaderStyles
          })]
        })
      });
    }, [isShowTickerband, inset.top]);
    var handleSavedFlight = function handleSavedFlight(item, index) {
      return (0, _jsxRuntime.jsx)(_savedFlightCard.SavedFlightCard, {
        state: showSkeleton ? _savedFlightCard2.SavedFlightCardState.loading : _savedFlightCard2.SavedFlightCardState.default,
        logo: item == null ? undefined : item.logo,
        flightDate: item == null ? undefined : item.scheduledDate,
        status: item == null ? undefined : item.flightStatus,
        flightNumber: item == null ? undefined : item.flightNumber,
        codeShare: item == null ? undefined : item.codeShares,
        transitCodes: item == null ? undefined : item.transits,
        departingCode: item == null ? undefined : item.departingCode,
        departingPlace: item == null ? undefined : item.departingPlace,
        destinationCode: item == null ? undefined : item.destinationCode,
        destinationPlace: item == null ? undefined : item.destinationPlace,
        flightStatusMapping: item == null ? undefined : item.flightStatusMapping,
        statusColor: item == null ? undefined : item.statusColor,
        timeOfFlight: item == null ? undefined : item.scheduledTime,
        onPressed: function onPressed() {
          var flightNumber = item.flightNumber,
            scheduledDate = item.scheduledDate;
          var dateFormat = (0, _moment.default)(new Date(scheduledDate)).format(_dateTime.DateFormats.DayMonthYear);
          (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountFolioTravel, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountFolioTravel, `Travel passes clicked | ${flightNumber} | ${dateFormat}`));
          navigation.navigate(_constants.NavigationConstants.flightDetails, {
            payload: {
              item: Object.assign({}, item, {
                isSaved: true,
                flightDate: item == null ? undefined : item.scheduledDate,
                flightTime: item == null ? undefined : item.scheduledTime
              })
            },
            direction: (item == null ? undefined : item.direction) || (item == null ? undefined : item.flightDirection)
          });
        },
        onCloseButtonPressed: function onCloseButtonPressed() {
          return _onCloseButtonPressed(item, index);
        },
        testID: `${SCREEN_NAME}__SavedFlightCard__${index}`,
        accessibilityLabel: `${SCREEN_NAME}__SavedFlightCard__${index}`,
        direction: (item == null ? undefined : item.direction) || (item == null ? undefined : item.flightDirection),
        terminal: item == null ? undefined : item.terminal,
        displayBelt: item == null ? undefined : item.displayBelt,
        checkInRow: item == null ? undefined : item.checkInRow,
        viaAirportDetails: item == null ? undefined : item.viaAirportDetails,
        displayTimestamp: item == null ? undefined : item.displayTimestamp,
        beltStatusMapping: item == null ? undefined : item.beltStatusMapping,
        type: _savedFlightCard2.TypeSavedFlightCardProp.NEW_CARD
      }, `${index}_save_flight_card`);
    };
    var showToastForRemoveFlight = function showToastForRemoveFlight() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastForRemoveFlight,
        style: _styles.styles.feedBackToastStyle,
        textButtonStyle: _styles.styles.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: _styles.styles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: (msg50 == null ? undefined : msg50.message) || (0, _i18n.translate)("flyLanding.removeFlight"),
        testID: `${SCREEN_NAME}__FeedBackToastRemoveFlight`,
        accessibilityLabel: `${SCREEN_NAME}__FeedBackToastRemoveFlight`
      });
    };
    var handleTitle = function handleTitle(item) {
      if (!(item != null && item.title)) return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
      if (!showSkeleton) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.styles.headerSectionStyle,
          children: item == null ? undefined : item.title
        });
      } else {
        return (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _styles.MID_GREY_LOADING_COLORS,
          shimmerStyle: [_styles.styles.shimmerPlaceholderStyle]
        });
      }
    };
    var Item = function Item(_ref3) {
      var item = _ref3.item,
        index = _ref3.index;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [handleTitle(item), handleSavedFlight(item == null ? undefined : item.dataFlight, index)]
      });
    };
    var loadMore = function loadMore() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountFolioTravel, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountFolioTravel, "Load more"));
      setLoading(true);
      timeout = setTimeout(function () {
        var startPosition = dataList.length;
        var newData = dataList.concat(walletMyTravel.slice(startPosition, startPosition + limit));
        setDataList(newData);
        setLoading(false);
      }, 1000);
    };
    var Footer = function Footer() {
      if (showSkeleton || (dataList == null ? undefined : dataList.length) >= (walletMyTravel == null ? undefined : walletMyTravel.length) || !(0, _lodash.isEmpty)(error)) {
        return null;
      } else {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _styles.styles.newLoadMoreBottonStyles,
          onPress: loadMore,
          testID: `${SCREEN_NAME}TouchableLoadMore`,
          accessibilityLabel: `${SCREEN_NAME}TouchableLoadMore`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.styles.loadMoreStyle,
            tx: "flightLanding.loadMoreFlights"
          })
        });
      }
    };
    var retry = function retry() {
      toast.current.closeNow();
    };
    var showErrorToastMessage = function showErrorToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toast,
        style: _styles.styles.feedBackToastStyle,
        textButtonStyle: _styles.styles.toastButtonStyle,
        position: "bottom",
        textStyle: _styles.styles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.fullWidthFeedBackWithCTA,
        text: (ehr51 == null ? undefined : ehr51.header) || (0, _i18n.translate)("transactionsScreen.unable"),
        buttonText: (ehr51 == null ? undefined : ehr51.buttonLabel) || (0, _i18n.translate)("transactionsScreen.retry"),
        onPress: retry,
        testID: `${SCREEN_NAME}FeedBackToast`
      });
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x3, _x4) {
        return _ref4.apply(this, arguments);
      };
    }();
    var ErrorUnplannedMaintenanceComponent = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.pageStyle,
        children: [isShowTickerband && (0, _jsxRuntime.jsx)(_tickerBand.default, {
          urgent: false,
          title: tickerBand,
          description: tickerBandDescription,
          buttonText: tickerBandButtonText,
          onCTAPress: onPressCTA,
          onClose: onCloseTickerBand,
          isLanding: false,
          tickerStyle: {
            paddingTop: inset.top
          }
        }), (0, _jsxRuntime.jsx)(HeaderPage, {}), (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
          header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
          subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
          icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
          buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
          buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
          onFirstButtonPress: function onFirstButtonPress() {
            return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
          },
          onSecondButtonPress: function onSecondButtonPress() {
            return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
          },
          testID: `${SCREEN_NAME}__ErrorUnplannedMaintenance`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorUnplannedMaintenance`
        })]
      });
    }, [screenMaintenanceObj, isShowTickerband]);
    var EmptyScreenComponent = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.pageStyle,
        children: [isShowTickerband && (0, _jsxRuntime.jsx)(_tickerBand.default, {
          urgent: false,
          title: tickerBand,
          description: tickerBandDescription,
          buttonText: tickerBandButtonText,
          onCTAPress: onPressCTA,
          onClose: onCloseTickerBand,
          isLanding: false,
          tickerStyle: {
            paddingTop: inset.top
          }
        }), (0, _jsxRuntime.jsx)(HeaderPage, {}), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.emptyTransactionStyle,
          children: (0, _jsxRuntime.jsx)(_emptyScreen.default, {
            iconUrl: _$$_REQUIRE(_dependencyMap[46]),
            title: ehr111 == null ? undefined : ehr111.header,
            description: ehr111 == null ? undefined : ehr111.subHeader,
            firstAction: ehr111 == null ? undefined : ehr111.buttonLabel,
            testID: `${SCREEN_NAME}__EmptyMyTravel`,
            accessibilityLabel: `${SCREEN_NAME}__EmptyMyTravel`,
            firstActionPress: actionEmptyScreenPress
          })
        })]
      });
    }, [ehr111, isShowTickerband]);
    if (!(0, _lodash.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(ErrorUnplannedMaintenanceComponent, {});
    }
    if (!showSkeleton && (0, _lodash.isEmpty)(walletMyTravel) && (0, _lodash.isEmpty)(error) && !isNoConnection && !isShowMaintenance) {
      if (!isFocused) return null;
      return (0, _jsxRuntime.jsx)(EmptyScreenComponent, {});
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.pageStyle,
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        backgroundColor: "transparent",
        barStyle: "dark-content",
        translucent: true
      }), isShowTickerband && (0, _jsxRuntime.jsx)(_tickerBand.default, {
        urgent: false,
        title: tickerBand,
        description: tickerBandDescription,
        buttonText: tickerBandButtonText,
        onCTAPress: onPressCTA,
        onClose: onCloseTickerBand,
        isLanding: false,
        tickerStyle: {
          paddingTop: inset.top
        }
      }), (0, _jsxRuntime.jsx)(HeaderPage, {}), !showSkeleton && isShowMaintenance ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.maintenanceErrorContainer,
        children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          skipStatusbar: true,
          style: {
            backgroundColor: "transparent",
            marginTop: -38
          },
          titleStyle: {
            marginTop: 16
          },
          buttonStyle: {
            width: "auto"
          },
          errorData: errorData,
          onPress: fetchTickerbandMaintanance
        })
      }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          data: showSkeleton ? dataLoading : dataList,
          keyExtractor: function keyExtractor(_, index) {
            return `${index}_list_savedflight_pass`;
          },
          renderItem: function renderItem(_ref5) {
            var item = _ref5.item,
              index = _ref5.index;
            return (0, _jsxRuntime.jsx)(Item, {
              item: item,
              index: index
            });
          },
          ListFooterComponent: Footer,
          showsVerticalScrollIndicator: false,
          testID: `${SCREEN_NAME}FlatListList`,
          accessibilityLabel: `${SCREEN_NAME}FlatListList`,
          contentContainerStyle: _styles.styles.spaceHorizontal
        }), isNoConnection && (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          visible: true,
          testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
          onReload: fetchData
        }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: !(0, _lodash.isEmpty)(walletMyTravelError),
          onReload: fetchData,
          onBack: function onBack() {
            navigation.goBack();
          },
          testID: `${SCREEN_NAME}__ErrorOverlay`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
          variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
          ignoreShowNoInternet: true
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: loading
        }), showErrorToastMessage(), showToastForRemoveFlight()]
      })]
    });
  };
  var _default = exports.default = SaveFlightsScreen;
