  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.MID_GREY_LOADING_COLORS = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    pageStyle: {
      flex: 1
    },
    containerStyle: {
      flex: 1
    },
    pointInfoStyle: {
      width: 335,
      height: 94,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 12,
      backgroundColor: _theme.color.palette.lightestGrey,
      marginVertical: 24
    },
    pointStyle: Object.assign({}, _text.presets.h1, {
      color: _theme.color.palette.lightPurple
    }),
    expireDateStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlack<PERSON>rey
    }),
    headerSectionStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 16,
      marginTop: 30
    }),
    infoTransactionsStyle: {
      flexDirection: "row",
      justifyContent: "space-between"
    },
    lineStyle: {
      borderStyle: "solid",
      borderBottomWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      marginVertical: 16
    },
    nameTransactionStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8,
      flex: 1
    }),
    pointEarnStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple
    }),
    transactionDateStyle: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    loadMoreStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      textAlign: "center"
    }),
    emptyTransactionStyle: {
      marginTop: 85,
      alignItems: "center",
      height: "100%",
      paddingHorizontal: 35
    },
    emptyH2TextStyle: Object.assign({}, _text.presets.h2, {
      marginBottom: 16,
      lineHeight: 28
    }),
    emptyTextStyle: Object.assign({}, _text.presets.caption1Regular, {
      textAlign: "center"
    }),
    loadMoreRetry: Object.assign({
      flexDirection: "row",
      justifyContent: "space-between",
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 8,
      height: 54,
      paddingHorizontal: 12,
      paddingVertical: 16,
      marginBottom: 27
    }, _theme.shadow.primaryShadow),
    unableText: Object.assign({}, _text.presets.bodyTextRegular),
    retryText: Object.assign({}, _text.presets.textLink, {
      color: _theme.color.palette.lightBlue
    }),
    unableTransactions: {
      justifyContent: "flex-end",
      height: "100%",
      marginHorizontal: 24
    },
    toastStyle: {
      backgroundColor: _theme.color.palette.black,
      width: "95%",
      height: 60,
      borderRadius: 8,
      marginBottom: 20,
      alignItems: "flex-start"
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      fontWeight: "normal",
      color: _theme.color.palette.lightBlue,
      alignItems: "flex-end"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    positionStyle: {
      bottom: 30
    },
    feedBackToastStyle: {
      width: "100%",
      paddingHorizontal: 16,
      marginBottom: 20
    },
    sepratorItem: {
      height: 17
    },
    wrapFlatList: {
      marginBottom: 17
    },
    spaceHorizontal: {
      paddingHorizontal: 24,
      paddingBottom: 24
    },
    shimmerPlaceholderStyle: {
      marginBottom: 10,
      marginTop: 30,
      height: 24,
      borderRadius: 5,
      width: "80%"
    },
    loadMoreBottonStyles: {
      marginTop: 6,
      marginBottom: 16
    },
    newLoadMoreBottonStyles: {
      marginTop: -4,
      marginBottom: 16
    },
    maintenanceErrorContainer: {
      flex: 1,
      alignItems: "center",
      marginTop: 52
    },
    headerContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey
    }, _theme.shadow.filterHeaderShadow),
    header: {
      height: 56,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey
    },
    backButtonHeaderStyles: {
      position: "absolute",
      left: 16,
      top: 0,
      bottom: 0,
      justifyContent: "center",
      alignItems: "center"
    },
    titleHeaderStyles: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center"
    }
  });
  var MID_GREY_LOADING_COLORS = exports.MID_GREY_LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
