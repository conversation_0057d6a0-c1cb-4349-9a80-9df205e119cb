  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useCountTimer = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _$$_REQUIRE(_dependencyMap[2]);
  var DURATION = 50000;
  var itvTime = 1000;
  var _useCountTimer = exports.useCountTimer = function useCountTimer() {
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isCounting = _useState2[0],
      setCounting = _useState2[1];
    var _useState3 = (0, _react.useState)(DURATION),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      timeLeft = _useState4[0],
      setTimeLeft = _useState4[1];
    (0, _react.useEffect)(function () {
      if (timeLeft === 0) {
        setCounting(false);
      }
    }, [timeLeft]);
    var start = (0, _react.useCallback)(function () {
      setTimeLeft(DURATION);
      setCounting(true);
    }, []);
    (0, _react.useEffect)(function () {
      if (timeLeft === 0) return;
      setCounting(true);
      var intervalId = setInterval(function () {
        setTimeLeft(timeLeft - itvTime);
      }, itvTime);
      return function () {
        return clearInterval(intervalId);
      };
    }, [timeLeft]);
    return {
      useCountTimer: _useCountTimer,
      isCounting: isCounting,
      start: start,
      timeLeft: timeLeft
    };
  };
