  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[12]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[13]);
  var _inputField = _$$_REQUIRE(_dependencyMap[14]);
  var _modalEditStyles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[16]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _lodash = _$$_REQUIRE(_dependencyMap[20]);
  var _i18n = _$$_REQUIRE(_dependencyMap[21]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var iconNoInternet = (0, _jsxRuntime.jsx)(_icons.InfoRed, {});
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var isValidPostalCode = function isValidPostalCode(value) {
    return value ? /^([0-9A-Za-z]{1,16})$/.test(value) : true;
  };
  var normalizeInput = function normalizeInput(value) {
    if (!value) return value;
    return value.replace(/[‘’]/g, "'").replace(/[“”]/g, '"').replace(/[\u2013\u2014]/g, '-').replace(/[\u2018\u2019\u201A]/g, "'");
  };
  var isValidAddress = function isValidAddress(value) {
    if (!value) return true;
    var normalizedValue = normalizeInput(value);
    var regex = /^[A-Za-z0-9# \-.,/"]{1,254}$/;
    return regex.test(normalizedValue);
  };
  var getBackgroundButtonDisable = function getBackgroundButtonDisable(disabled) {
    if (disabled) {
      return {
        backgroundColor: _theme.color.palette.lightGrey
      };
    }
  };
  var ModalEditAddress = function ModalEditAddress(props) {
    var disabled = props.disabled,
      hasError = props.hasError,
      firstError = props.firstError,
      setFieldValue = props.setFieldValue,
      clearErrorAt = props.clearErrorAt,
      formData = props.formData,
      onPressUpdate = props.onPressUpdate,
      screenName = props.screenName,
      SECTION_NAME = props.SECTION_NAME,
      modalEditAddressVisible = props.modalEditAddressVisible,
      onClosedEditModalAddress = props.onClosedEditModalAddress;
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var errorEHR15 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR15";
    });
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var handleUpdateOnPass = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var isConnected = yield checkInternet();
        setNoConnection(!isConnected);
        if (isConnected) {
          onPressUpdate();
        }
      });
      return function handleUpdateOnPass() {
        return _ref2.apply(this, arguments);
      };
    }();
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: modalEditAddressVisible,
      containerStyle: _modalEditStyles.default.modalContainer,
      onClosedSheet: onClosedEditModalAddress,
      stopDragCollapse: true,
      onBackPressHandle: onClosedEditModalAddress,
      animationInTiming: 100,
      animationOutTiming: 100,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _modalEditStyles.default.bottomSheetEditContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _modalEditStyles.default.headerEditModal,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.modalEditTitle.address",
            style: _modalEditStyles.default.editModalTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedEditModalAddress,
            style: _modalEditStyles.default.btnCloseModalStyles,
            testID: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24,
              fill: "currentColor",
              style: closeIconStyles
            })
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
          showsVerticalScrollIndicator: false,
          enableResetScrollToCoords: false,
          style: _modalEditStyles.default.modalContentContainer,
          children: [(0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
            isInvalid: hasError("address"),
            labelTx: "profile.address",
            helpText: firstError("address"),
            children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
              autoCapitalize: "none",
              autoCorrect: false,
              maxLength: 254,
              value: formData.address,
              isInvalid: hasError("address"),
              onChangeText: function onChangeText(value) {
                return isValidAddress(value) && setFieldValue("address", value, function () {
                  !value && clearErrorAt("address");
                });
              },
              testID: `${screenName}__${SECTION_NAME}__InputAddress`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__InputAddress`,
              highlightOnFocused: true
            })
          }), (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
            isInvalid: hasError("postalCode"),
            labelTx: "profile.postalCode",
            helpText: firstError("postalCode"),
            children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
              autoCapitalize: "none",
              autoCorrect: false,
              maxLength: 10,
              value: formData.postalCode,
              isInvalid: hasError("postalCode"),
              onChangeText: function onChangeText(value) {
                return isValidPostalCode(value) && setFieldValue("postalCode", value, function () {
                  !value && clearErrorAt("postalCode");
                });
              },
              testID: `${screenName}__${SECTION_NAME}__InputPostalCode`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__InputPostalCode`,
              highlightOnFocused: true
            })
          }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: _modalEditStyles.default.btnUpdateContainer,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              tx: "profile.submit",
              typePreset: "primary",
              statePreset: disabled ? "disabled" : "default",
              onPress: handleUpdateOnPass
              // onPress={handleSubmit(btnUpdateAddressOnPress)}
              ,
              backgroundPreset: "light",
              textPreset: "buttonLarge",
              sizePreset: "large",
              style: getBackgroundButtonDisable(disabled),
              textStyle: Object.assign({}, _modalEditStyles.default.textBtnUpdateStyle, {
                color: !disabled ? _theme.color.palette.almostWhiteGrey : _theme.color.palette.darkGrey
              }),
              testID: `${screenName}__${SECTION_NAME}__ButtonUpdateAddress`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__ButtonUpdateAddress`
            })
          })]
        }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
          visible: isNoConnection,
          title: (0, _lodash.get)(errorEHR15, "header") || (0, _i18n.translate)("popupError.somethingWrong"),
          errorMessage: (0, _lodash.get)(errorEHR15, "subHeader") || (0, _i18n.translate)("popupError.updateProfileMessage"),
          onClose: function onClose() {
            return setNoConnection(false);
          },
          buttonText: (0, _lodash.get)(errorEHR15, "buttonLabel") || (0, _i18n.translate)("common.okay"),
          onButtonPressed: function onButtonPressed() {
            return setNoConnection(false);
          },
          icon: iconNoInternet,
          testID: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`
        })]
      })
    });
  };
  var _default = exports.default = ModalEditAddress;
