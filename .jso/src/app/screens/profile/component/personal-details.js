  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectDestructuringEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _text2 = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _infoItem = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[13]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _button = _$$_REQUIRE(_dependencyMap[16]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[17]);
  var _selectPicker = _$$_REQUIRE(_dependencyMap[18]);
  var _formHook = _$$_REQUIRE(_dependencyMap[19]);
  var _profileScreen = _$$_REQUIRE(_dependencyMap[20]);
  var _inputField = _$$_REQUIRE(_dependencyMap[21]);
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[22]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _datePicker = _$$_REQUIRE(_dependencyMap[24]);
  var _constants = _$$_REQUIRE(_dependencyMap[25]);
  var _lodash = _$$_REQUIRE(_dependencyMap[26]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[27]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[29]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[30]);
  var _native = _$$_REQUIRE(_dependencyMap[31]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[32]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable no-useless-escape */

  var iconNoInternet = (0, _jsxRuntime.jsx)(_icons.InfoRed, {});
  var SECTION_NAME = "PersonalDetails";
  var isValidName = function isValidName(name) {
    return name ? /^[^<>\[\]\{\};:|\\\"!?@#$%*^+=_`~\t\n\r1-9]{1,50}$/.test(name) : true;
  };
  var trimSpace = function trimSpace(str) {
    return str ? str.replace(/\s\s+/g, " ") : "";
  };
  var genderOptions = [{
    value: "MALE",
    label: "Male"
  }, {
    value: "FEMALE",
    label: "Female"
  }, {
    value: "U",
    label: "Prefer not to say"
  }];
  var PersonalDetails = function PersonalDetails(props) {
    var personalData = props.personalData,
      countryOptions = props.countryOptions,
      nationOptions = props.nationOptions,
      onUpdateProfile = props.onUpdateProfile,
      screenName = props.screenName;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isModalVisible = _useState2[0],
      setModalVisible = _useState2[1];
    var PROFILE_SCREEN_CONTEXT_HANDLER = (0, _react.useContext)(_profileScreen.PROFILE_SCREEN_CONTEXT).Handlers;
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var errorEHR15 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR15";
    });
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNoConnection = _useState4[0],
      setNoConnection = _useState4[1];
    var closeIconStyles = {
      color: _theme.color.palette.lightPurple
    };
    var normalizeFormData = function normalizeFormData(profile) {
      return {
        firstName: (profile == null ? undefined : profile.firstName) || "",
        lastName: (profile == null ? undefined : profile.lastName) || "",
        phoneNum: (profile == null ? undefined : profile.phoneNum) || "",
        dob: (profile == null ? undefined : profile.dob) || "",
        gender: (profile == null ? undefined : profile.gender) || "",
        email: (profile == null ? undefined : profile.email) || "",
        residentialCountry: (profile == null ? undefined : profile.residentialCountry) || "SG",
        postalCode: (profile == null ? undefined : profile.postalCode) || "",
        countryCode: (profile == null ? undefined : profile.countryCode) || "65",
        address: (profile == null ? undefined : profile.address) || "",
        nationality: (profile == null ? undefined : profile.nationality) || "",
        vehicleIU: (profile == null ? undefined : profile.vehicleIU) || "",
        isStaff: !!(profile != null && profile.staffPassExpiry || profile != null && profile.staffPassNumber),
        staffPassExpiry: (profile == null ? undefined : profile.staffPassExpiry) || "",
        staffPassNumber: (profile == null ? undefined : profile.staffPassNumber) || ""
      };
    };
    var _useForm = (0, _formHook.useForm)({
        form: normalizeFormData(personalData),
        schema: PROFILE_SCREEN_CONTEXT_HANDLER.schema
      }),
      formData = _useForm.formData,
      hasError = _useForm.hasError,
      firstError = _useForm.firstError,
      setFieldValue = _useForm.setFieldValue,
      validateAt = _useForm.validateAt,
      handleSubmit = _useForm.handleSubmit,
      setFormData = _useForm.setFormData,
      isDirty = _useForm.isDirty,
      reset = _useForm.reset;
    var disabled = (0, _react.useMemo)(function () {
      if (isDirty) {
        try {
          return !_profileScreen.schemeLitePersonalDetail.isValidSync(formData);
        } catch (error) {
          return true;
        }
      }
      return true;
    }, [formData, isDirty]);
    (0, _react.useEffect)(function () {
      reset();
    }, [personalData]);
    var residentialCountry = (0, _react.useMemo)(function () {
      var option = countryOptions == null ? undefined : countryOptions.find(function (item) {
        return item.value === (personalData == null ? undefined : personalData.residentialCountry);
      });
      if (option) {
        return option.label || option.value;
      }
      return "";
    }, [countryOptions, personalData]);
    var nationalityValue = (0, _react.useMemo)(function () {
      var option = nationOptions == null ? undefined : nationOptions.find(function (item) {
        return item.value === (personalData == null ? undefined : personalData.nationality);
      });
      if (option) {
        return option.label || option.value;
      }
      return (0, _i18n.translate)("common.none");
    }, [nationOptions, personalData]);
    var genderValue = (0, _react.useMemo)(function () {
      var option = genderOptions == null ? undefined : genderOptions.find(function (item) {
        return item.value === (personalData == null ? undefined : personalData.gender);
      });
      if (option) {
        return option.label || option.value;
      }
      return (0, _i18n.translate)("profile.genderNone");
    }, [genderOptions, personalData]);
    var onClosedEditModal = function onClosedEditModal() {
      reset();
      setModalVisible(false);
    };
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var btnUpdateOnPress = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (form) {
        var isConnected = yield checkInternet();
        setNoConnection(!isConnected);
        if (isConnected) {
          var _data, _data2, _data3, _data4, _data5, _data6;
          var data = Object.assign({}, ((0, _objectDestructuringEmpty2.default)(form), form));
          data = Object.assign({}, data, {
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim()
          });
          setFormData(Object.assign({}, form, data));
          var dataNeedUpdate = {
            firstName: (_data = data) == null ? undefined : _data.firstName.trim(),
            lastName: (_data2 = data) == null ? undefined : _data2.lastName.trim(),
            dob: (_data3 = data) == null ? undefined : _data3.dob,
            residentialCountry: (_data4 = data) == null ? undefined : _data4.residentialCountry,
            nationality: (_data5 = data) == null ? undefined : _data5.nationality,
            gender: (_data6 = data) == null ? undefined : _data6.gender
          };
          reset();
          setModalVisible(false);
          setTimeout(function () {
            onUpdateProfile(dataNeedUpdate, _constants.UpdateProfileSectionKey.PERSONAL_DETAILS);
          }, 200);
        }
      });
      return function btnUpdateOnPress(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var getSpacingCharactor = function getSpacingCharactor() {
      return !(0, _lodash.isEmpty)(personalData == null ? undefined : personalData.firstName) && !(0, _lodash.isEmpty)(personalData == null ? undefined : personalData.lastName) ? " " : "";
    };
    var getBackgroundButtonDisable = function getBackgroundButtonDisable(isDisabled) {
      if (isDisabled) {
        return {
          backgroundColor: _theme.color.palette.lightGrey
        };
      }
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        onClosedEditModal();
      };
    }, []));
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.container,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.sectionTitleView,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            tx: "profile.sectionTitle.personalDetails",
            style: styles.sectionTitleStyles
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return setModalVisible(true);
            },
            testID: `${screenName}__${SECTION_NAME}__btnEdit`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__btnEdit`,
            children: (0, _jsxRuntime.jsx)(_icons.Edit, {})
          })]
        }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.name",
          value: (personalData == null ? undefined : personalData.firstName) + getSpacingCharactor() + (personalData == null ? undefined : personalData.lastName),
          testID: `${screenName}__${SECTION_NAME}__NameItemView`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__NameItemView`
        }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.dob",
          value: (0, _dateTime.toDate)(formData.dob, _dateTime.DateFormats.DayMonthYear),
          testID: `${screenName}__${SECTION_NAME}__DateOfBirthItemView`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__DateOfBirthItemView`
        }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.residentialCountry",
          value: residentialCountry,
          numberOfLinesValue: 1,
          testID: `${screenName}__${SECTION_NAME}__ResidentialCountryItemView`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__ResidentialCountryItemView`
        }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.nationality",
          value: nationalityValue
        }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.gender",
          value: genderValue,
          testID: `${screenName}__${SECTION_NAME}__GenderItemView`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__GenderItemView`
        })]
      }), (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
        isModalVisible: isModalVisible,
        containerStyle: styles.modalContainer,
        onClosedSheet: onClosedEditModal,
        stopDragCollapse: true,
        onBackPressHandle: onClosedEditModal,
        animationInTiming: 100,
        animationOutTiming: 100,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.bottomSheetEditContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.headerEditModal,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "profile.modalEditTitle.personalDetail",
              style: styles.editModalTitle
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onClosedEditModal,
              style: styles.btnCloseModalStyles,
              testID: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
              children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
                width: 24,
                height: 24,
                fill: "currentColor",
                style: closeIconStyles
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
            showsVerticalScrollIndicator: false,
            enableResetScrollToCoords: false,
            style: styles.modalContentContainer,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.nameFieldEditView,
              children: [(0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
                isInvalid: hasError("firstName"),
                labelTx: "profile.firstName",
                helpText: firstError("firstName"),
                style: styles.firstNameView,
                numberOfLinesError: 3,
                children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
                  autoCapitalize: "none",
                  autoCorrect: false,
                  maxLength: 50,
                  value: formData.firstName,
                  isInvalid: hasError("firstName"),
                  onChangeText: function onChangeText(value) {
                    isValidName(value) && setFieldValue("firstName", trimSpace(value));
                  },
                  onBlur: function onBlur() {
                    return validateAt("firstName", formData);
                  },
                  highlightOnFocused: true,
                  testID: `${screenName}__InputFirstName`,
                  accessibilityLabel: `${screenName}__InputFirstName`
                })
              }), (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
                isInvalid: hasError("lastName"),
                labelTx: "profile.lastName",
                helpText: firstError("lastName"),
                style: styles.lastNameView,
                numberOfLinesError: 3,
                children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
                  autoCapitalize: "none",
                  autoCorrect: false,
                  maxLength: 50,
                  value: formData.lastName,
                  isInvalid: hasError("lastName"),
                  onChangeText: function onChangeText(value) {
                    isValidName(value) && setFieldValue("lastName", trimSpace(value));
                  },
                  onBlur: function onBlur() {
                    return validateAt("lastName", formData);
                  },
                  highlightOnFocused: true,
                  testID: `${screenName}__InputLastName`,
                  accessibilityLabel: `${screenName}__InputLastName`
                })
              })]
            }), (0, _jsxRuntime.jsx)(_datePicker.DatePicker, {
              disabled: true,
              textTx: "profile.dob",
              isTouched: false,
              errorMessage: null,
              value: (0, _moment.default)(formData.dob, _dateTime.DateFormats.YearMonthDay),
              displayFormat: _dateTime.DateFormats.DayMonthYearWithSlash,
              testID: `${screenName}__DatePickerDOB`,
              accessibilityLabel: `${screenName}__DatePickerDOB`
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "profile.dobSuggestionToUpdate",
              style: styles.dobSuggestionToUpdate
            }), (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
              isInvalid: hasError("residentialCountry"),
              labelTx: "profile.residentialCountry",
              helpText: firstError("residentialCountry"),
              style: styles.residentialCountryView,
              numberOfLinesError: 3,
              children: (0, _jsxRuntime.jsx)(_selectPicker.SelectPicker, {
                options: countryOptions,
                isInvalid: hasError("residentialCountry"),
                value: formData.residentialCountry,
                onChangeValue: function onChangeValue(value) {
                  return setFieldValue("residentialCountry", value);
                },
                onBlur: function onBlur() {
                  return validateAt("residentialCountry", formData);
                },
                highlightOnFocused: true,
                icon: _icons.DownArrowGrey,
                testID: `${screenName}__ResidentialCountry__SelectPicker`,
                accessibilityLabel: `${screenName}__ResidentialCountry__SelectPicker`
              })
            }), (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
              isInvalid: hasError("nationality"),
              labelTx: "profile.nationality",
              helpText: firstError("nationality"),
              style: styles.nationalityView,
              numberOfLinesError: 3,
              children: (0, _jsxRuntime.jsx)(_selectPicker.SelectPicker, {
                options: nationOptions,
                isInvalid: hasError("nationality"),
                value: formData.nationality,
                onChangeValue: function onChangeValue(value) {
                  return setFieldValue("nationality", value);
                },
                onBlur: function onBlur() {
                  return validateAt("nationality", formData);
                },
                highlightOnFocused: true,
                icon: _icons.DownArrowGrey,
                testID: `${screenName}__Nationality__SelectPicker`,
                accessibilityLabel: `${screenName}__Nationality__SelectPicker`
              })
            }), (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
              isInvalid: hasError("gender"),
              labelTx: "profile.gender",
              helpText: firstError("gender"),
              style: styles.nationalityView,
              numberOfLinesError: 3,
              children: (0, _jsxRuntime.jsx)(_selectPicker.SelectPicker, {
                options: genderOptions,
                isInvalid: hasError("gender"),
                value: formData.gender,
                onChangeValue: function onChangeValue(value) {
                  return setFieldValue("gender", value);
                },
                onBlur: function onBlur() {
                  return validateAt("gender", formData);
                },
                highlightOnFocused: true,
                icon: _icons.DownArrowGrey,
                testID: `${screenName}__Gender__SelectPicker`,
                accessibilityLabel: `${screenName}__Gender__SelectPicker`
              })
            }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: styles.btnUpdateContainer,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                tx: "profile.submit",
                typePreset: "primary",
                statePreset: disabled ? "disabled" : "default",
                onPress: handleSubmit(btnUpdateOnPress),
                backgroundPreset: "light",
                textPreset: "buttonLarge",
                sizePreset: "large",
                style: getBackgroundButtonDisable(disabled),
                textStyle: Object.assign({}, styles.textBtnUpdateStyle, {
                  color: !disabled ? _theme.color.palette.almostWhiteGrey : _theme.color.palette.darkGrey
                }),
                testID: `${screenName}__${SECTION_NAME}__ButtonSubmit`,
                accessibilityLabel: `${screenName}__${SECTION_NAME}__ButtonSubmit`
              })
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
          visible: isNoConnection,
          title: (0, _lodash.get)(errorEHR15, "header") || (0, _i18n.translate)("popupError.somethingWrong"),
          errorMessage: (0, _lodash.get)(errorEHR15, "subHeader") || (0, _i18n.translate)("popupError.updateProfileMessage"),
          icon: iconNoInternet,
          onClose: function onClose() {
            return setNoConnection(false);
          },
          buttonText: (0, _lodash.get)(errorEHR15, "buttonLabel") || (0, _i18n.translate)("common.okay"),
          onButtonPressed: function onButtonPressed() {
            return setNoConnection(false);
          },
          testID: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomSheetEditContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: _reactNative2.Dimensions.get("window").height - (_reactNative2.Platform.OS === "android" ? _reactNative2.StatusBar.currentHeight + 14 : 54),
      width: "100%"
    },
    btnCloseModalStyles: {
      position: "absolute",
      right: 0
    },
    btnUpdateContainer: {
      borderRadius: 60,
      flex: 1,
      height: 44,
      marginBottom: 37,
      marginTop: 24
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingBottom: 42,
      paddingHorizontal: 24,
      paddingTop: 40,
      width: "100%"
    },
    dobSuggestionToUpdate: Object.assign({}, _text2.presets.caption1Italic, {
      color: _theme.color.palette.darkGrey,
      fontFamily: _theme.typography.regular,
      marginTop: 5
    }),
    editModalTitle: Object.assign({}, _text2.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    firstNameView: {
      flex: 1,
      marginRight: 7.5
    },
    headerEditModal: {
      flexDirection: "row",
      justifyContent: "center",
      marginHorizontal: 24,
      marginVertical: 21
    },
    lastNameView: {
      flex: 1,
      marginLeft: 7.5
    },
    modalContainer: {
      justifyContent: "flex-end",
      margin: 0
    },
    modalContentContainer: {
      paddingHorizontal: 24
    },
    nameFieldEditView: {
      flexDirection: "row",
      marginTop: 24
    },
    nationalityView: {
      marginTop: 16
    },
    residentialCountryView: {
      marginTop: 32
    },
    sectionTitleStyles: {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1
    },
    sectionTitleView: {
      flexDirection: "row"
    },
    textBtnUpdateStyle: {
      color: _theme.color.palette.almostWhiteGrey,
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      textAlign: "center"
    }
  });
  var _default = exports.default = PersonalDetails;
