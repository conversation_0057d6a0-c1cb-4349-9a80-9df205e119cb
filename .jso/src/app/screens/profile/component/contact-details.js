  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectDestructuringEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _infoItem = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _profileScreen = _$$_REQUIRE(_dependencyMap[10]);
  var _formHook = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _addressModalEdit = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _phoneNumberVerifyModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _emailModalEdit = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _profileRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _native = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SECTION_NAME = "ContactDetails";
  var ContactDetails = function ContactDetails(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var personalData = props.personalData,
      screenName = props.screenName,
      onUpdateProfile = props.onUpdateProfile;
    var PROFILE_SCREEN_CONTEXT_HANDLER = (0, _react.useContext)(_profileScreen.PROFILE_SCREEN_CONTEXT).Handlers;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      modalEditAddressVisible = _useState2[0],
      setShowModalEditAddress = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      modalEditMobileNumberVisible = _useState4[0],
      setShowModalEditMobileNumber = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      modalEditEmailVisible = _useState6[0],
      setShowModalEditEmail = _useState6[1];
    var normalizeFormData = function normalizeFormData(profile) {
      return {
        firstName: (profile == null ? undefined : profile.firstName) || "",
        lastName: (profile == null ? undefined : profile.lastName) || "",
        phoneNum: (profile == null ? undefined : profile.phoneNum) || "",
        dob: (profile == null ? undefined : profile.dob) || "",
        gender: (profile == null ? undefined : profile.gender) || "",
        email: (profile == null ? undefined : profile.email) || "",
        residentialCountry: (profile == null ? undefined : profile.residentialCountry) || "SG",
        postalCode: (profile == null ? undefined : profile.postalCode) || "",
        countryCode: (profile == null ? undefined : profile.countryCode) || "65",
        address: (profile == null ? undefined : profile.address) || "",
        nationality: (profile == null ? undefined : profile.nationality) || "SG",
        vehicleIU: (profile == null ? undefined : profile.vehicleIU) || "",
        isStaff: !!(profile != null && profile.staffPassExpiry || profile != null && profile.staffPassNumber),
        staffPassExpiry: (profile == null ? undefined : profile.staffPassExpiry) || "",
        staffPassNumber: (profile == null ? undefined : profile.staffPassNumber) || ""
      };
    };
    var _useForm = (0, _formHook.useForm)({
        form: normalizeFormData(personalData),
        schema: PROFILE_SCREEN_CONTEXT_HANDLER.schema
      }),
      formData = _useForm.formData,
      hasError = _useForm.hasError,
      firstError = _useForm.firstError,
      setFieldValue = _useForm.setFieldValue,
      handleSubmit = _useForm.handleSubmit,
      setFormData = _useForm.setFormData,
      validateAt = _useForm.validateAt,
      clearErrorAt = _useForm.clearErrorAt,
      isDirty = _useForm.isDirty,
      reset = _useForm.reset;
    (0, _react.useEffect)(function () {
      reset();
    }, [personalData]);
    var disabledBtnVerifyEmail = (0, _react.useMemo)(function () {
      if (isDirty) {
        try {
          return !_profileScreen.schemeLiteEmail.isValidSync(formData);
        } catch (error) {
          return true;
        }
      }
      return true;
    }, [formData, isDirty]);
    var disabledBtnVerifyPhone = (0, _react.useMemo)(function () {
      if (!(personalData != null && personalData.verifiedMobile) && (personalData == null ? undefined : personalData.phoneNum) === formData.phoneNum) {
        return false;
      }
      if (isDirty) {
        try {
          return !_profileScreen.schemeLitePhoneNumber.isValidSync(formData);
        } catch (error) {
          return true;
        }
      }
      return true;
    }, [formData, isDirty]);
    var disabledBtnVerifyAddress = (0, _react.useMemo)(function () {
      if (isDirty) {
        try {
          return !_profileScreen.schemeLiteAddress.isValidSync(formData);
        } catch (error) {
          return true;
        }
      }
      return true;
    }, [formData, isDirty]);
    var onClosedEditModalAddress = function onClosedEditModalAddress() {
      reset();
      setShowModalEditAddress(false);
    };
    var btnUpdateAddressOnPress = function btnUpdateAddressOnPress(form) {
      var data = Object.assign({}, ((0, _objectDestructuringEmpty2.default)(form), form));
      data = Object.assign({}, data);
      setFormData(Object.assign({}, form, data));
      var dataNeedUpdate = {
        address: formData == null ? undefined : formData.address,
        postalCode: formData == null ? undefined : formData.postalCode
      };
      setShowModalEditAddress(false);
      setTimeout(function () {
        onUpdateProfile(dataNeedUpdate, _constants.UpdateProfileSectionKey.ADDRESS);
      }, 200);
    };
    var btnEditPhonePress = function btnEditPhonePress() {
      dispatch(_profileRedux.default.sendOTPReset());
      dispatch(_profileRedux.default.setEditContactDetailKey(_constants.UpdateProfileSectionKey.PHONE_NUMBER));
      setShowModalEditMobileNumber(true);
    };
    var renderPhoneNumberItem = function renderPhoneNumberItem() {
      if ((0, _lodash.isEmpty)(personalData == null ? undefined : personalData.phoneNum)) {
        return (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.phoneNum",
          value: "None",
          isEditValue: true,
          isBottomLine: true,
          onPress: btnEditPhonePress,
          valueContainer: styles.valueContainer,
          testID: `${screenName}__${SECTION_NAME}__PhoneNumberItemView`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__PhoneNumberItemView`
        });
      } else {
        return (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
          labelTx: "profile.phoneNum",
          value: "+" + (personalData == null ? undefined : personalData.countryCode) + " " + (personalData == null ? undefined : personalData.phoneNum),
          isEditValue: true,
          isBottomLine: true,
          onPress: btnEditPhonePress,
          valueContainer: styles.valueContainer,
          verifiedStatus: personalData == null ? undefined : personalData.verifiedMobile,
          testID: `${screenName}__${SECTION_NAME}__PhoneNumberItemView`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__PhoneNumberItemView`
        });
      }
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        reset();
        setShowModalEditEmail(false);
        setShowModalEditAddress(false);
        setShowModalEditMobileNumber(false);
      };
    }, []));
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        tx: "profile.sectionTitle.contactDetails",
        style: styles.sectionTitleStyles
      }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
        labelTx: "profile.email",
        value: personalData == null ? undefined : personalData.email,
        isEditValue: true,
        isBottomLine: true,
        valueContainer: styles.valueContainer,
        verifiedStatus: personalData == null ? undefined : personalData.verifiedEmail,
        onPress: function onPress() {
          dispatch(_profileRedux.default.setEditContactDetailKey(_constants.UpdateProfileSectionKey.EMAIL_ADDRESS));
          setShowModalEditEmail(true);
        },
        testID: `${screenName}__${SECTION_NAME}__EmailItemView`,
        accessibilityLabel: `${screenName}__${SECTION_NAME}__EmailItemView`
      }), renderPhoneNumberItem(), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
        labelTx: "profile.address",
        value: (0, _lodash.isEmpty)(personalData == null ? undefined : personalData.address) ? (0, _i18n.translate)("common.none") : personalData == null ? undefined : personalData.address,
        valueLineTwo: personalData == null ? undefined : personalData.postalCode,
        isEditValue: true,
        onPress: function onPress() {
          return setShowModalEditAddress(true);
        },
        valueContainer: styles.valueContainer,
        testID: `${screenName}__${SECTION_NAME}__AddressItemView`,
        accessibilityLabel: `${screenName}__${SECTION_NAME}__AddressItemView`
      }), (0, _jsxRuntime.jsx)(_addressModalEdit.default, {
        disabled: disabledBtnVerifyAddress,
        hasError: hasError,
        firstError: firstError,
        setFieldValue: setFieldValue,
        clearErrorAt: clearErrorAt,
        formData: formData,
        onPressUpdate: handleSubmit(btnUpdateAddressOnPress),
        screenName: screenName,
        SECTION_NAME: SECTION_NAME,
        modalEditAddressVisible: modalEditAddressVisible,
        onClosedEditModalAddress: onClosedEditModalAddress
      }), (0, _jsxRuntime.jsx)(_phoneNumberVerifyModal.default, {
        resetFormData: reset,
        hasError: hasError,
        formData: formData,
        firstError: firstError,
        personalData: personalData,
        clearErrorAt: clearErrorAt,
        setFieldValue: setFieldValue,
        disabled: disabledBtnVerifyPhone,
        validateAt: validateAt,
        screenName: screenName,
        SECTION_NAME: SECTION_NAME,
        modalEditMobileNumberVisible: modalEditMobileNumberVisible,
        setShowModalEditMobileNumber: setShowModalEditMobileNumber
      }), (0, _jsxRuntime.jsx)(_emailModalEdit.default, {
        resetFormData: reset,
        hasError: hasError,
        formData: formData,
        firstError: firstError,
        personalData: personalData,
        clearErrorAt: clearErrorAt,
        setFieldValue: setFieldValue,
        disabled: disabledBtnVerifyEmail,
        validateAt: validateAt,
        screenName: screenName,
        SECTION_NAME: SECTION_NAME,
        modalEditEmailVisible: modalEditEmailVisible,
        setShowModalEditEmail: setShowModalEditEmail
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingBottom: 42,
      paddingHorizontal: 24,
      paddingTop: 50,
      width: "100%"
    },
    sectionTitleStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    valueContainer: {
      marginTop: 10
    }
  });
  var _default = exports.default = ContactDetails;
