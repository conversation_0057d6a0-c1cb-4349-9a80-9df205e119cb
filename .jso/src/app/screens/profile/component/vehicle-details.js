  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectDestructuringEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _infoItem = _$$_REQUIRE(_dependencyMap[9]);
  var _lodash = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _icons = _$$_REQUIRE(_dependencyMap[12]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _text2 = _$$_REQUIRE(_dependencyMap[14]);
  var _button = _$$_REQUIRE(_dependencyMap[15]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[17]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[18]);
  var _inputField = _$$_REQUIRE(_dependencyMap[19]);
  var _profileScreen = _$$_REQUIRE(_dependencyMap[20]);
  var _formHook = _$$_REQUIRE(_dependencyMap[21]);
  var _constants = _$$_REQUIRE(_dependencyMap[22]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[23]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[25]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _native = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var iconNoInternet = (0, _jsxRuntime.jsx)(_icons.InfoRed, {});
  var SECTION_NAME = "VehicleDetails";
  var isValidVehicleIU = function isValidVehicleIU(str) {
    return str ? /^[0-9]+$/.test(str) : true;
  };
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var VehicleDetails = function VehicleDetails(props) {
    var personalData = props.personalData,
      onUpdateProfile = props.onUpdateProfile,
      screenName = props.screenName;
    var PROFILE_SCREEN_CONTEXT_HANDLER = (0, _react.useContext)(_profileScreen.PROFILE_SCREEN_CONTEXT).Handlers;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isModalVisible = _useState2[0],
      setModalVisible = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFocusInput = _useState4[0],
      setFocusInput = _useState4[1];
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var errorEHR15 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR15";
    });
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoConnection = _useState6[0],
      setNoConnection = _useState6[1];
    var vehicleIUValue = (0, _react.useMemo)(function () {
      return (0, _lodash.isEmpty)(personalData == null ? undefined : personalData.vehicleIU) ? (0, _i18n.translate)("common.none") : personalData == null ? undefined : personalData.vehicleIU;
    }, [personalData]);
    var normalizeFormData = function normalizeFormData(profile) {
      return {
        firstName: (profile == null ? undefined : profile.firstName) || "",
        lastName: (profile == null ? undefined : profile.lastName) || "",
        phoneNum: (profile == null ? undefined : profile.phoneNum) || "",
        dob: (profile == null ? undefined : profile.dob) || "",
        gender: (profile == null ? undefined : profile.gender) || "",
        email: (profile == null ? undefined : profile.email) || "",
        residentialCountry: (profile == null ? undefined : profile.residentialCountry) || "SG",
        postalCode: (profile == null ? undefined : profile.postalCode) || "",
        countryCode: (profile == null ? undefined : profile.countryCode) || "65",
        address: (profile == null ? undefined : profile.address) || "",
        nationality: (profile == null ? undefined : profile.nationality) || "SG",
        vehicleIU: (profile == null ? undefined : profile.vehicleIU) || "",
        isStaff: !!(profile != null && profile.staffPassExpiry || profile != null && profile.staffPassNumber),
        staffPassExpiry: (profile == null ? undefined : profile.staffPassExpiry) || "",
        staffPassNumber: (profile == null ? undefined : profile.staffPassNumber) || ""
      };
    };
    var _useForm = (0, _formHook.useForm)({
        form: normalizeFormData(personalData),
        schema: PROFILE_SCREEN_CONTEXT_HANDLER.schema
      }),
      formData = _useForm.formData,
      setFieldValue = _useForm.setFieldValue,
      validateAt = _useForm.validateAt,
      handleSubmit = _useForm.handleSubmit,
      setFormData = _useForm.setFormData,
      isDirty = _useForm.isDirty,
      reset = _useForm.reset;
    var disabled = (0, _react.useMemo)(function () {
      if (isDirty) {
        try {
          return !_profileScreen.schemeLiteVehicleIU.isValidSync(formData);
        } catch (error) {
          return true;
        }
      }
      return true;
    }, [formData, isDirty]);
    var isValidFrom = (0, _react.useMemo)(function () {
      var _formData$vehicleIU;
      if ((0, _lodash.isEmpty)(formData.vehicleIU)) return false;
      if (!isFocusInput) {
        return !_profileScreen.schemeLiteVehicleIU.isValidSync(formData);
      }
      if (((_formData$vehicleIU = formData.vehicleIU) == null ? undefined : _formData$vehicleIU.length) > 9 && isDirty) {
        return disabled;
      }
      return false;
    }, [formData.vehicleIU, isDirty, isFocusInput]);
    (0, _react.useEffect)(function () {
      reset();
    }, [personalData]);
    var onClosedEditModal = function onClosedEditModal() {
      reset();
      setModalVisible(false);
    };
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var btnUpdateOnPress = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (form) {
        var isConnected = yield checkInternet();
        setNoConnection(!isConnected);
        if (isConnected) {
          var data = Object.assign({}, ((0, _objectDestructuringEmpty2.default)(form), form));
          data = Object.assign({}, data, {
            firstName: data.firstName.trim(),
            lastName: data.lastName.trim()
          });
          setFormData(Object.assign({}, form, data));
          reset();
          setModalVisible(false);
          setTimeout(function () {
            var _data;
            onUpdateProfile({
              vehicleIU: (_data = data) == null ? undefined : _data.vehicleIU
            }, _constants.UpdateProfileSectionKey.VEHICLE_DETAILS);
          }, 200);
        }
      });
      return function btnUpdateOnPress(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var getBackgroundButtonDisable = function getBackgroundButtonDisable(isDisabled) {
      if (isDisabled) {
        return {
          backgroundColor: _theme.color.palette.lightGrey
        };
      }
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        onClosedEditModal();
      };
    }, []));
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        preset: "h4",
        tx: "profile.sectionTitle.vehicleDetails",
        style: styles.sectionTitleStyles
      }), (0, _jsxRuntime.jsx)(_infoItem.InfoItemView, {
        isEditValue: true,
        labelTx: "profile.vehicleIU",
        value: vehicleIUValue,
        valueContainer: styles.valueContainer,
        onPress: function onPress() {
          return setModalVisible(true);
        },
        testID: `${screenName}__${SECTION_NAME}__VehicleIUItemView`,
        accessibilityLabel: `${screenName}__${SECTION_NAME}__VehicleIUItemView`
      }), (0, _jsxRuntime.jsx)(_bottomSheet.default, {
        isModalVisible: isModalVisible,
        containerStyle: styles.modalContainer,
        onClosedSheet: onClosedEditModal,
        stopDragCollapse: true,
        onBackPressHandle: onClosedEditModal,
        animationInTiming: 100,
        animationOutTiming: 100,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.bottomSheetEditContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.headerEditModal,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: "profile.modalEditTitle.verhicleDetail",
              style: styles.editModalTitle
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onClosedEditModal,
              style: styles.btnCloseModalStyles,
              testID: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
              children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
                width: 24,
                height: 24,
                fill: "currentColor",
                style: closeIconStyles
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
            showsVerticalScrollIndicator: false,
            enableResetScrollToCoords: false,
            style: styles.modalContentContainer,
            children: [(0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
              isInvalid: isValidFrom,
              labelTx: "profile.vehicleIU",
              helpText: isValidFrom ? (0, _i18n.translate)("profile.errorVehicleIU") : "",
              children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
                autoCapitalize: "none",
                autoCorrect: false,
                keyboardType: "numeric",
                maxLength: 10,
                placeholderTx: "profile.placeholderVehicleIU",
                value: formData.vehicleIU,
                isInvalid: isValidFrom,
                onChangeText: function onChangeText(value) {
                  isValidVehicleIU(value) && setFieldValue("vehicleIU", value);
                  validateAt("vehicleIU", formData);
                },
                onBlur: function onBlur() {
                  setFocusInput(false);
                },
                onFocus: function onFocus() {
                  return setFocusInput(true);
                },
                highlightOnFocused: true,
                testID: `${screenName}__InputVehicleIU`,
                accessibilityLabel: `${screenName}__InputVehicleIU`
              })
            }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: styles.btnUpdateContainer,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                tx: "profile.submit",
                typePreset: "primary",
                statePreset: disabled ? "disabled" : "default",
                onPress: handleSubmit(btnUpdateOnPress),
                backgroundPreset: "light",
                textPreset: "buttonLarge",
                sizePreset: "large",
                style: getBackgroundButtonDisable(disabled),
                textStyle: Object.assign({}, styles.textBtnUpdateStyle, {
                  color: !disabled ? _theme.color.palette.almostWhiteGrey : _theme.color.palette.darkGrey
                }),
                testID: `${screenName}__${SECTION_NAME}__ButtonSubmit`,
                accessibilityLabel: `${screenName}__${SECTION_NAME}__ButtonSubmit`
              })
            })]
          }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
            visible: isNoConnection,
            title: (0, _lodash.get)(errorEHR15, "header") || (0, _i18n.translate)("popupError.somethingWrong"),
            errorMessage: (0, _lodash.get)(errorEHR15, "subHeader") || (0, _i18n.translate)("popupError.updateProfileMessage"),
            onClose: function onClose() {
              return setNoConnection(false);
            },
            buttonText: (0, _lodash.get)(errorEHR15, "buttonLabel") || (0, _i18n.translate)("common.okay"),
            onButtonPressed: function onButtonPressed() {
              return setNoConnection(false);
            },
            icon: iconNoInternet,
            testID: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`
          })]
        })
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomSheetEditContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: _reactNative2.Dimensions.get("window").height - (_reactNative2.Platform.OS === "android" ? _reactNative2.StatusBar.currentHeight + 14 : 54),
      width: "100%"
    },
    btnCloseModalStyles: {
      position: "absolute",
      right: 0
    },
    btnUpdateContainer: {
      borderRadius: 60,
      flex: 1,
      height: 44,
      marginBottom: 37,
      marginTop: 24
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingBottom: 42,
      paddingHorizontal: 24,
      paddingTop: 50,
      width: "100%"
    },
    editModalTitle: Object.assign({}, _text2.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    headerEditModal: {
      flexDirection: "row",
      justifyContent: "center",
      marginHorizontal: 24,
      marginVertical: 21
    },
    modalContainer: {
      justifyContent: "flex-end",
      margin: 0
    },
    modalContentContainer: {
      paddingHorizontal: 24
    },
    sectionTitleStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    textBtnUpdateStyle: {
      color: _theme.color.palette.almostWhiteGrey,
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    valueContainer: {
      marginTop: 10
    }
  });
  var _default = exports.default = VehicleDetails;
