  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[12]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[13]);
  var _modalEditStyles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _i18n = _$$_REQUIRE(_dependencyMap[15]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeOtpInput = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _text2 = _$$_REQUIRE(_dependencyMap[18]);
  var _countDownHook = _$$_REQUIRE(_dependencyMap[19]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _lodash = _$$_REQUIRE(_dependencyMap[21]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[23]));
  var _reactNativeRootToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _inputField = _$$_REQUIRE(_dependencyMap[25]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[26]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _constants = _$$_REQUIRE(_dependencyMap[28]);
  var _utils = _$$_REQUIRE(_dependencyMap[29]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var iconNoInternet = (0, _jsxRuntime.jsx)(_icons.InfoRed, {});
  var screenWidth = _reactNative2.Dimensions.get("screen").width;
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var labelDisabled = {
    color: _theme.color.palette.darkGrey
  };
  var isValidEmail = function isValidEmail(value) {
    return value ? /^([A-Za-z0-9@.+']{1,50})$/.test(value) : true;
  };
  var EmailModalEdit = function EmailModalEdit(props) {
    var resetFormData = props.resetFormData,
      hasError = props.hasError,
      formData = props.formData,
      firstError = props.firstError,
      personalData = props.personalData,
      clearErrorAt = props.clearErrorAt,
      setFieldValue = props.setFieldValue,
      disabled = props.disabled,
      validateAt = props.validateAt,
      screenName = props.screenName,
      SECTION_NAME = props.SECTION_NAME,
      modalEditEmailVisible = props.modalEditEmailVisible,
      setShowModalEditEmail = props.setShowModalEditEmail;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      numberOTP = _useState2[0],
      setNumberOTP = _useState2[1];
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      stepEditEmail = _useState4[0],
      setStepEditEmail = _useState4[1];
    var messagesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var vToken = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.vToken);
    var sendOtpLoading = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.sendOtpLoading);
    var verifyOtpLoading = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.verifyOtpLoading);
    var verifyOtpStatus = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.verifyOtpStatus);
    var msg71 = messagesCommon == null ? undefined : messagesCommon.find(function (item) {
      return item.code === "MSG71";
    });
    var editContactDetailKey = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.editContactDetailKey);
    var sendOtpStatus = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.sendOtpStatus);
    var sendOtpMsgErr = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.sendOtpMsgErr);
    var errorEHR15 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR15";
    });
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoConnection = _useState6[0],
      setNoConnection = _useState6[1];
    var isErrorOTP = !(0, _lodash.isEmpty)(verifyOtpStatus) && verifyOtpStatus !== "ok";
    var _useCountDown = (0, _countDownHook.useCountDown)({
        initialTime: 50000,
        autoStart: stepEditEmail === 1
      }),
      isCounting = _useCountDown.isCounting,
      timeLeft = _useCountDown.timeLeft,
      start = _useCountDown.start;
    var getBackgroundButtonDisable = function getBackgroundButtonDisable(isDisabled) {
      if (isDisabled) {
        return {
          backgroundColor: _theme.color.palette.lightGrey
        };
      }
    };
    (0, _react.useEffect)(function () {
      if (verifyOtpStatus === "ok" && editContactDetailKey === _constants.UpdateProfileSectionKey.EMAIL_ADDRESS) {
        resetFormData();
        setNumberOTP("");
        setShowModalEditEmail(false);
        setStepEditEmail(0);
        dispatch(_profileRedux.default.updateProfileData({
          email: formData == null ? undefined : formData.email,
          verifiedEmail: true
        }));
        dispatch(_profileRedux.default.resetVerifyData());
        _reactNativeRootToast.default.show((0, _lodash.get)(messagesCommon == null ? undefined : messagesCommon.find(function (item) {
          return item.code === "MSG72";
        }), "title") || (0, _i18n.translate)("profile.msg72"), {
          duration: _reactNativeRootToast.default.durations.SHORT,
          position: -40,
          shadow: false,
          animation: true,
          hideOnPress: true,
          opacity: 1,
          backgroundColor: _theme.color.palette.almostBlackGrey,
          textColor: _theme.color.palette.whiteGrey,
          textStyle: {
            paddingHorizontal: 3,
            paddingVertical: 4,
            fontFamily: "Lato",
            fontSize: 14,
            lineHeight: 18,
            fontStyle: "normal"
          }
        });
      }
    }, [verifyOtpStatus]);
    (0, _react.useEffect)(function () {
      if (sendOtpStatus === "ok" && editContactDetailKey === _constants.UpdateProfileSectionKey.EMAIL_ADDRESS) {
        setStepEditEmail(1);
        start();
        dispatch(_profileRedux.default.sendOTPReset());
      }
    }, [sendOtpStatus]);
    var isShowVerifyEmailBtn = (0, _react.useMemo)(function () {
      return (personalData == null ? undefined : personalData.email) !== (formData == null ? undefined : formData.email);
    }, [personalData, formData]);
    var getResendTextStyles = function getResendTextStyles() {
      return {
        color: isCounting ? _theme.color.palette.darkestGrey : _theme.color.palette.lightPurple
      };
    };
    var timer = (0, _react.useMemo)(function () {
      return isCounting ? Math.floor(timeLeft / 1000) : 0;
    }, [timeLeft]);
    var onBackStepEditEmail = function onBackStepEditEmail() {
      dispatch(_profileRedux.default.resetVerifyData());
      setNumberOTP("");
      setStepEditEmail(0);
    };
    var onClosedEditModalEmail = function onClosedEditModalEmail() {
      resetFormData();
      setNumberOTP("");
      setStepEditEmail(0);
      setShowModalEditEmail(false);
      dispatch(_profileRedux.default.setEditContactDetailKey(""));
      dispatch(_profileRedux.default.sendOTPReset());
      dispatch(_profileRedux.default.resetVerifyData());
    };
    var onRendOnPress = function onRendOnPress() {
      if (!isCounting) {
        var email = formData == null ? undefined : formData.email;
        dispatch(_profileRedux.default.sendOTPRequest("", email));
        start();
      }
    };
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var btnSendOtpByEmail = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var isConnected = yield checkInternet();
        setNoConnection(!isConnected);
        if (isConnected) {
          var email = formData == null ? undefined : formData.email;
          dispatch(_profileRedux.default.sendOTPRequest("", email));
        }
      });
      return function btnSendOtpByEmail() {
        return _ref2.apply(this, arguments);
      };
    }();
    var _onCodeFilled = function onCodeFilled(otpCode) {
      var input = {
        otp: otpCode,
        email: formData == null ? undefined : formData.email,
        vToken: vToken
      };
      dispatch(_profileRedux.default.verifyOTPRequest(input));
    };
    var renderStatusComponent = function renderStatusComponent() {
      if ((personalData == null ? undefined : personalData.verifiedEmail) === undefined) {
        return null;
      }
      if (personalData != null && personalData.verifiedEmail) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _modalEditStyles.default.verifyContainer,
          children: [(0, _jsxRuntime.jsx)(_icons.VerifiedGreen, {
            width: 12,
            height: 12
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.verified",
            style: _modalEditStyles.default.verifiedTextStyles
          })]
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _modalEditStyles.default.verifyContainer,
        children: [(0, _jsxRuntime.jsx)(_icons.PedingVerify, {
          width: 12,
          height: 12
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "profile.pendingVerify",
          style: _modalEditStyles.default.pendingVerifyTextStyles
        })]
      });
    };
    var getEmailContainer = function getEmailContainer() {
      return {
        marginBottom: isShowVerifyEmailBtn ? 16 : 4
      };
    };
    var isInvalidEmail = function isInvalidEmail() {
      if ((0, _lodash.isEmpty)(sendOtpMsgErr)) {
        return hasError("email");
      } else return true;
    };
    var emailMsgErr = function emailMsgErr() {
      if ((0, _lodash.isEmpty)(sendOtpMsgErr)) {
        return firstError("email");
      } else return sendOtpMsgErr;
    };
    var renderComponentByStep = function renderComponentByStep() {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: stepEditEmail === 0 ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.modalEditTitle.emailLable1",
            style: _modalEditStyles.default.mobileNumberLable
          }), (0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
            isInvalid: isInvalidEmail(),
            labelTx: "profile.email",
            helpText: emailMsgErr(),
            style: getEmailContainer(),
            labelStyleProps: labelDisabled,
            isDisabled: true,
            children: (0, _jsxRuntime.jsx)(_inputField.InputField, {
              isDisabled: true,
              autoCapitalize: "none",
              autoCorrect: false,
              maxLength: 50,
              value: formData.email,
              isInvalid: isInvalidEmail(),
              onChangeText: function onChangeText(value) {
                return isValidEmail(value) && setFieldValue("email", value, function () {
                  !value && clearErrorAt("email");
                });
              },
              onBlur: function onBlur() {
                return validateAt("email", formData);
              },
              onFocus: function onFocus() {
                return dispatch(_profileRedux.default.sendOTPReset());
              },
              testID: `${screenName}__${SECTION_NAME}__InputEmail`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__InputEmail`,
              highlightOnFocused: true
            })
          }), (0, _utils.handleCondition)(isShowVerifyEmailBtn, (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: _modalEditStyles.default.btnUpdateContainer,
            start: {
              x: 0,
              y: 1
            },
            end: {
              x: 1,
              y: 0
            },
            colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
            children: (0, _jsxRuntime.jsx)(_button.Button, {
              tx: "profile.verifyViaOTP",
              typePreset: "primary",
              statePreset: (0, _utils.handleCondition)(disabled, "disabled", "default"),
              onPress: btnSendOtpByEmail,
              backgroundPreset: "light",
              textPreset: "buttonLarge",
              sizePreset: "large",
              style: getBackgroundButtonDisable(disabled),
              textStyle: Object.assign({}, _modalEditStyles.default.textBtnUpdateStyle, {
                color: (0, _utils.handleCondition)(!disabled, _theme.color.palette.almostWhiteGrey, _theme.color.palette.darkGrey)
              }),
              testID: `${screenName}__${SECTION_NAME}__ButtonVerifyEmail`,
              accessibilityLabel: `${screenName}__${SECTION_NAME}__ButtonVerifyEmail`
            })
          }), renderStatusComponent()), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.modalEditTitle.helpText",
            preset: "caption1Regular",
            style: styles.helpText
          })]
        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsxs)(_text.Text, {
            style: _modalEditStyles.default.mobileNumberLable,
            children: [(0, _i18n.translate)("profile.modalEditTitle.emailLable2"), (0, _jsxRuntime.jsx)(_text.Text, {
              text: formData == null ? undefined : formData.email,
              style: {
                fontWeight: _reactNative2.Platform.select({
                  ios: "700",
                  android: "normal"
                }),
                fontFamily: _theme.typography.bold
              }
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.otpInputNumberContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNativeOtpInput.default, {
              autoFocusOnLoad: true,
              style: styles.wrapInputNumber,
              pinCount: 6,
              code: numberOTP == null ? undefined : numberOTP.toString(),
              onCodeChanged: function onCodeChanged(code) {
                dispatch(_profileRedux.default.resetVerifyData());
                setNumberOTP(code);
              },
              onCodeFilled: function onCodeFilled(code) {
                return _onCodeFilled(code);
              },
              selectionColor: _theme.color.palette.lightPurple,
              codeInputFieldStyle: Object.assign({}, styles.inputNumber, isErrorOTP ? styles.errorInputStyle : {}),
              codeInputHighlightStyle: Object.assign({}, styles.borderStyleHighLighted, isErrorOTP ? styles.errorInputStyle : {})
            }), isErrorOTP && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _modalEditStyles.default.errorContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
                width: 20,
                height: 20,
                style: _modalEditStyles.default.errorIconStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.otpErrorMsgStyles,
                text: (0, _lodash.get)(msg71, "title") || (0, _i18n.translate)("profile.msg71"),
                numberOfLines: 1
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_text.Text, {
            style: styles.resendOtpView,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              onPress: onRendOnPress,
              style: getResendTextStyles(),
              children: "Resend OTP"
            }), (0, _utils.handleCondition)(timer, (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.timerTextStyles,
              children: ` in ${timer} secs`
            }), null)]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.editMobileNumberButton,
            onPress: onBackStepEditEmail,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "profile.editEmail",
              style: styles.editMobileNumberLink
            })
          })]
        })
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: modalEditEmailVisible,
      containerStyle: _modalEditStyles.default.modalContainer,
      onClosedSheet: onClosedEditModalEmail,
      stopDragCollapse: true,
      onBackPressHandle: onClosedEditModalEmail,
      animationInTiming: 100,
      animationOutTiming: 100,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _modalEditStyles.default.bottomSheetEditContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _modalEditStyles.default.headerEditModal,
          children: [stepEditEmail === 1 && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onBackStepEditEmail,
            style: _modalEditStyles.default.btnBackModalStyles,
            testID: `${screenName}__${SECTION_NAME}__BackStepModalEditEmail`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__BackStepModalEditEmail`,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
              width: "24",
              height: "24"
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: stepEditEmail === 0 ? "profile.modalEditTitle.editEmail" : "profile.modalEditTitle.verifyEmail",
            style: _modalEditStyles.default.editModalTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedEditModalEmail,
            style: _modalEditStyles.default.btnCloseModalStyles,
            testID: `${screenName}__${SECTION_NAME}__CloseModalEditEmail`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__CloseModalEditEmail`,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24,
              fill: "currentColor",
              style: closeIconStyles
            })
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
          showsVerticalScrollIndicator: false,
          enableResetScrollToCoords: false,
          style: _modalEditStyles.default.modalContentContainer,
          children: renderComponentByStep()
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
          visible: verifyOtpLoading || sendOtpLoading
        }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
          visible: isNoConnection,
          title: (0, _lodash.get)(errorEHR15, "header") || (0, _i18n.translate)("popupError.somethingWrong"),
          errorMessage: (0, _lodash.get)(errorEHR15, "subHeader") || (0, _i18n.translate)("popupError.updateProfileMessage"),
          onClose: function onClose() {
            return setNoConnection(false);
          },
          buttonText: (0, _lodash.get)(errorEHR15, "buttonLabel") || (0, _i18n.translate)("common.okay"),
          onButtonPressed: function onButtonPressed() {
            return setNoConnection(false);
          },
          icon: iconNoInternet,
          testID: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`,
          accessibilityLabel: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    borderStyleHighLighted: {
      borderColor: _theme.color.palette.lightPurple
    },
    editMobileNumberButton: {
      marginTop: 16
    },
    editMobileNumberLink: Object.assign({}, _text2.presets.textLink, {
      fontSize: 14,
      lineHeight: 18
    }),
    errorInputStyle: {
      borderColor: _theme.color.palette.baseRed
    },
    helpText: Object.assign({}, _text2.presets.caption1Regular, {
      color: _theme.color.palette.darkGrey,
      lineHeight: 18,
      marginTop: 8
    }),
    inputNumber: Object.assign({}, _text2.presets.h4, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 8,
      color: _theme.color.palette.almostBlackGrey,
      height: 48,
      textAlign: "center",
      width: 40
    }),
    otpErrorMsgStyles: {
      color: _theme.color.palette.baseRed,
      marginTop: 4
    },
    otpInputNumberContainer: {
      marginTop: 0
    },
    resendOtpView: Object.assign({}, _text2.presets.caption1Bold, {
      marginTop: 24,
      textAlign: "left"
    }),
    timerTextStyles: Object.assign({}, _text2.presets.caption1Regular),
    wrapInputNumber: Object.assign({}, _text2.presets.h4, {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderRadius: 8,
      color: _theme.color.palette.lightPurple,
      height: 48,
      textAlign: "center",
      width: screenWidth * 0.75
    })
  });
  var _default = exports.default = EmailModalEdit;
