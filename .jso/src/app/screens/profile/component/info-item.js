  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.InfoItemView = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var InfoItemView = exports.InfoItemView = function InfoItemView(props) {
    var lableText = props.lableText,
      labelTx = props.labelTx,
      valueStyles = props.valueStyles,
      lableStyles = props.lableStyles,
      valueContainer = props.valueContainer,
      itemStyles = props.itemStyles,
      _props$isEditValue = props.isEditValue,
      isEditValue = _props$isEditValue === undefined ? false : _props$isEditValue,
      value = props.value,
      valueLineTwo = props.valueLineTwo,
      icon = props.icon,
      valueTx = props.valueTx,
      _props$isBottomLine = props.isBottomLine,
      isBottomLine = _props$isBottomLine === undefined ? false : _props$isBottomLine,
      verifiedStatus = props.verifiedStatus,
      onPress = props.onPress,
      _props$numberOfLinesV = props.numberOfLinesValue,
      numberOfLinesValue = _props$numberOfLinesV === undefined ? 1 : _props$numberOfLinesV,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "testID" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "accessibilityLabel" : _props$accessibilityL;
    var renderStatusComponent = function renderStatusComponent() {
      if (verifiedStatus === undefined) {
        return null;
      } else {
        return verifiedStatus ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.verifyContainer,
          children: [(0, _jsxRuntime.jsx)(_icons.VerifiedGreen, {
            width: 12,
            height: 12
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.verified",
            style: styles.verifiedTextStyles
          })]
        }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.verifyContainer,
          children: [(0, _jsxRuntime.jsx)(_icons.PedingVerify, {
            width: 12,
            height: 12
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.pendingVerify",
            style: styles.pendingVerifyTextStyles
          })]
        });
      }
    };
    var Icon = icon;
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, styles.container, itemStyles),
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Bold",
          style: Object.assign({}, styles.lableTextStyles, lableStyles),
          tx: labelTx,
          testID: `${testID}__lable`,
          accessibilityLabel: `${accessibilityLabel}__lable`,
          children: lableText
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: Object.assign({}, styles.valueView, valueContainer),
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextBlackRegular",
            style: Object.assign({}, styles.valueTextStyles, valueStyles),
            tx: valueTx,
            numberOfLines: numberOfLinesValue,
            children: value
          }), isEditValue && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPress,
            testID: `${testID}__btnEdit`,
            accessibilityLabel: `${accessibilityLabel}__btnEdit`,
            children: icon ? (0, _jsxRuntime.jsx)(Icon, {}) : (0, _jsxRuntime.jsx)(_icons.Edit, {})
          })]
        }), !(0, _lodash.isEmpty)(valueLineTwo) && (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBlackRegular",
          style: Object.assign({}, styles.valueTextStyles, valueStyles),
          tx: valueTx,
          numberOfLines: numberOfLinesValue,
          children: valueLineTwo
        }), renderStatusComponent()]
      }), isBottomLine && (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.bottomLineStyles
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomLineStyles: {
      backgroundColor: _theme.color.palette.lightGrey,
      height: 1,
      marginTop: 16,
      width: "100%"
    },
    container: {
      marginTop: 16,
      paddingVertical: 8,
      width: "100%"
    },
    lableTextStyles: {
      color: _theme.color.palette.darkGrey,
      textAlign: "left"
    },
    pendingVerifyTextStyles: {
      color: _theme.color.palette.orangeDark,
      fontFamily: _theme.typography.regular,
      fontSize: 14,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 18,
      marginLeft: 4,
      textAlign: "left"
    },
    valueTextStyles: {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1,
      lineHeight: 20
    },
    valueView: {
      alignItems: "center",
      flexDirection: "row",
      marginTop: 8
    },
    verifiedTextStyles: {
      color: _theme.color.palette.basegreen,
      fontFamily: _theme.typography.regular,
      fontSize: 14,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 18,
      marginLeft: 4,
      textAlign: "left"
    },
    verifyContainer: {
      alignItems: "center",
      flexDirection: "row",
      marginTop: 10,
      width: "100%"
    }
  });
