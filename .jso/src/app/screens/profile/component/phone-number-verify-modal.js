  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _button = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[12]);
  var _inputWrapper = _$$_REQUIRE(_dependencyMap[13]);
  var _modalEditStyles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _inputPhone = _$$_REQUIRE(_dependencyMap[15]);
  var _countries = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[17]);
  var _i18n = _$$_REQUIRE(_dependencyMap[18]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[19]);
  var _reactNativeOtpInput = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _text2 = _$$_REQUIRE(_dependencyMap[21]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _lodash = _$$_REQUIRE(_dependencyMap[23]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[25]));
  var _reactNativeRootToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[27]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _constants = _$$_REQUIRE(_dependencyMap[29]);
  var _useCountTimer2 = _$$_REQUIRE(_dependencyMap[30]);
  var _utils = _$$_REQUIRE(_dependencyMap[31]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[32]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[33]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  // import ConfirmGoogleCaptcha from "react-native-google-recaptcha-v2"

  // import { env } from "app/config/env-params"
  var iconNoInternet = (0, _jsxRuntime.jsx)(_icons.InfoRed, {});
  var screenWidth = _reactNative2.Dimensions.get("screen").width;
  var closeIconStyles = {
    color: _theme.color.palette.lightPurple
  };
  var listOfContry = (0, _screenHelper.getListContryByPriorityOrder)(_countries.default);
  var countryCodeOptions = listOfContry.map(function (item) {
    return {
      value: item.code,
      label: item.country
    };
  });
  var PhoneNumberVerifyModal = function PhoneNumberVerifyModal(props) {
    var resetFormData = props.resetFormData,
      hasError = props.hasError,
      formData = props.formData,
      firstError = props.firstError,
      personalData = props.personalData,
      clearErrorAt = props.clearErrorAt,
      setFieldValue = props.setFieldValue,
      disabled = props.disabled,
      validateAt = props.validateAt,
      screenName = props.screenName,
      SECTION_NAME = props.SECTION_NAME,
      modalEditMobileNumberVisible = props.modalEditMobileNumberVisible,
      setShowModalEditMobileNumber = props.setShowModalEditMobileNumber;
    var dispatch = (0, _reactRedux.useDispatch)();
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var _useState = (0, _react.useState)(""),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      numberOTP = _useState2[0],
      setNumberOTP = _useState2[1];
    var _useState3 = (0, _react.useState)(0),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      stepEditPhoneNumber = _useState4[0],
      setStepEditPhoneNumber = _useState4[1];
    var messagesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var vToken = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.vToken);
    var editContactDetailKey = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.editContactDetailKey);
    var sendOtpLoading = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.sendOtpLoading);
    var verifyOtpLoading = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.verifyOtpLoading);
    var verifyOtpStatus = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.verifyOtpStatus);
    var sendOtpStatus = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.sendOtpStatus);
    var sendOtpMsgErr = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.sendOtpMsgErr);
    var errorEHR15 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR15";
    });
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoConnection = _useState6[0],
      setNoConnection = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isPassedCaptcha = _useState8[0],
      setPassedCaptcha = _useState8[1];
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isCaptchaErrorSend = _useState0[0],
      setCaptchaErrorSend = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isCaptchaErrorResend = _useState10[0],
      setCaptchaErrorResend = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isCaptchaNoConnection = _useState12[0],
      setCaptchaNoConnection = _useState12[1];
    var captchaRef = (0, _react.useRef)(null);
    var currentAction = (0, _react.useRef)("SEND_OTP");
    var siteKey = "6LdGEhEpAAAAAKiuh3265mIDYDdFB8oj4Dbbu7X3";
    var baseUrl = "https://google.com";
    var timeoutRef = (0, _react.useRef)(null);
    var otpInputRef = (0, _react.useRef)(null);
    var _useCountTimer = (0, _useCountTimer2.useCountTimer)(),
      isCounting = _useCountTimer.isCounting,
      timeLeft = _useCountTimer.timeLeft,
      start = _useCountTimer.start;
    var isErrorOTP = !(0, _lodash.isEmpty)(verifyOtpStatus) && verifyOtpStatus !== "ok";
    var msg71 = messagesCommon == null ? undefined : messagesCommon.find(function (item) {
      return item.code === "MSG71";
    });
    var isShowVerifyText = (0, _react.useMemo)(function () {
      return !(personalData != null && personalData.verifiedMobile);
    }, [personalData, formData]);
    var isShowVerifyPhoneNumBtn = (0, _react.useMemo)(function () {
      if (!(personalData != null && personalData.verifiedMobile)) return true;
      return (personalData == null ? undefined : personalData.phoneNum) !== (formData == null ? undefined : formData.phoneNum);
    }, [formData == null ? undefined : formData.phoneNum, personalData == null ? undefined : personalData.verifiedMobile]);
    var getBackgroundButtonDisable = function getBackgroundButtonDisable(isDisabled) {
      if (isDisabled) {
        return {
          backgroundColor: _theme.color.palette.lightGrey
        };
      }
    };
    (0, _react.useEffect)(function () {
      return clearTiming;
    }, []);
    var clearTiming = function clearTiming() {
      clearTimeout(timeoutRef.current);
    };
    (0, _react.useEffect)(function () {
      if (modalEditMobileNumberVisible === false) {
        setPassedCaptcha(false);
        setCaptchaErrorSend(false);
        setCaptchaErrorResend(false);
      }
    }, [modalEditMobileNumberVisible]);
    (0, _react.useEffect)(function () {
      if (verifyOtpStatus === "ok" && editContactDetailKey === _constants.UpdateProfileSectionKey.PHONE_NUMBER) {
        resetFormData();
        setNumberOTP("");
        setShowModalEditMobileNumber(false);
        setStepEditPhoneNumber(0);
        dispatch(_profileRedux.default.updateProfileData({
          phoneNum: formData == null ? undefined : formData.phoneNum,
          countryCode: formData == null ? undefined : formData.countryCode,
          verifiedMobile: true
        }));
        dispatch(_profileRedux.default.resetVerifyData());
        _reactNativeRootToast.default.show((0, _lodash.get)(messagesCommon == null ? undefined : messagesCommon.find(function (item) {
          return item.code === "MSG74";
        }), "title") || (0, _i18n.translate)("profile.msg74"), {
          duration: _reactNativeRootToast.default.durations.SHORT,
          position: -40,
          shadow: false,
          animation: true,
          hideOnPress: true,
          opacity: 1,
          backgroundColor: _theme.color.palette.almostBlackGrey,
          textColor: _theme.color.palette.whiteGrey,
          textStyle: {
            paddingHorizontal: 3,
            paddingVertical: 4,
            fontFamily: "Lato",
            fontSize: 14,
            lineHeight: 18,
            fontStyle: "normal"
          }
        });
      }
    }, [verifyOtpStatus]);
    (0, _react.useEffect)(function () {
      if (sendOtpStatus === "ok" && editContactDetailKey === _constants.UpdateProfileSectionKey.PHONE_NUMBER && vToken) {
        setStepEditPhoneNumber(1);
        start();
        if (_reactNative2.Platform.OS === "android") {
          var _otpInputRef$current;
          (_otpInputRef$current = otpInputRef.current) == null || _otpInputRef$current.focusField(numberOTP.length === 6 ? numberOTP.length - 1 : numberOTP.length);
        }
        dispatch(_profileRedux.default.sendOTPReset());
      }
    }, [sendOtpStatus]);
    var getPhoneNumContainer = function getPhoneNumContainer() {
      return {
        marginBottom: isShowVerifyText ? 16 : 4
      };
    };
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var btnSendOtpByPhone = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var isConnected = yield checkInternet();
        setNoConnection(!isConnected);
        if (isConnected) {
          // if (!isPassedCaptcha) {
          //   currentAction.current = "SEND_OTP"
          //   showCaptchaView()
          //   return
          // }
          onSendOtp();
        }
      });
      return function btnSendOtpByPhone() {
        return _ref2.apply(this, arguments);
      };
    }();
    var onSendOtp = function onSendOtp() {
      clearTiming();
      setPassedCaptcha(false);
      var phoneNumber = "+" + (formData == null ? undefined : formData.countryCode) + (formData == null ? undefined : formData.phoneNum);
      dispatch(_profileRedux.default.sendOTPRequest(phoneNumber, ""));
    };
    var onResendOtp = function onResendOtp() {
      var _otpInputRef$current2;
      // clear input opt and message
      setNumberOTP("");
      dispatch(_profileRedux.default.resetVerifyData());
      (_otpInputRef$current2 = otpInputRef.current) == null || _otpInputRef$current2.focusField(0);
      clearTiming();
      setPassedCaptcha(false);
      var phoneNumber = "+" + (formData == null ? undefined : formData.countryCode) + (formData == null ? undefined : formData.phoneNum);
      dispatch(_profileRedux.default.sendOTPRequest(phoneNumber, ""));
      start();
    };
    var proccessError = function proccessError(isError) {
      if (stepEditPhoneNumber === 0) {
        // send
        setCaptchaErrorSend(isError);
      } else {
        //  resend
        setCaptchaErrorResend(isError);
      }
    };
    var onMessage = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (event) {
        var _event$nativeEvent = event == null ? undefined : event.nativeEvent,
          data = _event$nativeEvent.data;
        var hideCaptcha = function hideCaptcha() {
          var _captchaRef$current2;
          return (_captchaRef$current2 = captchaRef.current) == null ? undefined : _captchaRef$current2.hide();
        };
        var handleResendOrSend = function handleResendOrSend() {
          switch (currentAction.current) {
            case "RESEND_OTP":
              onResendOtp();
              break;
            case "SEND_OTP":
              onSendOtp();
              break;
            default:
              break;
          }
        };
        if (!data) {
          hideCaptcha();
          return;
        }
        switch (data) {
          case "cancel":
            hideCaptcha();
            break;
          case "error":
          case "expired":
            {
              var isConnected = yield checkInternet();
              isConnected ? proccessError(true) : setCaptchaNoConnection(true);
              hideCaptcha();
              break;
            }
          default:
            setPassedCaptcha(true);
            proccessError(false);
            if (stepEditPhoneNumber === 1 && _reactNative2.Platform.OS === "ios") {
              var _otpInputRef$current3;
              (_otpInputRef$current3 = otpInputRef.current) == null || _otpInputRef$current3.focusField(numberOTP.length === 6 ? numberOTP.length - 1 : numberOTP.length);
            }
            hideCaptcha();
            setTimeout(handleResendOrSend, _reactNative2.Platform.OS === "ios" ? 1500 : 0);
            break;
        }
      });
      return function onMessage(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var onClosedEditModalMobileNumber = function onClosedEditModalMobileNumber() {
      resetFormData();
      setNumberOTP("");
      setStepEditPhoneNumber(0);
      setShowModalEditMobileNumber(false);
      dispatch(_profileRedux.default.setEditContactDetailKey(""));
      dispatch(_profileRedux.default.sendOTPReset());
      dispatch(_profileRedux.default.resetVerifyData());
    };
    var getResendTextStyles = function getResendTextStyles() {
      return {
        color: isCounting ? _theme.color.palette.darkestGrey : _theme.color.palette.lightPurple
      };
    };
    var onResendPress = function onResendPress() {
      if (!isCounting) {
        // if (!isPassedCaptcha) {
        //   currentAction.current = "RESEND_OTP"
        //   showCaptchaView()
        //   return
        // }
        onResendOtp();
      }
    };
    var onBackStepEditPhoneNumber = function onBackStepEditPhoneNumber() {
      setCaptchaErrorResend(false);
      dispatch(_profileRedux.default.resetVerifyData());
      setNumberOTP("");
      setStepEditPhoneNumber(0);
    };
    var timer = (0, _react.useMemo)(function () {
      return isCounting ? Math.floor(timeLeft / 1000) : 0;
    }, [timeLeft]);
    var renderStatusComponent = function renderStatusComponent(sendOtpMsgError, isShowVerifyPhoneNumButton, verifiedMobile, phoneNum) {
      if (!(0, _lodash.isEmpty)(sendOtpMsgError) || verifiedMobile === undefined || !verifiedMobile && (0, _lodash.isEmpty)(phoneNum)) {
        return null;
      }
      if (personalData != null && personalData.verifiedMobile) {
        if (!isShowVerifyPhoneNumButton) {
          return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _modalEditStyles.default.verifyContainer,
            children: [(0, _jsxRuntime.jsx)(_icons.VerifiedGreen, {
              width: 12,
              height: 12
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "profile.verified",
              style: _modalEditStyles.default.verifiedTextStyles
            })]
          });
        }
        return null;
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _modalEditStyles.default.verifyContainer,
        children: [(0, _jsxRuntime.jsx)(_icons.PedingVerify, {
          width: 12,
          height: 12
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "profile.pendingVerify",
          style: _modalEditStyles.default.pendingVerifyTextStyles
        })]
      });
    };
    var isInvalidPhoneNum = function isInvalidPhoneNum() {
      if ((0, _lodash.isEmpty)(sendOtpMsgErr)) {
        return hasError("phoneNum");
      } else return true;
    };
    var phongeNumMsgErr = function phongeNumMsgErr() {
      if ((0, _lodash.isEmpty)(sendOtpMsgErr)) {
        return firstError("phoneNum");
      } else return sendOtpMsgErr;
    };
    var onReloadData = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var isConnected = yield checkInternet();
        setCaptchaNoConnection(!isConnected);
      });
      return function onReloadData() {
        return _ref4.apply(this, arguments);
      };
    }();
    var renderComponentByStep = function renderComponentByStep() {
      if (isCaptchaNoConnection) {
        return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          hideScreenHeader: true,
          headerBackgroundColor: "transparent",
          visible: true,
          testID: `ErrorOverlayNoConnection`,
          onReload: onReloadData,
          storyMode: true,
          noInternetOverlayStyle: styles.noInternetConnection
        });
      }
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: stepEditPhoneNumber === 0 ? (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_inputWrapper.InputWrapper, {
            isInvalid: isInvalidPhoneNum(),
            labelTx: "profile.phoneNum",
            helpText: phongeNumMsgErr(),
            style: getPhoneNumContainer(),
            children: (0, _jsxRuntime.jsx)(_inputPhone.InputPhone, {
              isInvalid: isInvalidPhoneNum(),
              countryOptions: countryCodeOptions,
              phoneNumber: formData.phoneNum,
              countryCode: formData.countryCode,
              maxLength: 15,
              onPhoneChange: function onPhoneChange(value) {
                clearErrorAt("phoneNum");
                setFieldValue("phoneNum", value);
              },
              onCountryCodeChange: function onCountryCodeChange(value) {
                return setFieldValue("countryCode", value);
              },
              onBlurInput: function onBlurInput() {
                return validateAt("phoneNum", formData);
              },
              onFocus: function onFocus() {
                return dispatch(_profileRedux.default.sendOTPReset());
              },
              highlightOnFocused: true
            })
          }), (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [renderStatusComponent(sendOtpMsgErr, isShowVerifyPhoneNumBtn, personalData == null ? undefined : personalData.verifiedMobile, personalData == null ? undefined : personalData.phoneNum), (0, _utils.simpleCondition)({
              condition: isCaptchaErrorSend,
              ifValue: (0, _jsxRuntime.jsxs)(_text.Text, {
                style: styles.captchaError,
                children: [(0, _i18n.translate)("profile.captchaError1"), (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.captchaErrorDescription,
                  children: (0, _i18n.translate)("profile.captchaError2")
                })]
              }),
              elseValue: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})
            }), isShowVerifyPhoneNumBtn && (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: _modalEditStyles.default.btnUpdateContainer,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                tx: "profile.verifyViaOTP",
                typePreset: "primary",
                statePreset: (0, _utils.simpleCondition)({
                  condition: disabled,
                  ifValue: "disabled",
                  elseValue: "default"
                }),
                onPress: btnSendOtpByPhone,
                backgroundPreset: "light",
                textPreset: "buttonLarge",
                sizePreset: "large",
                style: getBackgroundButtonDisable(disabled),
                textStyle: Object.assign({}, _modalEditStyles.default.textBtnUpdateStyle, {
                  color: !disabled ? _theme.color.palette.almostWhiteGrey : _theme.color.palette.darkGrey
                }),
                testID: `${screenName}__${SECTION_NAME}__ButtonVerifyPhoneNum`,
                accessibilityLabel: `${screenName}__${SECTION_NAME}__ButtonVerifyPhoneNum`
              })
            })]
          })]
        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsxs)(_text.Text, {
            style: _modalEditStyles.default.mobileNumberLable,
            children: [(0, _i18n.translate)("profile.modalEditTitle.mobileNumberLable2"), (0, _jsxRuntime.jsx)(_text.Text, {
              text: "+" + (formData == null ? undefined : formData.countryCode) + " ****" + (formData == null ? undefined : formData.phoneNum.substring((formData == null ? undefined : formData.phoneNum.length) - 4)),
              style: {
                fontWeight: _reactNative2.Platform.select({
                  ios: "700",
                  android: "normal"
                }),
                fontFamily: _theme.typography.bold
              }
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.otpInputNumberContainer,
            children: [(0, _jsxRuntime.jsx)(_reactNativeOtpInput.default, {
              ref: otpInputRef,
              autoFocusOnLoad: true,
              style: styles.wrapInputNumber,
              pinCount: 6,
              code: numberOTP == null ? undefined : numberOTP.toString(),
              onCodeChanged: function onCodeChanged(code) {
                setNumberOTP(code);
              },
              onCodeFilled: function onCodeFilled(code) {
                return _onCodeFilled(code);
              },
              codeInputFieldStyle: Object.assign({}, styles.inputNumber, isErrorOTP ? styles.errorInputStyle : {}),
              codeInputHighlightStyle: Object.assign({}, styles.borderStyleHighLighted, isErrorOTP ? styles.errorInputStyle : {}),
              selectionColor: _theme.color.palette.lightPurple
            }), isErrorOTP && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _modalEditStyles.default.errorContainerStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.ErrorOutlined, {
                width: 20,
                height: 20,
                style: _modalEditStyles.default.errorIconStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.otpErrorMsgStyles,
                text: (0, _lodash.get)(msg71, "title"),
                numberOfLines: 1
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_text.Text, {
            style: styles.resendOtpView,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              onPress: onResendPress,
              style: getResendTextStyles(),
              preset: "bold",
              children: "Resend OTP"
            }), (0, _utils.handleCondition)(timer, (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.timerTextStyles,
              children: ` in ${timer} secs`
            }), null)]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.editMobileNumberButton,
            onPress: onBackStepEditPhoneNumber,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "profile.editMobileNumber",
              style: styles.editMobileNumberLink
            })
          }), (0, _utils.simpleCondition)({
            condition: isCaptchaErrorResend,
            ifValue: (0, _jsxRuntime.jsxs)(_text.Text, {
              style: styles.captchaError,
              children: [(0, _i18n.translate)("profile.captchaError1"), (0, _jsxRuntime.jsx)(_text.Text, {
                style: styles.captchaErrorDescription,
                children: (0, _i18n.translate)("profile.captchaError2")
              })]
            }),
            elseValue: (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})
          })]
        })
      });
    };
    var _onCodeFilled = function _onCodeFilled(otpCode) {
      var input = {
        otp: otpCode,
        phoneNumber: "+" + (formData == null ? undefined : formData.countryCode) + (formData == null ? undefined : formData.phoneNum),
        vToken: vToken
      };
      dispatch(_profileRedux.default.verifyOTPRequest(input));
    };
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      isModalVisible: modalEditMobileNumberVisible,
      containerStyle: _modalEditStyles.default.modalContainer,
      onClosedSheet: onClosedEditModalMobileNumber,
      stopDragCollapse: true,
      onBackPressHandle: onClosedEditModalMobileNumber,
      animationInTiming: 100,
      animationOutTiming: 100,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _modalEditStyles.default.bottomSheetEditContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _modalEditStyles.default.headerEditModal,
          children: [stepEditPhoneNumber === 1 && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onBackStepEditPhoneNumber,
            style: _modalEditStyles.default.btnBackModalStyles,
            testID: `${screenName}__${SECTION_NAME}__BackStepModalEdit`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__BackStepModalEdit`,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeft, {
              width: "24",
              height: "24"
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: stepEditPhoneNumber === 0 ? "profile.modalEditTitle.editMobileNumber" : "profile.modalEditTitle.verifyMobileNumber",
            style: _modalEditStyles.default.editModalTitle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onClosedEditModalMobileNumber,
            style: _modalEditStyles.default.btnCloseModalStyles,
            testID: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
            accessibilityLabel: `${screenName}__${SECTION_NAME}__CloseModalEdit`,
            children: (0, _jsxRuntime.jsx)(_icons.CloseCross, {
              width: 24,
              height: 24,
              fill: "currentColor",
              style: closeIconStyles
            })
          })]
        }), (0, _jsxRuntime.jsx)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
          showsVerticalScrollIndicator: false,
          enableResetScrollToCoords: false,
          style: _modalEditStyles.default.modalContentContainer,
          children: renderComponentByStep()
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
          visible: verifyOtpLoading || sendOtpLoading
        })]
      }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
        visible: isNoConnection,
        title: (0, _lodash.get)(errorEHR15, "header") || (0, _i18n.translate)("popupError.somethingWrong"),
        errorMessage: (0, _lodash.get)(errorEHR15, "subHeader") || (0, _i18n.translate)("popupError.updateProfileMessage"),
        onClose: function onClose() {
          return setNoConnection(false);
        },
        buttonText: (0, _lodash.get)(errorEHR15, "buttonLabel") || (0, _i18n.translate)("common.okay"),
        onButtonPressed: function onButtonPressed() {
          return setNoConnection(false);
        },
        icon: iconNoInternet,
        testID: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`,
        accessibilityLabel: `${screenName}__${SECTION_NAME}__BottomSheetErrorUpdateProfile`
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    borderStyleHighLighted: {
      borderColor: _theme.color.palette.lightPurple
    },
    captchaError: Object.assign({}, _text2.presets.caption1Bold, {
      color: _theme.color.palette.red,
      marginTop: 36
    }),
    captchaErrorDescription: Object.assign({}, _text2.presets.caption1Regular, {
      color: _theme.color.palette.red
    }),
    editMobileNumberButton: {
      marginTop: 16
    },
    editMobileNumberLink: Object.assign({}, _text2.presets.textLink, {
      fontSize: 14,
      lineHeight: 18
    }),
    errorInputStyle: {
      borderColor: _theme.color.palette.baseRed
    },
    inputNumber: Object.assign({}, _text2.presets.h4, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 8,
      color: _theme.color.palette.almostBlackGrey,
      height: 48,
      textAlign: "center",
      width: 40
    }),
    noInternetConnection: {
      height: "80%",
      justifyContent: "flex-start",
      paddingVertical: 30,
      width: "100%"
    },
    otpErrorMsgStyles: {
      color: _theme.color.palette.baseRed,
      marginTop: 4
    },
    otpInputNumberContainer: {
      marginTop: 0
    },
    resendOtpView: Object.assign({}, _text2.presets.caption1Bold, {
      marginTop: 24,
      textAlign: "left"
    }),
    timerTextStyles: Object.assign({}, _text2.presets.caption1Regular),
    wrapInputNumber: Object.assign({}, _text2.presets.h4, {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderRadius: 8,
      color: _theme.color.palette.lightPurple,
      height: 48,
      textAlign: "center",
      width: screenWidth * 0.75
    })
  });
  var _default = exports.default = PhoneNumberVerifyModal;
