  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetEditContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: _reactNative.Dimensions.get("window").height - (_reactNative.Platform.OS === "android" ? _reactNative.StatusBar.currentHeight + 14 : 54),
      width: "100%"
    },
    btnBackModalStyles: {
      left: 0,
      position: "absolute"
    },
    btnCloseModalStyles: {
      position: "absolute",
      right: 0
    },
    btnUpdateContainer: {
      borderRadius: 60,
      flex: 1,
      height: 44,
      marginBottom: 37,
      marginTop: 24
    },
    editModalTitle: Object.assign({}, _text.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    errorContainerStyle: {
      alignItems: "center",
      display: "flex",
      flexDirection: "row"
    },
    errorIconStyle: {
      marginRight: 5,
      marginTop: 5
    },
    headerEditModal: {
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 45,
      marginHorizontal: 24,
      marginTop: 21
    },
    mobileNumberLable: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 24
    }),
    modalContainer: {
      justifyContent: "flex-end",
      margin: 0
    },
    modalContentContainer: {
      paddingHorizontal: 24
    },
    pendingVerifyTextStyles: {
      color: _theme.color.palette.orangeDark,
      fontFamily: _theme.typography.regular,
      fontSize: 14,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 18,
      marginLeft: 4,
      textAlign: "left"
    },
    textBtnUpdateStyle: {
      color: _theme.color.palette.almostWhiteGrey,
      fontFamily: _theme.typography.bold,
      fontSize: 16,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 24,
      textAlign: "center"
    },
    verifiedTextStyles: {
      color: _theme.color.palette.basegreen,
      fontFamily: _theme.typography.regular,
      fontSize: 14,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 18,
      marginLeft: 4,
      textAlign: "left"
    },
    verifyContainer: {
      alignItems: "center",
      flexDirection: "row",
      width: "100%"
    }
  });
  var _default = exports.default = styles;
