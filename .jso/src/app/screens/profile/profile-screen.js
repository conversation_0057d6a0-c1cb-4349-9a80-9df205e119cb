  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ProfileScreen = ProfileScreen;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNativeRootToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var yup = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeKeyboardAwareScrollView = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _core = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _text = _$$_REQUIRE(_dependencyMap[13]);
  var _theme = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[15]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[16]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[18]);
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _nationalies = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _profileScreen = _$$_REQUIRE(_dependencyMap[21]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[22]);
  var _countries = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _component = _$$_REQUIRE(_dependencyMap[24]);
  var _constants = _$$_REQUIRE(_dependencyMap[25]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[26]);
  var _adobe = _$$_REQUIRE(_dependencyMap[27]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[28]);
  var _profileScreen2 = _$$_REQUIRE(_dependencyMap[29]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[30]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[31]);
  var _fly = _$$_REQUIRE(_dependencyMap[32]);
  var _icons = _$$_REQUIRE(_dependencyMap[33]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[34]);
  var _validate = _$$_REQUIRE(_dependencyMap[35]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[36]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[37]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var listOfContry = (0, _screenHelper.getListContryByPriorityOrder)(_countries.default);
  var SCREEN_NAME = "ProfileScreen";
  var countryOptions = listOfContry.map(function (item) {
    return {
      value: item.value,
      label: item.country
    };
  });
  var nationOptions = _nationalies.default.map(function (item) {
    return {
      value: item.value,
      label: item.name
    };
  }).sort(function (a, b) {
    return a.label.localeCompare(b.label);
  });
  function ProfileScreen() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("EDIT_PROFILE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var FLY_CONTEXT_HANDLERS = _react.default.useContext(_fly.FLY_CONTEXT).Handlers;
    var updateProfileSectionKey = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var profileError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileError);
    var profileUpdateFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileUpdateFetching);
    var profileUpdateError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileUpdateError);
    var profileUpdateSuccess = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileUpdateSuccess);
    var profileUpdateErrors = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileUpdateErrors);
    var navigation = (0, _core.useNavigation)();
    var profileContentData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.PROFILE_PAGE));
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var messagesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var PROFILE_SCREEN_CONTEXT_HANDLER = (0, _react.useContext)(_profileScreen2.PROFILE_SCREEN_CONTEXT).Handlers;
    var errorEHR78 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR7.8";
    });
    var errorEHR712 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR7.12";
    });
    var messageMSG70 = messagesCommon == null ? undefined : messagesCommon.find(function (item) {
      return item.code === "MSG70";
    });
    var loadingProfileAem = (0, _lodash.get)(profileContentData, "loading", false);
    var _useState3 = (0, _react.useState)(function () {
        return profileFetching;
      }),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isBooted = _useState4[0],
      setBooted = _useState4[1];
    var loading = profileUpdateFetching || profileFetching || loadingProfileAem;
    var errorEHR15 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR15";
    });
    var errorEHR29 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR29";
    });
    var errorEHR37 = errorCommon == null ? undefined : errorCommon.find(function (item) {
      return item.code === "EHR37";
    });
    var isInvalidIU = (profileUpdateErrors == null ? undefined : profileUpdateErrors.changi_rewards) === _constants2.PROFILE_UPDATE_ERROR_DETAILS.INVALID_IU;
    var invalidIUErrorHeader = isInvalidIU ? (0, _lodash.get)(errorEHR37, "header") : "";
    var invalidIUErrorSubHeader = isInvalidIU ? (0, _lodash.get)(errorEHR37, "subHeader") : "";
    var invalidIUErrorButtonLabel = isInvalidIU ? (0, _lodash.get)(errorEHR37, "buttonLabel") : "";
    var isExistingIU = (profileUpdateErrors == null ? undefined : profileUpdateErrors.changi_rewards) === _constants2.PROFILE_UPDATE_ERROR_DETAILS.EXISTING_IU;
    var existingIUErrorHeader = isExistingIU ? (0, _lodash.get)(errorEHR29, "header") : "";
    var existingIUErrorSubHeader = isExistingIU ? (0, _lodash.get)(errorEHR29, "subHeader") : "";
    var existingIUErrorButtonLabel = isExistingIU ? (0, _lodash.get)(errorEHR29, "buttonLabel") : "";
    var bottomSheetErrorTitle = invalidIUErrorHeader || existingIUErrorHeader || (0, _lodash.get)(errorEHR15, "header") || (0, _i18n.translate)("popupError.somethingWrong");
    var bottomSheetErrorMessage = invalidIUErrorSubHeader || existingIUErrorSubHeader || (0, _lodash.get)(errorEHR15, "subHeader") || (0, _i18n.translate)("popupError.updateProfileMessage");
    var bottomSheetErrorButtonText = invalidIUErrorButtonLabel || existingIUErrorButtonLabel || (0, _lodash.get)(errorEHR15, "buttonLabel") || (0, _i18n.translate)("common.okay");
    var splitedInvalidIUErrorSubHeader = invalidIUErrorSubHeader == null ? undefined : invalidIUErrorSubHeader.split(_constants.CONTACT_EMAIL);
    var invalidIUErrorMessageChildren = isInvalidIU && invalidIUErrorSubHeader != null && invalidIUErrorSubHeader.includes != null && invalidIUErrorSubHeader.includes(_constants.CONTACT_EMAIL) ? (0, _jsxRuntime.jsxs)(_text.Text, {
      style: _profileScreen.styles.errorMessageRegular,
      children: [splitedInvalidIUErrorSubHeader == null ? undefined : splitedInvalidIUErrorSubHeader[0], (0, _jsxRuntime.jsx)(_text.Text, {
        style: _profileScreen.styles.errorMessageBold,
        children: _constants.CONTACT_EMAIL
      }), splitedInvalidIUErrorSubHeader == null ? undefined : splitedInvalidIUErrorSubHeader[1]]
    }) : undefined;
    var splitedExistingIUErrorSubHeader = existingIUErrorSubHeader == null ? undefined : existingIUErrorSubHeader.split(_constants.CONTACT_EMAIL);
    var existingIUErrorMessageChildren = isExistingIU && existingIUErrorSubHeader != null && existingIUErrorSubHeader.includes != null && existingIUErrorSubHeader.includes(_constants.CONTACT_EMAIL) ? (0, _jsxRuntime.jsxs)(_text.Text, {
      style: _profileScreen.styles.errorMessageRegular,
      children: [splitedExistingIUErrorSubHeader == null ? undefined : splitedExistingIUErrorSubHeader[0], (0, _jsxRuntime.jsx)(_text.Text, {
        style: _profileScreen.styles.errorMessageBold,
        children: _constants.CONTACT_EMAIL
      }), splitedExistingIUErrorSubHeader == null ? undefined : splitedExistingIUErrorSubHeader[1]]
    }) : undefined;
    var bottomSheetErrorErrorMessageChildren = invalidIUErrorMessageChildren || existingIUErrorMessageChildren;
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.editProfile;
      });
      return data || {};
    }, [listErrorMaintenance]);
    PROFILE_SCREEN_CONTEXT_HANDLER.schema = yup.object().shape({
      firstName: yup.string().trim().required((0, _i18n.translate)("profile.errorFirstName")).test({
        message: (0, _i18n.translate)("nativeLoginScreen.supplementData.error.invalidName"),
        name: "firstName",
        test: function test(value) {
          return !value || _validate.profileNameRegex.test(value);
        }
      }),
      lastName: yup.string().trim().required((0, _i18n.translate)("profile.errorLastName")).test({
        message: (0, _i18n.translate)("nativeLoginScreen.supplementData.error.invalidName"),
        name: "lastName",
        test: function test(value) {
          return !value || _validate.profileNameRegex.test(value);
        }
      }),
      gender: yup.string().trim(),
      dob: yup.string().trim(),
      residentialCountry: yup.string().trim(),
      nationality: yup.string().trim(),
      email: yup.string().trim().required((0, _lodash.get)(messageMSG70, "title") || (0, _i18n.translate)("profile.errorEmail")).test({
        name: "email",
        message: (0, _lodash.get)(messageMSG70, "title") || (0, _i18n.translate)("profile.errorEmail"),
        test: function test(value) {
          return value ? /^[^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*@([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"0-9]{2,4}$/i.test(value) : true;
        }
      }),
      phoneNum: yup.string().trim().required((0, _i18n.translate)("profile.errorPhoneNum")).min(8, (0, _i18n.translate)("profile.errorPhoneMinLength")),
      address: yup.string().trim().test({
        name: "address",
        message: (0, _i18n.translate)("profile.errorAddress"),
        test: function test(value) {
          return value ? /^[A-Za-z0-9# \-.,/']{2,254}$/.test(value) : true;
        }
      }),
      postalCode: yup.string().trim().test({
        name: "postalCode",
        message: (0, _i18n.translate)("profile.errorPostalCode"),
        test: function test(value) {
          return value ? /^[A-Z0-9- ]{3,10}$/.test(value) : true;
        }
      }),
      vehicleIU: yup.string().trim().notRequired().matches(/(^$|^([17])([0-9\s]{9})+$)/, (0, _i18n.translate)("profile.errorVehicleIU")),
      isStaff: yup.bool(),
      staffPassNumber: yup.string().trim().notRequired().when("isStaff", {
        is: function is(value) {
          return !!value;
        },
        then: yup.string().trim().matches(/^([0-9A-Za-z]{1,16})$/, (0, _lodash.get)(errorEHR712, "subHeader") || (0, _i18n.translate)("profile.errorStaffPassNumber")).required((0, _lodash.get)(errorEHR712, "subHeader") || (0, _i18n.translate)("profile.errorStaffPassNumber")),
        otherwise: yup.string().trim().notRequired()
      }),
      staffPassExpiry: yup.string().trim().notRequired().when("isStaff", {
        is: function is(value) {
          return !!value;
        },
        then: yup.string().trim().required((0, _lodash.get)(errorEHR78, "subHeader") || (0, _i18n.translate)("profile.errorStaffPassExpiry")),
        otherwise: yup.string().trim().notRequired()
      })
    });
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_Profile");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_Profile", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        return isConnected;
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    // Initial profile data
    (0, _react.useEffect)(function () {
      // Prevent call profile if it currently in progress
      var fetchData = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var isConnected = yield checkInternet();
          setNoConnection(!isConnected);
          if (isConnected) {
            dispatch(_profileRedux.default.profileRequest());
          }
        });
        return function fetchData() {
          return _ref2.apply(this, arguments);
        };
      }();
      if (!isBooted) {
        setBooted(true);
        fetchData();
      }
    }, [isBooted]);
    var getMessage = function getMessage(mgsCode, defaultMsg) {
      return (0, _lodash.get)(messagesCommon == null ? undefined : messagesCommon.find(function (item) {
        return item.code === mgsCode;
      }), "title") || defaultMsg;
    };
    var getMessageUpdatedToast = function getMessageUpdatedToast() {
      switch (updateProfileSectionKey.current) {
        case _constants.UpdateProfileSectionKey.PERSONAL_DETAILS:
          return getMessage("MSG76", (0, _i18n.translate)("profile.msg76"));
        case _constants.UpdateProfileSectionKey.ADDRESS:
          return getMessage("MSG77", (0, _i18n.translate)("profile.msg77"));
        case _constants.UpdateProfileSectionKey.VEHICLE_DETAILS:
          return getMessage("MSG", (0, _i18n.translate)("profile.msg80"));
        case _constants.UpdateProfileSectionKey.AIRPORT_STAFF:
          return getMessage("MSG75", (0, _i18n.translate)("profile.msg75"));
        case _constants.UpdateProfileSectionKey.PHONE_NUMBER:
          return getMessage("MSG74", (0, _i18n.translate)("profile.msg74"));
        default:
          return (0, _i18n.translate)("profile.success");
      }
    };
    (0, _react.useEffect)(function () {
      if (profileUpdateSuccess) {
        dispatch(_profileRedux.default.profileUpdateReset());
        _reactNativeRootToast.default.show(getMessageUpdatedToast(), {
          duration: _reactNativeRootToast.default.durations.SHORT,
          position: -40,
          shadow: false,
          animation: true,
          hideOnPress: true,
          opacity: 1,
          backgroundColor: _theme.color.palette.almostBlackGrey,
          textColor: _theme.color.palette.whiteGrey,
          textStyle: {
            paddingHorizontal: 3,
            paddingVertical: 4,
            fontFamily: "Lato",
            fontSize: 14,
            lineHeight: 18,
            fontStyle: "normal"
          }
        });
      }
    }, [profileUpdateSuccess]);
    (0, _react.useEffect)(function () {
      var fetchAemData = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var isConnected = yield checkInternet();
          if (isConnected) {
            dispatch(_aemRedux.default.getAemConfigData({
              name: _aemRedux.AEM_PAGE_NAME.PROFILE_PAGE,
              pathName: "getProfileAemContent"
            }));
          }
        });
        return function fetchAemData() {
          return _ref3.apply(this, arguments);
        };
      }();
      fetchAemData();
      return function () {
        // Clear data on leave screen
        dispatch(_profileRedux.default.profileReset());
        dispatch(_profileRedux.default.profileUpdateReset());
      };
    }, []);
    var onReload = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var isConnected = yield checkInternet();
        setNoConnection(!isConnected);
        if (isConnected) {
          dispatch(_profileRedux.default.profileReset());
          dispatch(_profileRedux.default.profileRequest());
          dispatch(_aemRedux.default.getAemConfigData({
            name: _aemRedux.AEM_PAGE_NAME.PROFILE_PAGE,
            pathName: "getProfileAemContent"
          }));
        }
      });
      return function onReload() {
        return _ref4.apply(this, arguments);
      };
    }();
    var onUpdateProfile = function onUpdateProfile(data, keySectionUpdate) {
      updateProfileSectionKey.current = keySectionUpdate;
      dispatch(_profileRedux.default.profileUpdateRequest(data));
    };
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref5.apply(this, arguments);
      };
    }();
    if (isBooted && profileError) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        hideScreenHeader: false,
        header: false,
        visible: true,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
        onReload: onReload,
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlayUpdateProfile`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayUpdateProfile`,
        overlayStyle: _profileScreen.styles.overlayStyle,
        ignoreShowNoInternet: true
      });
    }
    if (!(0, _lodash.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${SCREEN_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorUnplannedMaintenance`
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        hideScreenHeader: false,
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: onReload,
        noInternetOverlayStyle: _profileScreen.styles.overlayStyle
      });
    }
    var renderAirportStaffView = function renderAirportStaffView() {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {});
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNativeKeyboardAwareScrollView.KeyboardAwareScrollView, {
        enableResetScrollToCoords: false,
        style: _profileScreen.styles.container,
        showsVerticalScrollIndicator: false,
        children: [(0, _jsxRuntime.jsx)(_component.PersonalDetails, {
          personalData: Object.assign({}, profilePayload, {
            gender: (0, _lodash.isEmpty)(profilePayload == null ? undefined : profilePayload.gender) ? "U" : profilePayload == null ? undefined : profilePayload.gender
          }),
          countryOptions: countryOptions,
          nationOptions: nationOptions,
          onUpdateProfile: onUpdateProfile,
          screenName: SCREEN_NAME
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _profileScreen.styles.spacingSection
        }), (0, _jsxRuntime.jsx)(_component.ContactDetails, {
          personalData: profilePayload,
          screenName: SCREEN_NAME,
          onUpdateProfile: onUpdateProfile
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _profileScreen.styles.spacingSection
        }), (0, _jsxRuntime.jsx)(_component.VehicleDetails, {
          personalData: profilePayload,
          onUpdateProfile: onUpdateProfile,
          screenName: SCREEN_NAME
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _profileScreen.styles.spacingSection
        }), renderAirportStaffView(), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _profileScreen.styles.accountDeleteSectionStyles,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            tx: "profile.accountDeletion.title",
            style: _profileScreen.styles.sectionTitleStyles
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "profile.accountDeletion.label",
            preset: "caption1Bold",
            style: _profileScreen.styles.deleteAccountTextStyle,
            onPress: function onPress() {
              return navigation.navigate("webview", {
                uri: "https://forms.office.com/Pages/ResponsePage.aspx?id=NJmMJfoiSECEyMUrRzGHzpvLqoCeK2NKusxsABc71LpURUczNzFTTDBRQjE4V0tYVDVGOUoyS0tPQS4u"
              });
            },
            testID: `${SCREEN_NAME}__DeleteAccountButton`,
            accessibilityLabel: `${SCREEN_NAME}__DeleteAccountButton`
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
        icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
        shouldFitContentHeight: true,
        visible: profileUpdateError,
        title: bottomSheetErrorTitle,
        errorMessage: bottomSheetErrorMessage,
        errorMessageChildren: bottomSheetErrorErrorMessageChildren,
        onClose: function onClose() {
          dispatch(_profileRedux.default.profileUpdateReset());
        },
        buttonText: bottomSheetErrorButtonText,
        onButtonPressed: function onButtonPressed() {
          dispatch(_profileRedux.default.profileUpdateReset());
        },
        iconUrl: (0, _lodash.get)(errorEHR15, "icon"),
        testID: `${SCREEN_NAME}__BottomSheetErrorUpdateProfile`,
        accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorUpdateProfile`
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingModal, {
        visible: loading
      })]
    });
  }
