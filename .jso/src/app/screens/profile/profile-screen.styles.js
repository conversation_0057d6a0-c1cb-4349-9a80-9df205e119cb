  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    accountDeleteSectionStyles: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingBottom: 180,
      paddingHorizontal: 24,
      paddingTop: 50,
      width: "100%"
    },
    bottomSheetStyleCommon: {
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: "100%",
      overflow: "hidden",
      width: "100%"
    },
    bottomSheetStyleNewResolution: {
      borderTopLeftRadius: 10,
      borderTopRightRadius: 10,
      height: "95%",
      overflow: "hidden",
      width: "100%"
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    deleteAccountTextStyle: {
      color: _theme.color.palette.lightPurple,
      marginTop: 24,
      textAlign: "left"
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      paddingBottom: 80
    },
    sectionTitleStyles: {
      color: _theme.color.palette.almostBlackGrey
    },
    spacingSection: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 8,
      width: "100%"
    },
    errorMessageRegular: Object.assign({}, _text.newPresets.bodyTextRegular, {
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    }),
    errorMessageBold: Object.assign({}, _text.newPresets.bodyTextBold, {
      textAlign: "center",
      color: _theme.color.palette.almostBlackGrey
    })
  });
