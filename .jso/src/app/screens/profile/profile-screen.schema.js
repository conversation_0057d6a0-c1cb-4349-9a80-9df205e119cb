  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.schemeLiteVehicleIU = exports.schemeLitePhoneNumber = exports.schemeLitePersonalDetail = exports.schemeLiteEmail = exports.schemeLiteAddress = exports.schemaLiteAirportStaff = exports.PROFILE_SCREEN_CONTEXT = exports.Handlers = undefined;
  var yup = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _i18n = _$$_REQUIRE(_dependencyMap[3]);
  var _validate = _$$_REQUIRE(_dependencyMap[4]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var Handlers = exports.Handlers = {
    schema: null
  };
  var PROFILE_SCREEN_CONTEXT = exports.PROFILE_SCREEN_CONTEXT = _react.default.createContext({
    Handlers: Handlers
  });
  var schemaLiteAirportStaff = exports.schemaLiteAirportStaff = yup.object().shape({
    isStaff: yup.bool(),
    staffPassNumber: yup.string().trim().notRequired().when("isStaff", {
      is: function is(value) {
        return !!value;
      },
      then: yup.string().trim().matches(/^([0-9A-Za-z]{5,16})$/).required(),
      otherwise: yup.string().trim().notRequired()
    }),
    staffPassExpiry: yup.string().trim().notRequired().when("isStaff", {
      is: function is(value) {
        return !!value;
      },
      then: yup.string().trim().required(),
      otherwise: yup.string().trim().notRequired()
    })
  });
  var schemeLitePersonalDetail = exports.schemeLitePersonalDetail = yup.object().shape({
    firstName: yup.string().trim().required().matches(_validate.profileNameRegex),
    lastName: yup.string().trim().required().matches(_validate.profileNameRegex),
    gender: yup.string().trim(),
    dob: yup.string().trim(),
    residentialCountry: yup.string().trim(),
    nationality: yup.string().trim()
  });
  var schemeLiteEmail = exports.schemeLiteEmail = yup.object().shape({
    email: yup.string().trim().required().matches(/^[^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*@([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"0-9]{2,4}$/i)
  });
  var schemeLitePhoneNumber = exports.schemeLitePhoneNumber = yup.object().shape({
    phoneNum: yup.string().trim().required().min(8)
  });
  var schemeLiteAddress = exports.schemeLiteAddress = yup.object().shape({
    address: yup.string().trim(),
    postalCode: yup.string().trim().test({
      name: "postalCode",
      message: (0, _i18n.translate)("profile.errorPostalCode"),
      test: function test(value) {
        return value ? /^[A-Z0-9- ]{3,10}$/.test(value) : true;
      }
    })
  });
  var schemeLiteVehicleIU = exports.schemeLiteVehicleIU = yup.object().shape({
    vehicleIU: yup.string().trim().notRequired().matches(/(^$|^([017])([0-9\s]{9})+$)/)
  });
