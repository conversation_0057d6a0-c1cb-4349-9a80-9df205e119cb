  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[10]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _check_mark = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[15]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var YourRewardRedeemSuccessScreen = function YourRewardRedeemSuccessScreen(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route;
    var _route$params = route == null ? undefined : route.params,
      data = _route$params.data,
      qtyRedeemed = _route$params.qtyRedeemed;
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var MSG88 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG88";
    });
    var currentTime = (0, _dateTime.getCurrentTimeSingapore)();
    var redeemedTime = "Used " + (0, _moment.default)(currentTime).format(_dateTime.DateFormats.DayMonthYear);
    var onPressOkay = function onPressOkay() {
      return navigation.pop(2);
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _styles.styles.container,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        showsVerticalScrollIndicator: false,
        contentContainerStyle: _styles.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
          source: _backgrounds.RedeemSuccessYourReward,
          imageStyle: _styles.styles.backgroundImage,
          resizeMode: "cover",
          children: [(0, _jsxRuntime.jsx)(_lottieReactNative.default, {
            style: _styles.styles.lottieBackgroundStyle,
            source: _check_mark.default,
            autoPlay: true,
            loop: false,
            autoSize: false,
            progress: 1
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.styles.wrapContent,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: MSG88 == null ? undefined : MSG88.title,
              style: _styles.styles.titleLottieStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: MSG88 == null ? undefined : MSG88.message,
              style: _styles.styles.messageLottieStyle
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.styles.cardContentPremium,
              children: (0, _jsxRuntime.jsxs)(_react.Fragment, {
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "subTitleBold",
                  style: _styles.styles.subHeading,
                  text: data == null ? undefined : data.title,
                  numberOfLines: 2
                }), (0, _jsxRuntime.jsx)(_baseImage.default, {
                  source: {
                    uri: data == null ? undefined : data.imageUrl
                  },
                  style: _styles.styles.imageVoucher
                }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _styles.styles.wrapValidTill,
                  children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                    style: _styles.styles.wrapQty,
                    children: (0, _jsxRuntime.jsx)(_text.Text, {
                      text: `Qty: ${qtyRedeemed}`,
                      style: _styles.styles.quantityText
                    })
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.validTillDateText,
                    text: redeemedTime,
                    numberOfLines: 1
                  })]
                })]
              })
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _styles.styles.buttonOkayStyle,
          onPress: onPressOkay,
          activeOpacity: 0.5,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: MSG88 == null ? undefined : MSG88.firstButton,
            style: _styles.styles.textButtonOkayStyle
          })
        })]
      })
    });
  };
  var _default = exports.default = YourRewardRedeemSuccessScreen;
