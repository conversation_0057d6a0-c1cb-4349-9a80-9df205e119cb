  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.CONTENT_WIDTH = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height,
    scale = _Dimensions$get.scale;
  var _Dimensions$get2 = _reactNative.Dimensions.get("screen"),
    scaleScreen = _Dimensions$get2.scale;
  var ratio = scale / scaleScreen;
  var CONTENT_WIDTH = exports.CONTENT_WIDTH = width - 48;
  var ImageHeight = height / 3 * ratio;
  var AnimatedSquareGifSize = width / 2 * ratio;
  var newLightestGrey = "#D5BBEA";
  var styles = exports.styles = _reactNative.StyleSheet.create({
    backgroundImage: {
      borderBottomLeftRadius: 40,
      height: ImageHeight,
      position: "absolute",
      width: width
    },
    buttonOkayStyle: {
      alignItems: "center",
      borderColor: _theme.color.palette.lightPurple,
      borderRadius: 60,
      borderWidth: 2,
      height: 44,
      justifyContent: "center",
      marginLeft: 24,
      width: width - 48
    },
    cardContentPremium: Object.assign({
      alignItems: "center",
      alignSelf: "center",
      borderRadius: 16,
      margin: 24,
      marginBottom: 50,
      padding: 24,
      width: CONTENT_WIDTH
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        borderRadius: 20,
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        borderRadius: 20,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    container: {
      flex: 1
    },
    contentContainerStyle: {
      paddingBottom: 20
    },
    imageVoucher: {
      borderRadius: 16,
      height: 116,
      marginVertical: 16,
      width: 190
    },
    lottieBackgroundStyle: Object.assign({
      alignSelf: 'center',
      height: AnimatedSquareGifSize,
      width: AnimatedSquareGifSize
    }, _reactNative.Platform.select({
      ios: {
        marginTop: -AnimatedSquareGifSize / 16
      },
      android: {
        marginTop: -AnimatedSquareGifSize / 10
      }
    })),
    messageLottieStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.whiteGrey,
      paddingHorizontal: 24,
      textAlign: "center",
      textAlignVertical: "center"
    }),
    quantityText: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    subHeading: Object.assign({}, _text.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center"
    }),
    textButtonOkayStyle: Object.assign({}, _text.presets.textLink, {
      color: _theme.color.palette.lightPurple,
      lineHeight: 24
    }),
    titleLottieStyle: Object.assign({}, _text.presets.h2, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 28,
      marginBottom: 12,
      textAlign: 'center',
      textAlignVertical: "center"
    }),
    validTillDateText: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    wrapContent: {
      marginTop: -(AnimatedSquareGifSize / 3)
    },
    wrapQty: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: newLightestGrey,
      borderRadius: 8,
      borderWidth: 1,
      height: 26,
      justifyContent: "center",
      marginRight: 8,
      paddingHorizontal: 8,
      paddingVertical: 4
    },
    wrapValidTill: {
      alignItems: "center",
      flexDirection: "row"
    }
  });
