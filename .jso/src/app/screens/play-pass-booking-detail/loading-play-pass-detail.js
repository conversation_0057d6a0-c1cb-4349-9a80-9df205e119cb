  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LoadingPlayPassDetail = function LoadingPlayPassDetail() {
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_styles.styles.contentContainer, _theme.shadow.secondaryShadow, {
          paddingVertical: 53
        }],
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _styles.lighterGreyLoadingColors,
          shimmerStyle: _styles.loadingPageStyles.loadingStyle
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _styles.loadingPageStyles.loadingContainer,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _styles.lineLoadingColors,
          shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingLine, {
            width: 96
          })
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _styles.lineLoadingColors,
          shimmerStyle: _styles.loadingPageStyles.loadingLine
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: _styles.lineLoadingColors,
          shimmerStyle: _styles.loadingPageStyles.loadingLine
        })]
      })]
    });
  };
  var _default = exports.default = LoadingPlayPassDetail;
