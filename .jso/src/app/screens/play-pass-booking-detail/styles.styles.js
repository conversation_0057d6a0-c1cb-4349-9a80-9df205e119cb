  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.loadingPageStyles = exports.lineLoadingColors = exports.lighterGreyLoadingColors = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    pageContainer: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    safeAreaView: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    headerContainer: {
      height: 56,
      justifyContent: "center",
      alignItems: "center",
      borderBottomWidth: 1,
      borderBottomColor: _theme.color.palette.lighterGrey
    },
    backButtonHeaderStyles: {
      position: "absolute",
      left: 16,
      top: 0,
      bottom: 0,
      justifyContent: "center",
      alignItems: "center"
    },
    titleHeaderStyles: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center"
    },
    scrollView: {
      backgroundColor: _theme.color.palette.lightestGrey
    },
    backgroundLinearGradient: {
      borderBottomLeftRadius: 50,
      height: 228,
      position: "absolute",
      width: "100%"
    },
    contentContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      marginHorizontal: 24,
      padding: 24,
      marginTop: 24,
      borderRadius: 16,
      justifyContent: "center",
      alignItems: "center"
    },
    eventName: Object.assign({}, _text.presets.subTitleBold, {
      textAlign: "center",
      width: "100%"
    }),
    image: {
      width: 100,
      height: 100,
      marginTop: 16,
      borderRadius: 16
    },
    quantityContainer: {
      marginTop: 16,
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center"
    },
    valueQuantityContainer: {
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: 4,
      paddingHorizontal: 8,
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 8,
      marginLeft: 8,
      minWidth: 48
    },
    quantityLabel: Object.assign({}, _text.presets.caption1Regular),
    quantity: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    passName: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 16,
      width: "100%"
    }),
    containerBorderTop: {
      width: "100%",
      marginTop: 24,
      paddingTop: 16,
      borderTopWidth: 1,
      borderColor: _theme.color.palette.lighterGrey
    },
    locationContainer: {
      flexDirection: "row",
      alignItems: "center"
    },
    locationLabel: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 12,
      textAlign: "left"
    }),
    calendarContainer: {
      flexDirection: "row",
      marginTop: 12
    },
    calendarLabel: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left",
      marginBottom: 4
    }),
    addToCalendarAction: Object.assign({}, _text.presets.caption1Bold, {
      textAlign: "left",
      color: _theme.color.palette.lightPurple
    }),
    calendarRightContainer: {
      marginLeft: 12
    },
    importantTitleContainer: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center"
    },
    importantTitleLable: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 4
    }),
    importantMessage: Object.assign({}, _text.presets.caption1Regular, {
      marginTop: 8,
      textAlign: "center",
      width: "100%"
    }),
    importantFindOutMore: Object.assign({}, _text.presets.caption1Bold, {
      marginTop: 8,
      color: _theme.color.palette.lightPurple
    }),
    touchableShowReward: {
      borderRadius: 30,
      overflow: "hidden",
      width: "100%"
    },
    gradientBtnContainer: {
      width: "100%"
    },
    viewShowReward: {
      alignItems: "center",
      borderRadius: 30,
      flexDirection: "row",
      justifyContent: "center",
      paddingVertical: 10
    },
    showRewardLabel: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostWhiteGrey
    }),
    flexRowCenterItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 16
    },
    actionContainer: {
      padding: 24
    },
    actionLabel: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 8
    }),
    actionMsgLabel: Object.assign({}, _text.presets.caption1Italic, {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      marginLeft: 8,
      marginTop: 4
    }),
    borderTop: {
      borderTopWidth: 1,
      borderColor: _theme.color.palette.lighterGrey
    },
    internetError: {
      top: 175,
      width: "100%",
      position: "absolute"
    },
    errorOverlay: {
      top: 175,
      position: "absolute"
    }
  });
  var loadingPageStyles = exports.loadingPageStyles = _reactNative.StyleSheet.create({
    loadingStyle: {
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 4,
      width: 200,
      height: 160
    },
    loadingLine: {
      backgroundColor: _theme.color.palette.lightGrey,
      marginTop: 16,
      height: 16,
      borderRadius: 4,
      width: "100%"
    },
    loadingContainer: {
      marginHorizontal: 32,
      marginTop: 8
    }
  });
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var lineLoadingColors = exports.lineLoadingColors = [_theme.color.palette.greyCCCCCC, _theme.color.background, _theme.color.palette.greyCCCCCC];
