  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _native = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _walletRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[14]));
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[16]);
  var _storage = _$$_REQUIRE(_dependencyMap[17]);
  var _i18n = _$$_REQUIRE(_dependencyMap[18]);
  var AddCalendarEvent = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[21]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[22]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[23]);
  var _constants = _$$_REQUIRE(_dependencyMap[24]);
  var _loadingPlayPassDetail = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _htmlRichtext = _$$_REQUIRE(_dependencyMap[27]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[28]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[30]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[31]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[32]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[33]));
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[34]);
  var _forYouRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[36]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "PlayPassBookingDetail";
  var LINEAR_GRADIENT_COLOR = {
    FIRST: "#8A2AA2",
    SECOND: "#7A35B0"
  };
  var LOCATIONS = {
    FIRST: 0.2024,
    SECOND: 0.9531
  };
  var ANGLE = 215.32;
  var ANGLE_CENTER = {
    X: 0.5,
    Y: 0.5
  };
  var ACTION_TYPE = {
    VIEW_PLAY_PASS: "viewPlayPass",
    CANCEL_PLAY_PASS: "cancelPlayPass",
    EDIT_PLAY_PASS: "editPlayPass"
  };
  var ALLOW_CANCLE_TYPE = {
    NO: "no",
    YES: "yes"
  };
  var PLAY_PASS_EVENT_TYPE = "event";
  var PlayPassBookingDetail = function PlayPassBookingDetail() {
    var _detailPlayPass$info2, _detailPlayPass$info3, _detailPlayPass$info4;
    var _useRoute = (0, _native.useRoute)(),
      params = _useRoute.params;
    var onBackPreviousScreen = params.onBackScreen,
      disableGesture = params.disableGesture,
      enableBookingsOrdersCache = params.enableBookingsOrdersCache;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var profile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var detailPlayPass = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyPlayPassDetailsPayload);
    var detailPlayPassError = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyPlayPassDetailsError);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setConnected = _useState2[1];
    var detailPlayPassFetching = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyPlayPassDetailsFetching);
    var editPlayPassPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassUrlPayload(ACTION_TYPE.EDIT_PLAY_PASS));
    var cancelPlayPassPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassUrlPayload(ACTION_TYPE.CANCEL_PLAY_PASS));
    var viewPlayPassPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassUrlPayload(ACTION_TYPE.VIEW_PLAY_PASS));
    var onBackBtnPress = function onBackBtnPress() {
      return navigation.pop(2);
    };
    var onExitWebView = function onExitWebView() {
      setIsRefreshData(false);
      navigation.goBack();
    };
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)(ACTION_TYPE.EDIT_PLAY_PASS, false, undefined, onBackBtnPress, onBackBtnPress),
      getPlayPassEditUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var _useGeneratePlayPassU2 = (0, _screenHook.useGeneratePlayPassUrl)(ACTION_TYPE.VIEW_PLAY_PASS, false, undefined),
      getPlayPassDetailUrl = _useGeneratePlayPassU2.getPlayPassUrl;
    var _useGeneratePlayPassU3 = (0, _screenHook.useGeneratePlayPassUrl)(ACTION_TYPE.CANCEL_PLAY_PASS, false, undefined, onBackBtnPress, onBackBtnPress),
      getPlayPassCancelUrl = _useGeneratePlayPassU3.getPlayPassUrl;
    var _ref = detailPlayPass || {},
      _ref$editCount = _ref.editCount,
      editCount = _ref$editCount === undefined ? 0 : _ref$editCount,
      _ref$editMaxCount = _ref.editMaxCount,
      editMaxCount = _ref$editMaxCount === undefined ? 0 : _ref$editMaxCount,
      _ref$latestEditBefSta = _ref.latestEditBefStartMins,
      latestEditBefStartMins = _ref$latestEditBefSta === undefined ? 0 : _ref$latestEditBefSta,
      _ref$latestCancelBefS = _ref.latestCancelBefStartMins,
      latestCancelBefStartMins = _ref$latestCancelBefS === undefined ? 0 : _ref$latestCancelBefS,
      _ref$allowCancel = _ref.allowCancel,
      allowCancel = _ref$allowCancel === undefined ? null : _ref$allowCancel;
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isRefreshData = _useState4[0],
      setIsRefreshData = _useState4[1];
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var isEnableEdit = (0, _react.useMemo)(function () {
      var currentTime = (0, _momentTimezone.default)(new Date()).tz("Asia/Singapore");
      var startDateTime = _momentTimezone.default.tz(`${detailPlayPass == null ? undefined : detailPlayPass.startDate} ${detailPlayPass == null ? undefined : detailPlayPass.startTime}`, "Asia/Singapore");
      var isValidDate = startDateTime.subtract(Number(latestEditBefStartMins), "minutes").isSameOrAfter(currentTime);
      return editCount < editMaxCount && isValidDate;
    }, [detailPlayPass, editCount, editMaxCount, latestEditBefStartMins]);
    var isEnableCancel = (0, _react.useMemo)(function () {
      var currentTime = (0, _momentTimezone.default)(new Date()).tz("Asia/Singapore");
      var startDateTime = _momentTimezone.default.tz(`${detailPlayPass == null ? undefined : detailPlayPass.startDate} ${detailPlayPass == null ? undefined : detailPlayPass.startTime}`, "Asia/Singapore");
      var isValidDate = startDateTime.subtract(Number(latestCancelBefStartMins), "minutes").isSameOrAfter(currentTime);
      return isValidDate && allowCancel === ALLOW_CANCLE_TYPE.YES;
    }, [allowCancel, latestCancelBefStartMins, detailPlayPass]);
    var handleEnableBookingsOrdersCache = function handleEnableBookingsOrdersCache() {
      dispatch(_forYouRedux.default.dataBookingAndOrderCacheData(true));
    };
    var fetchDetailPlayPass = (0, _react.useCallback)(function () {
      if (profile != null && profile.email && params != null && params.bookingKey) {
        dispatch(_walletRedux.default.walletMyPlayPassDetailsRequest(profile == null ? undefined : profile.email, params == null ? undefined : params.bookingKey));
      }
    }, [profile == null ? undefined : profile.email, params == null ? undefined : params.bookingKey]);
    var convertSingaporeTimeToUtcTime = function convertSingaporeTimeToUtcTime(dateString) {
      var eventStartTime = _momentTimezone.default.tz(dateString, _dateTime.DateFormats.CalendarEventUTC, "Asia/Singapore").utc().format(_dateTime.DateFormats.CalendarEventUTC);
      return eventStartTime;
    };
    var showAlertOpenSettingPermisstion = function showAlertOpenSettingPermisstion() {
      _reactNative2.Alert.alert((0, _i18n.translate)("addEventToCalendar.needAccessPermission.title"), (0, _i18n.translate)("addEventToCalendar.needAccessPermission.description"), [{
        text: (0, _i18n.translate)("addEventToCalendar.needAccessPermission.firstButton"),
        style: "cancel",
        onPress: function onPress() {
          (0, _reactNativePermissions.openSettings)();
        }
      }, {
        text: (0, _i18n.translate)("addEventToCalendar.needAccessPermission.secondButton"),
        onPress: function onPress() {
          return null;
        }
      }]);
    };
    var addEventToCalendar = function addEventToCalendar(detailPP) {
      var eventConfig = {
        title: detailPP == null ? undefined : detailPP.subTitle,
        startDate: convertSingaporeTimeToUtcTime(`${detailPP == null ? undefined : detailPP.startDate} ${detailPP == null ? undefined : detailPP.startTime}`),
        endDate: convertSingaporeTimeToUtcTime(`${detailPP == null ? undefined : detailPP.endDate} ${detailPP == null ? undefined : detailPP.endTime}`),
        location: detailPP == null ? undefined : detailPP.location
      };
      AddCalendarEvent.presentEventCreatingDialog(eventConfig);
    };
    var requestPermissionCalendarForAndroid = function requestPermissionCalendarForAndroid() {
      (0, _reactNativePermissions.request)(_reactNativePermissions.PERMISSIONS.ANDROID.WRITE_CALENDAR).then(function (result) {
        (0, _storage.save)(_storage.StorageKey.isFirstRequestPermissionCalendar, true);
        switch (result) {
          case _reactNativePermissions.RESULTS.BLOCKED:
            showAlertOpenSettingPermisstion();
            break;
          case _reactNativePermissions.RESULTS.GRANTED:
            addEventToCalendar(detailPlayPass);
            break;
        }
      });
    };
    var requestPermissionCalendarForIos = function requestPermissionCalendarForIos() {
      (0, _reactNativePermissions.request)(_reactNativePermissions.PERMISSIONS.IOS.CALENDARS).then(function (result) {
        if (result === _reactNativePermissions.RESULTS.GRANTED) {
          addEventToCalendar(detailPlayPass);
        }
      });
    };
    var getIsFirstRequest = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var isFirstRequest = yield (0, _storage.load)(_storage.StorageKey.isFirstRequestPermissionCalendar);
        return isFirstRequest;
      });
      return function getIsFirstRequest() {
        return _ref2.apply(this, arguments);
      };
    }();
    var handleAddEventToCalendar = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        if (_reactNative2.Platform.OS === "android") {
          (0, _reactNativePermissions.check)(_reactNativePermissions.PERMISSIONS.ANDROID.WRITE_CALENDAR).then(/*#__PURE__*/function () {
            var _ref4 = (0, _asyncToGenerator2.default)(function* (result) {
              var isFirstRequest = yield getIsFirstRequest();
              switch (result) {
                case _reactNativePermissions.RESULTS.DENIED:
                  if (isFirstRequest) {
                    showAlertOpenSettingPermisstion();
                  } else {
                    requestPermissionCalendarForAndroid();
                  }
                  break;
                case _reactNativePermissions.RESULTS.GRANTED:
                  addEventToCalendar(detailPlayPass);
                  break;
              }
            });
            return function (_x) {
              return _ref4.apply(this, arguments);
            };
          }());
        } else {
          (0, _reactNativePermissions.check)(_reactNativePermissions.PERMISSIONS.IOS.CALENDARS).then(function (result) {
            switch (result) {
              case _reactNativePermissions.RESULTS.DENIED:
                requestPermissionCalendarForIos();
                break;
              case _reactNativePermissions.RESULTS.BLOCKED:
              case _reactNativePermissions.RESULTS.LIMITED:
                showAlertOpenSettingPermisstion();
                break;
              case _reactNativePermissions.RESULTS.GRANTED:
                addEventToCalendar(detailPlayPass);
                break;
            }
          });
        }
      });
      return function handleAddEventToCalendar() {
        return _ref3.apply(this, arguments);
      };
    }();
    var viewPlayPassDetail = (0, _react.useCallback)(function () {
      if (detailPlayPass != null && detailPlayPass.packageCd) {
        var cookiesData = {
          entryPoint: _exploreItemType.PlayPassEntryPoint.PASS_DETAIL_EDIT_DETAILS,
          eventName: detailPlayPass.packageCd
        };
        getPlayPassDetailUrl(_constants.StateCode.PPEVENT3, detailPlayPass.packageCd, cookiesData);
      }
    }, [detailPlayPass == null ? undefined : detailPlayPass.packageCd]);
    var editPlayPass = (0, _react.useCallback)(function () {
      if (detailPlayPass != null && detailPlayPass.bookingId && detailPlayPass != null && detailPlayPass.ticketIds) {
        var _detailPlayPass$ticke;
        var ticketId = detailPlayPass == null || (_detailPlayPass$ticke = detailPlayPass.ticketIds) == null ? undefined : _detailPlayPass$ticke.split(",")[0];
        var cookiesData = {
          entryPoint: _exploreItemType.PlayPassEntryPoint.PASS_DETAIL_EDIT_BOOKING,
          eventName: detailPlayPass.packageCd
        };
        getPlayPassEditUrl(_constants.StateCode.PPEDITNEW3, `${detailPlayPass == null ? undefined : detailPlayPass.bookingId}__${ticketId}`, cookiesData);
      }
    }, [detailPlayPass == null ? undefined : detailPlayPass.bookingId, detailPlayPass == null ? undefined : detailPlayPass.ticketIds]);
    var cancelPlayPass = (0, _react.useCallback)(function () {
      if (detailPlayPass != null && detailPlayPass.bookingId && detailPlayPass != null && detailPlayPass.ticketIds) {
        var _detailPlayPass$ticke2;
        var ticketId = detailPlayPass == null || (_detailPlayPass$ticke2 = detailPlayPass.ticketIds) == null ? undefined : _detailPlayPass$ticke2.split(",")[0];
        var cookiesData = {
          entryPoint: _exploreItemType.PlayPassEntryPoint.PASS_DETAIL_EDIT_BOOKING,
          eventName: detailPlayPass.packageCd
        };
        getPlayPassCancelUrl(_constants.StateCode.PPCANCEL3, `${detailPlayPass == null ? undefined : detailPlayPass.bookingId}__${ticketId}`, cookiesData);
      }
    }, [detailPlayPass == null ? undefined : detailPlayPass.bookingId, detailPlayPass == null ? undefined : detailPlayPass.ticketIds]);
    var findOutMorePress = function findOutMorePress() {
      var _detailPlayPass$info;
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: detailPlayPass == null || (_detailPlayPass$info = detailPlayPass.info) == null ? undefined : _detailPlayPass$info.url,
        onGoBack: onExitWebView
      });
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        setConnected(isConnectedNetInfo);
      });
      return function checkConnection() {
        return _ref5.apply(this, arguments);
      };
    }();
    var retry = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch2.isConnected;
        setConnected(isConnectedNetInfo);
        if (isConnectedNetInfo) {
          fetchDetailPlayPass();
        }
      });
      return function retry() {
        return _ref6.apply(this, arguments);
      };
    }();
    var errorInternetConnection = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        onReload: retry,
        header: false,
        headerBackgroundColor: "transparent",
        noInternetOverlayStyle: _styles.styles.internetError,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        visible: true
      });
    }, []);
    var errorOverlay = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        visible: true,
        onReload: retry,
        overlayStyle: _styles.styles.errorOverlay,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL
      });
    }, []);
    var handlePressGoBack = (0, _react.useCallback)(function () {
      if (enableBookingsOrdersCache) {
        handleEnableBookingsOrdersCache();
      }
      onBackPreviousScreen == null || onBackPreviousScreen();
      navigation.goBack();
    }, [enableBookingsOrdersCache, onBackPreviousScreen]);
    var HeaderPage = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.headerContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: handlePressGoBack,
          style: _styles.styles.backButtonHeaderStyles,
          hitSlop: {
            top: 15,
            left: 15,
            right: 15,
            bottom: 15
          },
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftGray, {
            width: 24,
            height: 24
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "playPassBookingDetail.titlePage",
          preset: "bodyTextBold",
          style: _styles.styles.titleHeaderStyles
        })]
      });
    }, [enableBookingsOrdersCache, onBackPreviousScreen]);
    var onHardwareBackPress = function onHardwareBackPress() {
      if (enableBookingsOrdersCache) {
        handleEnableBookingsOrdersCache();
      }
      onBackPreviousScreen == null || onBackPreviousScreen();
      navigation.goBack();
      return true;
    };
    (0, _react.useEffect)(function () {
      checkConnection();
    }, []);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if (disableGesture) {
        navigation.setOptions({
          gestureEnabled: false
        });
      }
      var subscription = _reactNative2.BackHandler.addEventListener("hardwareBackPress", onHardwareBackPress);
      return function () {
        return subscription.remove();
      };
    }, [enableBookingsOrdersCache, disableGesture]));
    (0, _react.useEffect)(function () {
      if (isRefreshData && params != null && params.bookingKey) {
        fetchDetailPlayPass();
      }
      return function () {
        return setIsRefreshData(true);
      };
    }, [params == null ? undefined : params.bookingKey]);
    var onBottomSheetErrorClose = (0, _react.useCallback)(function () {
      if (cancelPlayPassPayload != null && cancelPlayPassPayload.error) {
        dispatch(_airportLandingRedux.default.getPlayPassUrlReset(ACTION_TYPE.CANCEL_PLAY_PASS));
      }
      if (viewPlayPassPayload != null && viewPlayPassPayload.error) {
        dispatch(_airportLandingRedux.default.getPlayPassUrlReset(ACTION_TYPE.VIEW_PLAY_PASS));
      }
      if (editPlayPassPayload != null && editPlayPassPayload.error) {
        dispatch(_airportLandingRedux.default.getPlayPassUrlReset(ACTION_TYPE.EDIT_PLAY_PASS));
      }
    }, [cancelPlayPassPayload == null ? undefined : cancelPlayPassPayload.error, viewPlayPassPayload == null ? undefined : viewPlayPassPayload.error, editPlayPassPayload == null ? undefined : editPlayPassPayload.error]);
    if (!isConnected) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.pageContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
          style: [_styles.styles.safeAreaView, {
            marginTop: inset.top
          }],
          children: [(0, _jsxRuntime.jsx)(HeaderPage, {}), errorInternetConnection]
        })
      });
    }
    if (detailPlayPassError) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.pageContainer,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
          style: [_styles.styles.safeAreaView, {
            marginTop: inset.top
          }],
          children: [(0, _jsxRuntime.jsx)(HeaderPage, {}), errorOverlay]
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.pageContainer,
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        backgroundColor: "transparent",
        barStyle: "dark-content",
        translucent: true
      }), (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
        style: [_styles.styles.safeAreaView, {
          marginTop: inset.top
        }],
        children: [(0, _jsxRuntime.jsx)(HeaderPage, {}), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          style: _styles.styles.scrollView,
          children: [(0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            style: _styles.styles.backgroundLinearGradient,
            colors: [LINEAR_GRADIENT_COLOR.FIRST, LINEAR_GRADIENT_COLOR.SECOND],
            locations: [LOCATIONS.FIRST, LOCATIONS.SECOND],
            useAngle: true,
            angle: ANGLE,
            angleCenter: {
              x: ANGLE_CENTER.X,
              y: ANGLE_CENTER.Y
            }
          }), detailPlayPassFetching ? (0, _jsxRuntime.jsx)(_loadingPlayPassDetail.default, {}) : (0, _jsxRuntime.jsxs)(_react.Fragment, {
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_styles.styles.contentContainer, _theme.shadow.secondaryShadow],
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.styles.eventName,
                text: detailPlayPass == null ? undefined : detailPlayPass.title
              }), (0, _jsxRuntime.jsx)(_baseImage.default, {
                style: _styles.styles.image,
                source: {
                  uri: detailPlayPass == null ? undefined : detailPlayPass.image
                },
                resizeMode: "cover"
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _styles.styles.quantityContainer,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: _styles.styles.quantityLabel,
                  tx: "playPassBookingDetail.quantityLabel"
                }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: _styles.styles.valueQuantityContainer,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.quantity,
                    text: detailPlayPass == null ? undefined : detailPlayPass.quantity
                  })
                })]
              }), (detailPlayPass == null ? undefined : detailPlayPass.type) === PLAY_PASS_EVENT_TYPE ? (0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.styles.passName,
                text: detailPlayPass == null ? undefined : detailPlayPass.passName
              }) : (0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.styles.passName,
                text: detailPlayPass == null ? undefined : detailPlayPass.productName
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _styles.styles.containerBorderTop,
                children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _styles.styles.locationContainer,
                  children: [(0, _jsxRuntime.jsx)(_icons.LocationPlayPassBookingDetailIcon, {
                    width: 20,
                    height: 20
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.locationLabel,
                    text: detailPlayPass == null ? undefined : detailPlayPass.location
                  })]
                }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _styles.styles.calendarContainer,
                  children: [(0, _jsxRuntime.jsx)(_icons.CalendarPlayPassBookingDetailIcon, {
                    width: 20,
                    height: 20
                  }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                    style: _styles.styles.calendarRightContainer,
                    children: [(detailPlayPass == null ? undefined : detailPlayPass.type) === PLAY_PASS_EVENT_TYPE ? (0, _jsxRuntime.jsx)(_text.Text, {
                      style: _styles.styles.calendarLabel,
                      text: detailPlayPass == null ? undefined : detailPlayPass.startScheduledTxt
                    }) : (0, _jsxRuntime.jsx)(_text.Text, {
                      style: _styles.styles.calendarLabel,
                      text: detailPlayPass == null ? undefined : detailPlayPass.endScheduledTxt
                    }), (detailPlayPass == null ? undefined : detailPlayPass.type) === PLAY_PASS_EVENT_TYPE && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                      onPress: handleAddEventToCalendar,
                      children: (0, _jsxRuntime.jsx)(_text.Text, {
                        style: _styles.styles.addToCalendarAction,
                        tx: "playPassBookingDetail.addToCalendar"
                      })
                    })]
                  })]
                })]
              }), (detailPlayPass == null || (_detailPlayPass$info2 = detailPlayPass.info) == null ? undefined : _detailPlayPass$info2.text) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _styles.styles.containerBorderTop,
                children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _styles.styles.importantTitleContainer,
                  children: [(0, _jsxRuntime.jsx)(_icons.ImportantPlayPassBookingDetailIcon, {
                    width: 20,
                    height: 20
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.importantTitleLable,
                    text: "Important"
                  })]
                }), (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
                  style: _styles.styles.importantMessage,
                  value: `<p>${detailPlayPass == null || (_detailPlayPass$info3 = detailPlayPass.info) == null ? undefined : _detailPlayPass$info3.text}</p>`
                }), (detailPlayPass == null || (_detailPlayPass$info4 = detailPlayPass.info) == null ? undefined : _detailPlayPass$info4.url) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: findOutMorePress,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.importantFindOutMore,
                    tx: "playPassBookingDetail.findOutMore"
                  })
                })]
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: [_styles.styles.containerBorderTop, {
                  marginTop: 16,
                  paddingTop: 24
                }],
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return _changiEcardControler.default.showModal(navigation);
                  },
                  testID: `${SCREEN_NAME}TouchableShowReward`,
                  accessibilityLabel: `${SCREEN_NAME}TouchableShowReward`,
                  style: _styles.styles.touchableShowReward,
                  children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
                    start: {
                      x: 0,
                      y: 1
                    },
                    end: {
                      x: 1,
                      y: 0
                    },
                    colors: [_theme.color.palette.lightPurple, _theme.color.palette.gradientColor1End],
                    style: _styles.styles.gradientBtnContainer,
                    children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                      style: _styles.styles.viewShowReward,
                      children: (0, _jsxRuntime.jsx)(_text.Text, {
                        tx: "playPassBookingDetail.showRewardCard",
                        preset: "bodyTextBold"
                      })
                    })
                  })
                })
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.styles.actionContainer,
              children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _styles.styles.flexRowCenterItem,
                children: [(0, _jsxRuntime.jsx)(_icons.PpEventDetailsIcon, {
                  width: 16,
                  height: 16
                }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: viewPlayPassDetail,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.actionLabel,
                    tx: "playPassBookingDetail.eventDetails"
                  })
                })]
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: [_styles.styles.flexRowCenterItem, _styles.styles.borderTop],
                children: [(0, _jsxRuntime.jsx)(_icons.PpEditIcon, {
                  width: 16,
                  height: 16,
                  color: !isEnableEdit ? _theme.color.palette.silver : _theme.color.palette.almostBlackGrey
                }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                  onPress: editPlayPass,
                  disabled: !isEnableEdit,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    style: [_styles.styles.actionLabel, !isEnableEdit ? {
                      color: _theme.color.palette.midGrey
                    } : {}],
                    tx: "playPassBookingDetail.editBooking"
                  }), !isEnableEdit && (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.actionMsgLabel,
                    tx: "playPassBookingDetail.editBookingDisableMsg"
                  })]
                })]
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: [_styles.styles.flexRowCenterItem, _styles.styles.borderTop],
                children: [(0, _jsxRuntime.jsx)(_icons.PpCancelIcon, {
                  width: 16,
                  height: 16,
                  color: !isEnableCancel ? _theme.color.palette.silver : _theme.color.palette.almostBlackGrey
                }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
                  disabled: !isEnableCancel,
                  onPress: cancelPlayPass,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    style: [_styles.styles.actionLabel, !isEnableCancel ? {
                      color: _theme.color.palette.midGrey
                    } : {}],
                    tx: "playPassBookingDetail.cancelBooking"
                  }), !isEnableCancel && (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.styles.actionMsgLabel,
                    tx: "playPassBookingDetail.cancelBookingDisableMsg"
                  })]
                })]
              })]
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
          icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
          visible: Boolean(editPlayPassPayload == null ? undefined : editPlayPassPayload.error) || Boolean(cancelPlayPassPayload == null ? undefined : cancelPlayPassPayload.error) || Boolean(viewPlayPassPayload == null ? undefined : viewPlayPassPayload.error),
          title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
          errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
          onClose: onBottomSheetErrorClose,
          onButtonPressed: onBottomSheetErrorClose,
          buttonText: (0, _i18n.translate)("subscription.close"),
          testID: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong`,
          accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong`
        })]
      })]
    });
  };
  var _default = exports.default = _react.default.memo(PlayPassBookingDetail);
