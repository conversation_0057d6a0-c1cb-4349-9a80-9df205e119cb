  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useParkingBenefitsMonarch = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _utils = _$$_REQUIRE(_dependencyMap[6]);
  var useParkingBenefitsMonarch = exports.useParkingBenefitsMonarch = function useParkingBenefitsMonarch() {
    var _useState = (0, _react.useState)(null),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      faqList = _useState2[0],
      setFAQList = _useState2[1];
    var _useState3 = (0, _react.useState)(true),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isFAQFetching = _useState4[0],
      setIsFAQFetching = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isFAQError = _useState6[0],
      setIsFAQError = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isRefresh = _useState8[0],
      setIsRefresh = _useState8[1];
    var fetchFAQList = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        try {
          var _response$data;
          setIsFAQFetching(true);
          setIsFAQError(false);
          yield new Promise(function (resolve) {
            return setTimeout(resolve, 1500);
          }); // Add small delay to view loading skeleton
          var paramsArray = _apis.default.getParkingBenefitsMonarchFAQs.split(" ");
          var method = paramsArray[0];
          var url = (0, _utils.mappingUrlAem)(paramsArray[1]);
          var response = yield (0, _request.default)({
            method: method,
            url: url
          });
          var payload = response == null || (_response$data = response.data) == null ? undefined : _response$data.list;
          if (payload) {
            setFAQList(payload);
            setIsFAQError(false);
          } else {
            setFAQList(null);
            setIsFAQError(true);
          }
        } catch (error) {
          setFAQList(null);
          setIsFAQError(true);
        } finally {
          setIsFAQFetching(false);
          setIsRefresh(false);
        }
      });
      return function fetchFAQList() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      fetchFAQList();
    }, []);
    (0, _react.useEffect)(function () {
      if (isRefresh && !isFAQFetching) {
        fetchFAQList();
      }
    }, [isRefresh]);
    return {
      faqList: faqList,
      fetchFAQList: fetchFAQList,
      isFAQError: isFAQError,
      isFAQFetching: isFAQFetching,
      isRefresh: isRefresh,
      setIsRefresh: setIsRefresh
    };
  };
