  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _parkingBenefitsMonarch = _$$_REQUIRE(_dependencyMap[6]);
  var _parkingBenefitsMonarch2 = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[10]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _parkingBenefitsMonarch3 = _$$_REQUIRE(_dependencyMap[12]);
  var _faqList = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[14]);
  var _react = _$$_REQUIRE(_dependencyMap[15]);
  var _native = _$$_REQUIRE(_dependencyMap[16]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _constants = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  var ParkingBenefitsMonarchScreen = function ParkingBenefitsMonarchScreen(props) {
    var navigation = props.navigation;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setIsConnected = _useState2[1];
    var _useParkingBenefitsMo = (0, _parkingBenefitsMonarch3.useParkingBenefitsMonarch)(),
      faqList = _useParkingBenefitsMo.faqList,
      isFAQFetching = _useParkingBenefitsMo.isFAQFetching,
      isRefresh = _useParkingBenefitsMo.isRefresh,
      setIsRefresh = _useParkingBenefitsMo.setIsRefresh;
    var handlePressBackBtn = function handlePressBackBtn() {
      navigation == null || navigation.goBack == null || navigation.goBack();
    };
    var handleDownloadGuidePDF = function handleDownloadGuidePDF() {
      navigation == null || navigation.navigate == null || navigation.navigate(_constants.NavigationConstants.webview, {
        uri: _parkingBenefitsMonarch.MONARCH_PARKING_GUIDE_PDF_URL
      });
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      _netinfo.default.fetch().then(function (state) {
        var _state$isConnected;
        setIsConnected((_state$isConnected = state.isConnected) != null ? _state$isConnected : true);
      });
    }, []));

    // No internet connection case
    if (!isConnected) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [_parkingBenefitsMonarch2.styles.headerContainerStyle, _parkingBenefitsMonarch2.styles.errorHeaderContainerStyle],
          children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__BackBtn`,
            androidRippleColor: "transparent",
            onPress: handlePressBackBtn,
            testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__BackBtn`,
            children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
              color: _theme.color.palette.darkestGrey
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__Title`,
            style: _parkingBenefitsMonarch2.styles.headerTitleTextStyle,
            testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__Title`,
            tx: "parkingBenefitsMonarch.title"
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: {
              width: 24
            }
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _parkingBenefitsMonarch2.styles.contentContainerStyle,
          children: (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {})
        })]
      });
    }
    // Normal case
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _parkingBenefitsMonarch2.styles.headerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__BackBtn`,
          androidRippleColor: "transparent",
          onPress: handlePressBackBtn,
          testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__BackBtn`,
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftV2, {
            color: _theme.color.palette.darkestGrey
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__Title`,
          style: _parkingBenefitsMonarch2.styles.headerTitleTextStyle,
          testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__Header__Title`,
          tx: "parkingBenefitsMonarch.title"
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: {
            width: 24
          }
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          colors: [_theme.color.palette.lightGrey],
          onRefresh: function onRefresh() {
            return setIsRefresh == null ? undefined : setIsRefresh(true);
          },
          progressBackgroundColor: "transparent",
          refreshing: isRefresh,
          tintColor: _theme.color.palette.lightGrey
        }),
        showsVerticalScrollIndicator: false,
        style: _parkingBenefitsMonarch2.styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BannerImage`,
          resizeMode: "contain",
          source: _backgrounds.ParkingBenefitsMonarchBanner,
          style: _parkingBenefitsMonarch2.styles.bannerImageStyle,
          testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BannerImage`
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: {
            paddingHorizontal: 20
          },
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _parkingBenefitsMonarch2.styles.benefitCardContainerStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__Title`,
              style: _parkingBenefitsMonarch2.styles.benefitCardTitleTextStyle,
              testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__Title`,
              tx: "parkingBenefitsMonarch.benefitCard.title"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__Description`,
              style: _parkingBenefitsMonarch2.styles.benefitCardDescriptionTextStyle,
              testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__Description`,
              tx: "parkingBenefitsMonarch.benefitCard.description"
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: {
                flexDirection: "row"
              },
              children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
                accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__CTAButton`,
                androidRippleColor: "transparent",
                onPress: handleDownloadGuidePDF,
                style: _parkingBenefitsMonarch2.styles.benefitCardCTABtnStyle,
                testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__CTAButton`,
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__CTAButton__Label`,
                  style: _parkingBenefitsMonarch2.styles.benefitCardCTABtnLabelTextStyle,
                  testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__BenefitCard__CTAButton__Label`,
                  tx: "parkingBenefitsMonarch.benefitCard.ctaButton"
                })
              })
            })]
          }), (0, _jsxRuntime.jsx)(_faqList.default, {
            data: faqList,
            isFetching: isFAQFetching
          })]
        })]
      })]
    });
  };
  var _default = exports.default = ParkingBenefitsMonarchScreen;
