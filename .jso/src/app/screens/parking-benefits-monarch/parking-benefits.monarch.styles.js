  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenHeight = _Dimensions$get.height,
    screenWidth = _Dimensions$get.width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    headerContainerStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      alignItems: "center",
      flexDirection: "row",
      height: 100,
      justifyContent: "space-between",
      paddingBottom: 16,
      paddingHorizontal: 16,
      paddingTop: 60 // Adjusted for safe area
    },
    errorHeaderContainerStyle: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1
    },
    headerTitleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    contentContainerStyle: {
      height: screenHeight,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    bannerImageStyle: {
      height: screenWidth * 250 / 375 + 1,
      // 1
      left: -1,
      // 2
      position: "absolute",
      top: 0,
      width: screenWidth + 1 // 3
      // 1, 2, 3: For BG image to cover the phone's edge
    },
    benefitCardContainerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      marginBottom: 40,
      marginTop: screenWidth * 195 / 375,
      padding: 16
    }, _theme.shadow.primaryShadow),
    benefitCardTitleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginBottom: 4
    }),
    benefitCardDescriptionTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      marginBottom: 16
    }),
    benefitCardCTABtnStyle: {
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 60,
      height: 28,
      paddingHorizontal: 12,
      paddingVertical: 5
    },
    benefitCardCTABtnLabelTextStyle: Object.assign({}, _text.newPresets.caption1Bold)
  });
