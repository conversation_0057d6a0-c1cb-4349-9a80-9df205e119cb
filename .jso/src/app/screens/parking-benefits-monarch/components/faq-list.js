  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.FAQList = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _parkingBenefitsMonarch = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _$$_REQUIRE(_dependencyMap[8]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _orderBy2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var LoadingSkeleton = function LoadingSkeleton() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.loadingContainerStyle,
      children: Array.from({
        length: 3
      }).map(function (_, index) {
        return (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.loadingItemContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: styles.loadingLine1stStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: styles.loadingLine2ndStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: LOADING_COLORS,
            shimmerStyle: styles.loadingLine3rdStyle
          })]
        }, `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__LoadingItem__${index}`);
      })
    });
  };
  var FAQListItemComponent = function FAQListItemComponent(props) {
    var item = props.item,
      index = props.index,
      updateExpandItems = props.updateExpandItems;
    var _useState = (0, _react.useState)(0),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      animationHeight = _useState2[0],
      setAnimationHeight = _useState2[1];
    (0, _react.useEffect)(function () {
      if (item != null && item.isExpanded) {
        setAnimationHeight("auto");
      } else {
        setAnimationHeight(0);
      }
    }, [item == null ? undefined : item.isExpanded]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.itemContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Item__${index}__Header`,
          androidRippleColor: "transparent",
          onPress: function onPress() {
            return updateExpandItems(index);
          },
          style: styles.itemHeaderContainerStyle,
          testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Item__${index}__Header`,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Item__${index}__Question`,
            style: styles.itemQuestionTextStyle,
            testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Item__${index}__Question`,
            text: item.question
          }), item != null && item.isExpanded ? (0, _jsxRuntime.jsx)(_icons.TopArrow, {
            height: 24,
            style: styles.expandIconStyle,
            width: 24
          }) : (0, _jsxRuntime.jsx)(_icons.DownArrow, {
            height: 24,
            style: styles.expandIconStyle,
            width: 24
          })]
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Item__${index}__Answer`,
          style: [styles.itemAnswerTextStyle, {
            height: animationHeight
          }],
          testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Item__${index}__Answer`,
          text: item.answer
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.dividerStyle
      })]
    });
  };

  // === Main Component ===
  var FAQList = exports.FAQList = function FAQList(props) {
    var data = props.data,
      isFetching = props.isFetching;
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      animationList = _useState4[0],
      setAnimationList = _useState4[1];
    var updateExpandItems = function updateExpandItems(index) {
      _reactNative.LayoutAnimation.configureNext(Object.assign({}, _reactNative.LayoutAnimation.Presets.linear, {
        duration: 100
      }));
      var updatedList = animationList.map(function (item, idx) {
        return Object.assign({}, item, {
          isExpanded: idx === index ? !item.isExpanded : false
        });
      });
      setAnimationList(updatedList);
    };
    (0, _react.useEffect)(function () {
      if (data) {
        setAnimationList((0, _orderBy2.default)(data == null || data.map == null ? undefined : data.map(function (item) {
          return item ? Object.assign({}, item, {
            sequenceNumber: (0, _utils.toNumber)(item.sequenceNumber)
          }) : item;
        }), ["sequenceNumber"], ["asc"]));
      }
    }, [JSON.stringify(data)]);

    // Loading case
    if (isFetching) {
      return (0, _jsxRuntime.jsx)(LoadingSkeleton, {});
    }
    // Empty case
    if (!(animationList != null && animationList.length)) {
      return null;
    }
    // Normal case
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        accessibilityLabel: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Title`,
        style: styles.titleTextStyle,
        testID: `${_parkingBenefitsMonarch.COMPONENT_NAME}__FAQ__Title`,
        tx: "parkingBenefitsMonarch.faq.title"
      }), animationList == null || animationList.map == null ? undefined : animationList.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(FAQListItemComponent, {
          index: index,
          item: item,
          updateExpandItems: updateExpandItems
        }, `${index}_${item.id}`);
      })]
    });
  };
  // ======================

  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var LOADING_COLORS = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      gap: 16,
      marginBottom: 179
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    }),
    itemContainerStyle: {
      marginBottom: 16
    },
    itemHeaderContainerStyle: {
      flexDirection: "row",
      justifyContent: "space-between"
    },
    itemQuestionTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1,
      marginBottom: 12
    }),
    expandIconStyle: {
      marginLeft: 12
    },
    itemAnswerTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      marginBottom: 8
    }),
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1,
      width: screenWidth * 332 / 375
    },
    loadingContainerStyle: {
      gap: 16
    },
    loadingItemContainerStyle: {
      gap: 8,
      paddingBottom: 24
    },
    loadingLine1stStyle: {
      borderRadius: 4,
      height: 12,
      width: screenWidth * 120 / 375
    },
    loadingLine2ndStyle: {
      borderRadius: 4,
      height: 12,
      width: "100%"
    },
    loadingLine3rdStyle: {
      borderRadius: 4,
      height: 12,
      width: screenWidth * 240 / 375
    }
  });
  var _default = exports.default = FAQList;
