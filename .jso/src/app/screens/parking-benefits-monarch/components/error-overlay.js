  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorOverlay = undefined;
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var ErrorOverlay = exports.ErrorOverlay = function ErrorOverlay() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        source: _icons.ParkingBenefitErrorIcon,
        style: styles.errorImgStyle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.titleTextStyle,
        tx: "parkingBenefitsMonarch.errorOverlay.title"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        style: styles.descriptionTextStyle,
        tx: "parkingBenefitsMonarch.errorOverlay.description"
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      marginLeft: 20,
      marginRight: 12,
      marginTop: 24
    },
    errorImgStyle: {
      height: 120,
      marginBottom: 16,
      width: 120
    },
    titleTextStyle: Object.assign({}, _text.newPresets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      }),
      marginBottom: 8,
      textAlign: "center"
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption1Regular, {
      textAlign: "center"
    })
  });
