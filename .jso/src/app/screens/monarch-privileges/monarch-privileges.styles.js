  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var CARD_WIDTH = width - 48;
  var LOGO_SIZE = 48;
  var PADDING_HORIZONTAL = 32;
  var SPACE_BETWEEN = 16;
  var CONTENT_CARD_WIDTH = CARD_WIDTH - LOGO_SIZE - PADDING_HORIZONTAL - SPACE_BETWEEN;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    contentContainerStyle: {
      paddingHorizontal: 24
    },
    logoStyle: {
      width: 64,
      height: 64,
      alignSelf: 'center',
      marginTop: 40
    },
    logoPlaceHolderStyle: {
      width: 64,
      height: 64,
      alignSelf: 'center',
      marginTop: 40,
      marginBottom: 24,
      borderRadius: 10
    },
    textTitleStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      textAlign: 'center',
      color: _theme.color.palette.almostBlackGrey,
      marginVertical: 24
    }),
    textTitlePlaceHolderStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      textAlign: 'center',
      color: _theme.color.palette.almostBlackGrey,
      width: CARD_WIDTH,
      alignSelf: 'center',
      borderRadius: 5
    }),
    textTitle2ndPlaceHolderStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      textAlign: 'center',
      color: _theme.color.palette.almostBlackGrey,
      width: CARD_WIDTH / 2,
      alignSelf: 'center',
      borderRadius: 5,
      marginTop: 5,
      marginBottom: 24
    }),
    wrapCardItemStyle: {
      alignSelf: 'center',
      width: CARD_WIDTH,
      borderRadius: 16,
      padding: 1
    },
    wrapCardItemPlaceHolderStyle: {
      alignSelf: 'center',
      width: CARD_WIDTH,
      borderRadius: 16,
      height: 104,
      padding: 1
    },
    viewContentButtonLabel: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      padding: 16,
      alignItems: 'center',
      flexDirection: 'row'
    },
    buttonLabel: Object.assign({}, _text.presets.bodyTextBold, {
      lineHeight: 24,
      textAlignVertical: 'center'
    }),
    separatorItemStyle: {
      height: 12,
      width: '100%'
    },
    flatListStyle: {},
    baseImageStyle: {
      width: 48,
      height: 48
    },
    contentCardItemStyle: {
      marginLeft: 16,
      width: CONTENT_CARD_WIDTH
    },
    titleCardItemStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.black
    }),
    copyTextCardItemStyle: Object.assign({}, _text.presets.caption1Regular, {
      marginTop: 4
    }),
    shimmerPlaceholderStype: {
      width: 133,
      height: 18,
      borderRadius: 4
    },
    flatListContentContainerStyle: {
      paddingBottom: 50,
      paddingHorizontal: 24
    },
    overlayStyle: {
      height: "100%",
      width: "100%"
    }
  });
