  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _text = _$$_REQUIRE(_dependencyMap[10]);
  var _monarchPrivileges = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _theme = _$$_REQUIRE(_dependencyMap[16]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[17]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var gradientColor = ["rgba(100, 94, 84, 0.32)", "rgba(255, 226, 128, 1)", "rgba(69, 42, 4, 0.32)"];
  var MonarchPrivilegesScreen = function MonarchPrivilegesScreen(_ref) {
    var navigation = _ref.navigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var rawData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.MONARCH_PRIVILEGES_SCREEN));
    var monarchAndPrivilegesData = (0, _lodash.get)(rawData, "data", []);
    var isLoading = (0, _lodash.get)(rawData, "loading", []);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var fetchData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setNoConnection(true);
        } else {
          setNoConnection(false);
          dispatch(_aemRedux.default.getAemConfigData({
            name: _aemRedux.AEM_PAGE_NAME.MONARCH_PRIVILEGES_SCREEN,
            pathName: "getMonarchPrivilegesScreen"
          }));
        }
      });
      return function fetchData() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      fetchData();
    }, []);
    var cardItem = function cardItem(_ref3) {
      var item = _ref3.item;
      return (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
        colors: gradientColor,
        start: {
          x: 0,
          y: 0
        },
        end: {
          x: 1,
          y: 1
        },
        locations: [0.4, 0.45, 0.8, 1],
        accessible: false,
        style: _monarchPrivileges.styles.wrapCardItemStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _monarchPrivileges.styles.viewContentButtonLabel,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _mediaHelper.handleImageUrl)(item == null ? undefined : item.icon)
            },
            style: _monarchPrivileges.styles.baseImageStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _monarchPrivileges.styles.contentCardItemStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: item == null ? undefined : item.title,
              style: _monarchPrivileges.styles.titleCardItemStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: item == null ? undefined : item.copy,
              style: _monarchPrivileges.styles.copyTextCardItemStyle
            })]
          })]
        })
      });
    };
    var separatorItem = function separatorItem() {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _monarchPrivileges.styles.separatorItemStyle
      });
    };
    if (isLoading) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _monarchPrivileges.styles.container,
        children: (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
          contentContainerStyle: _monarchPrivileges.styles.contentContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: _monarchPrivileges.styles.logoPlaceHolderStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: _monarchPrivileges.styles.textTitlePlaceHolderStyle
          }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: _monarchPrivileges.styles.textTitle2ndPlaceHolderStyle
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _monarchPrivileges.styles.flatListStyle,
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _monarchPrivileges.styles.wrapCardItemPlaceHolderStyle
            }), separatorItem(), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _monarchPrivileges.styles.wrapCardItemPlaceHolderStyle
            }), separatorItem(), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _monarchPrivileges.styles.wrapCardItemPlaceHolderStyle
            }), separatorItem(), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _monarchPrivileges.styles.wrapCardItemPlaceHolderStyle
            }), separatorItem(), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _monarchPrivileges.styles.wrapCardItemPlaceHolderStyle
            })]
          })]
        })
      });
    }
    var headerComponent = function headerComponent() {
      return (0, _jsxRuntime.jsxs)(_react.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNative.Image, {
          source: {
            uri: (0, _mediaHelper.handleImageUrl)(monarchAndPrivilegesData == null ? undefined : monarchAndPrivilegesData.logo)
          },
          style: _monarchPrivileges.styles.logoStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _monarchPrivileges.styles.textTitleStyle,
          text: monarchAndPrivilegesData == null ? undefined : monarchAndPrivilegesData.title
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _monarchPrivileges.styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        data: monarchAndPrivilegesData == null ? undefined : monarchAndPrivilegesData.cards,
        ListHeaderComponent: headerComponent,
        renderItem: cardItem,
        showsVerticalScrollIndicator: false,
        keyExtractor: function keyExtractor(_, index) {
          return index == null ? undefined : index.toString();
        },
        ItemSeparatorComponent: separatorItem,
        style: _monarchPrivileges.styles.flatListStyle,
        contentContainerStyle: _monarchPrivileges.styles.flatListContentContainerStyle
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        hideScreenHeader: true,
        headerBackgroundColor: "transparent",
        visible: isNoConnection,
        onReload: fetchData,
        onBack: function onBack() {
          navigation.goBack();
        },
        noInternetOverlayStyle: _monarchPrivileges.styles.overlayStyle
      })]
    });
  };
  var _default = exports.default = MonarchPrivilegesScreen;
