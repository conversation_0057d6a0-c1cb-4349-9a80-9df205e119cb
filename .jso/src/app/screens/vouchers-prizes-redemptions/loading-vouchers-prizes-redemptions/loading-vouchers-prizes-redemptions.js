  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _loadingVouchersPrizesRedemptions = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var shimmerColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var LoadingElement = function LoadingElement() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _loadingVouchersPrizesRedemptions.styles.elementWrapper,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _loadingVouchersPrizesRedemptions.styles.elementLeft,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: shimmerColors,
          shimmerStyle: _loadingVouchersPrizesRedemptions.styles.elementLeftShimmer,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _loadingVouchersPrizesRedemptions.styles.elementRight,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: shimmerColors,
          shimmerStyle: _loadingVouchersPrizesRedemptions.styles.elementRightFirstRow,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          shimmerColors: shimmerColors,
          shimmerStyle: _loadingVouchersPrizesRedemptions.styles.elementRightSecondRow,
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS
        })]
      })]
    });
  };
  var LoadingVouchersPrizesRedemptions = function LoadingVouchersPrizesRedemptions(props) {
    var customStyle = props.customStyle;
    var loadingElements = [1, 2, 3, 4, 5];
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [_loadingVouchersPrizesRedemptions.styles.wrapper, customStyle],
      children: loadingElements.map(function (_, i) {
        return (0, _jsxRuntime.jsx)(LoadingElement, {}, i);
      })
    });
  };
  var _default = exports.default = LoadingVouchersPrizesRedemptions;
