  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var deviceWidth = _reactNative.Dimensions.get("window").width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    wrapper: {
      flex: 1,
      padding: 16
    },
    elementWrapper: {
      marginBottom: 12,
      flexDirection: "row",
      alignItems: "center"
    },
    elementLeft: Object.assign({
      width: 114,
      padding: 12,
      marginRight: 1,
      borderRadius: 12,
      backgroundColor: _theme.color.palette.whiteGrey
    }, _theme.shadow.primaryShadow),
    elementLeftShimmer: {
      width: 90,
      height: 66
    },
    elementRight: Object.assign({
      flex: 1,
      height: 90,
      padding: 12,
      borderRadius: 12,
      justifyContent: "center",
      backgroundColor: _theme.color.palette.whiteGrey
    }, _theme.shadow.primaryShadow),
    elementRightFirstRow: {
      width: 74,
      height: 12,
      borderRadius: 4
    },
    elementRightSecondRow: {
      height: 12,
      marginTop: 4,
      borderRadius: 4,
      width: deviceWidth - 170
    }
  });
