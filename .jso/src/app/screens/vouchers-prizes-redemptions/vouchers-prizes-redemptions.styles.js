  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.loadingStyles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      paddingBottom: 24
    },
    cardListContainerStyle: {
      display: 'flex',
      gap: 12
    },
    cardItemContainerStyle: Object.assign({
      alignItems: "stretch",
      display: "flex",
      flexDirection: "row",
      marginBottom: 12,
      marginHorizontal: 16,
      gap: 1
    }, _reactNative.Platform.select({
      ios: {
        shadowColor: _theme.color.palette.almostBlackGrey,
        shadowOpacity: 0.08,
        shadowRadius: 8,
        shadowOffset: {
          height: 6,
          width: 0
        }
      }
    })),
    cardItemFirstContainerStyle: {
      marginTop: 24
    },
    cardItemFirstWithCM24ContainerStyle: {
      marginTop: 12
    },
    cardItemLastContainerStyle: {
      marginBottom: 56
    },
    cardItemImageContainerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 8,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 8,
      padding: 12,
      width: 114,
      justifyContent: 'center'
    }, _reactNative.Platform.select({
      android: {
        elevation: 0.5
      }
    })),
    cardItemImageStyle: {
      height: 60,
      width: 90
    },
    cardItemContentContainerStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderBottomLeftRadius: 8,
      borderBottomRightRadius: 12,
      borderTopLeftRadius: 8,
      borderTopRightRadius: 12,
      flex: 1,
      flexDirection: "row",
      gap: 8,
      paddingHorizontal: 12,
      paddingVertical: 16
    }, _reactNative.Platform.select({
      android: {
        elevation: 0.5
      }
    })),
    cardItemContentLeftSideStyle: {
      display: 'flex',
      flex: 1,
      gap: 4
    },
    cardItemContentCategoryTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 11,
      lineHeight: 14
    }),
    cardItemContentNameTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      lineHeight: 20
    }),
    cardItemContentValidTextStyle: Object.assign({}, _text.regularFontStyle, {
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      lineHeight: 16
    }),
    cardItemContentRightSideStyle: {
      alignItems: 'flex-end',
      justifyContent: 'center',
      width: 56
    },
    carditemContentQuantityContainerStyle: {
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 4,
      height: 32,
      minWidth: 32,
      padding: 6,
      alignItems: 'center'
    },
    cardItemContentQuantityTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: _theme.color.palette.lightPurple,
      fontSize: 14,
      lineHeight: 18
    }),
    redeemViaEmailContainerStyle: {
      alignItems: 'center',
      backgroundColor: _theme.color.palette.platinumTierBackground,
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 12,
      display: 'flex',
      justifyContent: 'flex-end',
      height: 42,
      marginBottom: 56,
      marginHorizontal: 16,
      marginTop: -24,
      padding: 8,
      zIndex: -1
    },
    redeemViaEmailTextStyle: Object.assign({}, _text.boldFontStyle, {
      color: '#254687',
      fontSize: 11,
      lineHeight: 14
    }),
    noShadowContainerStyle: Object.assign({}, _reactNative.Platform.select({
      ios: {
        shadowColor: _theme.color.palette.almostBlackGrey,
        shadowOpacity: 0.08,
        shadowRadius: 8,
        shadowOffset: {
          height: 6,
          width: 0
        }
      }
    })),
    emptySection: {
      marginTop: 80
    },
    emptyTitle: {
      marginTop: 24
    },
    emptyTitleText: {
      marginBottom: 7,
      textAlign: "center"
    },
    loadingCustomStyle: {
      marginTop: -60
    },
    errorOverlayStyle: {
      top: 80,
      width: "100%",
      position: "absolute"
    },
    cm24CardContainerStyle: {
      borderRadius: 12,
      marginTop: 24,
      minHeight: 96,
      marginHorizontal: 16
    }
  });
  var loadingStyles = exports.loadingStyles = _reactNative.StyleSheet.create({
    cm24CardContainerStyle: {
      display: "flex",
      flexDirection: "row"
    },
    cm24CardStyle: {
      borderRadius: 12,
      flex: 1,
      marginTop: 24,
      minHeight: 96,
      marginHorizontal: 16
    }
  });
