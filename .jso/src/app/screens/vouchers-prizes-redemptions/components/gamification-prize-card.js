  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _vouchersPrizesRedemptions = _$$_REQUIRE(_dependencyMap[2]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[3]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _i18n = _$$_REQUIRE(_dependencyMap[6]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _react = _$$_REQUIRE(_dependencyMap[8]);
  var _isEqual2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var GamificationPrizeCard = (0, _react.memo)(function (_ref) {
    var hasAppscapadeCard = _ref.hasAppscapadeCard,
      index = _ref.index,
      item = _ref.item,
      list = _ref.list,
      _onPress = _ref.onPress,
      shouldShowCM24Card = _ref.shouldShowCM24Card;
    var validUntilMsg = item != null && item.validTill ? (0, _i18n.translate)("vouchersPrizesRedemptionsScreen.validUntil", {
      date: (0, _moment.default)(item == null ? undefined : item.validTill).format("DD MMM YYYY")
    }) : "";
    var cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle];
    if (index === 0) {
      if (shouldShowCM24Card) {
        cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.cardItemFirstWithCM24ContainerStyle];
      } else {
        cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.cardItemFirstContainerStyle];
      }
    } else if (index === (list == null ? undefined : list.length) - 1 && !hasAppscapadeCard) {
      cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.cardItemLastContainerStyle];
    }
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: function onPress() {
        return _onPress(item);
      },
      style: cardItemContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _vouchersPrizesRedemptions.styles.cardItemImageContainerStyle,
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: item == null ? undefined : item.image
          },
          style: _vouchersPrizesRedemptions.styles.cardItemImageStyle
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _vouchersPrizesRedemptions.styles.cardItemContentContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.cardItemContentLeftSideStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _vouchersPrizesRedemptions.styles.cardItemContentCategoryTextStyle,
            text: item == null ? undefined : item.categoryCode
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 3,
            style: _vouchersPrizesRedemptions.styles.cardItemContentNameTextStyle,
            text: item == null ? undefined : item.title
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _vouchersPrizesRedemptions.styles.cardItemContentValidTextStyle,
            text: validUntilMsg
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.cardItemContentRightSideStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _vouchersPrizesRedemptions.styles.carditemContentQuantityContainerStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _vouchersPrizesRedemptions.styles.cardItemContentQuantityTextStyle,
              text: item == null ? undefined : item.qty
            })
          })
        })]
      })]
    });
  }, function (prevProps, nextProps) {
    return !(0, _isEqual2.default)(prevProps, nextProps);
  });
  var _default = exports.default = GamificationPrizeCard;
