  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _i18n = _$$_REQUIRE(_dependencyMap[1]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _utils = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _vouchersPrizesRedemptions = _$$_REQUIRE(_dependencyMap[5]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _react = _$$_REQUIRE(_dependencyMap[9]);
  var _isEqual2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var ChangiRewardCard = (0, _react.memo)(function (_ref) {
    var _item$category_code;
    var hasAppscapadeCard = _ref.hasAppscapadeCard,
      index = _ref.index,
      item = _ref.item,
      list = _ref.list,
      _onPress = _ref.onPress,
      shouldShowCM24Card = _ref.shouldShowCM24Card;
    var validUntilMsg = item != null && item.expiry_dt ? (0, _i18n.translate)("vouchersPrizesRedemptionsScreen.validUntil", {
      date: (0, _moment.default)(item == null ? undefined : item.expiry_dt).format("DD MMM YYYY")
    }) : "";
    var tenantName = (0, _utils.handleCondition)(Number(item == null ? undefined : item.token_qty) > 1, item == null ? undefined : item.token_name_plural, item == null ? undefined : item.token_name_singular);
    var cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle];
    if (index === 0) {
      if (shouldShowCM24Card) {
        cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.cardItemFirstWithCM24ContainerStyle];
      } else {
        cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.cardItemFirstContainerStyle];
      }
    } else if (index === (list == null ? undefined : list.length) - 1 && !hasAppscapadeCard) {
      cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.cardItemLastContainerStyle];
    }
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: function onPress() {
        return _onPress(item);
      },
      style: cardItemContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _vouchersPrizesRedemptions.styles.cardItemImageContainerStyle,
        children: _reactNative.Platform.OS === 'ios' ? (0, _jsxRuntime.jsx)(_reactNative.Image, {
          source: {
            uri: item == null ? undefined : item.token_img_url
          },
          style: _vouchersPrizesRedemptions.styles.cardItemImageStyle,
          resizeMode: "cover"
        }) : (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: item == null ? undefined : item.token_img_url
          },
          style: _vouchersPrizesRedemptions.styles.cardItemImageStyle
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _vouchersPrizesRedemptions.styles.cardItemContentContainerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.cardItemContentLeftSideStyle,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _vouchersPrizesRedemptions.styles.cardItemContentCategoryTextStyle,
            text: item == null || (_item$category_code = item.category_code) == null || _item$category_code.toUpperCase == null ? undefined : _item$category_code.toUpperCase()
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 3,
            style: _vouchersPrizesRedemptions.styles.cardItemContentNameTextStyle,
            text: tenantName
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _vouchersPrizesRedemptions.styles.cardItemContentValidTextStyle,
            text: validUntilMsg
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.cardItemContentRightSideStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _vouchersPrizesRedemptions.styles.carditemContentQuantityContainerStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _vouchersPrizesRedemptions.styles.cardItemContentQuantityTextStyle,
              text: item == null ? undefined : item.token_qty
            })
          })
        })]
      })]
    });
  }, function (prevProps, nextProps) {
    return !(0, _isEqual2.default)(prevProps, nextProps);
  });
  var _default = exports.default = ChangiRewardCard;
