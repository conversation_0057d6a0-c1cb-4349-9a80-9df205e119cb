  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _vouchersPrizesRedemptions = _$$_REQUIRE(_dependencyMap[6]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _react = _$$_REQUIRE(_dependencyMap[12]);
  var _native = _$$_REQUIRE(_dependencyMap[13]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _theme = _$$_REQUIRE(_dependencyMap[16]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _emptyScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[20]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[21]);
  var _loadingVouchersPrizesRedemptions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _account = _$$_REQUIRE(_dependencyMap[23]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[24]);
  var _forYouScreen = _$$_REQUIRE(_dependencyMap[25]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[26]);
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[27]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[28]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[30]);
  var _adobe = _$$_REQUIRE(_dependencyMap[31]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[32]);
  var _changiRewardCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _gamificationPrizeCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[34]));
  var _isEqual2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _i18n = _$$_REQUIRE(_dependencyMap[36]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[37]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[38]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var VPRCardItem = (0, _react.memo)(function (props) {
    var _props$item;
    if ((props == null || (_props$item = props.item) == null ? undefined : _props$item.perk_type) === _constants.PerkType.GamificationPrize) {
      return (0, _jsxRuntime.jsx)(_gamificationPrizeCard.default, Object.assign({}, props));
    }
    return (0, _jsxRuntime.jsx)(_changiRewardCard.default, Object.assign({}, props));
  }, function (prevProps, nextProps) {
    return !(0, _isEqual2.default)(prevProps, nextProps);
  });
  var AppscapadeCardItem = function AppscapadeCardItem(_ref) {
    var item = _ref.item,
      _onPress = _ref.onPress,
      vprCards = _ref.vprCards;
    var cardItemContainerStyle = [_vouchersPrizesRedemptions.styles.cardItemContainerStyle, _vouchersPrizesRedemptions.styles.noShadowContainerStyle, {
      marginTop: (vprCards == null ? undefined : vprCards.length) > 0 ? 0 : 12
    }];
    return (0, _jsxRuntime.jsxs)(_reactNative2.Pressable, {
      android_ripple: {
        color: _theme.color.palette.almostWhiteGrey80,
        foreground: true
      },
      onPress: function onPress() {
        return _onPress(item);
      },
      style: function style(_ref2) {
        var pressed = _ref2.pressed;
        return {
          opacity: _reactNative.Platform.select({
            android: 1,
            ios: pressed ? 0.85 : 1
          })
        };
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: cardItemContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.cardItemImageContainerStyle,
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: item == null ? undefined : item.image
            },
            style: _vouchersPrizesRedemptions.styles.cardItemImageStyle
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.cardItemContentContainerStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _vouchersPrizesRedemptions.styles.cardItemContentLeftSideStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _vouchersPrizesRedemptions.styles.cardItemContentCategoryTextStyle,
              tx: "vouchersPrizesRedemptionsScreen.appscapadeTitle"
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              numberOfLines: 3,
              style: _vouchersPrizesRedemptions.styles.cardItemContentNameTextStyle,
              text: item == null ? undefined : item.title
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _vouchersPrizesRedemptions.styles.cardItemContentRightSideStyle,
            children: (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _vouchersPrizesRedemptions.styles.carditemContentQuantityContainerStyle,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _vouchersPrizesRedemptions.styles.cardItemContentQuantityTextStyle,
                text: item == null ? undefined : item.labelText
              })
            })
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _vouchersPrizesRedemptions.styles.redeemViaEmailContainerStyle,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _vouchersPrizesRedemptions.styles.redeemViaEmailTextStyle,
          tx: "vouchersPrizesRedemptionsScreen.redeemViaEmail"
        })
      })]
    });
  };
  var VouchersPrizesRedemptionsPage = function VouchersPrizesRedemptionsPage(_ref3) {
    var _vouchersPrizesRedemp, _useSelector;
    var navigation = _ref3.navigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var vouchersPrizesRedemptionsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.vouchersPrizesRedemptionsData);
    var vouchersPrizesRedemptionsCacheData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.vouchersPrizesRedemptionsCacheData);
    var vprCards = vouchersPrizesRedemptionsData == null ? undefined : vouchersPrizesRedemptionsData.vprCards;
    var appscapadeCard = vouchersPrizesRedemptionsData == null || (_vouchersPrizesRedemp = vouchersPrizesRedemptionsData.appscapadeCards) == null ? undefined : _vouchersPrizesRedemp[0];
    var vouchersPrizesRedemptionsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.vouchersPrizesRedemptionsError);
    var vouchersPrizesRedemptionsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.vouchersPrizesRedemptionsFetching);
    var errorMessage = (_useSelector = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon)) == null ? undefined : _useSelector.find(function (item) {
      return (item == null ? undefined : item.code) === "EHR11.6";
    });
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var scrollViewRef = (0, _react.useRef)(null);
    var positionScrollRef = (0, _react.useRef)({
      contentOffsetY: 0
    });
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isShowNoInternetModel = _useState2[0],
      setIsShowNoInternetModel = _useState2[1];
    var shouldShowEmptySection = !(vprCards != null && vprCards.length) && !appscapadeCard && !isShowNoInternetModel && !vouchersPrizesRedemptionsError && !vouchersPrizesRedemptionsFetching;
    var shouldRenderList = !isShowNoInternetModel && !vouchersPrizesRedemptionsError && !vouchersPrizesRedemptionsFetching;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      accountCM24FeatureFlag = _useContext.accountCM24FeatureFlag,
      miffygameVPR = _useContext.miffygameVPR;
    var isAccountV2CM24 = (0, _remoteConfig.isFlagOnCondition)(accountCM24FeatureFlag);
    var isMiffyGameVPR = (0, _remoteConfig.isFlagOnCondition)(miffygameVPR);
    var accountBannerList = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.accountBannerList);
    var aemGroupTwoLoading = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.aemGroupTwoLoading);
    var vprCM24Data = accountBannerList == null || accountBannerList.find == null ? undefined : accountBannerList.find(function (item) {
      return (item == null ? undefined : item.type) === _forYouScreen.ACCOUNT_CM24_CARD_TYPE.VPR;
    });
    var shouldShowCM24Card = isAccountV2CM24 && !aemGroupTwoLoading && !!vprCM24Data;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("VPR"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var handleScroll = function handleScroll(event) {
      positionScrollRef.current.contentOffsetY = event.nativeEvent.contentOffset.y;
    };
    var _onGoBack = function onGoBack(valueOffsetY) {
      dispatch(_forYouRedux.default.vouchersPrizesRedemptionsCacheData(true));
      setTimeout(function () {
        var _scrollViewRef$curren;
        (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollTo({
          y: valueOffsetY,
          animated: true
        });
      }, 50);
    };
    var handlePressRewardsCard = function handlePressRewardsCard(item) {
      if (!(item != null && item.cr_vouchers_campaign)) {
        _globalLoadingController.default.showLoading(true);
        _globalLoadingController.default.hideLoading();
        if ((item == null ? undefined : item.redemption_mode) === null) {
          _changiEcardControler.default.showModal(navigation);
          return;
        }
      }
      if ((item == null ? undefined : item.perk_type) === _constants.PerkType.GamificationPrize) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppVPR, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppVPR, `${item == null ? undefined : item.categoryCode} | ${item == null ? undefined : item.title}`));
      } else {
        var tokenNameValue = (item == null ? undefined : item.token_qty) === 1 ? item == null ? undefined : item.token_name_singular : item == null ? undefined : item.token_name_plural;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppVPR, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppVPR, `${item == null ? undefined : item.category_code} | ${tokenNameValue}`));
      }
      navigation.navigate(_constants.NavigationConstants.yourReward, {
        item: item,
        onGoBack: function onGoBack() {
          return _onGoBack(positionScrollRef.current.contentOffsetY);
        }
      });
    };
    var handlePressAppscapadeCard = function handlePressAppscapadeCard(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppVPR, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppVPR, `${(0, _i18n.translate)("vouchersPrizesRedemptionsScreen.appscapadeTitle")} | ${item == null ? undefined : item.title}`));
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: item == null ? undefined : item.redirectUrl,
        onGoBack: function onGoBack() {
          return _onGoBack(positionScrollRef.current.contentOffsetY + 100);
        }
      });
    };
    var handleReloadVPR = function handleReloadVPR() {
      if (!vouchersPrizesRedemptionsCacheData) {
        var _rewardsData$reward;
        dispatch(_forYouRedux.default.vouchersPrizesRedemptionsRequest({
          cardNo: rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null ? undefined : _rewardsData$reward.cardNo,
          isMiffyGameVPR: isMiffyGameVPR
        }));
      }
    };
    var onPressRetryToConnectInternet = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          handleReloadVPR();
          setIsShowNoInternetModel(false);
        }
      });
      return function onPressRetryToConnectInternet() {
        return _ref4.apply(this, arguments);
      };
    }();
    var onRetryToRequestVouchersPrizesRedemptions = function onRetryToRequestVouchersPrizesRedemptions() {
      handleReloadVPR();
    };
    var handlePressCM24Card = function handlePressCM24Card() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAppscapadeBannerEntry, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAppscapadeBannerEntry, `${_adobe.AdobeValueByTagName.CAppBannerAccountLanding} | ${_adobe.AdobeValueByTagName.CAppBannerVPR}`));
      var navigationData = vprCM24Data == null ? undefined : vprCM24Data.navigation;
      if (navigationData != null && navigationData.value) {
        handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, undefined, {
          redirectFrom: _constants.CM24RedirectSource.VPR,
          routeParams: {
            onBackBtnPress: function onBackBtnPress(newNavigation) {
              _onGoBack(positionScrollRef.current.contentOffsetY);
              newNavigation == null || newNavigation.goBack == null || newNavigation.goBack();
            }
          }
        });
      }
    };
    var renderErrorOverlayNoInternet = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        visible: true,
        hideScreenHeader: false,
        onBack: navigation.goBack,
        onReload: onPressRetryToConnectInternet,
        noInternetOverlayStyle: _vouchersPrizesRedemptions.styles.errorOverlayStyle,
        testID: `${_constants.NavigationConstants.vouchersPrizesRedemptionsScreen}__ErrorOverlayNoConnection`
      });
    }, []);
    var renderErrorOverlay = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        visible: true,
        hideScreenHeader: false,
        onBack: navigation.goBack,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        overlayStyle: _vouchersPrizesRedemptions.styles.errorOverlayStyle,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL,
        onReload: onRetryToRequestVouchersPrizesRedemptions
      });
    }, []);
    var emptyTitleComponent = (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: _vouchersPrizesRedemptions.styles.emptyTitle,
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        text: errorMessage == null ? undefined : errorMessage.header,
        preset: "h2",
        style: _vouchersPrizesRedemptions.styles.emptyTitleText
      })
    });
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      handleReloadVPR();
      return function () {
        return dispatch(_forYouRedux.default.vouchersPrizesRedemptionsCacheData(false));
      };
    }, [vouchersPrizesRedemptionsCacheData]));
    (0, _react.useEffect)(function () {
      ;
      (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          setIsShowNoInternetModel(true);
        }
      })();
    }, []);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.VouchersPrizesRedemptions);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.VouchersPrizesRedemptions, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _vouchersPrizesRedemptions.styles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        backgroundColor: _theme.color.palette.whiteGrey,
        barStyle: "dark-content",
        translucent: true
      }), (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        onScroll: handleScroll,
        ref: scrollViewRef,
        showsHorizontalScrollIndicator: false,
        showsVerticalScrollIndicator: false,
        style: _vouchersPrizesRedemptions.styles.cardListContainerStyle,
        children: [shouldShowCM24Card && (aemGroupTwoLoading ? (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.loadingStyles.cm24CardContainerStyle,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _theme.color.shimmerPlacholderColor,
            shimmerStyle: _vouchersPrizesRedemptions.loadingStyles.cm24CardStyle
          })
        }) : (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handlePressCM24Card,
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: (0, _mediaHelper.handleImageUrl)(vprCM24Data == null ? undefined : vprCM24Data.bgImage)
            },
            style: _vouchersPrizesRedemptions.styles.cm24CardContainerStyle
          })
        })), shouldShowEmptySection && (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _vouchersPrizesRedemptions.styles.emptySection,
          children: (0, _jsxRuntime.jsx)(_emptyScreen.default, {
            titleComponent: emptyTitleComponent,
            description: errorMessage == null ? undefined : errorMessage.subHeader,
            iconUrl: _$$_REQUIRE(_dependencyMap[39]),
            testID: `${_constants.NavigationConstants.vouchersPrizesRedemptionsScreen}__EmptyMyTravel`,
            accessibilityLabel: `${_constants.NavigationConstants.vouchersPrizesRedemptionsScreen}__EmptyMyTravel`
          })
        }), shouldRenderList && (vprCards == null || vprCards.map == null ? undefined : vprCards.map(function (item, index, list) {
          return (0, _jsxRuntime.jsx)(VPRCardItem, {
            hasAppscapadeCard: !!appscapadeCard,
            index: index,
            item: item,
            list: list,
            onPress: handlePressRewardsCard,
            shouldShowCM24Card: shouldShowCM24Card
          }, `${index}_${item == null ? undefined : item.name}`);
        })), shouldRenderList && appscapadeCard && (0, _jsxRuntime.jsx)(AppscapadeCardItem, {
          item: appscapadeCard,
          onPress: handlePressAppscapadeCard,
          vprCards: vprCards
        }, appscapadeCard == null ? undefined : appscapadeCard.name), vouchersPrizesRedemptionsFetching && !isShowNoInternetModel && (0, _jsxRuntime.jsx)(_loadingVouchersPrizesRedemptions.default, {})]
      }), isShowNoInternetModel && !vouchersPrizesRedemptionsFetching && renderErrorOverlayNoInternet, vouchersPrizesRedemptionsError && !vouchersPrizesRedemptionsFetching && !isShowNoInternetModel && renderErrorOverlay]
    });
  };
  var _default = exports.default = VouchersPrizesRedemptionsPage;
