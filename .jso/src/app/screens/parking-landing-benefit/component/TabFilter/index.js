  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabFilter = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _color = _$$_REQUIRE(_dependencyMap[3]);
  var _typography = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function tabItemOnPress(navigation, route, isFocused) {
    var event = navigation == null ? undefined : navigation.emit({
      type: "tabPress",
      target: route == null ? undefined : route.key
    });
    if (!isFocused && !event.defaultPrevented) {
      navigation == null || navigation.navigate(route == null ? undefined : route.name);
    }
  }
  var TabFilter = exports.TabFilter = _react.default.memo(function (topTabProps) {
    var _topTabProps$props = topTabProps.props,
      state = _topTabProps$props.state,
      descriptors = _topTabProps$props.descriptors,
      navigation = _topTabProps$props.navigation;
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: state.routes.map(function (route, index) {
        var options = descriptors[route.key].options;
        var label;
        if (options.tabBarLabel) {
          label = options.tabBarLabel;
        } else {
          if (options.title !== undefined) {
            label = options.title;
          } else {
            label = route.name;
          }
        }
        var isFocused = state.index === index;
        var onPress = function onPress() {
          tabItemOnPress(navigation, route, isFocused);
        };
        return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.itemView,
          onPress: onPress,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: isFocused ? styles.txtContentActive : styles.txtContentInActive,
            children: label
          }), isFocused && (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.viewStatus
          })]
        }, label);
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: '100%',
      flexDirection: 'row',
      height: 42,
      borderBottomWidth: 1,
      borderColor: _color.color.palette.lighterGrey,
      backgroundColor: _color.color.palette.whiteGrey
    },
    itemView: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center'
    },
    txtContentInActive: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.darkGrey999,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    txtContentActive: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    viewStatus: {
      position: 'absolute',
      bottom: -1,
      width: '100%',
      height: 2,
      backgroundColor: _color.color.palette.lightPurple
    }
  });
