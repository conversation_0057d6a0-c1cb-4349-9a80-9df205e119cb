  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TabActive = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _titleDescription = _$$_REQUIRE(_dependencyMap[8]);
  var _viewParked = _$$_REQUIRE(_dependencyMap[9]);
  var _viewItem = _$$_REQUIRE(_dependencyMap[10]);
  var _text = _$$_REQUIRE(_dependencyMap[11]);
  var _viewLoading = _$$_REQUIRE(_dependencyMap[12]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[14]);
  var _constants = _$$_REQUIRE(_dependencyMap[15]);
  var _viewMissingBenefit = _$$_REQUIRE(_dependencyMap[16]);
  var _viewNotApplicable = _$$_REQUIRE(_dependencyMap[17]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[18]);
  var _viewEmpty = _$$_REQUIRE(_dependencyMap[19]);
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[20]);
  var _parkingLandingBenefit = _$$_REQUIRE(_dependencyMap[21]);
  var _errorOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _adobe = _$$_REQUIRE(_dependencyMap[24]);
  var _i18n = _$$_REQUIRE(_dependencyMap[25]);
  var _native = _$$_REQUIRE(_dependencyMap[26]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TabActive = exports.TabActive = _react.default.memo(function (props) {
    var navigation = props.navigation,
      pressedTabLabel = props.pressedTabLabel;
    var scrollRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setIsConnected = _useState2[1];
    var _useParkingBenefit = (0, _parkingLandingBenefit.useParkingBenefit)({
        payloadKey: "activeBenefits",
        scrollRef: scrollRef
      }),
      data = _useParkingBenefit.data,
      isError = _useParkingBenefit.isError,
      isFetching = _useParkingBenefit.isFetching,
      dataParked = _useParkingBenefit.dataParked,
      isRefresh = _useParkingBenefit.isRefresh,
      setIsRefresh = _useParkingBenefit.setIsRefresh;
    var isParked = (dataParked == null ? undefined : dataParked.status) === "PARKED";
    var parkingStatus = isParked ? "Parked" : "Not Parked";
    var trackingValue = `${parkingStatus} | Tile | ${(0, _i18n.translate)("parkingLandingBenefit.tabFilter.active")} | null`;
    var handlePressTotalSection = function handlePressTotalSection() {
      var dataToBeSent = `${trackingValue} | ${(0, _i18n.translate)("parkingLandingBenefit.active.totalSection.title")}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppBenefitsSummary, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppBenefitsSummary, dataToBeSent));
      navigation == null || navigation.navigate == null || navigation.navigate(_constants.NavigationConstants.carPark, {
        activeTab: 1
      });
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        setIsConnected(isConnected);
      });
      return function checkConnection() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkConnection();
    }, [navigation]);
    var renderTabContent = (0, _react.useMemo)(function () {
      var _data$applicable, _data$applicable2, _data$notApplicable;
      if (!isFetching && (isError || !isConnected)) {
        return (0, _jsxRuntime.jsx)(_errorOverlay.default, {});
      }
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(data == null || (_data$applicable = data.applicable) == null ? undefined : _data$applicable.length) === 0 ? (0, _jsxRuntime.jsx)(_viewEmpty.EmptyActive, {
          trackingValue: trackingValue
        }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_titleDescription.TitleDescription, {
            trackingValue: trackingValue
          }), isParked && (0, _jsxRuntime.jsx)(_viewParked.ViewParked, {
            value: dataParked == null ? undefined : dataParked.carparkLocation
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.viewContent,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.viewList,
              children: isFetching ? (0, _jsxRuntime.jsx)(_viewLoading.LoadingActive, {}) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
                children: data == null || (_data$applicable2 = data.applicable) == null ? undefined : _data$applicable2.map(function (item, index) {
                  return (0, _jsxRuntime.jsx)(_viewItem.ViewItem, {
                    item: item,
                    index: index
                  }, index);
                })
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.viewBottomContent,
              children: [(0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
                accessibilityLabel: `${_constants2.COMPONENT_NAME}__TotalSection__DescriptionSide`,
                onPress: handlePressTotalSection,
                style: styles.viewLeft,
                testID: `${_constants2.COMPONENT_NAME}__TotalSection__DescriptionSide`,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  accessibilityLabel: `${_constants2.COMPONENT_NAME}__viewBottomContent__title`,
                  style: styles.title,
                  testID: `${_constants2.COMPONENT_NAME}__viewBottomContent__title`,
                  tx: "parkingLandingBenefit.active.totalSection.title"
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  accessibilityLabel: `${_constants2.COMPONENT_NAME}__viewBottomContent__subTitle`,
                  style: styles.subTitle,
                  testID: `${_constants2.COMPONENT_NAME}__viewBottomContent__subTitle`,
                  tx: isParked || isFetching ? "parkingLandingBenefit.active.totalSection.parked.title" : "parkingLandingBenefit.active.totalSection.notParked.title"
                }), (0, _jsxRuntime.jsx)(_text.Text, {
                  accessibilityLabel: `${_constants2.COMPONENT_NAME}__viewBottomContent__content`,
                  style: styles.content,
                  testID: `${_constants2.COMPONENT_NAME}__viewBottomContent__content`,
                  tx: isParked || isFetching ? "parkingLandingBenefit.active.totalSection.parked.description" : "parkingLandingBenefit.active.totalSection.notParked.description"
                })]
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.viewRight,
                children: isFetching ? (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                  duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                  shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
                  shimmerStyle: styles.loadingText
                }) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.time,
                    testID: `${_constants2.COMPONENT_NAME}__viewBottomContentRight__time`,
                    children: data == null ? undefined : data.totalDuration
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    style: styles.price,
                    testID: `${_constants2.COMPONENT_NAME}__viewBottomContentRight__price`,
                    children: `$${data == null ? undefined : data.totalBalance}`
                  })]
                })
              })]
            })]
          })]
        }), (data == null || (_data$notApplicable = data.notApplicable) == null ? undefined : _data$notApplicable.length) > 0 && (0, _jsxRuntime.jsx)(_viewNotApplicable.ViewNotApplicable, {
          data: data
        }), (0, _jsxRuntime.jsx)(_viewMissingBenefit.ViewMissingBenefit, {
          trackingValue: trackingValue
        })]
      });
    }, [JSON.stringify(data), JSON.stringify(dataParked), isConnected, isError, isFetching, isParked]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      if ((pressedTabLabel == null ? undefined : pressedTabLabel.current) === (0, _i18n.translate)("parkingLandingBenefit.tabFilter.active") && !!(dataParked != null && dataParked.status)) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppBenefitsSummary, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppBenefitsSummary, `${parkingStatus} | Tab | ${(0, _i18n.translate)("parkingLandingBenefit.tabFilter.active")}`));
      }
    }, [dataParked == null ? undefined : dataParked.status, parkingStatus, pressedTabLabel]));
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        showsVerticalScrollIndicator: false,
        ref: scrollRef,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          colors: [_theme.color.palette.lightGrey],
          onRefresh: function onRefresh() {
            return setIsRefresh == null ? undefined : setIsRefresh(true);
          },
          progressBackgroundColor: "transparent",
          progressViewOffset: 16,
          refreshing: isRefresh,
          tintColor: _theme.color.palette.lightGrey
        }),
        children: renderTabContent
      })
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    viewContent: {
      paddingTop: 16,
      paddingHorizontal: 16
    },
    viewList: {
      paddingTop: 16,
      paddingBottom: 24,
      paddingHorizontal: 16,
      borderTopLeftRadius: 12,
      borderTopRightRadius: 12,
      borderWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      gap: 24
    },
    viewBottomContent: {
      padding: 16,
      borderBottomLeftRadius: 12,
      borderBottomRightRadius: 12,
      borderLeftWidth: 1,
      borderRightWidth: 1,
      borderBottomWidth: 1,
      borderColor: _theme.color.palette.lighterGrey,
      flexDirection: 'row'
    },
    viewLeft: {
      width: '60%'
    },
    viewRight: {
      width: '40%',
      alignItems: 'flex-end'
    },
    title: {
      fontFamily: _theme.typography.black,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20
    },
    subTitle: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16,
      marginVertical: 4
    },
    content: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16
    },
    price: {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 16
    },
    time: {
      fontFamily: _theme.typography.bold,
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 18
    },
    loadingText: {
      width: 64,
      height: 12,
      borderRadius: 12
    }
  });
