  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _component = _$$_REQUIRE(_dependencyMap[2]);
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _translate = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var Tab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var ParkingLandingBenefitScreen = function ParkingLandingBenefitScreen() {
    var pressedTabLabel = (0, _react.useRef)("");
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsxs)(Tab.Navigator, {
        initialRouteName: _constants.NavigationConstants.ParkingLandingBenefitActive,
        backBehavior: "none",
        tabBar: function tabBar(props) {
          return (0, _jsxRuntime.jsx)(_component.TabFilter, {
            props: Object.assign({}, props)
          });
        },
        children: [(0, _jsxRuntime.jsx)(Tab.Screen, {
          name: _constants.NavigationConstants.ParkingLandingBenefitActive,
          options: {
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: (0, _translate.translate)("parkingLandingBenefit.tabFilter.active")
          },
          listeners: {
            tabPress: function tabPress(e) {
              pressedTabLabel.current = (0, _translate.translate)("parkingLandingBenefit.tabFilter.active");
            }
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_component.TabActive, Object.assign({}, props, {
              pressedTabLabel: pressedTabLabel
            }));
          }
        }), (0, _jsxRuntime.jsx)(Tab.Screen, {
          name: _constants.NavigationConstants.ParkingLandingBenefitInActive,
          options: {
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: (0, _translate.translate)("parkingLandingBenefit.tabFilter.in-active")
          },
          listeners: {
            tabPress: function tabPress(e) {
              pressedTabLabel.current = (0, _translate.translate)("parkingLandingBenefit.tabFilter.in-active");
            }
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_component.TabInActive, Object.assign({}, props, {
              pressedTabLabel: pressedTabLabel
            }));
          }
        }), (0, _jsxRuntime.jsx)(Tab.Screen, {
          name: _constants.NavigationConstants.ParkingLandingBenefitExpire,
          options: {
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: (0, _translate.translate)("parkingLandingBenefit.tabFilter.expired")
          },
          listeners: {
            tabPress: function tabPress(e) {
              pressedTabLabel.current = (0, _translate.translate)("parkingLandingBenefit.tabFilter.expired");
            }
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_component.TabExpire, Object.assign({}, props, {
              pressedTabLabel: pressedTabLabel
            }));
          }
        })]
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    }
  });
  var _default = exports.default = ParkingLandingBenefitScreen;
