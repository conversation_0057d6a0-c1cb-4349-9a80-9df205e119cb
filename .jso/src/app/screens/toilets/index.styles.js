  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = _reactNative.StyleSheet.create({
    containerPrefix: {
      alignItems: "center",
      display: "flex",
      height: 20,
      justifyContent: "center",
      width: 20
    },
    headerSectionStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 40,
      paddingHorizontal: 24,
      textTransform: "uppercase"
    }),
    optionContainer: {
      marginTop: 12,
      paddingHorizontal: 24
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    prefixName: {
      color: _theme.color.palette.whiteGrey,
      fontSize: 10
    },
    sectionContainerStyles: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingBottom: 80
    }
  });
  var _default = exports.default = styles;
