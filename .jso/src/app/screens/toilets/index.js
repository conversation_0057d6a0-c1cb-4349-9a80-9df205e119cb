  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[9]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[10]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[11]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[12]);
  var _text = _$$_REQUIRE(_dependencyMap[13]);
  var _icons = _$$_REQUIRE(_dependencyMap[14]);
  var _index = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _pageConfigRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var mappingTerminalIcon = {
    "terminal 1": _icons.Terminal1,
    "terminal 2": _icons.Terminal2,
    "terminal 3": _icons.Terminal3,
    "terminal 4": _icons.Terminal4
  };
  var SCREEN_NAME = "ToiletsScreen_";
  var imageStyle = {
    width: 20,
    height: 20,
    resizeMode: "contain"
  };
  var jewelImageSource = _$$_REQUIRE(_dependencyMap[19]);
  var Toilets = function Toilets(props) {
    var navigation = props.navigation;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var dispatch = (0, _reactRedux.useDispatch)();
    var toiletData = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.toiletData);
    var data = (0, _lodash.get)(toiletData, "data");
    var loading = (0, _lodash.get)(toiletData, "loading");
    var error = (0, _lodash.get)(toiletData, "error");
    var fetchData = (0, _react.useCallback)(function () {
      dispatch(_pageConfigRedux.default.getToiletsRequest());
    }, []);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
            return;
          }
          fetchData();
        });
        return function checkInternet() {
          return _ref.apply(this, arguments);
        };
      }();
      checkInternet();
      return function () {
        dispatch(_pageConfigRedux.default.toiletsReset());
      };
    }, [fetchData]);
    var onPressToilet = function onPressToilet(_toilet) {
      // TO DO NAVIGATE TO MAP
    };
    var renderItemSection = function renderItemSection(item, section) {
      var _section$name;
      var iconCom = null;
      if ((section == null || (_section$name = section.name) == null ? undefined : _section$name.toLowerCase()) === "jewel") {
        iconCom = (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: jewelImageSource,
          style: imageStyle
        });
      } else {
        var _section$name2;
        var Icon = mappingTerminalIcon[(section == null || (_section$name2 = section.name) == null ? undefined : _section$name2.toLowerCase()) || ""];
        iconCom = (0, _jsxRuntime.jsx)(Icon, {});
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _index.default.optionContainer,
        children: (0, _jsxRuntime.jsx)(_menuOption.MenuOption, {
          type: _menuOption.MenuOptionType.default,
          redirection: _menuOption.RedirectType.Internal,
          title: item == null ? undefined : item.name,
          onPress: function onPress() {
            return onPressToilet(item == null ? undefined : item.data);
          },
          iconComponent: iconCom || null,
          useRightIcon: false,
          disabled: true
        })
      });
    };
    var _renderSectionHeader = function renderSectionHeader(section) {
      return (0, _jsxRuntime.jsx)(_text.Text, {
        style: _index.default.headerSectionStyle,
        text: section == null ? undefined : section.name
      });
    };
    var onReloadData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        setNoConnection(!isConnected);
        if (isConnected) {
          fetchData();
        }
      });
      return function onReloadData() {
        return _ref2.apply(this, arguments);
      };
    }();
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        hideScreenHeader: false,
        visible: true,
        onBack: function onBack() {
          return navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: onReloadData,
        noInternetOverlayStyle: _index.default.overlayStyle
      });
    }
    if (loading) {
      return (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: true
      });
    }
    if (error) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        hideScreenHeader: false,
        visible: true,
        header: false,
        onReload: fetchData,
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
        overlayStyle: _index.default.overlayStyle
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative2.SectionList, {
      refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
        refreshing: false,
        onRefresh: onReloadData
      }),
      sections: data || [],
      keyExtractor: function keyExtractor(_item, index) {
        return index.toString();
      },
      renderItem: function renderItem(_ref3) {
        var item = _ref3.item,
          section = _ref3.section;
        return renderItemSection(item, section);
      },
      renderSectionHeader: function renderSectionHeader(_ref4) {
        var section = _ref4.section;
        return _renderSectionHeader(section);
      },
      stickySectionHeadersEnabled: false,
      showsVerticalScrollIndicator: false,
      contentContainerStyle: _index.default.sectionContainerStyles,
      testID: `${SCREEN_NAME}SectionList`,
      accessibilityLabel: `${SCREEN_NAME}SectionList`
    });
  };
  var _default = exports.default = Toilets;
