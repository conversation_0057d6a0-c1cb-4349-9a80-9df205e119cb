  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _flightFilterPillStyles = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _store = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var tagTitleSelectedStyles = Object.assign({}, _flightFilterPillStyles.styles.tagTitleStyles, {
    color: _theme.color.palette.whiteGrey,
    fontWeight: _reactNative2.Platform.select({
      ios: "700",
      android: "normal"
    })
  });
  var FlightFilterPillsComponent = function FlightFilterPillsComponent(props) {
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var flatlistRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(props.listFilterPills),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      filterList = _useState2[0],
      setFilterList = _useState2[1];
    var timeout = null;
    (0, _react.useEffect)(function () {
      var _store$getState;
      var index = _store.store == null || (_store$getState = _store.store.getState()) == null || (_store$getState = _store$getState.flyReducer) == null || (_store$getState = _store$getState.filterPillList) == null ? undefined : _store$getState.findIndex(function (item) {
        return (item == null ? undefined : item.isSelected) === true;
      });
      timeout = setTimeout(function () {
        if (index !== -1) {
          var _flatlistRef$current;
          flatlistRef == null || (_flatlistRef$current = flatlistRef.current) == null || _flatlistRef$current.scrollToIndex({
            animated: true,
            index: index,
            viewPosition: 0.5,
            viewOffset: 0.5
          });
        }
      }, 500);
      return function () {
        clearTimeout(timeout);
      };
    }, [isFocused]);
    var getBackgroundColorItem = function getBackgroundColorItem(isSelected) {
      return {
        backgroundColor: isSelected ? _theme.color.palette.lightPurple : _theme.color.palette.whiteGrey,
        borderColor: isSelected ? _theme.color.palette.lightPurple : _theme.color.palette.lightGrey
      };
    };
    var handleItemPillOnPress = function handleItemPillOnPress(item, index) {
      if (!(item != null && item.isSelected)) {
        var _flatlistRef$current2;
        var nextData = filterList.map(function (pillItem) {
          if (pillItem.tagCode === item.tagCode) {
            return Object.assign({}, pillItem, {
              isSelected: true
            });
          } else {
            return Object.assign({}, pillItem, {
              isSelected: false
            });
          }
        });
        setFilterList(nextData);
        flatlistRef == null || (_flatlistRef$current2 = flatlistRef.current) == null || _flatlistRef$current2.scrollToIndex({
          animated: true,
          index: index,
          viewPosition: 0.5,
          viewOffset: 0.5
        });
        setTimeout(function () {
          dispatch(_flyRedux.FlyCreators.setSelectedItemFilterPill(item));
          props == null || props.itemPillOnPress(item);
        }, 0);
      }
    };
    var renderFilterPillItem = _react.default.useCallback(function (_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: [_flightFilterPillStyles.styles.itemFilterPillStyles, getBackgroundColorItem(item == null ? undefined : item.isSelected)],
        onPress: function onPress() {
          return handleItemPillOnPress(item, index);
        },
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBlackRegular",
          text: item.tagTitle,
          style: item != null && item.isSelected ? tagTitleSelectedStyles : _flightFilterPillStyles.styles.tagTitleStyles
        })
      });
    }, []);
    var onScrollToIndexFailed = function onScrollToIndexFailed(_ref2) {
      var index = _ref2.index;
      var wait = new Promise(function (resolve) {
        return setTimeout(resolve, 500);
      });
      wait.then(function () {
        var _flatlistRef$current3;
        flatlistRef == null || (_flatlistRef$current3 = flatlistRef.current) == null || _flatlistRef$current3.scrollToIndex({
          animated: true,
          index: index,
          viewPosition: 0.5,
          viewOffset: 0.5
        });
      });
    };
    var flightFilterPillsFlatList = _react.default.useMemo(function () {
      return (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        horizontal: true,
        ref: flatlistRef,
        initialScrollIndex: 0,
        onScrollToIndexFailed: onScrollToIndexFailed,
        data: filterList,
        renderItem: renderFilterPillItem,
        contentContainerStyle: _flightFilterPillStyles.styles.contentContainerStyle,
        showsHorizontalScrollIndicator: false,
        keyExtractor: function keyExtractor(item, index) {
          return `${item.tagCode}-${index.toString()}`;
        }
      });
    }, [filterList, flatlistRef]);
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _flightFilterPillStyles.styles.container,
      children: flightFilterPillsFlatList
    });
  };
  var arePropsEqual = function arePropsEqual(oldProps, newProps) {
    var _oldProps$listFilterP, _newProps$listFilterP, _oldProps$listFilterP2;
    return (oldProps == null || (_oldProps$listFilterP = oldProps.listFilterPills) == null ? undefined : _oldProps$listFilterP.length) === (newProps == null || (_newProps$listFilterP = newProps.listFilterPills) == null ? undefined : _newProps$listFilterP.length) && (oldProps == null || (_oldProps$listFilterP2 = oldProps.listFilterPills) == null ? undefined : _oldProps$listFilterP2.every(function (oldItem, index) {
      var _newProps$listFilterP2;
      var newItem = newProps == null || (_newProps$listFilterP2 = newProps.listFilterPills) == null ? undefined : _newProps$listFilterP2[index];
      return (oldItem == null ? undefined : oldItem.isSelected) === (newItem == null ? undefined : newItem.isSelected);
    }));
  };
  var _default = exports.default = _react.default.memo(FlightFilterPillsComponent, arePropsEqual);
