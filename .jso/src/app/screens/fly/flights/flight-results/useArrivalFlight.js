  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useArrivalFlight = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _envParams = _$$_REQUIRE(_dependencyMap[4]);
  var _queries = _$$_REQUIRE(_dependencyMap[5]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _store = _$$_REQUIRE(_dependencyMap[7]);
  var _fly = _$$_REQUIRE(_dependencyMap[8]);
  var _flySaga = _$$_REQUIRE(_dependencyMap[9]);
  var _eventName = _$$_REQUIRE(_dependencyMap[10]);
  var _analytics = _$$_REQUIRE(_dependencyMap[11]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[12]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[13]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _react = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _reactNativeFbsdkNext = _$$_REQUIRE(_dependencyMap[18]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[21]);
  var _useFlightSaveErrorHandling = _$$_REQUIRE(_dependencyMap[22]);
  var _useFlightGamification = _$$_REQUIRE(_dependencyMap[23]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[24]);
  var SCREEN_NAME = 'ArrivalResultScreen__';
  var PAGE_SIZE = 30;
  var useArrivalFlight = exports.useArrivalFlight = function useArrivalFlight() {
    var _env9;
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      sectionList = _useState2[0],
      setSectionList = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isLoading = _useState4[0],
      setLoading = _useState4[1];
    var _useState5 = (0, _react.useState)(1),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      searchPageNumber = _useState6[0],
      setSearchPageNumber = _useState6[1];
    var _useState7 = (0, _react.useState)(0),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      searchPageTotal = _useState8[0],
      setSearchPageTotal = _useState8[1];
    var searchMoreEnabled = (0, _react.useMemo)(function () {
      return searchPageNumber * PAGE_SIZE < searchPageTotal;
    }, [searchPageNumber, searchPageTotal]);
    var _useState9 = (0, _react.useState)((0, _dateTime.flyModuleUpdatedTime)()),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      lastUpdatedTime = _useState0[0],
      setLastUpdatedTime = _useState0[1];
    var _useState1 = (0, _react.useState)(""),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      nextToken = _useState10[0],
      setNextToken = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isEndLoadMore = _useState12[0],
      setEndLoadMore = _useState12[1];
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      isNetworkError = _useState14[0],
      setNetworkError = _useState14[1];
    var loadingRef = (0, _react.useRef)(false);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useSelector = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flightArrivalResultPayload),
      flyData = _useSelector.flightArrivalResultPayload;
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    (0, _useFlightSaveErrorHandling.useFlightSaveErrorHandling)();
    var _useFlightGamificatio = (0, _useFlightGamification.useFlightGamification)(),
      rewardGamificationSavedFlights = _useFlightGamificatio.rewardGamificationSavedFlights;
    (0, _react.useEffect)(function () {
      if (flyData != null && flyData.payload) {
        var _flyData$payload;
        var temp = flyData == null || (_flyData$payload = flyData.payload) == null ? undefined : _flyData$payload.map(function (item) {
          return {
            title: item.date,
            data: item.flightListingData
          };
        });
        setSectionList(temp || []);
      } else {
        setSectionList([]);
      }
    }, [flyData]);
    var resetSearchData = function resetSearchData() {
      setSearchPageNumber(1);
      setSearchPageTotal(0);
    };
    var resetLoadListData = function resetLoadListData() {
      setNextToken("");
    };
    var getFlyArrivalList = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (request) {
        var direction = request.direction,
          filterDate = request.filterDate,
          filters = request.filters,
          isFilter = request.isFilter,
          isLoadFlightAfter24h = request.isLoadFlightAfter24h,
          isLoadMore = request.isLoadMore,
          filterAirline = request.filterAirline,
          filterCityAirport = request.filterCityAirport;
        try {
          var _env, _env2, _store$getState$flyRe, _store$getState$mytra, _action$payload$data;
          if (loadingRef.current || isLoading) {
            return;
          }
          loadingRef.current = true;
          setLoading(true);
          var scheduledDate = (0, _dateTime.getDateSingapore)(filterDate);
          var body = Object.assign({
            direction: direction,
            page_size: 30
          }, isLoadMore ? {
            next_token: nextToken
          } : {}, {
            scheduled_date: (0, _flySaga.handleScheduledDateForFly)(isFilter, isLoadFlightAfter24h, scheduledDate),
            scheduled_time: (0, _flySaga.handleScheduledTimeForFly)(isLoadFlightAfter24h)
          }, (0, _flySaga.getQueryFilter)(filters), {
            airline: filterAirline,
            airport: filterCityAirport
          });
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getFlightsV2, Object.assign({}, body)),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
            }
          });
          var action = {
            payload: response.data
          };
          var convertedData = new _fly.FlyList().success(action, (_store$getState$flyRe = _store.store.getState().flyReducer) == null ? undefined : _store$getState$flyRe.flyCodesPayload, (_store$getState$mytra = _store.store.getState().mytravelReducer) == null ? undefined : _store$getState$mytra.myTravelFlightsPayload);
          var tempFlightList = [];
          if (isLoadMore) {
            var mergeList = {
              data: [].concat((0, _toConsumableArray2.default)(flyData == null ? undefined : flyData.payload), (0, _toConsumableArray2.default)(convertedData.data))
            };
            tempFlightList = (0, _flyRedux.mergeToDateGroup)(mergeList);
          } else {
            tempFlightList = convertedData == null ? undefined : convertedData.data;
          }
          setNetworkError(false);
          dispatch(_flyRedux.FlyCreators.flyArrivalListSuccessV2(tempFlightList));
          setLoading(false);
          setNextToken((_action$payload$data = action.payload.data) == null || (_action$payload$data = _action$payload$data.getFlights) == null ? undefined : _action$payload$data.next_token);
          setEndLoadMore(function (_state) {
            var _action$payload$data2;
            if ((_action$payload$data2 = action.payload.data) != null && (_action$payload$data2 = _action$payload$data2.getFlights) != null && _action$payload$data2.next_token) {
              return false;
            } else {
              return true;
            }
          });
        } catch (err) {
          setNetworkError(true);
        } finally {
          setLoading(false);
          loadingRef.current = false;
          setLastUpdatedTime((0, _dateTime.flyModuleUpdatedTime)());
          resetSearchData();
        }
      });
      return function getFlyArrivalList(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var searchArrivalFlight = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (request) {
        var direction = request.direction,
          filterDate = request.filterDate,
          filterTerminal = request.filterTerminal,
          keyword = request.keyword,
          _request$pageSize = request.pageSize,
          pageSize = _request$pageSize === undefined ? 30 : _request$pageSize,
          _request$isLoadMore = request.isLoadMore,
          isLoadMore = _request$isLoadMore === undefined ? false : _request$isLoadMore,
          filterAirline = request.filterAirline,
          filterCityAirport = request.filterCityAirport;
        try {
          var _env3, _env4, _store$getState$flyRe2, _store$getState$mytra2;
          if (loadingRef.current || isLoading) {
            return;
          }
          loadingRef.current = true;
          setLoading(true);
          var scheduledDate = (0, _moment.default)(filterDate).format(_dateTime.DateFormats.YearMonthDay);
          var bodyRequest = {
            text: keyword,
            filter: Object.assign({
              direction: direction,
              scheduled_date: scheduledDate
            }, (0, _flySaga.getQueryFilter)(filterTerminal), {
              airline: filterAirline,
              airport: filterCityAirport
            }),
            page_number: isLoadMore ? searchPageNumber + 1 : 1,
            page_size: pageSize
          };
          var response = yield (0, _request.default)({
            url: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.searchFlights, bodyRequest),
            parameters: {},
            headers: {
              "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.APPSYNC_GRAPHQL_API_KEY
            }
          });
          var mappedResponse = {
            data: {
              getFlights: {
                flights: response.data.data.flights.items
              }
            }
          };
          var action = {
            payload: mappedResponse
          };
          var convertedData = new _fly.FlyList().success(action, (_store$getState$flyRe2 = _store.store.getState().flyReducer) == null ? undefined : _store$getState$flyRe2.flyCodesPayload, (_store$getState$mytra2 = _store.store.getState().mytravelReducer) == null ? undefined : _store$getState$mytra2.myTravelFlightsPayload);
          var tempFlightList = [];
          if (isLoadMore) {
            var mergeList = {
              data: [].concat((0, _toConsumableArray2.default)(flyData == null ? undefined : flyData.payload), (0, _toConsumableArray2.default)(convertedData.data))
            };
            tempFlightList = (0, _flyRedux.mergeToDateGroup)(mergeList);
          } else {
            tempFlightList = convertedData == null ? undefined : convertedData.data;
          }
          setNetworkError(false);
          dispatch(_flyRedux.FlyCreators.flyArrivalListSuccessV2(tempFlightList));
          setLoading(false);
          setSearchPageNumber(response.data.data.flights.page_number);
          setSearchPageTotal(response.data.data.flights.total);
        } catch (err) {
          setNetworkError(true);
        } finally {
          loadingRef.current = false;
          setLoading(false);
          setLastUpdatedTime((0, _dateTime.flyModuleUpdatedTime)());
          resetLoadListData();
        }
      });
      return function searchArrivalFlight(_x2) {
        return _ref2.apply(this, arguments);
      };
    }();
    var myTravelInsertFlightQuery = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (input, payload, successCallback, failureCallback) {
        var _payload$item, _payload$item2, _payload$item3, _payload$item4, _payload$item5, _payload$item6, _payload$item7;
        var query = {
          input: {
            deviceId: _reactNativeDeviceInfo.default.getUniqueIdSync(),
            flightDirection: input == null ? undefined : input.flightDirection,
            ocidEmail: input == null ? undefined : input.enterpriseUserId,
            flightNo: input == null ? undefined : input.flightNumber,
            iataAirportCode: (payload == null || (_payload$item = payload.item) == null ? undefined : _payload$item.direction) === _flightProps.FlightDirection.arrival ? payload == null || (_payload$item2 = payload.item) == null ? undefined : _payload$item2.destinationCode : payload == null || (_payload$item3 = payload.item) == null ? undefined : _payload$item3.departingCode,
            isPassenger: input == null ? undefined : input.flightPax,
            scheduledDate: payload == null || (_payload$item4 = payload.item) == null ? undefined : _payload$item4.flightDate,
            scheduledTime: (payload == null || (_payload$item5 = payload.item) == null ? undefined : _payload$item5.timeOfFlight) || (payload == null || (_payload$item6 = payload.item) == null ? undefined : _payload$item6.scheduledTime),
            odtt: "OD",
            flightStatus: payload == null || (_payload$item7 = payload.item) == null ? undefined : _payload$item7.flightStatus
          }
        };
        var dtAction = (0, _analytics.dtManualActionEvent)(`${_analytics.FE_LOG_PREFIX}App__flight-arrival-listing-save`);
        try {
          var _query$input, _env5, _env6, _response$data, _myTravelFlightsPaylo;
          dtAction.reportStringValue("flight-arrival-listing-save-press-flightNumber", `${(_query$input = query.input) == null ? undefined : _query$input.flightNo}`);
          dtAction.reportStringValue("flight-arrival-listing-save-press-scheduledDate", `${query.input.scheduledDate}`);
          dtAction.reportStringValue("flight-arrival-listing-save-press-flightDirection", `${query.input.flightDirection}`);
          dtAction.reportStringValue("flight-arrival-listing-save-press-isPassenger", `${query.input.isPassenger}`);
          var response = yield (0, _request.default)({
            url: (_env5 = (0, _envParams.env)()) == null ? undefined : _env5.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.myTravelInsertFlight, query),
            parameters: {},
            headers: {
              "x-api-key": (_env6 = (0, _envParams.env)()) == null ? undefined : _env6.APPSYNC_GRAPHQL_API_KEY
            }
          });
          if (response.data.errors) {
            dtAction.reportStringValue("flight-arrival-listing-save-error-response", (0, _analytics.convertStringValue)(JSON.stringify(response.data.errors)));
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightFailure(payload));
            failureCallback();
            return;
          }
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightSuccess((0, _flySaga.formatResponseMyTravelInsertFlight)(response), Object.assign({}, payload, {
            isPassenger: input == null ? undefined : input.flightPax
          })));
          dtAction.reportStringValue('flight-arrival-listing-save-success', "success");
          // get gamification reward game chance
          if (response != null && (_response$data = response.data) != null && (_response$data = _response$data.data) != null && (_response$data = _response$data.saveFlight) != null && _response$data.eligibleForGameChance) {
            var _payload$item8 = payload == null ? undefined : payload.item,
              flightNumber = _payload$item8.flightNumber,
              actualTimestamp = _payload$item8.actualTimestamp,
              displayTimestamp = _payload$item8.displayTimestamp,
              scheduledDate = _payload$item8.scheduledDate,
              scheduledTime = _payload$item8.scheduledTime,
              timeOfFlight = _payload$item8.timeOfFlight;
            var priorityTime = actualTimestamp || displayTimestamp || `${scheduledDate} ${scheduledTime || timeOfFlight}`;
            var formatedScheduledTime = (0, _momentTimezone.default)(priorityTime).format("YYYY-MM-DD HH:mm").toString();
            var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore").format("YYYY-MM-DD HH:mm").toString();
            var gameChanceInput = {
              flightNumber: flightNumber,
              flightDatetime: formatedScheduledTime,
              saveTimestamp: currentTimeToUTC
            };
            rewardGamificationSavedFlights(gameChanceInput);
          }
          var isFirstSaveFlight = (myTravelFlightsPayload == null || (_myTravelFlightsPaylo = myTravelFlightsPayload.getMyTravelFlightDetails) == null ? undefined : _myTravelFlightsPaylo.length) == 0;
          successCallback(isFirstSaveFlight);
          (0, _analytics.analyticsLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL);
          (0, _analytics.dtACtionLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL);
          (0, _analytics.dtBizEvent)(SCREEN_NAME, _analytics.ANALYTICS_LOG_EVENT_NAME.SAVEFLIGHT_ARRIVAL, 'TAP-ON-SAVE-ARR-FLIGHT', {});
          _reactNativeFbsdkNext.AppEventsLogger.logEvent(_eventName.FB_EVENT_NAME.SAVE_FLIGHT_ARRIVAL, null);
        } catch (err) {
          dtAction.reportStringValue("flight-arrival-listing-save-error", (0, _analytics.convertStringValue)(err == null ? undefined : err.message));
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightFailure(payload));
          failureCallback();
        } finally {
          dtAction.leaveAction();
        }
      });
      return function myTravelInsertFlightQuery(_x3, _x4, _x5, _x6) {
        return _ref3.apply(this, arguments);
      };
    }();
    var removeSavedFlight = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (payload, successCallback, failureCallback) {
        var _payload$item9, _payload$item0, _payload$item1, _payload$item10, _payload$item11;
        var query = {
          input: {
            flightDirection: (payload == null || (_payload$item9 = payload.item) == null ? undefined : _payload$item9.flightDirection) || (payload == null || (_payload$item0 = payload.item) == null ? undefined : _payload$item0.direction),
            flightNo: payload == null || (_payload$item1 = payload.item) == null ? undefined : _payload$item1.flightNumber,
            scheduledDate: (payload == null || (_payload$item10 = payload.item) == null ? undefined : _payload$item10.scheduledDate) || (payload == null || (_payload$item11 = payload.item) == null ? undefined : _payload$item11.flightDate)
          }
        };
        var dtAction = (0, _analytics.dtManualActionEvent)(`${_analytics.FE_LOG_PREFIX}App__flight-arrival-listing-unsave`);
        try {
          var _env7, _env8;
          dtAction.reportStringValue("flight-arrival-listing-unsave-query-flightNo", `${query.input.flightNo}`);
          dtAction.reportStringValue("flight-arrival-listing-unsave-query-scheduledDate", `${query.input.scheduledDate}`);
          dtAction.reportStringValue("flight-arrival-listing-unsave-query-flightDirection", `${query.input.flightDirection}`);
          var response = yield (0, _request.default)({
            url: (_env7 = (0, _envParams.env)()) == null ? undefined : _env7.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.deleteMyTravelFlightDetail, query),
            parameters: {},
            headers: {
              "x-api-key": (_env8 = (0, _envParams.env)()) == null ? undefined : _env8.APPSYNC_GRAPHQL_API_KEY
            }
          });
          if (response.data.errors) {
            dtAction.reportStringValue("flight-arrival-listing-unsave-error-response", (0, _analytics.convertStringValue)(JSON.stringify(response.data.errors)));
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightFailure(payload));
            failureCallback();
            return;
          }
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightSuccess((0, _flySaga.formatResponseRemoveMyTravelFlight)(response == null ? undefined : response.data), payload));
          dtAction.reportStringValue('flight-arrival-listing-unsave-success', "success");
          successCallback();
        } catch (err) {
          dtAction.reportStringValue("flight-arrival-listing-unsave-error", (0, _analytics.convertStringValue)(err == null ? undefined : err.message));
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightFailure(payload));
          failureCallback();
        } finally {
          dtAction.leaveAction();
        }
      });
      return function removeSavedFlight(_x7, _x8, _x9) {
        return _ref4.apply(this, arguments);
      };
    }();
    var apiCallJob = (0, _react.useRef)(null);
    var isFocusedRef = (0, _react.useRef)(true);
    var refreshInterval = (_env9 = (0, _envParams.env)()) == null ? undefined : _env9.FLIGHT_REFRESH_INTERVAL;
    var _startLoopApiCall = function startLoopApiCall(callback) {
      cancelLoopApiJob();
      apiCallJob.current = setTimeout(function () {
        if (isFocusedRef.current) {
          callback();
          _startLoopApiCall(callback);
        }
      }, refreshInterval);
    };
    var cancelLoopApiJob = function cancelLoopApiJob() {
      clearTimeout(apiCallJob.current);
    };
    (0, _react.useEffect)(function () {
      return cancelLoopApiJob;
    }, []);
    return {
      sectionList: sectionList,
      isLoading: isLoading,
      lastUpdatedTime: lastUpdatedTime,
      isFocusedRef: isFocusedRef,
      isEndLoadMore: isEndLoadMore,
      searchMoreEnabled: searchMoreEnabled,
      isNetworkError: isNetworkError,
      setSectionList: setSectionList,
      startLoopApiCall: _startLoopApiCall,
      cancelLoopApiJob: cancelLoopApiJob,
      getFlyArrivalList: getFlyArrivalList,
      searchArrivalFlight: searchArrivalFlight,
      setNetworkError: setNetworkError,
      myTravelInsertFlightQuery: myTravelInsertFlightQuery,
      removeSavedFlight: removeSavedFlight
    };
  };
