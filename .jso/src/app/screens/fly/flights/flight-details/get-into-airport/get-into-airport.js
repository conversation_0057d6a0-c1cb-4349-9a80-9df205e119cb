  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.GetIntoAirport = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _cardWithLinks = _$$_REQUIRE(_dependencyMap[2]);
  var _error = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var cardLinksContainer = {
    alignSelf: "center",
    marginBottom: 12
  };
  var handleScheduledDateForAA = function handleScheduledDateForAA(statusMapping, displayTimestamp, scheduledDate) {
    var date = scheduledDate;
    if ((0, _utils.handleCondition)((statusMapping == null ? undefined : statusMapping.details_status_en) && ((statusMapping == null ? undefined : statusMapping.details_status_en.includes("Re-timed")) || (statusMapping == null ? undefined : statusMapping.details_status_en.includes("Delayed"))), true, false)) {
      var _displayTimestamp$spl;
      date = (0, _utils.handleCondition)(displayTimestamp, displayTimestamp == null || (_displayTimestamp$spl = displayTimestamp.split(" ")) == null ? undefined : _displayTimestamp$spl[0], scheduledDate);
    }
    return date;
  };
  var GetIntoAirport = exports.GetIntoAirport = function GetIntoAirport(_ref) {
    var _flyFlightDetailsPayl, _getIntoCityOrAirport, _flyFlightDetailsPayl2, _getIntoCityOrAirport2, _flyFlightDetailsPayl3, _getIntoCityOrAirport3, _flyFlightDetailsPayl4, _getIntoCityOrAirport4, _flyFlightDetailsPayl5, _flyFlightDetailsPayl6, _flyFlightDetailsPayl7, _flyFlightDetailsPayl8;
    var direction = _ref.direction,
      flightUniqueId = _ref.flightUniqueId,
      flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      _onPressed = _ref.onPressed;
    var dispatch = (0, _reactRedux.useDispatch)();
    var getIntoCityOrAirportPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.getIntoCityOrAirportPayload(state);
    });
    var _ref2 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || "",
      groundTransport = _ref2.groundTransport;
    var getIntoCityOrAirportApi = function getIntoCityOrAirportApi() {
      dispatch(_flyRedux.FlyCreators.flyGetIntoCityOrAirportRequest(direction, flightUniqueId));
    };
    if (!getIntoCityOrAirportPayload) {
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
    }
    if (getIntoCityOrAirportPayload != null && getIntoCityOrAirportPayload.errorFlag) {
      return (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
        type: _error.ErrorComponentType.standard,
        onPressed: getIntoCityOrAirportApi
      });
    }
    var dataGet = Object.assign({}, getIntoCityOrAirportPayload == null ? undefined : getIntoCityOrAirportPayload.getIntoCityOrAirport, {
      text1: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.dropOffDoor) || (getIntoCityOrAirportPayload == null || (_getIntoCityOrAirport = getIntoCityOrAirportPayload.getIntoCityOrAirport) == null ? undefined : _getIntoCityOrAirport.text1),
      text2: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl2.nearestCarpark) || (getIntoCityOrAirportPayload == null || (_getIntoCityOrAirport2 = getIntoCityOrAirportPayload.getIntoCityOrAirport) == null ? undefined : _getIntoCityOrAirport2.text2),
      link1: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl3.dropOffDoor) || (getIntoCityOrAirportPayload == null || (_getIntoCityOrAirport3 = getIntoCityOrAirportPayload.getIntoCityOrAirport) == null ? undefined : _getIntoCityOrAirport3.link1),
      link2: (flyFlightDetailsPayload == null || (_flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl4.nearestCarpark) || (getIntoCityOrAirportPayload == null || (_getIntoCityOrAirport4 = getIntoCityOrAirportPayload.getIntoCityOrAirport) == null ? undefined : _getIntoCityOrAirport4.link2),
      disabled: {
        disabled1: false,
        disabled2: false,
        disabled3: false
      },
      flightNumber: flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.flightNumber,
      scheduledDate: handleScheduledDateForAA(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl6 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl6.statusMapping, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl7 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl7.displayTimestamp, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl8 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl8.scheduledDate)
    });
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: cardLinksContainer,
      children: (0, _jsxRuntime.jsx)(_cardWithLinks.CardWithLinks, Object.assign({}, dataGet, {
        onPressed: function onPressed(type, item) {
          return _onPressed(type, item);
        },
        groundTransport: groundTransport
      }))
    });
  };
