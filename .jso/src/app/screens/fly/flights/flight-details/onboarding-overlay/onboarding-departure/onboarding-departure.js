  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.OnboardingDeparture = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _onboarding = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[7]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _theme = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_WIDTH = _reactNativeDeviceInfo.default.isTablet() ? _reactNative2.Dimensions.get("screen").width / 2 : _reactNative2.Dimensions.get("screen").width;
  var contentImageWidth = SCREEN_WIDTH - 32;
  var originalImageWidth = 343;
  var originalImageHeight = 330;
  var unit = contentImageWidth / originalImageWidth;
  var flyIconHeight = 126;
  var cmsImageWidth = SCREEN_WIDTH;
  var originalCmsImageWidth = 375;
  var originalCmsImageHeight = 854;
  var unit2 = cmsImageWidth / originalCmsImageWidth;
  var DELAY_TIME = 1400;
  var DELAY_TIME_STEP_2 = 1000;

  // IDs prefix constants
  var TEST_ID_PREFIX = "flight_onboarding_departure_";
  var ACCESSIBILITY_LABEL_PREFIX = "flight_onboarding_departure_";
  var AnimatedPressable = _reactNativeReanimated.default.createAnimatedComponent(_reactNative.Pressable);
  var _worklet_1402458859008_init_data = {
    code: "function onboardingDepartureTsx1(){const{flyIconTopPosition,flyIconTopOpacity,unit}=this.__closure;return{top:flyIconTopPosition.value,opacity:flyIconTopOpacity.value,width:unit*101,height:unit*126,alignSelf:\"center\"};}"
  };
  var _worklet_16104606264387_init_data = {
    code: "function onboardingDepartureTsx2(){const{titleOpacity,unit}=this.__closure;return{opacity:titleOpacity.value,marginTop:unit*16,alignSelf:\"center\"};}"
  };
  var _worklet_14766459854036_init_data = {
    code: "function onboardingDepartureTsx3(){const{unit,descOpacity,descTop}=this.__closure;return{marginTop:unit*20,width:158*unit,height:20*unit,opacity:descOpacity.value,alignSelf:'center',top:descTop.value};}"
  };
  var _worklet_3517784426032_init_data = {
    code: "function onboardingDepartureTsx4(){const{contentOpacity,contentImageWidth,unit,originalImageHeight}=this.__closure;return{opacity:contentOpacity.value,width:contentImageWidth,height:unit*originalImageHeight,alignSelf:\"center\",marginTop:unit*16};}"
  };
  var _worklet_1854358500503_init_data = {
    code: "function onboardingDepartureTsx5(){const{unit,descOpacity,desc2Top}=this.__closure;return{width:212*unit,height:67*unit,opacity:descOpacity.value,marginTop:14*unit,top:desc2Top.value};}"
  };
  var _worklet_3794802579339_init_data = {
    code: "function onboardingDepartureTsx6(){const{unit,contentOpacity}=this.__closure;return{marginTop:38*unit,opacity:contentOpacity.value,alignSelf:\"center\"};}"
  };
  var _worklet_17257651039661_init_data = {
    code: "function onboardingDepartureTsx7(){const{unit2,desc3Opacity,desc3Top}=this.__closure;return{width:288*unit2,height:20*unit2,opacity:desc3Opacity.value,alignSelf:'center',marginTop:28*unit2,marginBottom:20*unit2,top:desc3Top.value};}"
  };
  var _worklet_9707566107698_init_data = {
    code: "function onboardingDepartureTsx8(){const{originalCmsImageWidth,unit2,originalCmsImageHeight,desc3Opacity,cmsImageTop}=this.__closure;return{width:originalCmsImageWidth*unit2,height:originalCmsImageHeight*unit2,opacity:desc3Opacity.value,top:cmsImageTop.value};}"
  };
  var _worklet_9116743958999_init_data = {
    code: "function onboardingDepartureTsx9(){const{unit2,SCREEN_HEIGHT,desc3Opacity}=this.__closure;return{position:\"absolute\",height:316*unit2,top:SCREEN_HEIGHT-316*unit2,width:\"100%\",opacity:desc3Opacity.value};}"
  };
  var _worklet_3165335021526_init_data = {
    code: "function onboardingDepartureTsx10(){const{top,unit2,desc3Opacity}=this.__closure;return{bottom:(top+40)*unit2,opacity:desc3Opacity.value,position:\"absolute\",left:0,right:0,alignItems:\"center\"};}"
  };
  var _worklet_10154633612031_init_data = {
    code: "function onboardingDepartureTsx11(){const{containerOpacity}=this.__closure;return{opacity:containerOpacity.value};}"
  };
  var _worklet_10120521663477_init_data = {
    code: "function onboardingDepartureTsx12(){const{runOnJS,setMMKVdata,ENUM_STORAGE_MMKV,setIsStartOnboarding,setIsFinishedJourneyOnboarding}=this.__closure;runOnJS(setMMKVdata)(ENUM_STORAGE_MMKV.IS_FINISHED_JOURNEY_ONBOARDING,true);runOnJS(setIsStartOnboarding)(false);runOnJS(setIsFinishedJourneyOnboarding)(true);}"
  };
  var OnboardingDeparture = exports.OnboardingDeparture = function OnboardingDeparture(_ref) {
    var setIsStartOnboarding = _ref.setIsStartOnboarding,
      setIsFinishedJourneyOnboarding = _ref.setIsFinishedJourneyOnboarding;
    var _useSafeAreaInsets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)(),
      top = _useSafeAreaInsets.top;
    var scrollRef = (0, _react.useRef)(null);
    var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
      SCREEN_HEIGHT = _Dimensions$get.height;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      animationsStarted = _useState2[0],
      setAnimationsStarted = _useState2[1];
    var FLY_ICON_TOP_POSITION = 42 * unit;
    var DESC_TOP_INITIAL = -20 * unit;
    var DESC2_TOP_INITIAL = 20 * unit;
    var DESC3_TOP_INITIAL = -20 * unit;
    var CMS_IMAGE_TOP_INITIAL = 20 * unit;
    var containerOpacity = (0, _reactNativeReanimated.useSharedValue)(1);
    var flyIconTopPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var flyIconTopOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var titleOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var descOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var descTop = (0, _reactNativeReanimated.useSharedValue)(DESC_TOP_INITIAL);
    var desc2Top = (0, _reactNativeReanimated.useSharedValue)(DESC2_TOP_INITIAL);
    var contentOpacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var desc3Opacity = (0, _reactNativeReanimated.useSharedValue)(0);
    var desc3Top = (0, _reactNativeReanimated.useSharedValue)(DESC3_TOP_INITIAL);
    var cmsImageTop = (0, _reactNativeReanimated.useSharedValue)(CMS_IMAGE_TOP_INITIAL);
    var _useState3 = (0, _react.useState)("STEP_ONE"),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      step = _useState4[0],
      setStep = _useState4[1];
    var flyIconStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx1 = function onboardingDepartureTsx1() {
        return {
          top: flyIconTopPosition.value,
          opacity: flyIconTopOpacity.value,
          width: unit * 101,
          height: unit * 126,
          alignSelf: "center"
        };
      };
      onboardingDepartureTsx1.__closure = {
        flyIconTopPosition: flyIconTopPosition,
        flyIconTopOpacity: flyIconTopOpacity,
        unit: unit
      };
      onboardingDepartureTsx1.__workletHash = 1402458859008;
      onboardingDepartureTsx1.__initData = _worklet_1402458859008_init_data;
      return onboardingDepartureTsx1;
    }());
    var titleStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx2 = function onboardingDepartureTsx2() {
        return {
          opacity: titleOpacity.value,
          marginTop: unit * 16,
          alignSelf: "center"
        };
      };
      onboardingDepartureTsx2.__closure = {
        titleOpacity: titleOpacity,
        unit: unit
      };
      onboardingDepartureTsx2.__workletHash = 16104606264387;
      onboardingDepartureTsx2.__initData = _worklet_16104606264387_init_data;
      return onboardingDepartureTsx2;
    }());
    var desc1Style = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx3 = function onboardingDepartureTsx3() {
        return {
          marginTop: unit * 20,
          width: 158 * unit,
          height: 20 * unit,
          opacity: descOpacity.value,
          alignSelf: 'center',
          top: descTop.value
        };
      };
      onboardingDepartureTsx3.__closure = {
        unit: unit,
        descOpacity: descOpacity,
        descTop: descTop
      };
      onboardingDepartureTsx3.__workletHash = 14766459854036;
      onboardingDepartureTsx3.__initData = _worklet_14766459854036_init_data;
      return onboardingDepartureTsx3;
    }());
    var contentImgStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx4 = function onboardingDepartureTsx4() {
        return {
          opacity: contentOpacity.value,
          width: contentImageWidth,
          height: unit * originalImageHeight,
          alignSelf: "center",
          marginTop: unit * 16
        };
      };
      onboardingDepartureTsx4.__closure = {
        contentOpacity: contentOpacity,
        contentImageWidth: contentImageWidth,
        unit: unit,
        originalImageHeight: originalImageHeight
      };
      onboardingDepartureTsx4.__workletHash = 3517784426032;
      onboardingDepartureTsx4.__initData = _worklet_3517784426032_init_data;
      return onboardingDepartureTsx4;
    }());
    var animatedDesc2Style = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx5 = function onboardingDepartureTsx5() {
        return {
          width: 212 * unit,
          height: 67 * unit,
          opacity: descOpacity.value,
          marginTop: 14 * unit,
          top: desc2Top.value
        };
      };
      onboardingDepartureTsx5.__closure = {
        unit: unit,
        descOpacity: descOpacity,
        desc2Top: desc2Top
      };
      onboardingDepartureTsx5.__workletHash = 1854358500503;
      onboardingDepartureTsx5.__initData = _worklet_1854358500503_init_data;
      return onboardingDepartureTsx5;
    }());
    var desc2Style = {
      left: (_reactNativeDeviceInfo.default.isTablet() ? 28.5 : 114) * unit
    };
    var btnNextStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx6 = function onboardingDepartureTsx6() {
        return {
          marginTop: 38 * unit,
          opacity: contentOpacity.value,
          alignSelf: "center"
        };
      };
      onboardingDepartureTsx6.__closure = {
        unit: unit,
        contentOpacity: contentOpacity
      };
      onboardingDepartureTsx6.__workletHash = 3794802579339;
      onboardingDepartureTsx6.__initData = _worklet_3794802579339_init_data;
      return onboardingDepartureTsx6;
    }());
    var desc3Style = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx7 = function onboardingDepartureTsx7() {
        return {
          width: 288 * unit2,
          height: 20 * unit2,
          opacity: desc3Opacity.value,
          alignSelf: 'center',
          marginTop: 28 * unit2,
          marginBottom: 20 * unit2,
          top: desc3Top.value
        };
      };
      onboardingDepartureTsx7.__closure = {
        unit2: unit2,
        desc3Opacity: desc3Opacity,
        desc3Top: desc3Top
      };
      onboardingDepartureTsx7.__workletHash = 17257651039661;
      onboardingDepartureTsx7.__initData = _worklet_17257651039661_init_data;
      return onboardingDepartureTsx7;
    }());
    var cmsImageStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx8 = function onboardingDepartureTsx8() {
        return {
          width: originalCmsImageWidth * unit2,
          height: originalCmsImageHeight * unit2,
          opacity: desc3Opacity.value,
          top: cmsImageTop.value
        };
      };
      onboardingDepartureTsx8.__closure = {
        originalCmsImageWidth: originalCmsImageWidth,
        unit2: unit2,
        originalCmsImageHeight: originalCmsImageHeight,
        desc3Opacity: desc3Opacity,
        cmsImageTop: cmsImageTop
      };
      onboardingDepartureTsx8.__workletHash = 9707566107698;
      onboardingDepartureTsx8.__initData = _worklet_9707566107698_init_data;
      return onboardingDepartureTsx8;
    }());
    var bgOverlayStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx9 = function onboardingDepartureTsx9() {
        return {
          position: "absolute",
          height: 316 * unit2,
          top: SCREEN_HEIGHT - 316 * unit2,
          width: "100%",
          opacity: desc3Opacity.value
        };
      };
      onboardingDepartureTsx9.__closure = {
        unit2: unit2,
        SCREEN_HEIGHT: SCREEN_HEIGHT,
        desc3Opacity: desc3Opacity
      };
      onboardingDepartureTsx9.__workletHash = 9116743958999;
      onboardingDepartureTsx9.__initData = _worklet_9116743958999_init_data;
      return onboardingDepartureTsx9;
    }());
    var btnSeeDetailStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx10 = function onboardingDepartureTsx10() {
        return {
          bottom: (top + 40) * unit2,
          opacity: desc3Opacity.value,
          position: "absolute",
          left: 0,
          right: 0,
          alignItems: "center"
        };
      };
      onboardingDepartureTsx10.__closure = {
        top: top,
        unit2: unit2,
        desc3Opacity: desc3Opacity
      };
      onboardingDepartureTsx10.__workletHash = 3165335021526;
      onboardingDepartureTsx10.__initData = _worklet_3165335021526_init_data;
      return onboardingDepartureTsx10;
    }());
    var containerStyle = {
      alignItems: _reactNativeDeviceInfo.default.isTablet() ? 'center' : "baseline"
    };
    var animatedContainerStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var onboardingDepartureTsx11 = function onboardingDepartureTsx11() {
        return {
          opacity: containerOpacity.value
        };
      };
      onboardingDepartureTsx11.__closure = {
        containerOpacity: containerOpacity
      };
      onboardingDepartureTsx11.__workletHash = 10154633612031;
      onboardingDepartureTsx11.__initData = _worklet_10154633612031_init_data;
      return onboardingDepartureTsx11;
    }());
    var onPressNext = function onPressNext() {
      setStep("STEP_TWO");
    };
    (0, _react.useEffect)(function () {
      if (step === "STEP_ONE") {
        startAnimationStep1();
      }
      if (step === "STEP_TWO") {
        startAnimationStep2();
      }
    }, [step]);
    var startAnimationStep1 = function startAnimationStep1() {
      // Use a combination of setTimeout and InteractionManager to improve reliability
      // This ensures animations will start even if the JS thread is busy
      // Also try with InteractionManager as backup
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        var timeoutId = setTimeout(function () {
          if (!animationsStarted) {
            setAnimationsStarted(true);
            // Force animations to start with a direct call outside InteractionManager
            // Start first set of animations immediately
            flyIconTopPosition.value = (0, _reactNativeReanimated.withTiming)(FLY_ICON_TOP_POSITION, {
              duration: DELAY_TIME
            });
            flyIconTopOpacity.value = (0, _reactNativeReanimated.withTiming)(1, {
              duration: 700
            });
            titleOpacity.value = (0, _reactNativeReanimated.withTiming)(1, {
              duration: 700
            });
            // Schedule second set with normal delays
            descOpacity.value = (0, _reactNativeReanimated.withDelay)(700, (0, _reactNativeReanimated.withTiming)(1, {
              duration: 500
            }));
            descTop.value = (0, _reactNativeReanimated.withDelay)(700, (0, _reactNativeReanimated.withTiming)(0, {
              duration: 500
            }));
            contentOpacity.value = (0, _reactNativeReanimated.withDelay)(700, (0, _reactNativeReanimated.withTiming)(1, {
              duration: 500
            }));
            desc2Top.value = (0, _reactNativeReanimated.withDelay)(700, (0, _reactNativeReanimated.withTiming)(0, {
              duration: 500
            }));
          }
        }, 100); // Short timeout to prioritize animations

        return function () {
          return clearTimeout(timeoutId);
        };
      });
    };
    var startAnimationStep2 = function startAnimationStep2() {
      scrollToTop();
      descOpacity.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: 500
      });
      contentOpacity.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: 500
      });
      desc3Opacity.value = (0, _reactNativeReanimated.withDelay)(DELAY_TIME_STEP_2, (0, _reactNativeReanimated.withTiming)(1, {
        duration: 500
      }));
      desc3Top.value = (0, _reactNativeReanimated.withDelay)(DELAY_TIME_STEP_2, (0, _reactNativeReanimated.withTiming)(0, {
        duration: 500
      }));
      cmsImageTop.value = (0, _reactNativeReanimated.withDelay)(DELAY_TIME_STEP_2, (0, _reactNativeReanimated.withTiming)(0, {
        duration: 500
      }));
    };
    var scrollToTop = function scrollToTop() {
      if (scrollRef.current) {
        scrollRef.current.scrollTo({
          y: 0,
          animated: true
        });
      }
    };
    var onPressSeeDetailFlight = function onPressSeeDetailFlight() {
      containerOpacity.value = (0, _reactNativeReanimated.withTiming)(0, {
        duration: 500
      }, function () {
        var onboardingDepartureTsx12 = function onboardingDepartureTsx12() {
          (0, _reactNativeReanimated.runOnJS)(_mmkvStorage.setMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FINISHED_JOURNEY_ONBOARDING, true);
          (0, _reactNativeReanimated.runOnJS)(setIsStartOnboarding)(false);
          (0, _reactNativeReanimated.runOnJS)(setIsFinishedJourneyOnboarding)(true);
        };
        onboardingDepartureTsx12.__closure = {
          runOnJS: _reactNativeReanimated.runOnJS,
          setMMKVdata: _mmkvStorage.setMMKVdata,
          ENUM_STORAGE_MMKV: _mmkvStorage.ENUM_STORAGE_MMKV,
          setIsStartOnboarding: setIsStartOnboarding,
          setIsFinishedJourneyOnboarding: setIsFinishedJourneyOnboarding
        };
        onboardingDepartureTsx12.__workletHash = 10120521663477;
        onboardingDepartureTsx12.__initData = _worklet_10120521663477_init_data;
        return onboardingDepartureTsx12;
      }());
    };
    var onBackgroundPress = function onBackgroundPress() {
      if (step === "STEP_ONE") {
        onPressNext();
      } else {
        onPressSeeDetailFlight();
      }
    };
    var renderStep1 = function renderStep1() {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.Image, {
          source: _onboarding.Desc1Onboarding,
          style: desc1Style,
          resizeMode: "contain",
          testID: `${TEST_ID_PREFIX}desc1`,
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}description_1`
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.Image, {
          source: _onboarding.ContentOnboardingDep,
          style: contentImgStyle,
          resizeMode: "contain",
          testID: `${TEST_ID_PREFIX}content_image`,
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}content_image`
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.Image, {
          source: _onboarding.DescOnboardingDep,
          style: [desc2Style, animatedDesc2Style],
          resizeMode: "contain",
          testID: `${TEST_ID_PREFIX}desc2`,
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}description_2`
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: btnNextStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPressNext,
            testID: `${TEST_ID_PREFIX}next_button`,
            accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}next_button`,
            accessibilityRole: "button",
            children: (0, _jsxRuntime.jsx)(_onboarding.BtnNextIcon, {})
          })
        })]
      });
    };
    var renderStep2 = function renderStep2() {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.Image, {
          source: _onboarding.Desc2Onboarding,
          style: desc3Style,
          resizeMode: "contain",
          testID: `${TEST_ID_PREFIX}desc3`,
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}description_3`
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.Image, {
          source: _onboarding.CmsDepExample,
          style: cmsImageStyle,
          resizeMode: "contain",
          testID: `${TEST_ID_PREFIX}cms_example`,
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}cms_example`
        })]
      });
    };
    var renderStep2BottomButtons = function renderStep2BottomButtons() {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: bgOverlayStyle,
          testID: `${TEST_ID_PREFIX}bg_overlay`,
          pointerEvents: "none",
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}background_overlay`,
          children: (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
            colors: _theme.color.palette.overlayOnboarding,
            locations: [0.0667, 0.9927],
            start: {
              x: 0.5,
              y: 1
            },
            end: {
              x: 0.5,
              y: 0
            },
            style: {
              flex: 1
            }
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
          style: btnSeeDetailStyle,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onPressSeeDetailFlight,
            testID: `${TEST_ID_PREFIX}see_detail_button`,
            accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}see_detail_button`,
            accessibilityRole: "button",
            children: (0, _jsxRuntime.jsx)(_onboarding.BtnSeeDetail, {})
          })
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        ref: scrollRef,
        contentContainerStyle: {
          minHeight: SCREEN_HEIGHT
        },
        showsVerticalScrollIndicator: false,
        bounces: false,
        testID: `${TEST_ID_PREFIX}scroll_view`,
        accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}scroll_view`,
        children: (0, _jsxRuntime.jsxs)(AnimatedPressable, {
          style: [containerStyle, animatedContainerStyle],
          onPress: onBackgroundPress,
          testID: `${TEST_ID_PREFIX}container`,
          accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}container`,
          children: [(0, _jsxRuntime.jsx)(_reactNativeReanimated.default.Image, {
            source: _onboarding.OnboardingDepIcon,
            style: flyIconStyle,
            testID: `${TEST_ID_PREFIX}icon`,
            accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}icon`
          }), (0, _jsxRuntime.jsx)(_reactNativeReanimated.default.View, {
            style: titleStyle,
            testID: `${TEST_ID_PREFIX}title_container`,
            accessibilityLabel: `${ACCESSIBILITY_LABEL_PREFIX}title_container`,
            children: (0, _jsxRuntime.jsx)(_onboarding.TitleOnboarding, {})
          }), step === "STEP_ONE" && renderStep1(), step === "STEP_TWO" && renderStep2()]
        })
      }), step === "STEP_TWO" && renderStep2BottomButtons()]
    });
  };
