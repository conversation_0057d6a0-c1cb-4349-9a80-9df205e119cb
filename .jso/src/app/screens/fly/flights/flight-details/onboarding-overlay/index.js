  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[3]);
  var _onboardingDeparture = _$$_REQUIRE(_dependencyMap[4]);
  var _onboardingArrival = _$$_REQUIRE(_dependencyMap[5]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var OnboardingOverlay = function OnboardingOverlay(_ref) {
    var direction = _ref.direction,
      isLoadingDetailFlight = _ref.isLoadingDetailFlight,
      setIsFinishedJourneyOnboarding = _ref.setIsFinishedJourneyOnboarding;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isStartOnboarding = _useState2[0],
      setIsStartOnboarding = _useState2[1];
    (0, _react.useEffect)(function () {
      var res = (0, _mmkvStorage.getMMKVdata)(_mmkvStorage.ENUM_STORAGE_MMKV.IS_FINISHED_JOURNEY_ONBOARDING, _mmkvStorage.ENUM_STORAGE_TYPE.boolean);
      setIsFinishedJourneyOnboarding(Boolean(res));
      if (!res) {
        if (!isLoadingDetailFlight) {
          setTimeout(function () {
            setIsStartOnboarding(true);
          }, 600);
        }
      }
    }, [isLoadingDetailFlight]);
    if (!isStartOnboarding) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.overlay,
      children: direction === _flightProps.FlightDirection.departure ? (0, _jsxRuntime.jsx)(_onboardingDeparture.OnboardingDeparture, {
        setIsStartOnboarding: setIsStartOnboarding,
        setIsFinishedJourneyOnboarding: setIsFinishedJourneyOnboarding
      }) : (0, _jsxRuntime.jsx)(_onboardingArrival.OnboardingArrival, {
        setIsStartOnboarding: setIsStartOnboarding,
        setIsFinishedJourneyOnboarding: setIsFinishedJourneyOnboarding
      })
    });
  };
  var _default = exports.default = OnboardingOverlay;
  var styles = _reactNative.StyleSheet.create({
    overlay: Object.assign({}, _reactNative.StyleSheet.absoluteFillObject, {
      backgroundColor: "rgba(18, 18, 18, 0.9)",
      opacity: 1,
      zIndex: 2
    })
  });
