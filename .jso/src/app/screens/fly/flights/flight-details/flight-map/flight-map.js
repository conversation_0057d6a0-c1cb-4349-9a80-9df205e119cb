  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _staffPerkPromotionDetailModal = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[9]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var container = Object.assign({
    width: _staffPerkPromotionDetailModal.width - (0, _reactNativeSizeMatters.scale)(45),
    backgroundColor: _theme.color.palette.whiteGrey
  }, _reactNative.Platform.select({
    android: {
      elevation: 2
    },
    ios: {
      shadowColor: _theme.color.palette.almostBlackGrey,
      shadowOffset: {
        width: 0,
        height: 6
      },
      shadowOpacity: 0.08,
      shadowRadius: 5
    }
  }), {
    borderRadius: 16,
    marginBottom: 12,
    alignSelf: "center"
  });
  var FlightMap = function FlightMap(_ref) {
    var terminal = _ref.terminal,
      onPressed = _ref.onPressed,
      isFlightDetailsFirst = _ref.isFlightDetailsFirst,
      title = _ref.title,
      onSendTrackingData = _ref.onSendTrackingData;
    var handlePressMap = function handlePressMap() {
      onPressed(_flightDetails.TypePressDetailFlightCard.TERMINAL);
      onSendTrackingData == null || onSendTrackingData((0, _i18n.translate)("flightDetailV2.flightInformationHub.sections.map.viewMapButton", {
        terminal: terminal
      }));
    };
    var mapTitle = title == null ? undefined : title.replace("<terminal_number>", terminal);
    if (!terminal) return null;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [container, {
        width: isFlightDetailsFirst ? _staffPerkPromotionDetailModal.width - (0, _reactNativeSizeMatters.scale)(32) : _staffPerkPromotionDetailModal.width - (0, _reactNativeSizeMatters.scale)(45)
      }],
      children: (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        onPress: handlePressMap,
        children: (0, _jsxRuntime.jsx)(_reactNative.ImageBackground, {
          source: _backgrounds.FlightMapBackground,
          style: styles.mapBackground,
          resizeMode: "stretch",
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.mapButton,
            children: [(0, _jsxRuntime.jsx)(_icons.FlightMapIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
              text: mapTitle,
              style: styles.mapButtonText
            })]
          })
        })
      })
    });
  };
  var _default = exports.default = FlightMap;
  var styles = _reactNative.StyleSheet.create({
    mapBackground: {
      width: "100%",
      height: 80,
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 16,
      overflow: "hidden"
    },
    mapButton: {
      flexDirection: "row",
      columnGap: 4,
      borderRadius: 60,
      backgroundColor: "white",
      paddingVertical: 5,
      paddingHorizontal: 10,
      alignItems: "center"
    },
    mapButtonText: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.gradientColor1Start
    })
  });
