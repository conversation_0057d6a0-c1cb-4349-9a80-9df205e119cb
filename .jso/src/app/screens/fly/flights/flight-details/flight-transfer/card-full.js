  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _flightTransfer = _$$_REQUIRE(_dependencyMap[7]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[8]);
  var _transportItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _markerItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _constants = _$$_REQUIRE(_dependencyMap[11]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _utils = _$$_REQUIRE(_dependencyMap[14]);
  var _markerItemTraveller = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var CardFull = function CardFull(_ref) {
    var terminal = _ref.terminal,
      onPressedCollapse = _ref.onPressedCollapse,
      section = _ref.section,
      onPressedMarker = _ref.onPressedMarker,
      onSendTrackingData = _ref.onSendTrackingData,
      isTravelling = _ref.isTravelling;
    var navigation = (0, _navigationHelper.useNavigation)();
    var _ref2 = _flightTransfer.skyTrainMappingT123[terminal] || {},
      skyTrain1 = _ref2.skyTrain1,
      skyTrain2 = _ref2.skyTrain2;
    var navigateToT4ShuttleBus = function navigateToT4ShuttleBus() {
      onSendTrackingData == null || onSendTrackingData((0, _i18n.translate)("flightDetailV2.flightInformationHub.sections.transferInfo.toFromT4"));
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: 'https://airportbus.plotigo.app/'
      });
    };
    if (terminal === 4) {
      return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: section == null ? undefined : section.title,
          style: styles.cardTitle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
          style: styles.infoContainer,
          onPress: navigateToT4ShuttleBus,
          children: [(0, _jsxRuntime.jsx)(_icons.FlightShuttleBus, {}), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.infoContentT4,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.infoRow,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flightDetailV2.flightInformationHub.sections.transferInfo.toFromT4",
                style: styles.rowTitle
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "flightDetailV2.flightInformationHub.sections.transferInfo.inPublicTransit",
              style: styles.infoText
            })]
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.divider
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.transportContainer,
          children: [(0, _jsxRuntime.jsx)(_transportItem.default, {
            keyString: "betweenT123",
            onSendTrackingData: onSendTrackingData
          }), (0, _jsxRuntime.jsx)(_transportItem.default, {
            keyString: "toFromJewel",
            onSendTrackingData: onSendTrackingData
          })]
        }), !!(section != null && section.enableExpandCollapse) && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
          style: styles.arrowContainer,
          onPress: onPressedCollapse,
          children: (0, _jsxRuntime.jsx)(_icons.TopArrow, {})
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: section == null ? undefined : section.title,
        style: styles.cardTitle
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.infoContainer,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightSkyTrain, {}), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.infoContent,
          children: [[skyTrain1, skyTrain2].map(function (train, index) {
            return (0, _utils.handleCondition)(isTravelling, (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.infoRowTraveller,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flightDetailV2.flightInformationHub.sections.transferInfo.skytrainTo",
                txOptions: {
                  start: terminal,
                  end: train
                },
                style: styles.rowTitle
              }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: styles.markerRow,
                children: [(0, _jsxRuntime.jsx)(_markerItemTraveller.default, {
                  type: _flightDetails.TypeTransport.PUBLIC,
                  train: train,
                  onPressedMarker: onPressedMarker
                }), (0, _jsxRuntime.jsx)(_markerItemTraveller.default, {
                  type: _flightDetails.TypeTransport.TRANSIT,
                  train: train,
                  onPressedMarker: onPressedMarker
                })]
              })]
            }, index), (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
              testID: "button_skytrain_MarkerItem",
              accessibilityLabel: "button_skytrain_MarkerItem",
              onPress: function onPress() {
                return onPressedMarker(_flightDetails.TypePressDetailFlightCard.SKYTRAIN, null, {
                  type: _flightDetails.TypeTransport.PUBLIC,
                  terminal: train
                });
              },
              children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: styles.containerText,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  tx: "flightDetailV2.flightInformationHub.sections.transferInfo.skytrainTo",
                  txOptions: {
                    start: terminal,
                    end: train
                  },
                  style: styles.rowTitle
                }), (0, _jsxRuntime.jsx)(_markerItem.default, {
                  type: _flightDetails.TypeTransport.PUBLIC,
                  train: train,
                  onSendTrackingData: onSendTrackingData
                })]
              })
            }, index));
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightInformationHub.sections.transferInfo.operating",
            txOptions: {
              startTime: "04:30",
              endTime: "01:30"
            },
            style: styles.infoText
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.divider
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.transportContainer,
        children: [(0, _jsxRuntime.jsx)(_transportItem.default, {
          keyString: "toFromT4",
          onSendTrackingData: onSendTrackingData
        }), (0, _jsxRuntime.jsx)(_transportItem.default, {
          keyString: "toFromJewel",
          onSendTrackingData: onSendTrackingData
        })]
      }), !!(section != null && section.enableExpandCollapse) && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        style: styles.arrowContainer,
        onPress: onPressedCollapse,
        children: (0, _jsxRuntime.jsx)(_icons.TopArrow, {})
      })]
    });
  };
  var _default = exports.default = CardFull;
  var styles = _reactNative.StyleSheet.create({
    cardTitle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    containerText: {
      flexDirection: 'row',
      gap: 6
    },
    rowTitle: Object.assign({}, _text.newPresets.title1Bold, {
      lineHeight: 18,
      fontSize: 16
    }),
    infoContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: 12
    },
    infoContent: {
      flex: 1,
      rowGap: 12
    },
    infoContentT4: {
      flex: 1,
      rowGap: 4
    },
    infoRow: {
      columnGap: 4
    },
    infoRowTraveller: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    markerRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      columnGap: 4
    },
    transportContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      columnGap: 12
    },
    infoText: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    arrowContainer: {
      width: "100%",
      alignItems: "center"
    },
    divider: {
      width: '100%',
      height: 1,
      backgroundColor: _theme.color.palette.lighterGrey
    }
  });
