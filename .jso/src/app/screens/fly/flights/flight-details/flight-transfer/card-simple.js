  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _flightTransfer = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var CardSimple = function CardSimple(_ref) {
    var terminal = _ref.terminal,
      onPressedCollapse = _ref.onPressedCollapse,
      section = _ref.section;
    var _ref2 = _flightTransfer.skyTrainMappingT123[terminal] || {},
      skyTrain1 = _ref2.skyTrain1,
      skyTrain2 = _ref2.skyTrain2;
    var renderTx = function renderTx() {
      if (terminal === 4) return "flightDetailV2.flightInformationHub.sections.transferInfo.t4Info";
      return "flightDetailV2.flightInformationHub.sections.transferInfo.skytrainInfo";
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: section == null ? undefined : section.title,
        style: styles.cardTitle
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.infoRow,
        children: [(0, _jsxRuntime.jsx)(_icons.UpwardArrow, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: renderTx(),
          txOptions: {
            terminal: terminal,
            skyTrain1: skyTrain1,
            skyTrain2: skyTrain2
          },
          style: styles.infoText
        })]
      }), !!(section != null && section.enableExpandCollapse) && (0, _jsxRuntime.jsx)(_reactNative2.TouchableOpacity, {
        style: styles.arrowContainer,
        onPress: onPressedCollapse,
        children: (0, _jsxRuntime.jsx)(_icons.DownArrow, {})
      })]
    });
  };
  var _default = exports.default = CardSimple;
  var styles = _reactNative.StyleSheet.create({
    cardTitle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      columnGap: 8
    },
    infoText: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      flex: 1,
      flexWrap: 'wrap'
    }),
    arrowContainer: {
      width: "100%",
      alignItems: "center"
    }
  });
