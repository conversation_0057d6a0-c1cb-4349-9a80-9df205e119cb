  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _flightDetails = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var MarkerItemTraveller = function MarkerItemTraveller(_ref) {
    var type = _ref.type,
      train = _ref.train,
      onPressedMarker = _ref.onPressedMarker,
      onSendTrackingData = _ref.onSendTrackingData;
    return (0, _jsxRuntime.jsxs)(_reactNative2.TouchableOpacity, {
      testID: "button_MarkerItemTraveller",
      accessibilityLabel: "button_MarkerItemTraveller",
      style: styles.markerItem,
      onPress: function onPress() {
        return onPressedMarker(_flightDetails.TypePressDetailFlightCard.SKYTRAIN, null, {
          type: type,
          terminal: train
        });
      },
      children: [(0, _jsxRuntime.jsx)(_icons.FlightMarker, {}), (0, _jsxRuntime.jsx)(_text.Text, {
        testID: "text_MarkerItemTraveller",
        accessibilityLabel: "text_MarkerItemTraveller",
        tx: `flightDetailV2.flightInformationHub.sections.transferInfo.${type}`,
        style: styles.infoText
      })]
    });
  };
  var _default = exports.default = MarkerItemTraveller;
  var styles = _reactNative.StyleSheet.create({
    markerItem: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    infoText: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey
    })
  });
