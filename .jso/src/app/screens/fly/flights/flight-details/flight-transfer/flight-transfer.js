  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.skyTrainMappingT123 = exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _cardFull = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _cardSimple = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _flightDetail = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var skyTrainMappingT123 = exports.skyTrainMappingT123 = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, 1, {
    skyTrain1: 2,
    skyTrain2: 3
  }), 2, {
    skyTrain1: 1,
    skyTrain2: 3
  }), 3, {
    skyTrain1: 1,
    skyTrain2: 2
  });
  var FlightTransfer = function FlightTransfer(_ref) {
    var terminal = _ref.terminal,
      section = _ref.section,
      onPressedMarker = _ref.onPressedMarker,
      onSendTrackingData = _ref.onSendTrackingData,
      isTravelling = _ref.isTravelling;
    var _useState = (0, _react.useState)(!(section != null && section.enableExpandCollapse) || (section == null ? undefined : section.expandCollapse) === _flightDetail.ExpandCollapseEnum.EXPAND),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isOpened = _useState2[0],
      setIsOpened = _useState2[1];
    (0, _react.useEffect)(function () {
      setIsOpened(!(section != null && section.enableExpandCollapse) || (section == null ? undefined : section.expandCollapse) === _flightDetail.ExpandCollapseEnum.EXPAND);
    }, [section]);
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.card,
      children: isOpened ? (0, _jsxRuntime.jsx)(_cardFull.default, {
        section: section,
        terminal: Number(terminal),
        onPressedCollapse: function onPressedCollapse() {
          return setIsOpened(false);
        },
        onPressedMarker: onPressedMarker,
        onSendTrackingData: onSendTrackingData,
        isTravelling: isTravelling
      }) : (0, _jsxRuntime.jsx)(_cardSimple.default, {
        section: section,
        terminal: Number(terminal),
        onPressedCollapse: function onPressedCollapse() {
          return setIsOpened(true);
        }
      })
    });
  };
  var _default = exports.default = FlightTransfer;
  var styles = _reactNative.StyleSheet.create({
    card: {
      borderRadius: 16,
      backgroundColor: _theme.color.palette.whiteGrey,
      rowGap: 16
    }
  });
