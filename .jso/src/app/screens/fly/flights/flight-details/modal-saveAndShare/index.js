  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _radio = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _button = _$$_REQUIRE(_dependencyMap[13]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[14]);
  var _travelOptions = _$$_REQUIRE(_dependencyMap[15]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var activeGradient = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var ModalSaveAndShare = function ModalSaveAndShare(props) {
    var visible = props.visible,
      onClosed = props.onClosed,
      onModalHide = props.onModalHide,
      selectedOption = props.selectedOption,
      onPress = props.onPress,
      loadingSaveFlight = props.loadingSaveFlight,
      flightDirection = props.flightDirection,
      savedFlightOnPress = props.savedFlightOnPress,
      onShareOnlyPress = props.onShareOnlyPress;
    var renderSelectOption = function renderSelectOption() {
      if (flightDirection === _flightProps.FlightDirection.departure) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: _travelOptions.TravelOption.iAmTravelling,
            isChecked: selectedOption === _travelOptions.TravelOption.iAmTravelling,
            onChecked: function onChecked() {
              return onPress(_travelOptions.TravelOption.iAmTravelling);
            },
            style: _styles.styles.travellingText,
            isLoadingToProcess: loadingSaveFlight
          }), (0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: _travelOptions.TravelOption.iAmPicking,
            isChecked: selectedOption === _travelOptions.TravelOption.iAmPicking,
            onChecked: function onChecked() {
              return onPress(_travelOptions.TravelOption.iAmPicking);
            },
            style: _styles.styles.pickingText,
            isLoadingToProcess: loadingSaveFlight
          })]
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: "dropdownSelectCard.imPickingSomeone",
            isChecked: selectedOption === _travelOptions.TravelOption.iAmPicking,
            onChecked: function onChecked() {
              return onPress(_travelOptions.TravelOption.iAmPicking);
            },
            style: _styles.styles.travellingText,
            isLoadingToProcess: loadingSaveFlight
          }), (0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: _travelOptions.TravelOption.iAmTravelling,
            isChecked: selectedOption === _travelOptions.TravelOption.iAmTravelling,
            onChecked: function onChecked() {
              return onPress(_travelOptions.TravelOption.iAmTravelling);
            },
            style: _styles.styles.pickingText,
            isLoadingToProcess: loadingSaveFlight
          })]
        });
      }
    };
    var renderTitle = function renderTitle(title) {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.wrapTitle,
        children: [(0, _jsxRuntime.jsx)(_icons.TickPurpleIcon, {}), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: title,
          preset: "caption1Regular",
          style: _styles.styles.title
        })]
      });
    };
    var renderInfo = function renderInfo() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.parentContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _styles.styles.containerCloseIcon,
          onPress: onClosed,
          children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {})
        }), (0, _jsxRuntime.jsx)(_baseImage.default, {
          source: _icons.FlightSave,
          style: _styles.styles.img
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetailV2.titleModalSaveAndShare",
          preset: "bodyTextBlack",
          style: _styles.styles.headerTitle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.containerTitle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.styles.containerTitleLeft,
            children: [renderTitle("flightDetailV2.saveFlightPopup.flightJourney"), renderTitle("flightDetailV2.saveFlightPopup.destinationWeather")]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.containerTitleRight,
            children: renderTitle("flightDetailV2.saveFlightPopup.destionationAirportDetails")
          })]
        })]
      });
    };
    var renderSelectYourProfileContent = function renderSelectYourProfileContent() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.containerSelectYourProfile,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.wrapSelectYourProfile,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.styles.wrapHeaderBottomSheet,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "dropdownSelectCard.selectYourProfile",
              preset: "h2",
              style: _styles.styles.selectYourProfile
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.saveFlightPopup.descSelectProfile",
            preset: "caption1Regular",
            style: _styles.styles.descriptionHeader
          }), renderSelectOption()]
        })
      });
    };
    var renderButtonSave = function renderButtonSave() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.bottomContainer,
        children: loadingSaveFlight ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.styles.loadingButtonStyles,
          children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
            source: _$$_REQUIRE(_dependencyMap[17]),
            autoPlay: true,
            loop: true,
            style: _styles.styles.lottieView
          })
        }) : (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: _styles.styles.buttonLinearStyle,
          start: {
            x: 0,
            y: 1
          },
          end: {
            x: 1,
            y: 0
          },
          colors: activeGradient,
          children: (0, _jsxRuntime.jsx)(_button.Button, {
            tx: "flightDetailV2.saveAndShare",
            sizePreset: "large",
            textPreset: "buttonLarge",
            typePreset: "secondary",
            statePreset: "default",
            backgroundPreset: "light",
            onPress: savedFlightOnPress,
            textStyle: _styles.styles.textButtonStyleas
          })
        })
      });
    };
    var renderSubButton = function renderSubButton() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.viewSubButton,
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onShareOnlyPress,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.shareOnly",
            style: _styles.styles.txtSubButton
          })
        })
      });
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: visible,
      onModalHide: onModalHide,
      onClosedSheet: onClosed,
      containerStyle: _styles.styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onClosed,
      animationInTiming: 500,
      animationOutTiming: 500,
      openPendingModal: true,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.modalContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [renderInfo(), renderSelectYourProfileContent()]
        }), renderButtonSave(), renderSubButton()]
      })
    });
  };
  var _default = exports.default = ModalSaveAndShare;
