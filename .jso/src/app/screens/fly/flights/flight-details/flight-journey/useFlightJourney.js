  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFlightJourney = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  var _flightInfo = _$$_REQUIRE(_dependencyMap[1]);
  var useFlightJourney = exports.useFlightJourney = function useFlightJourney(_ref) {
    var flightDetails = _ref.flightDetails,
      getMyTripData = _ref.getMyTripData;
    var flightJourneyStatus = (0, _react.useMemo)(function () {
      var _flightDetails$upcomi2;
      if (!flightDetails || !getMyTripData) {
        return _flightInfo.FlightJourneyEnumStatus.NOT_STARTED;
      }
      if ((flightDetails == null ? undefined : flightDetails.direction) === "ARR") {
        if (!getMyTripData.flightStatus) {
          return _flightInfo.FlightJourneyEnumStatus.NOT_STARTED;
        }
        if (getMyTripData.flightStatus === _flightInfo.FlightJourneyEnumStatus.NOT_STARTED) {
          return _flightInfo.FlightJourneyEnumStatus.NOT_STARTED;
        } else {
          var _flightDetails$upcomi;
          if (flightDetails != null && (_flightDetails$upcomi = flightDetails.upcomingStatusMapping) != null && _flightDetails$upcomi.toLowerCase().includes("landed")) {
            return _flightInfo.FlightJourneyEnumStatus.LANDED;
          }
          return _flightInfo.FlightJourneyEnumStatus.DEPARTED;
        }
      }
      // DEP
      if (!(flightDetails != null && (_flightDetails$upcomi2 = flightDetails.upcomingStatusMapping) != null && _flightDetails$upcomi2.toLowerCase().includes("departed"))) {
        return _flightInfo.FlightJourneyEnumStatus.NOT_STARTED;
      }
      if (!getMyTripData.flightStatus) {
        return _flightInfo.FlightJourneyEnumStatus.NOT_STARTED;
      }
      if (getMyTripData.flightStatus === _flightInfo.FlightJourneyEnumStatus.LANDED) {
        return _flightInfo.FlightJourneyEnumStatus.LANDED;
      }
      return _flightInfo.FlightJourneyEnumStatus.DEPARTED;
    }, [flightDetails, getMyTripData]);
    return {
      flightJourneyStatus: flightJourneyStatus
    };
  };
