  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var SCREEN_NAME = "FlightJourney";
  var PopupJourneyError = function PopupJourneyError(_ref) {
    var visible = _ref.visible,
      onPressClosePopup = _ref.onPressClosePopup,
      description = _ref.description,
      titleBtn = _ref.titleBtn,
      errorType = _ref.errorType,
      _ref$headerHeight = _ref.headerHeight,
      headerHeight = _ref$headerHeight === undefined ? 0 : _ref$headerHeight;
    if (!visible) return null;
    var window = _reactNative2.Dimensions.get("window");
    var containerWidth = errorType === "unableToLoad" ? window.width - 64 : window.width - 114;
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.overlay,
      testID: `${SCREEN_NAME}__PopupJourneyErrorOverlay`,
      accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorOverlay`,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.backdrop,
        pointerEvents: "none"
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [styles.container, {
          width: containerWidth,
          marginTop: -headerHeight / 2
        }],
        testID: `${SCREEN_NAME}__PopupJourneyErrorContainer`,
        accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorContainer`,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightTracker, {
          testID: `${SCREEN_NAME}__PopupJourneyErrorIcon`,
          accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorIcon`
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.descriptionContainer,
          testID: `${SCREEN_NAME}__PopupJourneyErrorDescriptionContainer`,
          accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorDescriptionContainer`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.contentTextStyles,
            numberOfLines: 3,
            text: description,
            preset: "caption1Regular",
            testID: `${SCREEN_NAME}__PopupJourneyErrorDescription`,
            accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorDescription`
          })
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.bottomContainer,
          testID: `${SCREEN_NAME}__PopupJourneyErrorBottomContainer`,
          accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorBottomContainer`,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonStyle,
            onPress: onPressClosePopup,
            testID: `${SCREEN_NAME}__PopupJourneyErrorButton`,
            accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorButton`,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.btnStyle,
              preset: "caption1Bold",
              text: titleBtn,
              testID: `${SCREEN_NAME}__PopupJourneyErrorButtonText`,
              accessibilityLabel: `${SCREEN_NAME}__PopupJourneyErrorButtonText`
            })
          })
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    overlay: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1000,
      justifyContent: "center",
      alignItems: "center"
    },
    backdrop: Object.assign({}, _reactNative2.StyleSheet.absoluteFillObject, {
      backgroundColor: "#eee",
      opacity: 0.1
    }),
    bottomContainer: {
      justifyContent: "center",
      alignItems: "center",
      marginTop: 12
    },
    buttonStyle: {
      paddingVertical: 5,
      paddingHorizontal: 10,
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 60,
      borderWidth: 1,
      borderColor: "white"
    },
    container: {
      backgroundColor: _theme.color.palette.almostBlackGrey,
      borderRadius: 8,
      padding: 16,
      alignItems: "center",
      overflow: "hidden"
    },
    contentTextStyles: {
      color: _theme.color.palette.whiteGrey,
      textAlign: "center"
    },
    descriptionContainer: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: 7
    },
    btnStyle: {
      color: _theme.color.palette.whiteGrey
    }
  });
  var _default = exports.default = PopupJourneyError;
