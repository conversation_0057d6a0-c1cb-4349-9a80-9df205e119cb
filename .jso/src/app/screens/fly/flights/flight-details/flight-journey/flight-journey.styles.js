  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: "#5D5145",
      width: screenWidth,
      height: "100%"
    },
    headerContainer: {
      alignItems: "center",
      marginTop: 8,
      marginBottom: 32
    },
    headerContainerFullScreen: {
      paddingBottom: 16
    },
    viewTopHeader: {
      width: 40,
      height: 4,
      borderRadius: 24,
      backgroundColor: _theme.color.palette.almostWhite30
    },
    headerTitle: {
      color: "white",
      marginBottom: 18,
      marginTop: 12
    },
    headerTitleFullScreen: {
      color: "white",
      marginBottom: 5,
      textAlign: "center"
    },
    headerSubtitle: Object.assign({}, _text.newPresets.caption1Regular, {
      color: "white"
    }),
    containerCloseBtn: {
      position: "absolute",
      left: 0,
      zIndex: 10
    },
    closeBtnIcon: {
      paddingLeft: 16,
      paddingRight: 35,
      paddingBottom: 30,
      paddingTop: 18
    },
    airlineTitleLoading: {
      width: 204,
      height: 16,
      borderRadius: 4,
      alignSelf: "center"
    },
    containerContentLoading: {
      flex: 1,
      backgroundColor: _theme.color.palette.almostBlackGrey
    },
    containerContent: {
      flex: 1,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    map: {
      width: "100%",
      height: "100%"
    },
    airlineTitle: {
      color: _theme.color.palette.greyCCCCCC,
      textAlign: "center"
    },
    headerTitleLoading: {
      width: 115,
      height: 16,
      borderRadius: 4,
      alignSelf: "center",
      marginBottom: 18,
      marginTop: 12
    },
    markerContainer: {
      alignItems: "center"
    },
    markerLabelContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 6,
      paddingVertical: 7,
      paddingHorizontal: 10,
      borderWidth: 1,
      borderColor: _theme.color.palette.lightGrey,
      flexDirection: "row",
      marginVertical: 4,
      shadowColor: _theme.color.palette.lightPurple,
      shadowOffset: {
        width: 0,
        height: 4
      },
      shadowOpacity: 0.6,
      shadowRadius: 4,
      elevation: 8,
      // For Android
      alignItems: "center",
      justifyContent: "center"
    },
    markerLabel: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center",
      marginLeft: 2
    },
    containerCircle: {
      width: 14,
      height: 14,
      borderRadius: 7,
      backgroundColor: _theme.color.palette.whiteGrey,
      alignItems: "center",
      justifyContent: "center",
      position: "absolute",
      top: "50%",
      left: "50%",
      transform: [{
        translateX: -7
      }, {
        translateY: -7
      }]
    },
    circle: {
      width: 9,
      height: 9,
      borderRadius: 4.5,
      backgroundColor: _theme.color.palette.lightPurple
    },
    containerFlightMark: {
      shadowColor: _theme.color.palette.lightPurple,
      shadowOffset: {
        width: 0,
        height: 4
      },
      shadowOpacity: 0.6,
      shadowRadius: 4,
      elevation: 8 // For Android
    },
    statusHeader: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.whiteGrey
    }),
    statusDescription: Object.assign({}, _text.newPresets.caption1Regular, {
      color: _theme.color.palette.whiteGrey,
      marginTop: 2
    })
  });
