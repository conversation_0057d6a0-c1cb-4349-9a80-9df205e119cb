  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightStatusTagV2 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _flightStatusTagV = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var FlightStatusTagV2 = exports.FlightStatusTagV2 = function FlightStatusTagV2(_ref) {
    var _statusMapping$status;
    var statusMapping = _ref.statusMapping;
    var statusTag = statusMapping == null ? undefined : statusMapping.details_status_en;
    var beltStatusMapping = statusMapping == null ? undefined : statusMapping.belt_status_en;
    var statusColor = statusMapping == null || (_statusMapping$status = statusMapping.status_text_color) == null ? undefined : _statusMapping$status.toLowerCase();
    var status = statusTag == null ? undefined : statusTag.toLowerCase();
    if (!(0, _lodash.isEmpty)(beltStatusMapping)) {
      if (["grey", "black"].includes(statusColor)) {
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [_flightStatusTagV.styles.container, _flightStatusTagV.styles.blackBackgroundStyle],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallBold",
            style: Object.assign({}, _flightStatusTagV.styles.blackStatusText, {
              color: statusColor
            }),
            children: beltStatusMapping
          })
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: [_flightStatusTagV.styles.container, _flightStatusTagV.styles.greenBackgroundStyle],
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "XSmallBold",
          style: Object.assign({}, _flightStatusTagV.styles.greenStatusText, {
            color: statusColor
          }),
          children: beltStatusMapping
        })
      });
    }
    switch (true) {
      case /gate open/gim.test(status):
      case /boarding/gim.test(status):
      case /landed/gim.test(status):
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [_flightStatusTagV.styles.container, _flightStatusTagV.styles.greenBackgroundStyle],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallBold",
            style: Object.assign({}, _flightStatusTagV.styles.greenStatusText, {
              color: statusColor
            }),
            children: statusTag
          })
        });
      case /gate closed/gim.test(status):
      case /last call/gim.test(status):
      case /gate closing/gim.test(status):
      case /cancelled/gim.test(status):
      case /re-timed/gim.test(status):
      case /delayed/gim.test(status):
      case /diverted/gim.test(status):
      case /new gate/gim.test(status):
      case /go to info counter/gim.test(status):
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [_flightStatusTagV.styles.container, _flightStatusTagV.styles.redBackgroundStyle],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallBold",
            style: Object.assign({}, _flightStatusTagV.styles.redStatusText, {
              color: statusColor
            }),
            children: statusTag
          })
        });
      default:
        return (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: [_flightStatusTagV.styles.container, _flightStatusTagV.styles.blackBackgroundStyle],
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "XSmallBold",
            style: Object.assign({}, _flightStatusTagV.styles.blackStatusText, {
              color: statusColor
            }),
            children: statusTag
          })
        });
    }
  };
