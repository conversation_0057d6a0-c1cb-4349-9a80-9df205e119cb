  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 99
    },
    greenBackgroundStyle: {
      backgroundColor: _theme.color.palette.lightestGreen
    },
    blackBackgroundStyle: {
      backgroundColor: _theme.color.palette.lighterGrey
    },
    redBackgroundStyle: {
      backgroundColor: _theme.color.palette.lightestRed
    },
    greenStatusText: {
      paddingBottom: 1,
      color: _theme.color.palette.basegreen,
      textTransform: 'none'
    },
    blackStatusText: {
      paddingBottom: 1,
      color: _theme.color.palette.almostBlackGrey,
      textTransform: 'none'
    },
    redStatusText: {
      paddingBottom: 1,
      color: _theme.color.palette.baseRed,
      textTransform: 'none'
    }
  });
