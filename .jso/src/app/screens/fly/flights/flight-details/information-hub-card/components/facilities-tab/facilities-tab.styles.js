  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1
    },
    textAlmostBackColor: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    },
    descAllTerminalLabel: {
      marginTop: 8,
      color: _theme.color.palette.darkestGrey,
      textAlign: "left"
    },
    containerTitle: {
      paddingHorizontal: 16,
      marginBottom: 8
    },
    contentContainerStyle: {
      marginHorizontal: 16,
      paddingBottom: 20
    },
    flatListStyle: {
      width: "100%"
    },
    styleBottomListButton: {
      borderRadius: 60,
      borderWidth: 2,
      borderColor: _theme.color.palette.lightPurple
    },
    textBottomListButton: Object.assign({}, _text.presets.tabsSmall, {
      color: _theme.color.palette.lightPurple
    }),
    footerComponentStyle: {
      marginTop: 24
    }
  });
