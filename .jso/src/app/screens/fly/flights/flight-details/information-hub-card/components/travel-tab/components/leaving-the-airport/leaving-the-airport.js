  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LeavingTheAirport = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _leavingTheAirport = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _gttdUtils = _$$_REQUIRE(_dependencyMap[8]);
  var _adobe = _$$_REQUIRE(_dependencyMap[9]);
  var _native = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _icons = _$$_REQUIRE(_dependencyMap[13]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[14]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _flyTileCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[18]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[19]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var TWO_HOURS = 120; // 2 hours in minutes

  var handleScheduledDateForAA = function handleScheduledDateForAA(statusMapping, displayTimestamp, scheduledDate) {
    var date = scheduledDate;
    if ((0, _utils.handleCondition)((statusMapping == null ? undefined : statusMapping.details_status_en) && ((statusMapping == null ? undefined : statusMapping.details_status_en.includes("Re-timed")) || (statusMapping == null ? undefined : statusMapping.details_status_en.includes("Delayed"))), true, false)) {
      var _displayTimestamp$spl;
      date = (0, _utils.handleCondition)(displayTimestamp, displayTimestamp == null || (_displayTimestamp$spl = displayTimestamp.splitranslate(" ")) == null ? undefined : _displayTimestamp$spl[0], scheduledDate);
    }
    return date;
  };
  var LeavingTheAirport = exports.LeavingTheAirport = function LeavingTheAirport(_ref) {
    var _flyFlightDetailsPayl, _flyFlightDetailsPayl2;
    var flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      isTravelling = _ref.isTravelling,
      title = _ref.title,
      hubTabTitle = _ref.hubTabTitle,
      onSendTrackingData = _ref.onSendTrackingData;
    var navigation = (0, _native.useNavigation)();
    var _ref2 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || "",
      groundTransport = _ref2.groundTransport;
    var terminal = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.displayTerminal;
    var displayTimestamp = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl2.displayTimestamp;
    var isMoreThan2HoursBeforeDisplay = (0, _react.useMemo)(function () {
      var currentSGTTimestamp = (0, _dateTime.getCurrentTimeSingapore)();
      var diffTime = (0, _moment.default)(currentSGTTimestamp, _dateTime.DateFormats["YearMonthDayTime"]).diff((0, _moment.default)(displayTimestamp, _dateTime.DateFormats["YearMonthDayTime"]), "minutes");
      return diffTime > TWO_HOURS;
    }, [displayTimestamp]);
    var onPressGTTDOptions = function onPressGTTDOptions() {
      var _flyFlightDetailsPayl3, _flyFlightDetailsPayl4, _flyFlightDetailsPayl5;
      var _ref3 = (flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData) || {},
        flightNumber = _ref3.flightNumber;
      var scheduledDate = handleScheduledDateForAA(flyFlightDetailsPayload == null || (_flyFlightDetailsPayl3 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl3.statusMapping, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl4 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl4.displayTimestamp, flyFlightDetailsPayload == null || (_flyFlightDetailsPayl5 = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl5.scheduledDate);
      var name = groundTransport == null ? undefined : groundTransport.name;
      var url = groundTransport == null ? undefined : groundTransport.url;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppFlyFlightDetailFlightCardLinks, `${name}|${flightNumber}|${scheduledDate}`));
      // @ts-ignore
      navigation.navigate(_constants.NavigationConstants.webview, {
        uri: url
      });
    };
    var renderGroundTransport = function renderGroundTransport(props) {
      var name = props.name,
        subText2 = props.subText2,
        price = props.price,
        time = props.time,
        transportType = props.transportType;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _leavingTheAirport.styles.wrapGTTDCard,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "flightDetails.shortestWaitTimeNow",
          preset: "caption1Bold",
          style: _leavingTheAirport.styles.textTitleGTTDStyle
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _leavingTheAirport.styles.wrapContentGTTDCardStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _leavingTheAirport.styles.nameAndSubTextSectionStyle,
            children: [(0, _gttdUtils.handleGTTDIcon)(transportType), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                text: name,
                preset: "bodyTextBold",
                style: _leavingTheAirport.styles.textNameGTTDStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: subText2,
                preset: "XSmallBold",
                style: _leavingTheAirport.styles.textSubText2GTTDStyle
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _leavingTheAirport.styles.priceAndTimeSectionStyle,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: price,
              preset: "caption1Regular",
              style: _leavingTheAirport.styles.textPriceGTTDStyle
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: time,
              preset: "caption1Regular",
              style: _leavingTheAirport.styles.textTimeGTTDStyle
            })]
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: _leavingTheAirport.styles.containerMoreOptions,
          onPress: onPressGTTDOptions,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _leavingTheAirport.styles.containerLeftMoreOptions,
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flightDetails.moreOptions",
                preset: "bodyTextBold",
                style: _leavingTheAirport.styles.textTouchableGTTDStyle
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                tx: "flightDetails.subTextMoreOptions",
                preset: "caption2Regular",
                style: _leavingTheAirport.styles.textTouchableGTTDStyle
              })]
            })
          }), (0, _jsxRuntime.jsx)(_icons.CaretRight, {})]
        })]
      });
    };
    var renderDefaultGTTDCard = function renderDefaultGTTDCard() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _leavingTheAirport.styles.containerDefaultLeavingTheAirport,
        children: (0, _jsxRuntime.jsx)(_flyTileCard.default, {
          icon: (0, _jsxRuntime.jsx)(_icons.FlightCarConcierge, {}),
          title: (0, _i18n.translate)("flightDetails.transportForLargerGroup"),
          desc: (0, _i18n.translate)("flightDetails.bookLargerVehiclesToSuiltYourNeeds"),
          navigationTiles: {
            type: _menuOption.NavigationType.external,
            value: "https://www.cityshuttle.com.sg"
          },
          tileTag: _flightDetail.SectionTileTagNameEnum.LONG_DESIGN,
          onSendTrackingData: onSendTrackingData
        })
      });
    };
    var renderTransportServices = function renderTransportServices() {
      if (isTravelling) {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _leavingTheAirport.styles.transportServicesRow,
            children: [(0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: (0, _jsxRuntime.jsx)(_icons.FlightCar, {}),
              title: (0, _i18n.translate)("flightDetails.carRental"),
              navigationTiles: {
                type: _menuOption.NavigationType.external,
                value: "https://www.changiairport.com/en/at-changi/facilities-and-services-directory/car-rental.html?src=app"
              },
              tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
              onSendTrackingData: onSendTrackingData
            }), (0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: (0, _jsxRuntime.jsx)(_icons.FlightBus, {}),
              title: (0, _i18n.translate)("flightDetails.busTrainTaxi"),
              navigationTiles: {
                type: _menuOption.NavigationType.external,
                value: "https://changiapp-aem.changiairport.com/content/ichangi/en/airport/leaving-airport.html"
              },
              tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
              onSendTrackingData: onSendTrackingData
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _leavingTheAirport.styles.transportServicesRow,
            children: [(0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: (0, _jsxRuntime.jsx)(_icons.FlightCoach, {}),
              title: (0, _i18n.translate)("flightDetails.shuttleServices"),
              navigationTiles: {
                type: _menuOption.NavigationType.external,
                value: "https://changiapp-aem.changiairport.com/content/ichangi/en/airport/shuttle-services.html"
              },
              tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
              onSendTrackingData: onSendTrackingData
            }), (0, _jsxRuntime.jsx)(_flyTileCard.default, {
              icon: (0, _jsxRuntime.jsx)(_icons.FlightCoach, {}),
              title: (0, _i18n.translate)("flightDetails.coachToJohorBahru"),
              navigationTiles: {
                type: _menuOption.NavigationType.external,
                value: "https://changiapp-aem.changiairport.com/content/ichangi/en/airport/coach-to-johor.html"
              },
              tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
              onSendTrackingData: onSendTrackingData
            })]
          })]
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _leavingTheAirport.styles.containerDefaultLeavingTheAirport,
          children: (0, _jsxRuntime.jsx)(_flyTileCard.default, {
            icon: (0, _jsxRuntime.jsx)(_icons.FlightCar, {}),
            title: (0, _i18n.translate)("flightDetails.car"),
            desc: (0, _i18n.translate)("flightDetails.carDesc"),
            navigationTiles: {
              type: _menuOption.NavigationType.inapp,
              value: "carPark"
            },
            tileTag: _flightDetail.SectionTileTagNameEnum.LONG_DESIGN,
            onSendTrackingData: onSendTrackingData
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _leavingTheAirport.styles.transportServicesRow,
          children: [(0, _jsxRuntime.jsx)(_flyTileCard.default, {
            icon: (0, _jsxRuntime.jsx)(_icons.FlightBus, {}),
            title: (0, _i18n.translate)("flightDetails.busTrainTaxi"),
            navigationTiles: {
              type: _menuOption.NavigationType.external,
              value: "https://changiapp-aem.changiairport.com/content/ichangi/en/airport/leaving-airport.html"
            },
            tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
            onSendTrackingData: onSendTrackingData
          }), (0, _jsxRuntime.jsx)(_flyTileCard.default, {
            icon: (0, _jsxRuntime.jsx)(_icons.FlightCoach, {}),
            title: (0, _i18n.translate)("flightDetails.shuttleServices"),
            navigationTiles: {
              type: _menuOption.NavigationType.external,
              value: "https://changiapp-aem.changiairport.com/content/ichangi/en/airport/shuttle-services.html"
            },
            tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
            onSendTrackingData: onSendTrackingData
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _leavingTheAirport.styles.transportServicesRow,
          children: [(0, _jsxRuntime.jsx)(_flyTileCard.default, {
            icon: (0, _jsxRuntime.jsx)(_icons.FlightCoach, {}),
            title: (0, _i18n.translate)("flightDetails.coachToJohorBahru"),
            navigationTiles: {
              type: _menuOption.NavigationType.external,
              value: "https://changiapp-aem.changiairport.com/content/ichangi/en/airport/coach-to-johor.html"
            },
            tileTag: _flightDetail.SectionTileTagNameEnum.SHORT_DESIGN,
            onSendTrackingData: onSendTrackingData
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _leavingTheAirport.styles.containerTransportCard
          })]
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _leavingTheAirport.styles.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          text: title || "",
          preset: "caption1Bold",
          numberOfLines: 1,
          style: _leavingTheAirport.styles.textAlmostBackColor
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [Number(terminal) !== 1 || (0, _lodash.isEmpty)(groundTransport) || isMoreThan2HoursBeforeDisplay ? renderDefaultGTTDCard() : renderGroundTransport(groundTransport), renderTransportServices()]
      })]
    });
  };
