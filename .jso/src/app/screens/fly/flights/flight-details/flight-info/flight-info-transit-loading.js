  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _timelineHeaderItem = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var lighterGreyLoadingColors = [_theme.color.palette.backgroundStartFlightInfoV2, _theme.color.palette.backgroundEndFlightInfoV2, _theme.color.palette.backgroundStartFlightInfoV2];
  var FlightInfoTransitLoading = function FlightInfoTransitLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.componentTop
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.componentBottom
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      marginTop: _timelineHeaderItem.GAP_10 + _timelineHeaderItem.GAP_8
    },
    componentTop: {
      width: 160,
      height: 20,
      borderRadius: 4
    },
    componentBottom: {
      width: 204,
      height: 16,
      borderRadius: 4,
      marginTop: 4
    }
  });
  var _default = exports.default = FlightInfoTransitLoading;
