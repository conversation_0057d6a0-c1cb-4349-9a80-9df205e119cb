  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FlightInfo = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _flightInfo = _$$_REQUIRE(_dependencyMap[4]);
  var _flightLocationPlace = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _flightFastCheckin = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _utils = _$$_REQUIRE(_dependencyMap[7]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[8]);
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[9]);
  var _flightCheckinInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _flightPickupDropoff = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _flightCarpark = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _useFlightInfoV2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _flightInfoLoadingDep = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _flightInfoLoadingArr = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _flightInfoHeaderLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _flightInfoTitleLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _flightInfoTransitLoading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _flightInfo2 = _$$_REQUIRE(_dependencyMap[19]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _text = _$$_REQUIRE(_dependencyMap[21]);
  var _i18n = _$$_REQUIRE(_dependencyMap[22]);
  var _flightStatusTagV = _$$_REQUIRE(_dependencyMap[23]);
  var _lodash = _$$_REQUIRE(_dependencyMap[24]);
  var _flightDetailV2Helper = _$$_REQUIRE(_dependencyMap[25]);
  var _timelineHeaderItem = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[26]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[27]);
  var _blurView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _flightTransitError = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var maxSharecodeFlightNumber = 12;
  var FlightInfo = exports.FlightInfo = function FlightInfo(_ref) {
    var _flyFlightDetailsPayl, _getMyTripData$transi, _getMyTripData$transi2, _getMyTripData$transi3, _getMyTripData$transi4, _getMyTripData$transi5, _getMyTripData$transi6, _getMyTripData$transi7, _getMyTripData$transi8;
    var flyFlightDetailsPayload = _ref.flyFlightDetailsPayload,
      flyLastUpdatedTimeStamp = _ref.flyLastUpdatedTimeStamp,
      shouldShowSQArrivalTerminalInfo = _ref.shouldShowSQArrivalTerminalInfo,
      inf22 = _ref.inf22,
      handleMap = _ref.handleMap,
      isLoadingDetailFlight = _ref.isLoadingDetailFlight,
      intoCityOrAirportPayload = _ref.intoCityOrAirportPayload,
      marginTop = _ref.marginTop,
      isSaved = _ref.isSaved,
      onSaveFlight = _ref.onSaveFlight,
      getMyTripData = _ref.getMyTripData,
      isLoadingGetMyTripData = _ref.isLoadingGetMyTripData,
      isErrorGetMyTrip = _ref.isErrorGetMyTrip,
      onRetryGetMyTripData = _ref.onRetryGetMyTripData,
      directionFromParams = _ref.directionFromParams;
    var travelInfo = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.heroImageData) == null ? undefined : _flyFlightDetailsPayl.travelInfo;
    var flightDetailsData = flyFlightDetailsPayload == null ? undefined : flyFlightDetailsPayload.flightDetailsData;
    var _ref2 = flightDetailsData || {},
      direction = _ref2.direction,
      airlineDetails = _ref2.airlineDetails,
      flightNumber = _ref2.flightNumber,
      slaves = _ref2.slaves,
      statusMapping = _ref2.statusMapping,
      scheduledTime = _ref2.scheduledTime,
      originDepDate = _ref2.originDepDate,
      airport = _ref2.airport,
      airportDetails = _ref2.airportDetails,
      via = _ref2.via,
      viaAirportDetails = _ref2.viaAirportDetails;
    var formatOriginDepDate = (0, _utils.handleCondition)(originDepDate, (0, _dateTime.toDate)(originDepDate, _dateTime.DateFormats.DayMonthYear), undefined);
    var _useFlightInfoV = (0, _useFlightInfoV2.default)({
        flightDetailsData: flightDetailsData
      }),
      processFlightTime = _useFlightInfoV.processFlightTime,
      processFlightDate = _useFlightInfoV.processFlightDate,
      checkFlightCanSave = _useFlightInfoV.checkFlightCanSave,
      processFlightForeignTransitDateTime = _useFlightInfoV.processFlightForeignTransitDateTime;
    var hasFastCheckIn = !!(airlineDetails != null && airlineDetails.eligible_fast_checkin);
    var transitsData = (0, _utils.handleCondition)(!!via && !!viaAirportDetails, [{
      id: via,
      code: via,
      fullName: viaAirportDetails == null ? undefined : viaAirportDetails.name
    }], null);
    var _processFlightTime = processFlightTime(),
      reTimeFlag = _processFlightTime.reTimeFlag,
      numberDaysDiff = _processFlightTime.numberDaysDiff,
      mainTime = _processFlightTime.mainTime;
    var _processFlightDate = processFlightDate(),
      flightScheduledDate = _processFlightDate.flightScheduledDate;
    var heightEndDEPRef = (0, _react.useRef)(0);
    var heightStartARRRef = (0, _react.useRef)(0);
    var foreignARRDateTime = processFlightForeignTransitDateTime(getMyTripData == null ? undefined : getMyTripData.scheduledDepartureLocalDate, (getMyTripData == null ? undefined : getMyTripData.actualDepartureLocalDate) || (getMyTripData == null ? undefined : getMyTripData.estimatedDepartureLocalDate) || (getMyTripData == null ? undefined : getMyTripData.scheduledDepartureLocalDate), isSaved);
    var transitARRDateTime = processFlightForeignTransitDateTime(getMyTripData == null || (_getMyTripData$transi = getMyTripData.transit) == null ? undefined : _getMyTripData$transi.scheduledDepartureLocalDate, (getMyTripData == null || (_getMyTripData$transi2 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi2.actualDepartureLocalDate) || (getMyTripData == null || (_getMyTripData$transi3 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi3.estimatedDepartureLocalDate) || (getMyTripData == null || (_getMyTripData$transi4 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi4.scheduledDepartureLocalDate), isSaved);
    var foreignDEPDateTime = processFlightForeignTransitDateTime(getMyTripData == null ? undefined : getMyTripData.scheduledArrivalLocalDate, (getMyTripData == null ? undefined : getMyTripData.actualArrivalLocalDate) || (getMyTripData == null ? undefined : getMyTripData.estimatedArrivalLocalDate) || (getMyTripData == null ? undefined : getMyTripData.scheduledArrivalLocalDate), isSaved);
    var transitDEPDateTime = processFlightForeignTransitDateTime(getMyTripData == null || (_getMyTripData$transi5 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi5.scheduledArrivalLocalDate, (getMyTripData == null || (_getMyTripData$transi6 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi6.actualArrivalLocalDate) || (getMyTripData == null || (_getMyTripData$transi7 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi7.estimatedArrivalLocalDate) || (getMyTripData == null || (_getMyTripData$transi8 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi8.scheduledArrivalLocalDate), isSaved);
    var isHasTransit = (0, _react.useMemo)(function () {
      return !!(transitsData != null && transitsData.length);
    }, [transitsData == null ? undefined : transitsData.length]);
    var isHasTransitData = (0, _react.useMemo)(function () {
      return isHasTransit && !!foreignDEPDateTime;
    }, [isHasTransit, foreignDEPDateTime]);
    var isHasTransitDataAndDestinationEmpty = (0, _react.useMemo)(function () {
      return isHasTransitData && ((transitsData == null ? undefined : transitsData.length) > 0 ? !transitDEPDateTime : !foreignDEPDateTime);
    }, [isHasTransitData, transitsData == null ? undefined : transitsData.length, transitDEPDateTime, foreignDEPDateTime]);
    var onPressSaveFlight = function onPressSaveFlight() {
      var priorActionLabel = "Show Detail";
      onSaveFlight == null || onSaveFlight(priorActionLabel);
    };
    var renderShowButtonDetails = function renderShowButtonDetails() {
      if (!isSaved && !!checkFlightCanSave()) {
        return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressSaveFlight,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "flightDetailV2.flightInfo.showDetails",
            style: _flightInfo2.styles.buttonShowDetails
          })
        });
      }
      return null;
    };
    var renderShowInfoFlightDEP = function renderShowInfoFlightDEP(_ref3) {
      var terminal = _ref3.terminal,
        gate = _ref3.gate,
        baggageClaim = _ref3.baggageClaim,
        updateFields = _ref3.updateFields;
      if (!isSaved) return null;
      var flightInfo = [{
        label: "Terminal",
        value: terminal,
        key: "arrivalTerminal"
      }, {
        label: "Gate",
        value: gate,
        key: "arrivalGate"
      }, {
        label: "Baggage Belt",
        value: baggageClaim,
        key: "arrivalBaggageClaim"
      }];
      var getStyledText = function getStyledText(label, value, key) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          testID: `${key}_text_DEP`,
          accessibilityLabel: `${key}_text_DEP`,
          style: [_text.newPresets.caption2Bold, _flightInfo2.styles.transitText],
          text: `${label} ${value || '-'}`
        });
      };
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightInfo2.styles.foreignTransitDetails,
        children: flightInfo.map(function (_ref4) {
          var label = _ref4.label,
            value = _ref4.value,
            key = _ref4.key;
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: getStyledText(label, value, key)
          }, key);
        })
      });
    };
    var renderShowInfoFlightARR = function renderShowInfoFlightARR(_ref5) {
      var terminal = _ref5.terminal,
        gate = _ref5.gate,
        checkinDesk = _ref5.checkinDesk,
        updateFields = _ref5.updateFields;
      if (!isSaved) return null;
      var flightInfo = [{
        label: "Terminal",
        value: terminal,
        key: "departureTerminal"
      }, {
        label: "Check-In Row",
        value: checkinDesk,
        key: "departureCheckInDesk"
      }, {
        label: "Gate",
        value: gate,
        key: "departureGate"
      }];
      var getStyledText = function getStyledText(label, value, key) {
        return (0, _jsxRuntime.jsx)(_text.Text, {
          style: [_text.newPresets.caption2Bold, _flightInfo2.styles.transitText],
          text: `${label} ${value || '-'}`
        });
      };
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightInfo2.styles.foreignTransitDetails,
        children: flightInfo.map(function (_ref6) {
          var label = _ref6.label,
            value = _ref6.value,
            key = _ref6.key;
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            children: getStyledText(label, value, key)
          }, key);
        })
      });
    };
    var renderSlaves = function renderSlaves(slaves) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightInfo2.styles.containerSlavesTextStyle,
        children: !!(slaves != null && slaves.length) && (0, _jsxRuntime.jsx)(_text.Text, {
          style: _flightInfo2.styles.slavesTextStyle,
          preset: "caption2Bold",
          children: slaves == null ? undefined : slaves.slice(0, maxSharecodeFlightNumber).join(", ")
        })
      });
    };
    var renderHeader = function renderHeader() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_flightInfo2.styles.containerHeader, {
          marginTop: marginTop
        }],
        children: isLoadingDetailFlight ? (0, _jsxRuntime.jsx)(_flightInfoHeaderLoading.default, {}) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _flightInfo2.styles.containerFlightNumber,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightInfo2.styles.wrapImageStyle,
              children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                source: {
                  uri: airlineDetails == null ? undefined : airlineDetails.logo_url
                },
                style: _flightInfo2.styles.imageStyle
              })
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _flightInfo2.styles.mainFlightCodeText,
              preset: "h4",
              text: flightNumber
            })]
          }), renderSlaves(slaves), shouldShowSQArrivalTerminalInfo && (0, _jsxRuntime.jsx)(_text.Text, {
            text: inf22 == null ? undefined : inf22.informativeText,
            style: _flightInfo2.styles.informativeTextStyle,
            preset: "XSmallBold"
          })]
        })
      });
    };
    var contentTimeLeft = function contentTimeLeft() {
      var _flightDetailsData$up;
      var statusNeedIgnore = ["cancelled", "go to info counter", "diverted"];
      if (statusNeedIgnore != null && statusNeedIgnore.includes(flightDetailsData == null || (_flightDetailsData$up = flightDetailsData.upcomingStatusMapping) == null ? undefined : _flightDetailsData$up.toLowerCase())) {
        return null;
      }
      var timeLeft = (0, _flightDetailV2Helper.handleTimeLeft)(flightDetailsData);
      return !(0, _lodash.isEmpty)(timeLeft) && (0, _jsxRuntime.jsx)(_text.Text, {
        text: timeLeft,
        preset: "caption1Black",
        numberOfLines: 1,
        style: _flightInfo2.styles.contentTimeLeft
      });
    };
    var renderFlightInfoTitle = function renderFlightInfoTitle() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _flightInfo2.styles.containerFlightInfoTitle,
        children: isLoadingDetailFlight ? (0, _jsxRuntime.jsx)(_flightInfoTitleLoading.default, {}) : (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [travelInfo && (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _flightInfo2.styles.flightNumberText,
              preset: "caption2Bold",
              text: (0, _i18n.translate)("flightDetails.flightInfo.updatedSystemTime") + " " + flyLastUpdatedTimeStamp
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _flightInfo2.styles.flightNumberText,
              preset: "h6",
              text: (0, _i18n.translate)("flightDetails.flightInfo.cityTitle", {
                dep: travelInfo[0].city,
                arr: travelInfo[travelInfo.length - 1].city
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _flightInfo2.styles.containerFlightStatusTag,
            children: [isSaved && contentTimeLeft(), (0, _utils.handleCondition)(!(0, _lodash.isEmpty)(statusMapping == null ? undefined : statusMapping.details_status_en), (0, _jsxRuntime.jsx)(_flightStatusTagV.FlightStatusTagV2, {
              statusMapping: statusMapping
            }), null)]
          })]
        })
      });
    };
    var renderTimelineHeaderItemARR = function renderTimelineHeaderItemARR() {
      if (!isSaved) {
        return (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
          height: isSaved ? (0, _utils.handleCondition)(transitsData == null ? undefined : transitsData.length, _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT + _timelineHeaderItem.GAP_8, _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT - _timelineHeaderItem.GAP_8) : _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT + _timelineHeaderItem.GAP_8,
          type: _flightInfo.TypeTimelineHeader.ArrDateTimeEnd,
          flightScheduledDate: formatOriginDepDate
        });
      }
      return (0, _utils.handleCondition)(!!foreignARRDateTime, (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
        height: (0, _utils.handleCondition)(transitsData == null ? undefined : transitsData.length, _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT, _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT - _timelineHeaderItem.GAP_8),
        type: _flightInfo.TypeTimelineHeader.ArrDateTimeEnd,
        flightScheduledDate: foreignARRDateTime == null ? undefined : foreignARRDateTime.scheduledDate,
        mainTime: foreignARRDateTime == null ? undefined : foreignARRDateTime.mainTime,
        scheduledTime: foreignARRDateTime == null ? undefined : foreignARRDateTime.scheduledTime,
        reTimeFlag: foreignARRDateTime == null ? undefined : foreignARRDateTime.reTimeFlag,
        numberDaysDiff: foreignARRDateTime == null ? undefined : foreignARRDateTime.numberDaysDiff,
        gapEnd: (0, _utils.handleCondition)(foreignARRDateTime == null ? undefined : foreignARRDateTime.reTimeFlag, _timelineHeaderItem.GAP_8 * 4, _timelineHeaderItem.GAP_8 * 2)
      }), (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
        height: _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT + _timelineHeaderItem.GAP_8,
        type: _flightInfo.TypeTimelineHeader.ArrDateTimeEnd,
        flightScheduledDate: formatOriginDepDate
      }));
    };
    var renderFlightInfoARR = function renderFlightInfoARR() {
      if (isLoadingDetailFlight) return (0, _jsxRuntime.jsx)(_flightInfoLoadingArr.default, {});
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _flightInfo2.styles.container,
        children: [(0, _jsxRuntime.jsx)(_blurView.default, {}), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _flightInfo2.styles.containerFlightInfo,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: [_flightInfo2.styles.row, {
              height: _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT
            }],
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightInfo2.styles.timeColumn,
              children: renderTimelineHeaderItemARR()
            }), isLoadingGetMyTripData ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightInfo2.styles.viewMarginTop,
              children: (0, _jsxRuntime.jsx)(_flightInfoTransitLoading.default, {})
            }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_flightInfo2.styles.destinationInfo, _flightInfo2.styles.rowGap4],
              children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
                shortName: airport,
                fullName: airportDetails == null ? undefined : airportDetails.name,
                type: _flightDetailsCard.FlightDetailsCardState.departure
              }), isErrorGetMyTrip && !getMyTripData && isSaved ? (0, _jsxRuntime.jsx)(_flightTransitError.default, {
                onRetry: onRetryGetMyTripData
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _flightInfo2.styles.containerBodyInfo,
                children: [renderShowButtonDetails(), renderShowInfoFlightARR({
                  terminal: getMyTripData == null ? undefined : getMyTripData.departureTerminal,
                  checkinDesk: getMyTripData == null ? undefined : getMyTripData.departureCheckInDesk,
                  gate: getMyTripData == null ? undefined : getMyTripData.departureGate,
                  updateFields: (getMyTripData == null ? undefined : getMyTripData.updateFields) || []
                })]
              })]
            })]
          }), transitsData == null ? undefined : transitsData.map(function (item, index) {
            var _getMyTripData$transi9, _getMyTripData$transi0, _getMyTripData$transi1, _getMyTripData$transi10;
            return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_flightInfo2.styles.row, {
                height: _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT
              }],
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _flightInfo2.styles.timeColumn,
                children: (0, _utils.handleCondition)(!!transitARRDateTime, (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                  height: (0, _utils.handleCondition)(transitsData == null ? undefined : transitsData.length, _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT, _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT - _timelineHeaderItem.GAP_8),
                  type: _flightInfo.TypeTimelineHeader.ArrDateTimeMiddle,
                  flightScheduledDate: transitARRDateTime == null ? undefined : transitARRDateTime.scheduledDate,
                  mainTime: transitARRDateTime == null ? undefined : transitARRDateTime.mainTime,
                  scheduledTime: transitARRDateTime == null ? undefined : transitARRDateTime.scheduledTime,
                  reTimeFlag: transitARRDateTime == null ? undefined : transitARRDateTime.reTimeFlag,
                  numberDaysDiff: transitARRDateTime == null ? undefined : transitARRDateTime.numberDaysDiff,
                  gapEnd: (0, _utils.handleCondition)(transitARRDateTime == null ? undefined : transitARRDateTime.reTimeFlag, _timelineHeaderItem.GAP_8, 0)
                }), (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                  type: _flightInfo.TypeTimelineHeader.ArrCircleMiddle,
                  height: _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT
                }))
              }), isLoadingGetMyTripData ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _flightInfo2.styles.viewMarginTop,
                children: (0, _jsxRuntime.jsx)(_flightInfoTransitLoading.default, {})
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: [_flightInfo2.styles.destinationInfo, _flightInfo2.styles.rowGap4],
                children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
                  shortName: item == null ? undefined : item.code,
                  fullName: item == null ? undefined : item.fullName,
                  type: _flightDetailsCard.FlightDetailsCardState.departure
                }), isErrorGetMyTrip && !getMyTripData && isSaved ? (0, _jsxRuntime.jsx)(_flightTransitError.default, {
                  onRetry: onRetryGetMyTripData
                }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _flightInfo2.styles.containerBodyInfo,
                  children: [renderShowButtonDetails(), getMyTripData && renderShowInfoFlightARR({
                    terminal: getMyTripData == null || (_getMyTripData$transi9 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi9.departureTerminal,
                    checkinDesk: getMyTripData == null || (_getMyTripData$transi0 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi0.departureCheckInDesk,
                    gate: getMyTripData == null || (_getMyTripData$transi1 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi1.departureGate,
                    updateFields: (getMyTripData == null || (_getMyTripData$transi10 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi10.updateFields) || []
                  })]
                })]
              })]
            }, item == null ? undefined : item.id);
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            onLayout: function onLayout(event) {
              var componentHeight = event.nativeEvent.layout.height || 0;
              heightStartARRRef.current = componentHeight;
            },
            style: _flightInfo2.styles.row,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightInfo2.styles.timeColumn,
              children: (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                height: heightStartARRRef.current,
                type: _flightInfo.TypeTimelineHeader.ArrDateTimeStart,
                flightScheduledDate: flightScheduledDate,
                mainTime: mainTime,
                scheduledTime: scheduledTime,
                reTimeFlag: reTimeFlag,
                numberDaysDiff: numberDaysDiff
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _flightInfo2.styles.destinationInfo,
              children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
                shortName: 'SIN',
                fullName: 'Singapore',
                type: _flightDetailsCard.FlightDetailsCardState.arrival
              }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _flightInfo2.styles.containerBodyInfo,
                children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _flightInfo2.styles.containerBodyInfoWrapper,
                  children: [(0, _jsxRuntime.jsx)(_flightCheckinInfo.default, {
                    onPressGate: handleMap,
                    onPressBaggageBelt: handleMap,
                    onPressCheckinRow: handleMap,
                    flightDetailsData: flightDetailsData
                  }), (0, _jsxRuntime.jsx)(_flightPickupDropoff.default, {
                    onPressAccessiblePickupOrDropOff: handleMap,
                    onPressPickupOrDropOff: handleMap,
                    intoCityOrAirportPayload: intoCityOrAirportPayload,
                    flightDetailsData: {
                      direction: flightDetailsData == null ? undefined : flightDetailsData.direction
                    }
                  }), (0, _jsxRuntime.jsx)(_flightCarpark.default, {
                    onPressCarpark: handleMap,
                    intoCityOrAirportPayload: intoCityOrAirportPayload,
                    flightDetailsData: {
                      terminal: flightDetailsData == null ? undefined : flightDetailsData.terminal,
                      direction: flightDetailsData == null ? undefined : flightDetailsData.direction
                    }
                  })]
                })
              })]
            })]
          })]
        })]
      });
    };
    var renderFlightInfoDEP = function renderFlightInfoDEP() {
      var _getMyTripData$transi11, _getMyTripData$transi12, _getMyTripData$transi13, _getMyTripData$transi14;
      var flightItemHeight = (0, _utils.handleCondition)(hasFastCheckIn, _timelineHeaderItem.HEIGHT_NORMAL_FLIGHT_FASTCHECKIN + _timelineHeaderItem.HEIGHT_FAST_CHECKIN, (0, _utils.handleCondition)(transitsData == null ? undefined : transitsData.length, _timelineHeaderItem.HEIGHT_NORMAL_FLIGHT_TRANSIT, _timelineHeaderItem.HEIGHT_NORMAL_FLIGHT));
      if (isLoadingDetailFlight) return (0, _jsxRuntime.jsx)(_flightInfoLoadingDep.default, {});
      var needCircleEnd = isHasTransit && !foreignDEPDateTime ? false : !!transitDEPDateTime || !!foreignDEPDateTime;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _flightInfo2.styles.container,
        children: [(0, _jsxRuntime.jsx)(_blurView.default, {}), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: [_flightInfo2.styles.containerFlightInfo, {
            justifyContent: 'flex-start'
          }],
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _flightInfo2.styles.row,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightInfo2.styles.timeColumn,
              children: (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                height: flightItemHeight + _timelineHeaderItem.GAP_8,
                type: _flightInfo.TypeTimelineHeader.DepDateTimeStart,
                flightScheduledDate: flightScheduledDate,
                mainTime: mainTime,
                scheduledTime: scheduledTime,
                reTimeFlag: reTimeFlag,
                numberDaysDiff: numberDaysDiff,
                needCircleEnd: needCircleEnd
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _flightInfo2.styles.destinationInfo,
              children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
                shortName: 'SIN',
                fullName: 'Singapore',
                type: _flightDetailsCard.FlightDetailsCardState.departure
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: [_flightInfo2.styles.containerBodyInfo, _flightInfo2.styles.rowGap8],
                children: [(0, _utils.handleCondition)(hasFastCheckIn, (0, _jsxRuntime.jsx)(_flightFastCheckin.default, {}), null), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _flightInfo2.styles.containerBodyInfoWrapper,
                  children: [(0, _jsxRuntime.jsx)(_flightCheckinInfo.default, {
                    onPressGate: handleMap,
                    onPressBaggageBelt: handleMap,
                    onPressCheckinRow: handleMap,
                    flightDetailsData: flightDetailsData
                  }), (0, _jsxRuntime.jsx)(_flightPickupDropoff.default, {
                    onPressAccessiblePickupOrDropOff: handleMap,
                    onPressPickupOrDropOff: handleMap,
                    intoCityOrAirportPayload: intoCityOrAirportPayload,
                    flightDetailsData: {
                      direction: flightDetailsData == null ? undefined : flightDetailsData.direction
                    }
                  }), (0, _jsxRuntime.jsx)(_flightCarpark.default, {
                    onPressCarpark: handleMap,
                    intoCityOrAirportPayload: intoCityOrAirportPayload,
                    flightDetailsData: {
                      terminal: flightDetailsData == null ? undefined : flightDetailsData.terminal,
                      direction: flightDetailsData == null ? undefined : flightDetailsData.direction
                    }
                  })]
                })]
              })]
            })]
          }), transitsData == null ? undefined : transitsData.map(function (item, index) {
            return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: [_flightInfo2.styles.row, {
                height: _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT
              }],
              children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _flightInfo2.styles.timeColumn,
                children: (0, _utils.handleCondition)(foreignDEPDateTime, (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                  height: isHasTransitDataAndDestinationEmpty ? _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT : heightEndDEPRef.current,
                  type: _flightInfo.TypeTimelineHeader.DepDateTimeMiddle,
                  flightScheduledDate: foreignDEPDateTime == null ? undefined : foreignDEPDateTime.scheduledDate,
                  mainTime: foreignDEPDateTime == null ? undefined : foreignDEPDateTime.mainTime,
                  scheduledTime: foreignDEPDateTime == null ? undefined : foreignDEPDateTime.scheduledTime,
                  reTimeFlag: foreignDEPDateTime == null ? undefined : foreignDEPDateTime.reTimeFlag,
                  numberDaysDiff: foreignDEPDateTime == null ? undefined : foreignDEPDateTime.numberDaysDiff,
                  gapEnd: (0, _utils.handleCondition)(foreignDEPDateTime == null ? undefined : foreignDEPDateTime.reTimeFlag, _timelineHeaderItem.GAP_8, 0),
                  isHasTransitDataAndDestinationEmpty: isHasTransitDataAndDestinationEmpty
                }), (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                  type: _flightInfo.TypeTimelineHeader.DepCircleMiddle,
                  height: _timelineHeaderItem.HEIGHT_TRANSIT_FOREIGN_FLIGHT
                }))
              }), isLoadingGetMyTripData ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
                children: (0, _jsxRuntime.jsx)(_flightInfoTransitLoading.default, {})
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _flightInfo2.styles.destinationInfo,
                children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
                  shortName: item == null ? undefined : item.code,
                  fullName: item == null ? undefined : item.fullName,
                  type: _flightDetailsCard.FlightDetailsCardState.arrival
                }), isErrorGetMyTrip && !getMyTripData && isSaved ? (0, _jsxRuntime.jsx)(_flightTransitError.default, {
                  onRetry: onRetryGetMyTripData
                }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: _flightInfo2.styles.containerBodyInfo,
                  children: [renderShowButtonDetails(), getMyTripData && renderShowInfoFlightDEP({
                    terminal: getMyTripData == null ? undefined : getMyTripData.arrivalTerminal,
                    gate: getMyTripData == null ? undefined : getMyTripData.arrivalGate,
                    baggageClaim: getMyTripData == null ? undefined : getMyTripData.arrivalBaggageClaim,
                    updateFields: (getMyTripData == null ? undefined : getMyTripData.updateFields) || []
                  })]
                })]
              })]
            }, index);
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            onLayout: function onLayout(event) {
              var componentHeight = event.nativeEvent.layout.height || 0;
              heightEndDEPRef.current = componentHeight;
            },
            style: _flightInfo2.styles.row,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _flightInfo2.styles.timeColumn,
              children: (0, _utils.handleCondition)((transitsData == null ? undefined : transitsData.length) > 0 ? transitDEPDateTime : foreignDEPDateTime, (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                height: heightEndDEPRef.current,
                type: _flightInfo.TypeTimelineHeader.DepDateTimeEnd,
                flightScheduledDate: (transitsData == null ? undefined : transitsData.length) > 0 ? transitDEPDateTime == null ? undefined : transitDEPDateTime.scheduledDate : foreignDEPDateTime == null ? undefined : foreignDEPDateTime.scheduledDate,
                mainTime: (transitsData == null ? undefined : transitsData.length) > 0 ? transitDEPDateTime == null ? undefined : transitDEPDateTime.mainTime : foreignDEPDateTime == null ? undefined : foreignDEPDateTime.mainTime,
                scheduledTime: (transitsData == null ? undefined : transitsData.length) > 0 ? transitDEPDateTime == null ? undefined : transitDEPDateTime.scheduledTime : foreignDEPDateTime == null ? undefined : foreignDEPDateTime.scheduledTime,
                reTimeFlag: (transitsData == null ? undefined : transitsData.length) > 0 ? transitDEPDateTime == null ? undefined : transitDEPDateTime.reTimeFlag : foreignDEPDateTime == null ? undefined : foreignDEPDateTime.reTimeFlag,
                numberDaysDiff: (transitsData == null ? undefined : transitsData.length) > 0 ? transitDEPDateTime == null ? undefined : transitDEPDateTime.numberDaysDiff : foreignDEPDateTime == null ? undefined : foreignDEPDateTime.numberDaysDiff,
                needCircleDepDateTimeEnd: isHasTransit && !foreignDEPDateTime
              }), (0, _utils.handleCondition)(isHasTransitData, null, (0, _jsxRuntime.jsx)(_timelineHeaderItem.default, {
                height: heightEndDEPRef.current,
                type: _flightInfo.TypeTimelineHeader.DepCircleEnd
              })))
            }), isLoadingGetMyTripData ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
              children: (0, _jsxRuntime.jsx)(_flightInfoTransitLoading.default, {})
            }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _flightInfo2.styles.destinationInfo,
              children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
                shortName: airport,
                fullName: airportDetails == null ? undefined : airportDetails.name,
                type: _flightDetailsCard.FlightDetailsCardState.arrival
              }), isErrorGetMyTrip && !getMyTripData && isSaved ? (0, _jsxRuntime.jsx)(_flightTransitError.default, {
                onRetry: onRetryGetMyTripData
              }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _flightInfo2.styles.containerBodyInfo,
                children: [renderShowButtonDetails(), getMyTripData && renderShowInfoFlightDEP({
                  terminal: (transitsData == null ? undefined : transitsData.length) > 0 ? getMyTripData == null || (_getMyTripData$transi11 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi11.arrivalTerminal : getMyTripData == null ? undefined : getMyTripData.arrivalTerminal,
                  gate: (transitsData == null ? undefined : transitsData.length) > 0 ? getMyTripData == null || (_getMyTripData$transi12 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi12.arrivalGate : getMyTripData == null ? undefined : getMyTripData.arrivalGate,
                  baggageClaim: (transitsData == null ? undefined : transitsData.length) > 0 ? getMyTripData == null || (_getMyTripData$transi13 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi13.arrivalBaggageClaim : getMyTripData == null ? undefined : getMyTripData.arrivalBaggageClaim,
                  updateFields: (transitsData == null ? undefined : transitsData.length) > 0 ? getMyTripData == null || (_getMyTripData$transi14 = getMyTripData.transit) == null ? undefined : _getMyTripData$transi14.updateFields : (getMyTripData == null ? undefined : getMyTripData.updateFields) || []
                })]
              })]
            })]
          })]
        })]
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [renderHeader(), renderFlightInfoTitle(), (0, _utils.handleCondition)(directionFromParams === _flightProps.FlightDirection.arrival || direction === _flightProps.FlightDirection.arrival, renderFlightInfoARR(), renderFlightInfoDEP())]
    });
  };
