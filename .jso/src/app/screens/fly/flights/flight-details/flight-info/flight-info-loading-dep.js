  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _flightInfo = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _flightLocationPlace = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[9]);
  var _blurView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var lighterGreyLoadingColors = [_theme.color.palette.backgroundStartFlightInfoV2, _theme.color.palette.backgroundEndFlightInfoV2, _theme.color.palette.backgroundStartFlightInfoV2];
  var loadingStyle = [{
    width: 67,
    borderRadius: 4,
    height: 16
  }, {
    width: 67,
    borderRadius: 4,
    height: 20
  }, {
    width: 160,
    borderRadius: 4,
    height: 20
  }, {
    width: 204,
    borderRadius: 4,
    height: 16
  }];
  var renderLongLine = function renderLongLine() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [_flightInfo.styles.longLine, _flightInfo.styles.longLineAutoHeight],
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
        height: '100%',
        width: 2,
        children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Line, {
          x1: 1,
          y1: 0,
          x2: 1,
          y2: 700,
          stroke: _theme.color.palette.whiteGrey,
          strokeWidth: 1,
          strokeDasharray: "2 2"
        })
      })
    });
  };
  var renderCircle = function renderCircle() {
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
      height: 9,
      width: 9,
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Circle, {
        cx: 4.5,
        cy: 4.5,
        r: 4.5,
        fill: _theme.color.palette.whiteGrey
      })
    });
  };
  var FlightInfoLoadingViewDep = function FlightInfoLoadingViewDep() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _flightInfo.styles.container,
      children: [(0, _jsxRuntime.jsx)(_blurView.default, {}), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _flightInfo.styles.containerFlightInfo,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: _flightInfo.styles.row,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo.styles.timeColumn,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: [_flightInfo.styles.rowGap4, _flightInfo.styles.centered],
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[0]
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[1]
              })]
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: _flightInfo.styles.centered,
              children: [renderLongLine(), renderCircle()]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: _flightInfo.styles.destinationInfo,
            children: [(0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
              shortName: "SIN",
              fullName: "Singapore",
              type: _flightDetailsCard.FlightDetailsCardState.departure
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: _flightInfo.styles.containerBodyInfo,
              children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: _flightInfo.styles.containerBodyInfoWrapper,
                children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                  style: _flightInfo.styles.rowFlexGap4,
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: _flightInfo.styles.darkGreyBoxLarge
                  }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: _flightInfo.styles.darkGreyBoxLarge
                  })]
                }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                  style: _flightInfo.styles.rowFlex,
                  children: (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: _flightInfo.styles.darkGreyBoxMedium
                  })
                }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                  style: _flightInfo.styles.rowFlex,
                  children: (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: _flightInfo.styles.darkGreyBoxSmall
                  })
                }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
                  style: _flightInfo.styles.shimmerContainer,
                  children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                    duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                    shimmerColors: lighterGreyLoadingColors,
                    shimmerStyle: loadingStyle[2]
                  }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                    duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                    shimmerColors: lighterGreyLoadingColors,
                    shimmerStyle: loadingStyle[3]
                  })]
                })]
              })
            })]
          })]
        })
      })]
    });
  };
  var _default = exports.default = FlightInfoLoadingViewDep;
