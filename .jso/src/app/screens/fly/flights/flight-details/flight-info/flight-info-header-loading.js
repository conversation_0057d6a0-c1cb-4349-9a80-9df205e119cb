  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var lighterGreyLoadingColors = _theme.color.palette.platinumLinearGradient;
  var width_content = _reactNative.Dimensions.get("window").width - 48;
  var FlightInfoHeaderLoading = function FlightInfoHeaderLoading() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.title
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.content
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    title: {
      width: 115,
      height: 29,
      borderRadius: 4
    },
    content: {
      width: width_content,
      height: 32,
      borderRadius: 4,
      marginTop: 8
    }
  });
  var _default = exports.default = FlightInfoHeaderLoading;
