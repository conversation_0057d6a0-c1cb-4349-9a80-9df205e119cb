  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _moment6 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[2]);
  var _utils = _$$_REQUIRE(_dependencyMap[3]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[4]);
  var useFlightInfoV2 = function useFlightInfoV2(_ref) {
    var flightDetailsData = _ref.flightDetailsData;
    var _ref2 = flightDetailsData || {},
      scheduledDate = _ref2.scheduledDate,
      displayTimestamp = _ref2.displayTimestamp,
      statusMapping = _ref2.statusMapping,
      scheduledTime = _ref2.scheduledTime;
    var processFlightTime = function processFlightTime() {
      var mainTime = scheduledTime;
      var reTimeFlag = false;
      var numberDaysDiff = 0;
      if (displayTimestamp) {
        mainTime = displayTimestamp == null ? undefined : displayTimestamp.split(" ")[1];
        var mainDate = displayTimestamp == null ? undefined : displayTimestamp.split(" ")[0];
        if (scheduledDate !== mainDate || scheduledTime !== mainTime) {
          reTimeFlag = true;
          numberDaysDiff = (0, _moment6.default)(mainDate).diff((0, _moment6.default)(scheduledDate), "days");
        }
      }
      if (numberDaysDiff < 0) {
        return {
          reTimeFlag: reTimeFlag,
          mainTime: mainTime,
          numberDaysDiff: `${numberDaysDiff}`
        };
      }
      if (numberDaysDiff > 0) {
        return {
          reTimeFlag: reTimeFlag,
          mainTime: mainTime,
          numberDaysDiff: `+${numberDaysDiff}`
        };
      }
      return {
        reTimeFlag: reTimeFlag,
        mainTime: mainTime,
        numberDaysDiff: 0
      };
    };
    var processFlightDate = function processFlightDate() {
      var flightScheduledDate = (0, _dateTime.toDate)(scheduledDate, _dateTime.DateFormats.DayMonthYear);
      if ((0, _utils.handleCondition)((statusMapping == null ? undefined : statusMapping.details_status_en) && ((statusMapping == null ? undefined : statusMapping.details_status_en.includes("Re-timed")) || (statusMapping == null ? undefined : statusMapping.details_status_en.includes("Delayed"))), true, false)) {
        var _displayTimestamp$spl;
        var date = (0, _utils.handleCondition)(displayTimestamp, displayTimestamp == null || (_displayTimestamp$spl = displayTimestamp.split(" ")) == null ? undefined : _displayTimestamp$spl[0], scheduledDate);
        flightScheduledDate = (0, _dateTime.toDate)(date, _dateTime.DateFormats.DayMonthYear);
      }
      return {
        flightScheduledDate: flightScheduledDate
      };
    };
    var processFlightForeignTransitDateTime = function processFlightForeignTransitDateTime(scheduleLocalDate, estimatedLocalDate, isSaved) {
      var _moment4, _moment5;
      if (!isSaved || !scheduleLocalDate || !estimatedLocalDate) {
        return null;
      }
      var processDaysDiff = function processDaysDiff(scheduleLocalDate, estimatedLocalDate) {
        var scheduledDate = (0, _moment6.default)(scheduleLocalDate).startOf('day');
        var estimatedDate = (0, _moment6.default)(estimatedLocalDate).startOf('day');
        var daysDiff = (0, _moment6.default)(estimatedDate).diff((0, _moment6.default)(scheduledDate), "days");
        if (estimatedDate.isAfter(scheduledDate)) {
          return `+${daysDiff}`;
        }
        if (estimatedDate.isBefore(scheduledDate)) {
          return `${daysDiff}`;
        }
        return ''; // Same day
      };
      if ((0, _moment6.default)(scheduleLocalDate).format(_dateTime.DateFormats.YearMonthDayTime) === (0, _moment6.default)(estimatedLocalDate).format(_dateTime.DateFormats.YearMonthDayTime)) {
        var _moment, _moment2, _moment3;
        return {
          scheduledDate: (_moment = (0, _moment6.default)(estimatedLocalDate, _dateTime.DateFormats.DateTime)) == null ? undefined : _moment.format(_dateTime.DateFormats.DayMonthYear),
          reTimeFlag: false,
          mainTime: (_moment2 = (0, _moment6.default)(estimatedLocalDate, _dateTime.DateFormats.DateTime)) == null ? undefined : _moment2.format(_dateTime.DateFormats.HoursMinutes),
          numberDaysDiff: 0,
          scheduledTime: (_moment3 = (0, _moment6.default)(scheduleLocalDate, _dateTime.DateFormats.DateTime)) == null ? undefined : _moment3.format(_dateTime.DateFormats.HoursMinutes)
        };
      }
      var numberDaysDiff = processDaysDiff(scheduleLocalDate, estimatedLocalDate);
      return {
        scheduledDate: (_moment4 = (0, _moment6.default)(estimatedLocalDate, _dateTime.DateFormats.DateTime)) == null ? undefined : _moment4.format(_dateTime.DateFormats.DayMonthYear),
        reTimeFlag: true,
        mainTime: (0, _moment6.default)(estimatedLocalDate).format(_dateTime.DateFormats.HoursMinutes),
        numberDaysDiff: numberDaysDiff,
        scheduledTime: (_moment5 = (0, _moment6.default)(scheduleLocalDate, _dateTime.DateFormats.DateTime)) == null ? undefined : _moment5.format(_dateTime.DateFormats.HoursMinutes)
      };
    };
    var checkFlightCanSave = function checkFlightCanSave() {
      var flightStatusCheckSave = flightDetailsData == null ? undefined : flightDetailsData.flightStatus;
      var status = flightStatusCheckSave == null ? undefined : flightStatusCheckSave.toLowerCase();
      var priorityTime = (flightDetailsData == null ? undefined : flightDetailsData.actualTimestamp) || (flightDetailsData == null ? undefined : flightDetailsData.estimatedTimestamp) || `${flightDetailsData == null ? undefined : flightDetailsData.scheduledDate} ${flightDetailsData == null ? undefined : flightDetailsData.scheduledTime}`;
      var currentTimeToUTC = (0, _moment6.default)().tz("Asia/Singapore");
      var flightTime = (0, _moment6.default)(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
      if (_flightProps.FlightDirection.departure === (flightDetailsData == null ? undefined : flightDetailsData.direction)) {
        switch (true) {
          case /departed/gim.test(status):
          case /cancelled/gim.test(status):
            return false;
          default:
            return true;
        }
      }
      switch (true) {
        case /cancelled/gim.test(status):
        case /landed/gim.test(status) && (0, _moment6.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") < currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
          return false;
        default:
          return true;
      }
    };
    return {
      processFlightTime: processFlightTime,
      processFlightDate: processFlightDate,
      checkFlightCanSave: checkFlightCanSave,
      processFlightForeignTransitDateTime: processFlightForeignTransitDateTime
    };
  };
  var _default = exports.default = useFlightInfoV2;
