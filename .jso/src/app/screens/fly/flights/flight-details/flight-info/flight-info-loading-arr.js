  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeSvg = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _flightLocationPlace = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[8]);
  var _blurView = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _timelineHeaderItem = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get('screen'),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var lighterGreyLoadingColors = [_theme.color.palette.backgroundStartFlightInfoV2, _theme.color.palette.backgroundEndFlightInfoV2, _theme.color.palette.backgroundStartFlightInfoV2];
  var loadingStyle = [{
    width: 67,
    borderRadius: 4,
    height: 16
  }, {
    width: 67,
    borderRadius: 4,
    height: 20
  }, {
    width: 160,
    borderRadius: 4,
    height: 20
  }, {
    width: 204,
    borderRadius: 4,
    height: 16,
    marginBottom: 62
  }];
  var renderLongLine = function renderLongLine() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.longLine, styles.longLineAutoHeight],
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
        height: '100%',
        width: 2,
        children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Line, {
          x1: 1,
          y1: 0,
          x2: 1,
          y2: 700,
          stroke: _theme.color.palette.whiteGrey,
          strokeWidth: 1,
          strokeDasharray: "2 2"
        })
      })
    });
  };
  var renderCircle = function renderCircle() {
    return (0, _jsxRuntime.jsx)(_reactNativeSvg.default, {
      height: 9,
      width: 9,
      children: (0, _jsxRuntime.jsx)(_reactNativeSvg.Circle, {
        cx: 4.5,
        cy: 4.5,
        r: 4.5,
        fill: _theme.color.palette.whiteGrey
      })
    });
  };
  var FlightInfoLoadingViewArr = function FlightInfoLoadingViewArr() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_blurView.default, {}), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.containerFlightInfo,
        children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.row,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.timeColumn,
            children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: [styles.rowGap4, styles.centered],
              children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[0]
              })
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.centered,
              children: [renderCircle(), renderLongLine()]
            }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: [styles.rowGap4, styles.centered],
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[0]
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[1]
              })]
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: styles.destinationInfo,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: styles.shimmerContainer,
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[2]
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: lighterGreyLoadingColors,
                shimmerStyle: loadingStyle[3]
              })]
            }), (0, _jsxRuntime.jsx)(_flightLocationPlace.default, {
              shortName: "SIN",
              fullName: "Singapore",
              type: _flightDetailsCard.FlightDetailsCardState.arrival
            }), (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: styles.containerBodyInfo,
              children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
                style: styles.containerBodyInfoWrapper,
                children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
                  style: styles.rowFlexGap4,
                  children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.darkGreyBoxLargeLeft
                  }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.darkGreyBoxLargeRight
                  })]
                }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                  style: styles.rowFlex,
                  children: (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.darkGreyBoxMedium
                  })
                }), (0, _jsxRuntime.jsx)(_reactNative.View, {
                  style: styles.rowFlex,
                  children: (0, _jsxRuntime.jsx)(_reactNative.View, {
                    style: styles.darkGreyBoxSmall
                  })
                })]
              })
            })]
          })]
        })
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    longLine: {
      minHeight: 10,
      maxHeight: 50,
      alignItems: 'center'
    },
    longLineAutoHeight: {
      maxHeight: 'auto',
      height: 62
    },
    container: {
      width: '100%',
      minHeight: 330,
      maxHeight: 700,
      borderRadius: 16,
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingTop: 12,
      paddingBottom: 24,
      columnGap: 8,
      justifyContent: 'space-between',
      overflow: 'hidden'
    },
    containerBlur: {
      width: width,
      height: height,
      position: "absolute"
    },
    containerFlightInfo: {
      flex: 1,
      justifyContent: 'space-between'
    },
    row: {
      flexDirection: 'row',
      columnGap: 8
    },
    timeColumn: {
      width: 72,
      rowGap: 10
    },
    centered: {
      width: '100%',
      alignItems: 'center'
    },
    rowGap4: {
      rowGap: 4
    },
    destinationInfo: {
      marginTop: _timelineHeaderItem.PADDING_TOP_FLIGHT_ICON,
      flex: 1,
      rowGap: 8
    },
    containerBodyInfo: {
      width: '100%'
    },
    containerBodyInfoWrapper: {
      rowGap: 4,
      width: "100%"
    },
    rowFlexGap4: {
      flexDirection: 'row',
      columnGap: 4
    },
    rowFlex: {
      flexDirection: 'row'
    },
    darkGreyBoxLargeLeft: {
      width: 80,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 56
    },
    darkGreyBoxLargeRight: {
      flex: 1,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 56
    },
    darkGreyBoxMedium: {
      flex: 1,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 52
    },
    darkGreyBoxSmall: {
      flex: 1,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 32
    },
    shimmerContainer: {
      rowGap: 4
    }
  });
  var _default = exports.default = FlightInfoLoadingViewArr;
