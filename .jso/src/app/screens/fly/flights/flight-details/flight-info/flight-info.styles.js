  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _timelineHeaderItem = _$$_REQUIRE(_dependencyMap[3]);
  var _Dimensions$get = _reactNative.Dimensions.get('screen'),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      borderRadius: 16,
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingTop: 12,
      paddingBottom: 24,
      columnGap: 8,
      justifyContent: 'space-between',
      overflow: 'hidden'
    },
    containerBlur: {
      width: width,
      height: height,
      position: "absolute"
    },
    containerFlightInfo: {
      flex: 1,
      justifyContent: 'space-between'
    },
    row: {
      flexDirection: 'row',
      columnGap: 8
    },
    timeColumn: {
      width: 72,
      rowGap: 10
    },
    centered: {
      width: '100%',
      alignItems: 'center'
    },
    shortLine: {
      height: 5,
      alignItems: 'center'
    },
    longLine: {
      minHeight: 10,
      maxHeight: 50,
      alignItems: 'center'
    },
    destinationInfo: {
      marginTop: _timelineHeaderItem.PADDING_TOP_FLIGHT_ICON,
      flex: 1,
      rowGap: 8
    },
    containerBodyInfo: {
      width: '100%'
    },
    containerBodyInfoWrapper: {
      rowGap: 4,
      width: "100%"
    },
    timeRow: {
      flexDirection: "row",
      columnGap: 4,
      alignItems: "flex-end"
    },
    dayAdded: Object.assign({}, _text.newPresets.caption2Regular, {
      color: _theme.color.palette.whiteGrey
    }),
    oldTime: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.midGrey,
      textDecorationLine: "line-through"
    }),
    date: {
      color: _theme.color.palette.midGrey
    },
    rowGap2: {
      rowGap: 2
    },
    longLineAutoHeight: {
      maxHeight: 'auto',
      height: 196
    },
    // loading
    rowGap4: {
      rowGap: 4
    },
    rowGap8: {
      rowGap: _timelineHeaderItem.GAP_8
    },
    longLineContainer: {
      marginRight: 15,
      alignItems: 'center'
    },
    rowFlexGap4: {
      flexDirection: 'row',
      columnGap: 4
    },
    rowFlex: {
      flexDirection: 'row'
    },
    darkGreyBoxLarge: {
      flex: 1,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 56
    },
    darkGreyBoxMedium: {
      flex: 1,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 52
    },
    darkGreyBoxSmall: {
      flex: 1,
      borderRadius: 8,
      backgroundColor: _theme.color.palette.darkestGrey,
      height: 32
    },
    shimmerContainer: {
      rowGap: 4,
      marginTop: 28
    },
    containerHeader: {
      alignItems: "center",
      marginBottom: 64
    },
    containerFlightNumber: {
      flexDirection: "row",
      alignItems: "center",
      height: 29
    },
    wrapImageStyle: {
      width: 24,
      height: 24,
      borderRadius: 8,
      marginRight: 8,
      overflow: "hidden"
    },
    imageStyle: {
      width: 24,
      height: 24
    },
    mainFlightCodeText: {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 22
    },
    flightNumberText: {
      color: _theme.color.palette.whiteGrey
    },
    darkTextStyle: {
      color: _theme.color.palette.whiteGrey
    },
    containerSlavesTextStyle: {
      marginTop: 8,
      marginHorizontal: 24
    },
    slavesTextStyle: {
      color: _theme.color.palette.whiteGrey,
      textAlign: "center",
      minHeight: 32
    },
    containerFlightInfoTitle: {
      width: '100%',
      marginBottom: 16,
      paddingHorizontal: 8
    },
    containerFlightStatusTag: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 8
    },
    contentTimeLeft: {
      color: _theme.color.palette.whiteGrey,
      marginRight: 8
    },
    informativeTextStyle: {
      color: _theme.color.palette.lightBeige,
      textAlign: "center",
      marginTop: 2,
      textTransform: "none"
    },
    absoluteUp: {
      position: 'absolute',
      top: _timelineHeaderItem.HEIGHT_LINE_CIRCLE_ONLY
    },
    absoluteDown: {
      position: 'absolute',
      top: -8
    },
    buttonShowDetails: Object.assign({}, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.lighterPurple
    }),
    foreignTransitDetails: {
      flexDirection: "row",
      columnGap: 8,
      flexWrap: "wrap"
    },
    viewMarginTop: {
      marginTop: 16
    },
    transitText: {
      color: _theme.color.palette.midGrey
    }
  });
