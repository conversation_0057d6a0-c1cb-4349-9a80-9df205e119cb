  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TypeTimelineHeader = exports.FlightJourneyEnumStatus = exports.EDisplayDirection = undefined;
  var EDisplayDirection = exports.EDisplayDirection = /*#__PURE__*/function (EDisplayDirection) {
    EDisplayDirection["topToBottom"] = "topToBottom";
    EDisplayDirection["bottomToTop"] = "bottomToTop";
    return EDisplayDirection;
  }({});
  var FlightJourneyEnumStatus = exports.FlightJourneyEnumStatus = /*#__PURE__*/function (FlightJourneyEnumStatus) {
    FlightJourneyEnumStatus["DEPARTED"] = "DEPARTED";
    FlightJourneyEnumStatus["NOT_STARTED"] = "NOT_STARTED";
    FlightJourneyEnumStatus["LANDED"] = "LANDED";
    return FlightJourneyEnumStatus;
  }({});
  var TypeTimelineHeader = exports.TypeTimelineHeader = /*#__PURE__*/function (TypeTimelineHeader) {
    TypeTimelineHeader["DepDateTimeMiddle"] = "dep-datetime-middle";
    TypeTimelineHeader["DepDateTimeEnd"] = "dep-datetime-end";
    TypeTimelineHeader["DepCircleMiddle"] = "dep-circle-middle";
    TypeTimelineHeader["DepCircleEnd"] = "dep-circle-end";
    TypeTimelineHeader["DepDateTimeStart"] = "dep-datetime-start";
    TypeTimelineHeader["ArrDateTimeMiddle"] = "arr-datetime-middle";
    TypeTimelineHeader["ArrDateTimeEnd"] = "arr-datetime-end";
    TypeTimelineHeader["ArrCircleMiddle"] = "arr-circle-middle";
    TypeTimelineHeader["ArrCircleEnd"] = "arr-circle-end";
    TypeTimelineHeader["ArrDateTimeStart"] = "arr-datetime-start";
    return TypeTimelineHeader;
  }({});
