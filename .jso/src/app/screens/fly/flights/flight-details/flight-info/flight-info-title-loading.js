  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _constants = _$$_REQUIRE(_dependencyMap[3]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[4]);
  var lighterGreyLoadingColors = ['rgba(229, 229, 229, 0.80)', 'rgba(153, 153, 153, 0.50)'];
  var width_content = _reactNative.Dimensions.get("window").width - 40;
  var FlightInfoTitleLoading = function FlightInfoTitleLoading() {
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.componentTop
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.componentMiddle
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lighterGreyLoadingColors,
        shimmerStyle: styles.componentBottom
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    componentTop: {
      width: 176,
      height: 16,
      borderRadius: 4
    },
    componentMiddle: {
      width: width_content,
      height: 29,
      borderRadius: 4,
      marginTop: 2
    },
    componentBottom: {
      width: 115,
      height: 22,
      borderRadius: 4,
      marginTop: 8
    }
  });
  var _default = exports.default = FlightInfoTitleLoading;
