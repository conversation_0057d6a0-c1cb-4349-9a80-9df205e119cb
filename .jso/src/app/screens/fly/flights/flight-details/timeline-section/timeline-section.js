  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TimelineSection = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _error = _$$_REQUIRE(_dependencyMap[8]);
  var _errorProps = _$$_REQUIRE(_dependencyMap[9]);
  var _flightTimelineSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _sectionImage = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _verticalLine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _icons = _$$_REQUIRE(_dependencyMap[14]);
  var _timelineFlyTiles = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _timelineFlyTiles2 = _$$_REQUIRE(_dependencyMap[16]);
  var _theme = _$$_REQUIRE(_dependencyMap[17]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[19]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[20]));
  var _utils = _$$_REQUIRE(_dependencyMap[21]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var sectionSeparator = {
    height: 40
  };
  var tilesSeparator = {
    height: 30
  };
  var containerStyle = {
    marginLeft: 16
  };
  var sectionHeadStyle = {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginRight: 26
  };
  var sectionTitleStyle = {
    flexDirection: "row",
    alignItems: "center"
  };
  var silverLoadingColors = [_theme.color.palette.silver, _theme.color.palette.midGrey, _theme.color.palette.silver];
  var loadingStyle = {
    flex: 0.8,
    height: 13,
    borderRadius: 4,
    marginLeft: 16
  };
  var errorComponent = {
    backgroundColor: "transperant"
  };
  var SINGAPORE = "Singapore ";
  var TimelineSection = exports.TimelineSection = function TimelineSection(props) {
    var _flyDetailAemData$dat, _flyDetailAemData$dat2;
    var flyItem = props.flyItem,
      direction = props.direction,
      flyFlightDetailsPayload = props.flyFlightDetailsPayload,
      onTrackingTimeline = props.onTrackingTimeline,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "TimeLineSection" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "TimeLineSection" : _props$accessibilityL;
    var dispatch = (0, _reactRedux.useDispatch)();
    var flyTimelineTilesPayload = (0, _reactRedux.useSelector)(function (state) {
      return _flyRedux.FlySelectors.flyTimelineTilesPayload(state);
    });
    var flyFlightDetailsFetching = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.flyFlightDetailsFetching);
    var prevFlyFlightDetailsFetching = (0, _screenHook.usePrevious)(flyFlightDetailsFetching);
    var _useState = (0, _react.useState)({
        0: true
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedTimelineTileSection = _useState2[0],
      setSelectedTimelineTileSection = _useState2[1];
    var flyDetailAemData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.FLY_DETAIL_PAGE));
    var loading = (0, _lodash.get)(flyDetailAemData, "loading");
    var callBackAfterSuccess = function callBackAfterSuccess(data) {
      var _data$list;
      var travelCheckListData = data == null || (_data$list = data.list) == null ? undefined : _data$list.find(function (item) {
        return item.displayLocation === "fly";
      });
      if (travelCheckListData) {
        return Object.assign({}, travelCheckListData, {
          image: (0, _utils.mappingUrlAem)(travelCheckListData == null ? undefined : travelCheckListData.image)
        });
      }
      return {};
    };
    var getTimelineTilesApi = (0, _react.useCallback)(function () {
      var _flyFlightDetailsPayl, _flyFlightDetailsPayl2;
      var airline = flyFlightDetailsPayload == null || (_flyFlightDetailsPayl = flyFlightDetailsPayload.flightDetailsData) == null ? undefined : _flyFlightDetailsPayl.airline;
      var destination = direction === _flightProps.FlightDirection.departure ? flyFlightDetailsPayload == null || (_flyFlightDetailsPayl2 = flyFlightDetailsPayload.flightDetailsData) == null || (_flyFlightDetailsPayl2 = _flyFlightDetailsPayl2.airportDetails) == null ? undefined : _flyFlightDetailsPayl2.name : SINGAPORE;
      var directionType = direction;
      var flight = flyItem == null ? undefined : flyItem.flightNumber;
      dispatch(_flyRedux.FlyCreators.flyTimelineTilesRequest(airline, destination, directionType, flight));
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.FLY_DETAIL_PAGE,
        pathName: "getFlyDetailPage",
        callBackAfterSuccess: callBackAfterSuccess
      }));
    }, [flyFlightDetailsPayload, flyItem, direction]);
    (0, _react.useEffect)(function () {
      if (prevFlyFlightDetailsFetching && !flyFlightDetailsFetching) {
        getTimelineTilesApi();
      }
    }, [getTimelineTilesApi, flyFlightDetailsFetching, prevFlyFlightDetailsFetching]);
    var renderTimeLineLoading = function renderTimeLineLoading() {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: containerStyle,
        children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
          children: [(0, _jsxRuntime.jsx)(_verticalLine.default, {}), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: sectionHeadStyle,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: sectionTitleStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.DotWithoutBg, {
                width: "16",
                height: "16"
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: silverLoadingColors,
                shimmerStyle: loadingStyle
              })]
            }), (0, _jsxRuntime.jsx)(_icons.TopArrow, {})]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: tilesSeparator
          }), (0, _jsxRuntime.jsx)(_timelineFlyTiles.default, {
            type: _timelineFlyTiles2.TimelineFlyTilesDefaultTypes.loading
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: tilesSeparator
          }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: sectionHeadStyle,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: sectionTitleStyle,
              children: [(0, _jsxRuntime.jsx)(_icons.DotWithoutBg, {
                width: "16",
                height: "16"
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: silverLoadingColors,
                shimmerStyle: loadingStyle
              })]
            }), (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
              color: _theme.color.palette.lightPurple
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: tilesSeparator
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: sectionHeadStyle,
          children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: sectionTitleStyle,
            children: [(0, _jsxRuntime.jsx)(_icons.DotWithoutBg, {
              width: "16",
              height: "16"
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: silverLoadingColors,
              shimmerStyle: loadingStyle
            })]
          }), (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
            color: _theme.color.palette.lightPurple
          })]
        })]
      });
    };
    var renderTimeLine = function renderTimeLine() {
      var _flyTimelineTilesPayl;
      var timelineTiles = (0, _lodash.get)(flyTimelineTilesPayload, "timelineTileData.timelineTiles");
      if ((flyTimelineTilesPayload == null || (_flyTimelineTilesPayl = flyTimelineTilesPayload.timelineTileData) == null ? undefined : _flyTimelineTilesPayl.sectionImageType) === _sectionImage.SectionImageComponentType.loading) {
        return renderTimeLineLoading();
      }
      if (flyTimelineTilesPayload != null && flyTimelineTilesPayload.errorFlag) {
        return (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
          type: _errorProps.ErrorComponentType.cloud,
          errorText: "flightDetails.timelineError",
          onPressed: getTimelineTilesApi,
          style: errorComponent
        });
      }
      if ((0, _lodash.isEmpty)(timelineTiles)) {
        return null;
      }
      return timelineTiles == null ? undefined : timelineTiles.map(function (item, index) {
        return (0, _jsxRuntime.jsx)(_flightTimelineSection.default, Object.assign({}, item, {
          isOpen: selectedTimelineTileSection[index],
          isLastSection: index === (timelineTiles == null ? undefined : timelineTiles.length) - 1,
          onPressSection: function onPressSection() {
            setSelectedTimelineTileSection(Object.assign({}, selectedTimelineTileSection, (0, _defineProperty2.default)({}, index, !selectedTimelineTileSection[index])));
          },
          flyFlightDetailsPayload: flyFlightDetailsPayload,
          flyItem: flyItem,
          onTrackingTimeline: onTrackingTimeline,
          testID: `${testID}__${index}`,
          accessibilityLabel: `${accessibilityLabel}__${index}`
        }), `${index}-${item.toString()}`);
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: sectionSeparator
      }), (0, _jsxRuntime.jsx)(_sectionImage.SectionImageComponent, {
        type: loading ? _sectionImage.SectionImageComponentType.loading : _sectionImage.SectionImageComponentType.defaultDarkGradient,
        title: flyDetailAemData == null || (_flyDetailAemData$dat = flyDetailAemData.data) == null ? undefined : _flyDetailAemData$dat.text,
        sectionImgUrl: flyDetailAemData == null || (_flyDetailAemData$dat2 = flyDetailAemData.data) == null ? undefined : _flyDetailAemData$dat2.image
      }), renderTimeLine(), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: sectionSeparator
      })]
    });
  };
