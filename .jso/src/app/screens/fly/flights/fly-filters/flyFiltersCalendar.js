  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _datetimepicker = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var bottomSheetStyle = {
    height: 303,
    borderRadius: 16,
    backgroundColor: _theme.color.palette.whiteGrey,
    marginHorizontal: 8,
    marginBottom: 33
  };
  var bottomSheetLineSeparatorStyle = {
    height: 1,
    backgroundColor: _theme.color.palette.lightGrey
  };
  var bottomSheetTextStyle = Object.assign({
    alignSelf: "center",
    marginTop: 20,
    color: _theme.color.palette.darkGrey,
    fontSize: 17
  }, _reactNative2.Platform.select({
    android: {
      fontWeight: "normal"
    },
    ios: {
      fontWeight: "600"
    }
  }));
  var bottomSheetOKTextStyle = {
    alignSelf: "center",
    marginVertical: 10,
    color: "#4676ee",
    fontSize: 20
  };
  var FlyFiltersCalendar = function FlyFiltersCalendar(props) {
    var visible = props.visible,
      onClosed = props.onClosed,
      onBackPressed = props.onBackPressed,
      setVisibleState = props.setVisibleState,
      selectedDateCallback = props.selectedDateCallback,
      date = props.date,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "FlyFiltersCalendar" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FlyFiltersCalendar" : _props$accessibilityL;
    var dateChangeRef = _react.default.useRef(null);
    var onChange = function onChange(event, selectedDate) {
      var currentDate = selectedDate || date;
      dateChangeRef.current = selectedDate;
      setVisibleState(_reactNative2.Platform.OS === "ios");
      if (_reactNative2.Platform.OS === "ios") {
        selectedDateCallback(currentDate);
      } else {
        if (event.type === "set") {
          selectedDateCallback(currentDate);
        } else {
          selectedDateCallback(undefined);
        }
      }
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: visible && _reactNative2.Platform.OS === "ios" ? (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
        isModalVisible: visible,
        onClosedSheet: function onClosedSheet() {
          return onClosed(false);
        },
        containerStyle: bottomSheetStyle,
        stopDragCollapse: true,
        onBackPressHandle: function onBackPressHandle() {
          return onBackPressed();
        },
        animationInTiming: 500,
        animationOutTiming: 500,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: bottomSheetTextStyle,
          tx: "flyDateFilter.selectDate"
        }), (0, _jsxRuntime.jsx)(_datetimepicker.default, {
          testID: "dateTimePicker",
          value: (dateChangeRef == null ? undefined : dateChangeRef.current) || date,
          mode: "date",
          is24Hour: true,
          display: "spinner",
          onChange: onChange,
          minimumDate: new Date(),
          maximumDate: (0, _moment.default)().add(1, "year").toDate(),
          textColor: _theme.color.palette.almostBlackGrey
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: bottomSheetLineSeparatorStyle
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            return onClosed(true);
          },
          testID: `${testID}__TouchableClose`,
          accessibilityLabel: `${accessibilityLabel}__TouchableClose`,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: bottomSheetOKTextStyle,
            tx: "flyDateFilter.ok"
          })
        })]
      }) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: visible && (0, _jsxRuntime.jsx)(_datetimepicker.default, {
          testID: "dateTimePicker",
          value: (dateChangeRef == null ? undefined : dateChangeRef.current) || date,
          mode: "date",
          is24Hour: true,
          display: "default",
          onChange: onChange,
          minimumDate: new Date(),
          maximumDate: (0, _moment.default)().add(1, "year").toDate()
        })
      })
    });
  };
  var _default = exports.default = _react.default.memo(FlyFiltersCalendar);
