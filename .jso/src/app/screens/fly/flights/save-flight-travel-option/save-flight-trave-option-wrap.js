  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _fly = _$$_REQUIRE(_dependencyMap[2]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[3]);
  var _saveFlightTravelOptionV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _saveFlightTravelOption = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SavedFlightTravelOptionsModal = function SavedFlightTravelOptionsModal(props) {
    var _useContext;
    var _useContext$Handlers = (_useContext = (0, _react.useContext)(_fly.FLY_CONTEXT)) == null ? undefined : _useContext.Handlers,
      flyDetailsFirstFlag = _useContext$Handlers.flyDetailsFirstFlag;
    var isFlightDetailsFirst = (0, _remoteConfig.isFlagOnCondition)(flyDetailsFirstFlag);
    if (!!props.visible) {
      _reactNative.Keyboard.dismiss();
    }
    if (isFlightDetailsFirst) return (0, _jsxRuntime.jsx)(_saveFlightTravelOptionV.default, Object.assign({}, props));
    return (0, _jsxRuntime.jsx)(_saveFlightTravelOption.default, Object.assign({}, props));
  };
  var _default = exports.default = SavedFlightTravelOptionsModal;
