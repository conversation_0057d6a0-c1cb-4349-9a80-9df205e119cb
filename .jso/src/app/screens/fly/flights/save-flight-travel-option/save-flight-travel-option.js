  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.outerRadioRingStyle = exports.innerRadioCircleStyle = exports.default = exports.TravelOption = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _radio = _$$_REQUIRE(_dependencyMap[7]);
  var _divider = _$$_REQUIRE(_dependencyMap[8]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _button = _$$_REQUIRE(_dependencyMap[11]);
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
    height = _Dimensions$get.height;
  var activeGradient = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var TravelOption = exports.TravelOption = /*#__PURE__*/function (TravelOption) {
    TravelOption["iAmTravelling"] = "dropdownSelectCard.imTravellingOnThisFlight";
    TravelOption["iAmPicking"] = "dropdownSelectCard.imDroppingOffSomeone";
    return TravelOption;
  }({});
  var SavedFlightTravelOptions = function SavedFlightTravelOptions(props) {
    var visible = props.visible,
      onClosed = props.onClosed,
      onBackPressed = props.onBackPressed,
      selectedOption = props.selectedOption,
      onPress = props.onPress,
      onModalHide = props.onModalHide,
      loadingSaveFlight = props.loadingSaveFlight,
      savedFlightOnPress = props.savedFlightOnPress,
      flightDirection = props.flightDirection;
    var renderSelectOption = function renderSelectOption() {
      if (flightDirection === _flightProps.FlightDirection.departure) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: TravelOption.iAmTravelling,
            isChecked: selectedOption === TravelOption.iAmTravelling,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmTravelling);
            },
            style: styles.travellingText,
            isLoadingToProcess: loadingSaveFlight
          }), (0, _jsxRuntime.jsx)(_divider.Divider, {}), (0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: TravelOption.iAmPicking,
            isChecked: selectedOption === TravelOption.iAmPicking,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmPicking);
            },
            style: styles.pickingText,
            isLoadingToProcess: loadingSaveFlight
          }), (0, _jsxRuntime.jsx)(_divider.Divider, {})]
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: "dropdownSelectCard.imPickingSomeone",
            isChecked: selectedOption === TravelOption.iAmPicking,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmPicking);
            },
            style: styles.travellingText,
            isLoadingToProcess: loadingSaveFlight
          }), (0, _jsxRuntime.jsx)(_divider.Divider, {}), (0, _jsxRuntime.jsx)(_radio.Radio, {
            tx: TravelOption.iAmTravelling,
            isChecked: selectedOption === TravelOption.iAmTravelling,
            onChecked: function onChecked() {
              return onPress(TravelOption.iAmTravelling);
            },
            style: styles.pickingText,
            isLoadingToProcess: loadingSaveFlight
          }), (0, _jsxRuntime.jsx)(_divider.Divider, {})]
        });
      }
    };
    return (0, _jsxRuntime.jsx)(_bottomSheet.default, {
      isModalVisible: visible,
      onModalHide: onModalHide,
      onClosedSheet: onClosed,
      containerStyle: styles.bottomSheetStyle,
      stopDragCollapse: true,
      onBackPressHandle: onBackPressed,
      animationInTiming: 500,
      animationOutTiming: 500,
      openPendingModal: true,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.modalContainer,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: styles.topModalContainer,
          onPress: onClosed
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.parentContainer,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.wrapHeaderBottomSheet,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "dropdownSelectCard.selectYourProfile",
              preset: "h2",
              style: styles.selectYourProfile
            })
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "dropdownSelectCard.descriptionSelectProfile",
            preset: "bodyTextBlackRegular",
            style: styles.descriptionHeader
          }), renderSelectOption(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.bottomContainer,
            children: loadingSaveFlight ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.loadingButtonStyles,
              children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
                source: _$$_REQUIRE(_dependencyMap[14]),
                autoPlay: true,
                loop: true,
                style: styles.lottieView
              })
            }) : (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
              style: styles.buttonLinearStyle,
              start: {
                x: 0,
                y: 1
              },
              end: {
                x: 1,
                y: 0
              },
              colors: activeGradient,
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                tx: "common.save",
                sizePreset: "large",
                textPreset: "buttonLarge",
                typePreset: "secondary",
                statePreset: "default",
                backgroundPreset: "light",
                onPress: savedFlightOnPress,
                textStyle: styles.textButtonStyleas
              })
            })
          })]
        })]
      })
    });
  };
  var outerRadioRingStyle = exports.outerRadioRingStyle = {
    height: 24,
    width: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: _theme.color.palette.lightPurple,
    alignItems: "center",
    justifyContent: "center"
  };
  var innerRadioCircleStyle = exports.innerRadioCircleStyle = {
    height: 12,
    width: 12,
    borderRadius: 6,
    backgroundColor: _theme.color.palette.lightPurple
  };
  var styles = _reactNative2.StyleSheet.create({
    bottomContainer: {
      alignItems: "center",
      justifyContent: "flex-end",
      marginBottom: 40
    },
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.transparent,
      height: height
    },
    buttonLinearStyle: {
      borderRadius: 60,
      marginTop: 16,
      width: "100%"
    },
    descriptionHeader: {
      marginBottom: 32,
      marginTop: 10,
      paddingHorizontal: 20,
      textAlign: "center"
    },
    loadingButtonStyles: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 60,
      height: 44,
      justifyContent: "center",
      marginTop: 16,
      width: "100%"
    },
    lottieView: {
      height: 24,
      width: '100%'
    },
    modalContainer: {
      flex: 1,
      justifyContent: "flex-end"
    },
    parentContainer: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: "auto",
      paddingHorizontal: 24
    },
    pickingText: {
      marginBottom: 16,
      marginTop: 16
    },
    selectYourProfile: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      paddingVertical: 21
    }),
    textButtonStyleas: {
      color: _theme.color.palette.almostWhiteGrey
    },
    topModalContainer: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1
    },
    travellingText: {
      marginBottom: 18
    },
    wrapHeaderBottomSheet: {
      alignItems: "center",
      justifyContent: "center"
    }
  });
  var _default = exports.default = SavedFlightTravelOptions;
