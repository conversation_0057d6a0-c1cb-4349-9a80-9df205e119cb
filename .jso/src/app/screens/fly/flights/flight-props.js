  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ScreenState = exports.RecordSource = exports.NavigationType = exports.FlyBenefitType = exports.FlightResultType = exports.FlightRequestType = exports.FlightParamerters = exports.FlightNavigationType = exports.FlightListingActionType = exports.FlightLandingContext = exports.FlightDirection = exports.FLIGHT_REFRESH_INTERVAL = exports.CustomerEligibility = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  var FlightDirection = exports.FlightDirection = /*#__PURE__*/function (FlightDirection) {
    FlightDirection["departure"] = "DEP";
    FlightDirection["arrival"] = "ARR";
    return FlightDirection;
  }(FlightDirection || {});
  var RecordSource = exports.RecordSource = /*#__PURE__*/function (RecordSource) {
    RecordSource["ichangi"] = "iChangi";
    return RecordSource;
  }(RecordSource || {});
  var FlightParamerters = exports.FlightParamerters = /*#__PURE__*/function (FlightParamerters) {
    FlightParamerters["filterLocations"] = "locations";
    return FlightParamerters;
  }(FlightParamerters || {});
  var NavigationType = exports.NavigationType = /*#__PURE__*/function (NavigationType) {
    NavigationType["FlightsLanding"] = "flightsLanding";
    NavigationType["FlightsResult"] = "flightsResult";
    return NavigationType;
  }(NavigationType || {});
  var FlightResultType = exports.FlightResultType = /*#__PURE__*/function (FlightResultType) {
    FlightResultType["FlightsDepatureResult"] = "departureResultScreen";
    FlightResultType["FlightsArrivalResult"] = "arrivalResultScreen";
    return FlightResultType;
  }(FlightResultType || {});
  var FlightNavigationType = exports.FlightNavigationType = /*#__PURE__*/function (FlightNavigationType) {
    FlightNavigationType["DepartureLanding"] = "departureLanding";
    FlightNavigationType["DepartureResults"] = "departureResults";
    FlightNavigationType["ArrivalLanding"] = "arrivalLanding";
    FlightNavigationType["ArrivalResults"] = "arrivalResults";
    FlightNavigationType["FlightAllSearch"] = "flightAllSearch";
    FlightNavigationType["FlightSearch"] = "FlightSearch";
    FlightNavigationType["ArrivalSearch"] = "arrivalSearch";
    FlightNavigationType["DepartureSearch"] = "departureSearch";
    FlightNavigationType["FLightSearchDetail"] = "fightSearchDetail";
    return FlightNavigationType;
  }(FlightNavigationType || {});
  var FlightRequestType = exports.FlightRequestType = /*#__PURE__*/function (FlightRequestType) {
    FlightRequestType["FlightDefault"] = "flightDefault";
    FlightRequestType["FlightRefresh"] = "flightRefresh";
    return FlightRequestType;
  }(FlightRequestType || {});
  var FlyBenefitType = exports.FlyBenefitType = /*#__PURE__*/function (FlyBenefitType) {
    FlyBenefitType["benefitType"] = "crt";
    return FlyBenefitType;
  }(FlyBenefitType || {});
  var CustomerEligibility = exports.CustomerEligibility = /*#__PURE__*/function (CustomerEligibility) {
    CustomerEligibility["FlyingDep"] = "flying_dep";
    CustomerEligibility["FlyingArr"] = "flying_arr";
    CustomerEligibility["Flying"] = "flying";
    CustomerEligibility["All"] = "all";
    return CustomerEligibility;
  }(CustomerEligibility || {});
  var FLIGHT_REFRESH_INTERVAL = exports.FLIGHT_REFRESH_INTERVAL = 30000;
  var ScreenState = exports.ScreenState = /*#__PURE__*/function (ScreenState) {
    ScreenState["focus"] = "focus";
    ScreenState["blur"] = "blur";
    return ScreenState;
  }(ScreenState || {});
  var FlightListingActionType = exports.FlightListingActionType = /*#__PURE__*/function (FlightListingActionType) {
    FlightListingActionType["KEYWORD_SEARCH"] = "KEYWORD_SEARCH";
    return FlightListingActionType;
  }(FlightListingActionType || {});
  var FlightLandingContext = exports.FlightLandingContext = (0, _react.createContext)({
    screenDirection: FlightDirection.arrival
  });
