  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _lottieReactNative = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _loading = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var FooterLoading = function FooterLoading(props) {
    var _props$lottieStyle = props.lottieStyle,
      lottieStyle = _props$lottieStyle === undefined ? {} : _props$lottieStyle,
      _props$containerStyle = props.containerStyle,
      containerStyle = _props$containerStyle === undefined ? {} : _props$containerStyle;
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: [styles.container, containerStyle],
      children: (0, _jsxRuntime.jsx)(_lottieReactNative.default, {
        style: [styles.lottieStyle, lottieStyle],
        source: _loading.default,
        autoPlay: true,
        loop: true
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center"
    },
    lottieStyle: {
      height: 60,
      width: '100%'
    }
  });
  var _default = exports.default = FooterLoading;
