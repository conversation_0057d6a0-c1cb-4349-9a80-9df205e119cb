  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.sectionsPayloadFlightListing = exports.filterPillListData = undefined;
  var _mask = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _mobile_gst = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _transit = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  // This is the temp payload of each sections

  var sectionsPayloadFlightListing = exports.sectionsPayloadFlightListing = {
    shortcutLinks: {
      default: [{
        title: "Mobile GST",
        label: "",
        imageImport: _mobile_gst.default
      }, {
        title: "Covid-19 Information",
        label: "",
        imageImport: _mask.default
      }, {
        title: "Transit Guide",
        label: "",
        imageImport: _transit.default
      }],
      loading: [{
        id: 0
      }, {
        id: 1
      }, {
        id: 2
      }, {
        id: 3
      }]
    },
    getFilterListFly: {
      data: {
        tagName: "locations",
        tagTitle: "Locations",
        childTags: [{
          tagName: "terminal-1",
          tagTitle: "Terminal 1"
        }, {
          tagName: "terminal-2",
          tagTitle: "Terminal 2"
        }, {
          tagName: "terminal-3",
          tagTitle: "Terminal 3"
        }, {
          tagName: "terminal-4",
          tagTitle: "Terminal 4"
        }]
      }
    }
  };
  var filterPillListData = exports.filterPillListData = [{
    tagTitle: "All",
    tagName: "all",
    tagCode: "all",
    isSelected: true
  }, {
    tagTitle: "Terminal 1",
    tagName: "terminal-1",
    tagCode: "t1",
    isSelected: false
  }, {
    tagTitle: "Terminal 2",
    tagName: "terminal-2",
    tagCode: "t2",
    isSelected: false
  }, {
    tagTitle: "Terminal 3",
    tagName: "terminal-3",
    tagCode: "t3",
    isSelected: false
  }, {
    tagTitle: "Terminal 4",
    tagName: "terminal-4",
    tagCode: "t4",
    isSelected: false
  }];
