  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _subscriptions = _$$_REQUIRE(_dependencyMap[7]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[8]);
  var _subscription = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _store = _$$_REQUIRE(_dependencyMap[11]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var mappingFLightData = function mappingFLightData(_ref) {
    var isExisted = _ref.isExisted,
      dataFlight = _ref.dataFlight,
      keyFlight = _ref.keyFlight,
      flightStatus = _ref.flightStatus,
      statusMapping = _ref.statusMapping,
      timeOfFlight = _ref.timeOfFlight,
      displayTimestamp = _ref.displayTimestamp;
    if (dataFlight) {
      var _statusMapping$status;
      isExisted[keyFlight] = true;
      dataFlight.flightStatus = flightStatus === "hide" ? "" : flightStatus;
      dataFlight.flightStatusMapping = statusMapping == null ? undefined : statusMapping.listing_status_en;
      dataFlight.statusColor = statusMapping == null || (_statusMapping$status = statusMapping.status_text_color) == null ? undefined : _statusMapping$status.toLowerCase();
      dataFlight.showGate = statusMapping == null ? undefined : statusMapping.show_gate;
      dataFlight.timeOfFlight = timeOfFlight;
      dataFlight.beltStatusMapping = statusMapping == null ? undefined : statusMapping.belt_status_en;
      dataFlight.displayTimestamp = displayTimestamp;
    }
  };
  var INTERVAL_UPDATE_FLIGHT = 5000;
  var FlyListSubscription = function FlyListSubscription(props) {
    var direction = props.direction,
      screen = props.screen,
      duplicateDirection = props.duplicateDirection;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(new Date().getTime()),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      toggleError = _useState2[0],
      setToggleError = _useState2[1];
    var subScriptionRef = (0, _react.useRef)(null);
    var appState = (0, _react.useRef)(_reactNative.AppState.currentState);
    var tempStoreFlight = (0, _react.useRef)({});
    var intervalId = (0, _react.useRef)(null);
    var checkFlightExisted = function checkFlightExisted(data, flyDepartureLanding, flyDepartureListing, flightArrivalLanding, flightArrivalListing) {
      var flyLandingDeparture = (0, _lodash.get)(flyDepartureLanding, "data", []);
      var flyListingDeparture = (0, _lodash.get)(flyDepartureListing, "payload", []);
      var flyLandingArrival = (0, _lodash.get)(flightArrivalLanding, "data", []);
      var flyListingArrival = (0, _lodash.get)(flightArrivalListing, "payload", []);
      var isExisted = {
        isExistedFlyLandingDeparture: false,
        isExistedFlyListingDeparture: false,
        isExistedFlyLandingArrival: false,
        isExistedFlyListingArrival: false
      };
      !(0, _lodash.isEmpty)(data) && (0, _lodash.forEach)(data, function (rawData) {
        var flightNumber = rawData.flight_number,
          direction = rawData.direction,
          flightStatus = rawData.flight_status,
          statusMapping = rawData.status_mapping,
          timeOfFlight = rawData.scheduled_time,
          scheduledDate = rawData.scheduled_date,
          displayTimestamp = rawData.display_timestamp;
        if (direction === _subscription.FlySubscriptionDirectionEnum.DEP) {
          !(0, _lodash.isEmpty)(flyLandingDeparture) && (0, _lodash.forEach)(flyLandingDeparture, function (_element, index) {
            var flyLandingDepartureData = (0, _lodash.get)(flyLandingDeparture, `${index}.flightLanding`);
            var existedFlyLandingDeparture = (0, _lodash.find)(flyLandingDepartureData, function (el) {
              var _el$statusColor, _statusMapping$status2;
              return el.flightNumber === flightNumber && el.flightDate === scheduledDate && ((el == null ? undefined : el.flightStatusMapping) !== (statusMapping == null ? undefined : statusMapping.listing_status_en) || (el == null || (_el$statusColor = el.statusColor) == null ? undefined : _el$statusColor.toLowerCase()) !== (statusMapping == null || (_statusMapping$status2 = statusMapping.status_text_color) == null ? undefined : _statusMapping$status2.toLowerCase()) || (el == null ? undefined : el.timeOfFlight) !== timeOfFlight || (el == null ? undefined : el.showGate) !== (statusMapping == null ? undefined : statusMapping.show_gate));
            });
            if (existedFlyLandingDeparture) {
              mappingFLightData({
                isExisted: isExisted,
                dataFlight: existedFlyLandingDeparture,
                keyFlight: "isExistedFlyLandingDeparture",
                flightStatus: flightStatus,
                statusMapping: statusMapping,
                timeOfFlight: timeOfFlight,
                displayTimestamp: displayTimestamp
              });
            } else {
              delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`];
            }
          });
          !(0, _lodash.isEmpty)(flyListingDeparture) && (0, _lodash.forEach)(flyListingDeparture, function (_element, index) {
            var flyListingDepartureData = (0, _lodash.get)(flyListingDeparture, `${index}.flightListingData`);
            var existedFlyListingDeparture = (0, _lodash.find)(flyListingDepartureData, function (el) {
              var _el$statusColor2, _statusMapping$status3;
              return el.flightNumber === flightNumber && el.flightDate === scheduledDate && ((el == null ? undefined : el.flightStatusMapping) !== (statusMapping == null ? undefined : statusMapping.listing_status_en) || (el == null || (_el$statusColor2 = el.statusColor) == null ? undefined : _el$statusColor2.toLowerCase()) !== (statusMapping == null || (_statusMapping$status3 = statusMapping.status_text_color) == null ? undefined : _statusMapping$status3.toLowerCase()) || (el == null ? undefined : el.timeOfFlight) !== timeOfFlight || (el == null ? undefined : el.showGate) !== (statusMapping == null ? undefined : statusMapping.show_gate));
            });
            if (existedFlyListingDeparture) {
              mappingFLightData({
                isExisted: isExisted,
                dataFlight: existedFlyListingDeparture,
                keyFlight: "isExistedFlyListingDeparture",
                flightStatus: flightStatus,
                statusMapping: statusMapping,
                timeOfFlight: timeOfFlight,
                displayTimestamp: displayTimestamp
              });
            } else {
              delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`];
            }
          });
        }
        if (direction === _subscription.FlySubscriptionDirectionEnum.ARR) {
          !(0, _lodash.isEmpty)(flyLandingArrival) && (0, _lodash.forEach)(flyLandingArrival, function (_element, index) {
            var flyLandingArrivalData = (0, _lodash.get)(flyLandingArrival, `${index}.flightLanding`);
            var existedFlyLandingArrival = (0, _lodash.find)(flyLandingArrivalData, function (el) {
              var _el$statusColor3, _statusMapping$status4;
              return el.flightNumber === flightNumber && el.flightDate === scheduledDate && ((el == null ? undefined : el.flightStatusMapping) !== (statusMapping == null ? undefined : statusMapping.listing_status_en) || (el == null || (_el$statusColor3 = el.statusColor) == null ? undefined : _el$statusColor3.toLowerCase()) !== (statusMapping == null || (_statusMapping$status4 = statusMapping.status_text_color) == null ? undefined : _statusMapping$status4.toLowerCase()) || (el == null ? undefined : el.timeOfFlight) !== timeOfFlight || (el == null ? undefined : el.showGate) !== (statusMapping == null ? undefined : statusMapping.show_gate) || (el == null ? undefined : el.beltStatusMapping) !== (statusMapping == null ? undefined : statusMapping.belt_status_en));
            });
            if (existedFlyLandingArrival) {
              mappingFLightData({
                isExisted: isExisted,
                dataFlight: existedFlyLandingArrival,
                keyFlight: "isExistedFlyLandingArrival",
                flightStatus: flightStatus,
                statusMapping: statusMapping,
                timeOfFlight: timeOfFlight,
                displayTimestamp: displayTimestamp
              });
            } else {
              delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`];
            }
          });
          !(0, _lodash.isEmpty)(flyListingArrival) && (0, _lodash.forEach)(flyListingArrival, function (_element, index) {
            var flyListingArrivalData = (0, _lodash.get)(flyListingArrival, `${index}.flightListingData`);
            var existedFlyListingArrival = (0, _lodash.find)(flyListingArrivalData, function (el) {
              var _el$statusColor4, _statusMapping$status5;
              return el.flightNumber === flightNumber && el.flightDate === scheduledDate && ((el == null ? undefined : el.flightStatusMapping) !== (statusMapping == null ? undefined : statusMapping.listing_status_en) || (el == null || (_el$statusColor4 = el.statusColor) == null ? undefined : _el$statusColor4.toLowerCase()) !== (statusMapping == null || (_statusMapping$status5 = statusMapping.status_text_color) == null ? undefined : _statusMapping$status5.toLowerCase()) || (el == null ? undefined : el.timeOfFlight) !== timeOfFlight || (el == null ? undefined : el.showGate) !== (statusMapping == null ? undefined : statusMapping.show_gate) || (el == null ? undefined : el.beltStatusMapping) !== (statusMapping == null ? undefined : statusMapping.belt_status_en));
            });
            if (existedFlyListingArrival) {
              mappingFLightData({
                isExisted: isExisted,
                dataFlight: existedFlyListingArrival,
                keyFlight: "isExistedFlyListingArrival",
                flightStatus: flightStatus,
                statusMapping: statusMapping,
                timeOfFlight: timeOfFlight,
                displayTimestamp: displayTimestamp
              });
            } else {
              delete tempStoreFlight.current[`${flightNumber}_${scheduledDate}`];
            }
          });
        }
      });
      var flyData = {};
      if (!(0, _lodash.isEmpty)(flyDepartureLanding == null ? undefined : flyDepartureLanding.data) && isExisted != null && isExisted.isExistedFlyLandingDeparture) {
        flyData.flyDeparturePayload = flyDepartureLanding;
      }
      if (!(0, _lodash.isEmpty)(flightArrivalLanding == null ? undefined : flightArrivalLanding.data) && isExisted != null && isExisted.isExistedFlyLandingArrival) {
        flyData.flyArrivalPayload = flightArrivalLanding;
      }
      if (!(0, _lodash.isEmpty)(flyDepartureListing == null ? undefined : flyDepartureListing.data) && isExisted != null && isExisted.isExistedFlyListingDeparture) {
        flyData.flyPayload = flyDepartureListing;
      }
      if (!(0, _lodash.isEmpty)(flightArrivalListing == null ? undefined : flightArrivalListing.data) && isExisted != null && isExisted.isExistedFlyListingArrival) {
        flyData.flyArrivalResultPayload = flightArrivalListing;
      }
      return Object.assign({}, flyData);
    };
    var updateDataToStore = function updateDataToStore() {
      var data = (0, _toConsumableArray2.default)((0, _lodash.values)(Object.assign({}, tempStoreFlight.current)));
      if (!(0, _lodash.isEmpty)(data)) {
        var _store$getState = _store.store.getState(),
          flyReducer = _store$getState.flyReducer;
        var flyDepartureLanding = flyReducer == null ? undefined : flyReducer.flyDeparturePayload;
        var flyDepartureListing = flyReducer == null ? undefined : flyReducer.flyPayload;
        var flightArrivalLanding = flyReducer == null ? undefined : flyReducer.flyArrivalPayload;
        var flightArrivalListing = flyReducer == null ? undefined : flyReducer.flyArrivalResultPayload;
        var updatedFlyData = checkFlightExisted(data, flyDepartureLanding, flyDepartureListing, flightArrivalLanding, flightArrivalListing);
        if (Object.keys(updatedFlyData).length > 0) {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            dispatch(_flyRedux.FlyCreators.flyUpdateBySubscription(Object.assign({
              flyUpdatedTimeBySubscription: new Date().getTime()
            }, updatedFlyData)));
          });
        }
      }
    };
    var setIntervalSubscription = function setIntervalSubscription() {
      if (intervalId.current) return;
      _reactNative.InteractionManager.runAfterInteractions(function () {
        intervalId.current = setInterval(function () {
          _reactNative.InteractionManager.runAfterInteractions(function () {
            updateDataToStore();
          });
        }, INTERVAL_UPDATE_FLIGHT);
      });
    };
    var clearIntervalSubscription = function clearIntervalSubscription() {
      clearInterval(intervalId.current);
      intervalId.current = null;
    };
    (0, _react.useEffect)(function () {
      setIntervalSubscription();
      var subscription = _reactNative.AppState.addEventListener("change", function (nextAppState) {
        if ((0, _utils.ifAllTrue)([appState.current.match(/active/), nextAppState === "inactive"])) {
          clearIntervalSubscription();
        }
        if ((0, _utils.ifAllTrue)([appState.current.match(/inactive|background/), nextAppState === "active"])) {
          setIntervalSubscription();
        }
        appState.current = nextAppState;
      });
      return function () {
        subscription.remove();
        clearIntervalSubscription();
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (screen === _subscription.FlySubscriptionScreenEnum.landing || screen === _subscription.FlySubscriptionScreenEnum.result && direction !== duplicateDirection) {
        subScriptionRef.current = _awsAmplify.API.graphql({
          query: direction === _subscription.FlySubscriptionDirectionEnum.DEP ? _subscriptions.flightDepUpdatesSubscription : _subscriptions.flightArrUpdatesSubscription
        }).subscribe({
          next: function () {
            var _next = (0, _asyncToGenerator2.default)(function* (res) {
              var _res$value, _res$value2;
              var data = direction === _subscription.FlySubscriptionDirectionEnum.DEP ? res == null || (_res$value = res.value) == null || (_res$value = _res$value.data) == null ? undefined : _res$value.flightDepUpdates : res == null || (_res$value2 = res.value) == null || (_res$value2 = _res$value2.data) == null ? undefined : _res$value2.flightArrUpdates;
              var transformResponse = (0, _lodash.reduce)(data, function (obj, param) {
                obj[`${param == null ? undefined : param.flight_number}_${param == null ? undefined : param.scheduled_date}`] = param;
                return obj;
              }, {});
              tempStoreFlight.current = (0, _lodash.merge)(tempStoreFlight.current, transformResponse);
            });
            function next(_x) {
              return _next.apply(this, arguments);
            }
            return next;
          }(),
          error: function error(_err) {
            setToggleError(new Date().getTime());
          }
        });
      }
      return function () {
        (subScriptionRef == null ? undefined : subScriptionRef.current) && (subScriptionRef == null ? undefined : subScriptionRef.current.unsubscribe());
      };
    }, [toggleError]);
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
  };
  var _default = exports.default = _react.default.memo(FlyListSubscription);
