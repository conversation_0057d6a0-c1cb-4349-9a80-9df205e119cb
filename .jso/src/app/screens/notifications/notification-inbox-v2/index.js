  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.NotificationInboxV2 = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[2]);
  var _styles = _$$_REQUIRE(_dependencyMap[3]);
  var _FilterListNoti = _$$_REQUIRE(_dependencyMap[4]);
  var _useFunction2 = _$$_REQUIRE(_dependencyMap[5]);
  var _listNotificationV = _$$_REQUIRE(_dependencyMap[6]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[7]);
  var _EmptyNoti = _$$_REQUIRE(_dependencyMap[8]);
  var _contentNoPermissionNoti = _$$_REQUIRE(_dependencyMap[9]);
  var _ErrorNoti = _$$_REQUIRE(_dependencyMap[10]);
  var _Loading = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var NotificationInboxV2 = exports.NotificationInboxV2 = _react.default.memo(function () {
    var filterCategoriesRef = (0, _react.useRef)([]);
    var _useFunction = (0, _useFunction2.useFunction)(),
      addCategory = _useFunction.addCategory,
      removeCategory = _useFunction.removeCategory,
      loading = _useFunction.loading,
      formatedNotificationPayload = _useFunction.formatedNotificationPayload,
      previousRef = _useFunction.previousRef,
      notificationError = _useFunction.notificationError,
      reloadData = _useFunction.reloadData,
      isShowNoInternetModel = _useFunction.isShowNoInternetModel,
      onDeleteNotificationItem = _useFunction.onDeleteNotificationItem,
      isNotificationsPermissionGranted = _useFunction.isNotificationsPermissionGranted;
    var renderContent = function renderContent() {
      if (loading) {
        return (0, _jsxRuntime.jsx)(_Loading.LoadingView, {});
      }
      if (notificationError) {
        return (0, _jsxRuntime.jsx)(_ErrorNoti.ErrorNoti, {
          reloadData: reloadData
        });
      }
      return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
        children: isShowNoInternetModel ? (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          visible: isShowNoInternetModel,
          onReload: reloadData,
          headerBackgroundColor: "transparent",
          noInternetOverlayStyle: _styles.styles.viewNoInternet
        }) : (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          data: [1],
          renderItem: function renderItem() {
            return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
              children: (formatedNotificationPayload == null ? undefined : formatedNotificationPayload.length) === 0 ? (0, _jsxRuntime.jsx)(_EmptyNoti.EmptyNoti, {
                isNotificationsPermissionGranted: isNotificationsPermissionGranted
              }) : (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
                children: formatedNotificationPayload == null ? undefined : formatedNotificationPayload.map(function (element, index) {
                  return (0, _jsxRuntime.jsx)(_listNotificationV.ListNotificationV2, {
                    index: index,
                    previousRef: previousRef,
                    notificationPayload: element,
                    filterCategoriesRef: filterCategoriesRef,
                    onDeleteNotificationItem: onDeleteNotificationItem,
                    formatedNotificationPayload: formatedNotificationPayload
                  }, index);
                })
              })
            });
          },
          keyExtractor: function keyExtractor(item) {
            return item == null ? undefined : item.toString();
          },
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: reloadData
          })
        })
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.container,
      children: [!isNotificationsPermissionGranted && (0, _jsxRuntime.jsx)(_contentNoPermissionNoti.NoPermissionNoti, {
        containerStyle: {
          marginTop: -4
        }
      }), (0, _jsxRuntime.jsx)(_FilterListNoti.FilterListNoti, {
        addCategory: addCategory,
        removeCategory: removeCategory,
        disable: loading,
        filterCategoriesRef: filterCategoriesRef
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.styles.viewContent,
        children: renderContent()
      })]
    });
  });
