  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.getSelectedCategoryLabels = undefined;
  var _i18n = _$$_REQUIRE(_dependencyMap[0]);
  var getSelectedCategoryLabels = exports.getSelectedCategoryLabels = function getSelectedCategoryLabels(filterCategories) {
    var activeCount = 0;
    var selectedLabels = "";
    filterCategories == null || filterCategories.forEach == null || filterCategories.forEach(function (item) {
      if (item != null && item.active) {
        activeCount++;
        var itemTitle = (0, _i18n.translate)(item == null ? undefined : item.title) || "";
        selectedLabels += !!selectedLabels ? `, ${itemTitle}` : itemTitle;
      }
    });
    if (!selectedLabels || activeCount === (filterCategories == null ? undefined : filterCategories.length)) {
      selectedLabels = "All";
    }
    return selectedLabels;
  };
