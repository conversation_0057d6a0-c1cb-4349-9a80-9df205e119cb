  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useFunction = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[11]);
  var _helper = _$$_REQUIRE(_dependencyMap[12]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[13]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactNativeSdk = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _deleteToastRef = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _validate = _$$_REQUIRE(_dependencyMap[17]);
  var _account = _$$_REQUIRE(_dependencyMap[18]);
  var _useAppStateChange2 = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[20]);
  var _adobe = _$$_REQUIRE(_dependencyMap[21]);
  var _i18n = _$$_REQUIRE(_dependencyMap[22]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var categoryForAdvisories = [_constants.NOTIFICATION_TYPES.ADVISORIES, _constants.NOTIFICATION_TYPES.L1_ADVISORIES, _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_ADVISORIES];
  var useFunction = exports.useFunction = function useFunction() {
    var previousRef = _react.default.useRef(null);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useAppStateChange = (0, _useAppStateChange2.default)(),
      appStateVisible = _useAppStateChange.appStateVisible;
    var notificationUserPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.listUserNotications);
    var notificationError = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.getUserNotificationsError);
    var deletedL1AdivoryIds = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.deletedL1AdivoryIds);
    var deletedAnnouncementIds = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.deletedAnnouncementIds);
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      l1AnnouncementFeatureFlag = _useContext.l1AnnouncementFeatureFlag,
      l2AnnouncementFeatureFlag = _useContext.l2AnnouncementFeatureFlag;
    var isL1AnnouncementFlagOn = (0, _remoteConfig.isFlagOnCondition)(l1AnnouncementFeatureFlag);
    var isL2AnnouncementFlagOn = (0, _remoteConfig.isFlagOnCondition)(l2AnnouncementFeatureFlag);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.NOTIFICATION_TAB_ALL),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance;
    var eventAndPromotionNotificationPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.eventAndPromotionNotificationPayload);
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      category = _useState2[0],
      setCategory = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      filterListCategory = _useState4[0],
      setFilterListCategory = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isShowNoInternetModel = _useState6[0],
      setIsShowNoInternetModel = _useState6[1];
    var _useState7 = (0, _react.useState)(true),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      loading = _useState8[0],
      setLoading = _useState8[1];
    var _useState9 = (0, _react.useState)(true),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      isNotificationsPermissionGranted = _useState0[0],
      setIsNotificationsPermissionGranted = _useState0[1];
    var _useState1 = (0, _react.useState)(false),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      isShowAdvisories = _useState10[0],
      setIsShowAdvisories = _useState10[1];
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setIsShowNoInternetModel(true);
          } else {}
        });
        return function checkInternet() {
          return _ref.apply(this, arguments);
        };
      }();
      checkInternet();
      return function () {
        setCategory([]);
        setFilterListCategory([]);
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (appStateVisible === _useAppStateChange2.APP_STATES.ACTIVE) {
        (0, _reactNativePermissions.checkNotifications)().then(function (result) {
          if ((result == null ? undefined : result.status) === _reactNativePermissions.RESULTS.GRANTED) {
            setIsNotificationsPermissionGranted(true);
          } else {
            setIsNotificationsPermissionGranted(false);
          }
        });
      }
    }, [appStateVisible]);
    var addValueToListFilter = function addValueToListFilter(value) {
      setFilterListCategory(filterListCategory == null ? undefined : filterListCategory.concat(value));
    };
    var removeValueToListFilter = function removeValueToListFilter(value) {
      var resultArray = filterListCategory.filter(function (item) {
        return !value.includes(item);
      });
      setFilterListCategory(resultArray);
    };
    var addCategory = (0, _react.useCallback)(function (dataAdd) {
      var dataToBeSent = `${_adobe.AdobeValueByTagName.CAppNotificationCenterFilter}${(0, _i18n.translate)(dataAdd == null ? undefined : dataAdd.title)}${_adobe.AdobeValueByTagName.CAppNotificationCenterSelected}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppNotificationCenter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppNotificationCenter, dataToBeSent));
      setLoading(true);
      if ((dataAdd == null ? undefined : dataAdd.title) === "notification.filterNotificationInboxV2.Advisories") {
        setIsShowAdvisories(true);
        setCategory(category == null ? undefined : category.concat(dataAdd == null ? undefined : dataAdd.dataFilter));
      } else {
        setCategory(category == null ? undefined : category.concat(dataAdd == null ? undefined : dataAdd.dataFilter));
      }
      addValueToListFilter(dataAdd == null ? undefined : dataAdd.dataFilter);
    }, [category, addValueToListFilter]);
    var removeCategory = (0, _react.useCallback)(function (dataRemove) {
      var dataToBeSent = `${_adobe.AdobeValueByTagName.CAppNotificationCenterFilter}${(0, _i18n.translate)(dataRemove == null ? undefined : dataRemove.title)}${_adobe.AdobeValueByTagName.CAppNotificationCenterUnselected}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppNotificationCenter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppNotificationCenter, dataToBeSent));
      setLoading(true);
      if ((dataRemove == null ? undefined : dataRemove.title) === "notification.filterNotificationInboxV2.Advisories") {
        setIsShowAdvisories(false);
        var resultArray = category.filter(function (item) {
          return !(dataRemove != null && dataRemove.dataFilter.includes(item));
        });
        setCategory(resultArray);
      } else {
        var _resultArray = category.filter(function (item) {
          return !(dataRemove != null && dataRemove.dataFilter.includes(item));
        });
        setCategory(_resultArray);
      }
      removeValueToListFilter(dataRemove == null ? undefined : dataRemove.dataFilter);
    }, [category, removeValueToListFilter]);
    (0, _react.useEffect)(function () {
      getDataListNoti();
    }, [category]);
    var getDataListNoti = (0, _react.useCallback)((0, _lodash.debounce)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
        isConnected = _yield$NetInfo$fetch2.isConnected;
      if (!isConnected) {
        setIsShowNoInternetModel(true);
        setLoading(false);
      } else {
        setIsShowNoInternetModel(false);
        if ((category == null ? undefined : category.length) === 0) {
          dispatch(_notificationRedux.default.getUserNotificationsRequest({
            category: ""
          }));
        } else {
          dispatch(_notificationRedux.default.getUserNotificationsRequest({
            categories: category
          }));
        }
      }
    }), 700), [category, _netinfo.default]);
    var formatNotificationData = function formatNotificationData(notificationPayload) {
      var _announcementNotifica, _announcementNotifica2;
      var filteredNotifications = [];
      var announcementNotifications = [];
      if (notificationPayload != null && notificationPayload.length) {
        var deletedAdvisoryIds = [].concat((0, _toConsumableArray2.default)(deletedL1AdivoryIds || []), (0, _toConsumableArray2.default)(deletedAnnouncementIds || []));
        var currentDateObj = (0, _moment.default)();
        for (var i = 0; i < notificationPayload.length; i++) {
          var element = notificationPayload[i];
          var isAdvisories = (element == null ? undefined : element.category) === _constants.NOTIFICATION_TYPES.ADVISORIES;
          var isL1Advisories = (element == null ? undefined : element.category) === _constants.NOTIFICATION_TYPES.L1_ADVISORIES;
          var isL2Announcement = (element == null ? undefined : element.category) === _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT;
          if (isAdvisories || isL1Advisories || isL2Announcement) {
            var isDeletedElement = deletedAdvisoryIds == null ? undefined : deletedAdvisoryIds.includes(element == null ? undefined : element.id);
            var startDateObj = element != null && element.createdDt ? (0, _moment.default)(element == null ? undefined : element.createdDt) : null;
            var endDateObj = element != null && element.expiredDt ? (0, _moment.default)(element == null ? undefined : element.expiredDt) : null;
            var isValidDateElement = currentDateObj.isSameOrAfter(startDateObj) && currentDateObj.isSameOrBefore(endDateObj);
            var shouldAddL1Element = isL1AnnouncementFlagOn && isL1Advisories;
            var shouldAddL2Element = isL2AnnouncementFlagOn && isL2Announcement;
            var shouldAddThisElement = isAdvisories || shouldAddL1Element || shouldAddL2Element;
            if (isValidDateElement && !isDeletedElement && shouldAddThisElement) {
              announcementNotifications = [].concat((0, _toConsumableArray2.default)(announcementNotifications), [element]);
            }
          } else {
            filteredNotifications = [].concat((0, _toConsumableArray2.default)(filteredNotifications), [element]);
          }
        }
      }
      var announcementNotificationItem = {
        notificationData: (_announcementNotifica = announcementNotifications) == null ? undefined : _announcementNotifica.sort(function (a, b) {
          var _b$extraJsonData, _a$extraJsonData;
          return new Date((b == null || (_b$extraJsonData = b.extraJsonData) == null ? undefined : _b$extraJsonData.startDate) || (b == null ? undefined : b.createdDt)).getTime() - new Date((a == null || (_a$extraJsonData = a.extraJsonData) == null ? undefined : _a$extraJsonData.startDate) || (a == null ? undefined : a.createdDt)).getTime();
        })
      };
      var announcementGroupItem = (_announcementNotifica2 = announcementNotifications) != null && _announcementNotifica2.length ? [announcementNotificationItem] : [];
      if (isShowMaintenance) {
        var _filteredNotification;
        var removedFlightAppscapadeNotifications = (0, _helper.dateGroup)((_filteredNotification = filteredNotifications) == null ? undefined : _filteredNotification.filter(function (ele) {
          return ![_constants.NOTIFICATION_TYPES.FLIGHTS, _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE].includes(ele == null ? undefined : ele.category);
        }), true) || [];
        return [].concat(announcementGroupItem, (0, _toConsumableArray2.default)(removedFlightAppscapadeNotifications));
      }
      if (isShowAdvisories && (category == null ? undefined : category.length) === (categoryForAdvisories == null ? undefined : categoryForAdvisories.length)) {
        var _announcementGroupIte, _groupNotiBrazeFilter;
        var groupNotiBraze = (_announcementGroupIte = announcementGroupItem[0]) == null || (_announcementGroupIte = _announcementGroupIte.notificationData) == null ? undefined : _announcementGroupIte.concat(eventAndPromotionNotificationPayload);
        var groupNotiBrazeFilter = [];
        if (Array.isArray(groupNotiBraze)) {
          groupNotiBrazeFilter = groupNotiBraze.filter(function (item) {
            return filterListCategory == null ? undefined : filterListCategory.includes(item == null ? undefined : item.category);
          });
        }
        var dataAnnouncementGroupItemSort = (0, _helper.dateGroup)(((_groupNotiBrazeFilter = groupNotiBrazeFilter) == null ? undefined : _groupNotiBrazeFilter.length) > 0 ? groupNotiBrazeFilter : [], true);
        return (0, _toConsumableArray2.default)(dataAnnouncementGroupItemSort);
      } else {
        var dataListNotiFilter = (filterListCategory == null ? undefined : filterListCategory.length) === 0 ? filteredNotifications : filteredNotifications.filter(function (item) {
          return filterListCategory == null ? undefined : filterListCategory.includes(item == null ? undefined : item.category);
        });
        var groupedByDateNotifications = (0, _helper.dateGroup)(dataListNotiFilter, true) || [];
        return (category == null ? undefined : category.length) === 0 || isShowAdvisories && (category == null ? undefined : category.length) > 0 ? [].concat(announcementGroupItem, (0, _toConsumableArray2.default)(groupedByDateNotifications)) : (0, _toConsumableArray2.default)(groupedByDateNotifications);
      }
    };
    var formatedNotificationPayload = formatNotificationData(notificationUserPayload == null ? undefined : notificationUserPayload.concat(eventAndPromotionNotificationPayload));
    (0, _react.useEffect)(function () {
      if (notificationError) {
        setLoading(false);
      }
    }, [notificationError]);
    var turnOffLoadingDebounce = (0, _react.useCallback)((0, _lodash.debounce)(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
      setLoading(false);
    }), 2000), []);
    (0, _react.useEffect)(function () {
      if (formatedNotificationPayload) {
        turnOffLoadingDebounce();
      }
    }, [formatedNotificationPayload]);
    var reloadData = function reloadData() {
      setLoading(true);
      getDataListNoti();
      _reactNativeSdk.default.requestContentCardsRefresh();
    };
    var onDeleteNotificationItem = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (_notificationItem) {
        var category = _notificationItem == null ? undefined : _notificationItem.category;
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (isConnected && !(0, _validate.isEmpty)(_notificationItem)) {
          if ((0, _helper.isNotiBraze)(category)) {
            _deleteToastRef.default.closeNowDeleteSingleToast();
            _reactNativeSdk.default.logContentCardDismissed(_notificationItem.id);
            setTimeout(function () {
              if ((0, _helper.isNotiBraze)(category)) {
                dispatch(_notificationRedux.default.deleteSingleNotificationBrazeSuccess(_notificationItem == null ? undefined : _notificationItem.id));
              } else {
                dispatch(_notificationRedux.default.deleteSingleNotificationSuccess(_notificationItem == null ? undefined : _notificationItem.id));
              }
            }, 1000);
          } else if ((_notificationItem == null ? undefined : _notificationItem.category) === _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT) {
            dispatch(_notificationRedux.default.deleteSingleAnnouncement(_notificationItem == null ? undefined : _notificationItem.id));
          } else if ((_notificationItem == null ? undefined : _notificationItem.category) === _constants.NOTIFICATION_TYPES.L1_ADVISORIES) {
            dispatch(_notificationRedux.default.deleteSingleL1Advisory(_notificationItem == null ? undefined : _notificationItem.id));
          } else {
            dispatch(_notificationRedux.default.deleteSingleNotificationRequest(_notificationItem == null ? undefined : _notificationItem.id));
          }
        } else {
          _deleteToastRef.default.showErrorToast();
        }
      });
      return function onDeleteNotificationItem(_x) {
        return _ref4.apply(this, arguments);
      };
    }();
    return {
      addCategory: addCategory,
      removeCategory: removeCategory,
      loading: loading,
      formatedNotificationPayload: formatedNotificationPayload,
      previousRef: previousRef,
      notificationError: notificationError,
      reloadData: reloadData,
      isShowNoInternetModel: isShowNoInternetModel,
      onDeleteNotificationItem: onDeleteNotificationItem,
      isNotificationsPermissionGranted: isNotificationsPermissionGranted
    };
  };
