  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ErrorNoti = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var ErrorNoti = exports.ErrorNoti = _react.default.memo(function (props) {
    var reloadData = props.reloadData;
    var errorData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var ehr11 = errorData == null ? undefined : errorData.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR1.1";
    });
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
        title: ehr11 == null ? undefined : ehr11.header,
        content: ehr11 == null ? undefined : ehr11.subHeader,
        style: {
          backgroundColor: "transparent"
        },
        onPress: reloadData
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 40,
      alignItems: 'center'
    }
  });
