  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EmptyNoti = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _text2 = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var EmptyNoti = exports.EmptyNoti = _react.default.memo(function (props) {
    var isNotificationsPermissionGranted = props.isNotificationsPermissionGranted;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [!isNotificationsPermissionGranted ? (0, _jsxRuntime.jsx)(_icons.EmptyNotificationNoPermission, {
        width: "180",
        height: "180"
      }) : (0, _jsxRuntime.jsx)(_icons.EmptyNotificationV2, {
        width: "180",
        height: "180"
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: !isNotificationsPermissionGranted ? 'notification.turnOffPermissionNotiTitle' : 'notification.emptyNotiTitleV2',
        style: styles.txtTitle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: !isNotificationsPermissionGranted ? 'notification.turnOffPermissionNotiContent' : 'notification.emptyNotiContentV2',
        style: styles.txtContent
      })]
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      flex: 1,
      paddingTop: 32,
      alignItems: 'center'
    },
    txtTitle: Object.assign({}, _text2.presets.h2, {
      marginVertical: 24
    }),
    txtContent: Object.assign({}, _text2.presets.caption1Regular, {
      textAlign: 'center',
      paddingHorizontal: 50
    })
  });
