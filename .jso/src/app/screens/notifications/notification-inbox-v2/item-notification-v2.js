  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _color = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _text = _$$_REQUIRE(_dependencyMap[11]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[12]);
  var _htmlRichtext = _$$_REQUIRE(_dependencyMap[13]);
  var _native = _$$_REQUIRE(_dependencyMap[14]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _constants = _$$_REQUIRE(_dependencyMap[17]);
  var _utils = _$$_REQUIRE(_dependencyMap[18]);
  var _i18n = _$$_REQUIRE(_dependencyMap[19]);
  var _adobe = _$$_REQUIRE(_dependencyMap[20]);
  var _helper = _$$_REQUIRE(_dependencyMap[21]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _alertApp = _$$_REQUIRE(_dependencyMap[23]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[24]);
  var _baggageTracker = _$$_REQUIRE(_dependencyMap[25]);
  var _notificationScreen = _$$_REQUIRE(_dependencyMap[26]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[27]);
  var _reactNativeGestureHandler = _$$_REQUIRE(_dependencyMap[28]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[29]);
  var _theme = _$$_REQUIRE(_dependencyMap[30]);
  var _notification = _$$_REQUIRE(_dependencyMap[31]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _helper2 = _$$_REQUIRE(_dependencyMap[33]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[34]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable @typescript-eslint/no-unused-vars */
  /* eslint-disable react-native/no-inline-styles */

  var InAppBrowserManager = _reactNative2.NativeModules.InAppBrowserManager;
  var listCategoryUrgent = ["Claim Declined", "CANCELLED", "GATE CLOSING", _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS];
  var isUrgent = function isUrgent(category) {
    return listCategoryUrgent == null ? undefined : listCategoryUrgent.includes(category);
  };
  var listCategoryNoTitle = [_constants.NOTIFICATION_TYPES.FLIGHTS, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS, _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE, _constants.NOTIFICATION_TYPES.RETRO_CLAIM, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_EPIC, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_SERVICES];
  var isGTTDItem24H = function isGTTDItem24H(payload) {
    var _payload$extraJsonDat;
    if (!payload) return false;
    if ((payload == null ? undefined : payload.source) === "GTTD" && !(0, _screenHelper.isFirstBagOnBelt)(payload == null || (_payload$extraJsonDat = payload.extraJsonData) == null ? undefined : _payload$extraJsonDat.flight_status)) {
      return true;
    }
    return false;
  };
  var ItemNotificationV2 = function ItemNotificationV2(props) {
    var _notificationItem$ext;
    var notificationItem = props == null ? undefined : props.data;
    var swipeRef = (0, _react.useRef)(null);
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHTS_APPSCAPADE"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var alertApp = (0, _react.useRef)(null);
    var BAGGAGE_HANDLERS = (0, _react.useContext)(_baggageTracker.BAGGAGE_TRACKER_CONTEXT).Handlers;
    var readL1AdivoryIds = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.readL1AdivoryIds);
    var readAnnouncementIds = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.readAnnouncementIds);
    var isL1AdvisoryType = (notificationItem == null ? undefined : notificationItem.category) === _constants.NOTIFICATION_TYPES.L1_ADVISORIES;
    var isL2AnnouncementType = (notificationItem == null ? undefined : notificationItem.category) === _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT;
    var isL1AdvisoryOrL2Announcement = isL1AdvisoryType || isL2AnnouncementType;
    var initialIsRead = isL1AdvisoryOrL2Announcement ? false : notificationItem == null ? undefined : notificationItem.isRead;
    var extraJsonData = notificationItem == null ? undefined : notificationItem.extraJsonData;
    var _useState = (0, _react.useState)(initialIsRead),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isRead = _useState2[0],
      setRead = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isSwipe = _useState4[0],
      setIsSwipe = _useState4[1];
    var titleNumberOfLines = notificationItem != null && notificationItem.title && isL1AdvisoryOrL2Announcement ? 2 : 1;
    var leftContentStyle = isL1AdvisoryOrL2Announcement ? Object.assign({}, styles.leftContent, {
      backgroundColor: null,
      paddingTop: notificationItem != null && notificationItem.title ? 24 : 0,
      borderWidth: 0
    }) : styles.leftContent;
    var shouldRenderByStartDate = (notificationItem == null ? undefined : notificationItem.category) === _constants.NOTIFICATION_TYPES.ADVISORIES || isL1AdvisoryOrL2Announcement;
    var renderNotificationDate = shouldRenderByStartDate ? (0, _screenHelper.formatDate)((notificationItem == null || (_notificationItem$ext = notificationItem.extraJsonData) == null ? undefined : _notificationItem$ext.startDate) || (notificationItem == null ? undefined : notificationItem.createdDt), "DD MMM") : (0, _screenHelper.formatDate)(notificationItem == null ? undefined : notificationItem.createdDt, "DD MMM");
    var handleGlobalNavigate = function handleGlobalNavigate() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      navigation.navigate.apply(navigation, (0, _toConsumableArray2.default)(args));
    };
    var handleSwipeableWillOpen = function handleSwipeableWillOpen() {
      setIsSwipe(true);
      if (props != null && props.previousRef && (props == null ? undefined : props.previousRef.current) !== null) {
        if ((props == null ? undefined : props.previousRef.current) !== swipeRef.current) {
          var _props$previousRef$cu;
          props == null || (_props$previousRef$cu = props.previousRef.current) == null || _props$previousRef$cu.close();
        }
      }
    };
    var handleOnSwipeableOpen = function handleOnSwipeableOpen(index) {
      props.previousRef.current = swipeRef.current;
    };
    var renderIconNotiBrazeMode = function renderIconNotiBrazeMode() {
      var _notificationItem$ext2;
      switch (notificationItem == null || (_notificationItem$ext2 = notificationItem.extras) == null ? undefined : _notificationItem$ext2.notification_mode) {
        case "parking_page":
          return (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: _icons.ParkingNotificationIconPNG,
            style: styles.iconNoti
          });
        case "epic_page":
          return (0, _jsxRuntime.jsx)(_icons.PerkTypeNotification, {});
        case "retro_claims_details":
          return (0, _jsxRuntime.jsx)(_icons.EventAndPromotionType, {});
        default:
          return (0, _jsxRuntime.jsx)(_icons.RetroClaimNotificationIcon, {});
      }
    };
    var handleIconForLeftSection = function handleIconForLeftSection(category) {
      var _notificationItem$url;
      var l2AnnouncementIcon = extraJsonData != null && extraJsonData.icon ? (0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _screenHelper.getUriImage)(extraJsonData == null ? undefined : extraJsonData.icon)
        },
        style: styles.l2AnnouncementIcon
      }) : (0, _jsxRuntime.jsx)(_icons.AnnouncementBell, {});
      var iconNotiBraze = notificationItem != null && (_notificationItem$url = notificationItem.url) != null && _notificationItem$url.startsWith(_constants.APP_DEEPLINKS == null ? undefined : _constants.APP_DEEPLINKS.EPICPAGE_PROD) ? (0, _jsxRuntime.jsx)(_icons.PerkTypeNotification, {}) : renderIconNotiBrazeMode();
      var isGTTD24h = isGTTDItem24H(notificationItem);
      if (isGTTD24h) {
        return (0, _jsxRuntime.jsx)(_icons.GTTDType, {});
      }
      switch (category) {
        case _constants.NOTIFICATION_TYPES.FLIGHTS:
          if ((notificationItem == null ? undefined : notificationItem.source) === "PBE") {
            return (0, _jsxRuntime.jsx)(_icons.BaggageNotificationIcon, {});
          } else {
            return (0, _jsxRuntime.jsx)(_icons.FlightType, {});
          }
        case _constants.NOTIFICATION_TYPES.ADVISORIES:
          return (0, _jsxRuntime.jsx)(_icons.AdvisoriesType, {});
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_EPIC:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_SERVICES:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_TRAVEL:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_ADVISORIES:
          return iconNotiBraze;
        case _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_PERK:
          return (0, _jsxRuntime.jsx)(_icons.PerkTypeNotification, {});
        case _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK:
          return (0, _jsxRuntime.jsx)(_icons.PerkTypeNotification, {});
        case _constants.NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING:
          return (0, _jsxRuntime.jsx)(_icons.PerkTypeNotification, {});
        case _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS:
        case _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS:
          return (0, _jsxRuntime.jsx)(_icons.PerkTypeNotification, {});
        case _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE:
          return (0, _jsxRuntime.jsx)(_icons.AppscapadeType, {});
        case _constants.NOTIFICATION_TYPES.L1_ADVISORIES:
        case _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT:
          return l2AnnouncementIcon;
        case _constants.NOTIFICATION_TYPES.RETRO_CLAIM:
          return (0, _jsxRuntime.jsx)(_icons.RetroClaimNotificationIcon, {});
        default:
          return (0, _jsxRuntime.jsx)(_icons.FlightType, {});
      }
    };
    var handleTrackingNotificatiOnPress = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (prefixText) {
        var _props$filterCategori;
        var selectedCategoryLabels = (0, _helper2.getSelectedCategoryLabels)(props == null || (_props$filterCategori = props.filterCategoriesRef) == null ? undefined : _props$filterCategori.current);
        var titleValue = (0, _utils.simpleCondition)({
          condition: !_lodash.default.isEmpty(notificationItem == null ? undefined : notificationItem.label),
          ifValue: `| ${notificationItem == null ? undefined : notificationItem.label}`,
          elseValue: ""
        });
        var subTitleValue = (0, _utils.simpleCondition)({
          condition: (0, _utils.ifAllTrue)([!_lodash.default.isEmpty(notificationItem == null ? undefined : notificationItem.title), !listCategoryNoTitle.includes(notificationItem == null ? undefined : notificationItem.category)]),
          ifValue: ` | ${notificationItem == null ? undefined : notificationItem.title}`,
          elseValue: ""
        });
        var UID = yield (0, _screenHelper.getViewerUID)({
          shouldReturnNull: true
        });
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppNotificationCenter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppNotificationCenter, `${prefixText} | ${selectedCategoryLabels} ${titleValue}${subTitleValue} | ${UID}`));
      });
      return function handleTrackingNotificatiOnPress(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var handleOpenFlightDetail = function handleOpenFlightDetail(data) {
      var scheduleDate = data == null ? undefined : data.scheduled_date;
      var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
      var flightTime = (0, _momentTimezone.default)(scheduleDate, "YYYY-MM-DD", "Asia/Singapore");
      var diffDay = currentTimeToUTC.diff(flightTime, "days");
      if (diffDay >= 3) {
        var _alertApp$current;
        alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
          title: (0, _i18n.translate)("expiryFlightNotification.title"),
          description: (0, _i18n.translate)("expiryFlightNotification.message"),
          labelAccept: (0, _i18n.translate)("expiryFlightNotification.buttonText"),
          onAccept: function onAccept() {
            return null;
          },
          type: _alertApp2.AlertTypes.ALERT
        });
        return;
      }
      handleGlobalNavigate("flightDetails", {
        payload: {
          item: {
            flightNumber: data == null ? undefined : data.flight_number,
            arlineCode: data == null ? undefined : data.airline_code,
            flightDate: data == null ? undefined : data.scheduled_date,
            flightUniqueId: data == null ? undefined : data.flight_unique_id,
            direction: data == null ? undefined : data.direction
          }
        },
        direction: data == null ? undefined : data.direction
      });
    };
    var handleNavigationForGTTDItem = function handleNavigationForGTTDItem() {
      var _ref2 = extraJsonData || "",
        navigation_type = _ref2.navigation_type,
        navigation_value = _ref2.navigation_value,
        redirect = _ref2.redirect;
      if (navigation_type && navigation_value) {
        if (navigation_type === _navigationType.NavigationTypeEnum.inApp && navigation_value === "flightDetails") {
          handleOpenFlightDetail(extraJsonData);
        } else {
          handleNavigation(navigation_type, navigation_value, redirect);
        }
      }
    };
    var handleOnPressNotiBraze = function handleOnPressNotiBraze() {
      setRead(true);
      if (notificationItem != null && notificationItem.url.startsWith("cagichangi://")) {
        var _getQueryParams = (0, _utils.getQueryParams)(notificationItem == null ? undefined : notificationItem.url),
          event_notification_link = _getQueryParams.event_notification_link;
        if (event_notification_link) {
          handleGlobalNavigate(_constants.NavigationConstants.webview, {
            uri: (0, _utils.convertToURLFormat)(event_notification_link),
            screen: "notificationScreen"
          });
        } else {
          _reactNative2.Linking.openURL(notificationItem == null ? undefined : notificationItem.url);
        }
      } else if (notificationItem != null && notificationItem.url.startsWith("https://cagapp3.page.link") || notificationItem != null && notificationItem.url.startsWith("https://ichangi3.page.link")) {
        if (_reactNative2.Platform.OS === "ios") {
          InAppBrowserManager.openUniversalLinkInApp(notificationItem == null ? undefined : notificationItem.url);
        } else {
          _reactNative2.Linking.openURL(notificationItem == null ? undefined : notificationItem.url);
        }
      } else if ((0, _helper.isUrlValid)(notificationItem == null ? undefined : notificationItem.url)) {
        handleGlobalNavigate(_constants.NavigationConstants.webview, {
          uri: notificationItem == null ? undefined : notificationItem.url,
          screen: "notificationScreen"
        });
      } else {
        handleGlobalNavigate(notificationItem == null ? undefined : notificationItem.url);
      }
      (0, _helper.handlePressEventAndPromotion)(notificationItem == null ? undefined : notificationItem.id, dispatch);
    };
    var handlePressCard = function handlePressCard() {
      handleTrackingNotificatiOnPress("Notification");
      switch (notificationItem == null ? undefined : notificationItem.category) {
        case _constants.NOTIFICATION_TYPES.FLIGHTS:
          setRead(true);
          dispatch(_notificationRedux.default.markNotificationAsReadRequest(notificationItem == null ? undefined : notificationItem.id));
          if (extraJsonData != null && extraJsonData.navigationFirst) {
            var _extraJsonData$naviga, _extraJsonData$naviga2;
            handleNavigation(extraJsonData == null || (_extraJsonData$naviga = extraJsonData.navigationFirst) == null ? undefined : _extraJsonData$naviga.type, _lodash.default.toLower(extraJsonData == null || (_extraJsonData$naviga2 = extraJsonData.navigationFirst) == null ? undefined : _extraJsonData$naviga2.value));
          } else if ((notificationItem == null ? undefined : notificationItem.source) === "PBE") {
            (0, _notificationScreen.sendAATrackingNotification)(_notificationScreen.NotificationValueForAA.flight, _notificationScreen.NotificationValueForAA.personalisedBaggageExperience);
            BAGGAGE_HANDLERS.baggage_tracker_request({
              navigation: navigation,
              messageID: extraJsonData == null ? undefined : extraJsonData.messageID
            });
          } else if ((notificationItem == null ? undefined : notificationItem.source) === "GTTD") {
            handleNavigationForGTTDItem();
          } else {
            (0, _notificationScreen.sendAATrackingNotification)(_notificationScreen.NotificationValueForAA.flight, extraJsonData == null ? undefined : extraJsonData.flight_number);
            handleOpenFlightDetail(extraJsonData);
          }
          break;
        case _constants.NOTIFICATION_TYPES.ADVISORIES:
          setRead(true);
          if (extraJsonData != null && extraJsonData.navigationFirst) {
            var _extraJsonData$naviga3, _extraJsonData$naviga4;
            handleNavigation(extraJsonData == null || (_extraJsonData$naviga3 = extraJsonData.navigationFirst) == null ? undefined : _extraJsonData$naviga3.type, _lodash.default.toLower(extraJsonData == null || (_extraJsonData$naviga4 = extraJsonData.navigationFirst) == null ? undefined : _extraJsonData$naviga4.value));
          } else {
            handleGlobalNavigate(_constants.NavigationConstants.webview, {
              uri: (0, _helper.isUrlValid)(extraJsonData == null ? undefined : extraJsonData.detailPage) ? extraJsonData == null ? undefined : extraJsonData.detailPage : (0, _utils.mappingUrlAem)(extraJsonData == null ? undefined : extraJsonData.detailPage),
              screen: "notificationScreen",
              useAATracking: _notificationScreen.NotificationValueForAA.advisories
            });
          }
          if (!(notificationItem != null && notificationItem.isRead)) {
            dispatch(_notificationRedux.default.markNotificationAsReadRequest(notificationItem == null ? undefined : notificationItem.id));
          }
          break;
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_EPIC:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_SERVICES:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_TRAVEL:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_ADVISORIES:
          handleOnPressNotiBraze();
          break;
        case _constants.NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING:
          {
            var bookingKey = (0, _notification.generateNotificationBookingKey)(extraJsonData);
            setRead(true);
            handleGlobalNavigate(_constants.NavigationConstants.playPassBookingDetail, {
              bookingKey: bookingKey,
              disableGesture: true
            });
            if (!(notificationItem != null && notificationItem.isRead)) {
              dispatch(_notificationRedux.default.markNotificationAsReadRequest(notificationItem == null ? undefined : notificationItem.id));
            }
            break;
          }
        case _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK:
        case _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_PERK:
          setRead(true);
          (0, _notificationScreen.sendAATrackingNotification)(_notificationScreen.NotificationValueForAA.playpass, _notificationScreen.NotificationValueForAA.perks);
          handleGlobalNavigate(_constants.NavigationConstants.redemptionCatalogueScreen, {
            screen: _constants.NavigationConstants.perksTab
          });
          if (!(notificationItem != null && notificationItem.isRead)) {
            dispatch(_notificationRedux.default.markNotificationAsReadRequest(notificationItem == null ? undefined : notificationItem.id));
          }
          break;
        case _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS:
        case _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS:
          setRead(true);
          (0, _notificationScreen.sendAATrackingNotification)(_notificationScreen.NotificationValueForAA.playpass, _notificationScreen.NotificationValueForAA.credits);
          handleGlobalNavigate(_constants.NavigationConstants.creditsScreen);
          if (!(notificationItem != null && notificationItem.isRead)) {
            dispatch(_notificationRedux.default.markNotificationAsReadRequest(notificationItem == null ? undefined : notificationItem.id));
          }
          break;
        case _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE:
          {
            setRead(true);
            handleNavigation("deep-link", "appscapade");
            if (!(notificationItem != null && notificationItem.isRead)) {
              dispatch(_notificationRedux.default.markNotificationAsReadRequest(notificationItem == null ? undefined : notificationItem.id));
            }
            break;
          }
        case _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT:
          handleGlobalNavigate(_constants.NavigationConstants.l2AnnouncementDetails, {
            notificationItem: notificationItem
          });
          if (!(readAnnouncementIds != null && readAnnouncementIds.includes != null && readAnnouncementIds.includes(notificationItem == null ? undefined : notificationItem.id))) {
            setRead(true);
            dispatch(_notificationRedux.default.markAnnouncementAsRead(notificationItem == null ? undefined : notificationItem.id));
          }
          break;
        case _constants.NOTIFICATION_TYPES.RETRO_CLAIM:
          handleGlobalNavigate(_constants.NavigationConstants.retroClaimsNotifications, {
            data: notificationItem == null ? undefined : notificationItem.extras
          });
          (0, _helper.handlePressEventAndPromotion)(notificationItem == null ? undefined : notificationItem.id, dispatch);
          break;
        case _constants.NOTIFICATION_TYPES.L1_ADVISORIES:
          handleGlobalNavigate(_constants.NavigationConstants.l1AdvisoryDetails, {
            notificationItem: notificationItem,
            isFromInbox: true
          });
          if (!(readL1AdivoryIds != null && readL1AdivoryIds.includes != null && readL1AdivoryIds.includes(notificationItem == null ? undefined : notificationItem.id))) {
            setRead(true);
            dispatch(_notificationRedux.default.markL1AdvisoryAsRead(notificationItem == null ? undefined : notificationItem.id));
          }
          break;
        default:
          break;
      }
    };
    var showDot = function showDot() {
      var dotStyle = isL1AdvisoryOrL2Announcement ? Object.assign({}, styles.dot, {
        width: 12,
        height: 12,
        left: 1,
        top: notificationItem != null && notificationItem.title ? 13 : 1
      }) : styles.dot;
      var readAAIds = [].concat((0, _toConsumableArray2.default)(readAnnouncementIds || []), (0, _toConsumableArray2.default)(readL1AdivoryIds || []));
      var isReadNotification = isL1AdvisoryOrL2Announcement ? readAAIds == null || readAAIds.includes == null ? undefined : readAAIds.includes(notificationItem == null ? undefined : notificationItem.id) : notificationItem == null ? undefined : notificationItem.isRead;
      if (isReadNotification || isRead) {
        return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: dotStyle,
        testID: `BadgeUnReadNotification_${props == null ? undefined : props.index}`,
        accessibilityLabel: `BadgeUnReadNotification_${props == null ? undefined : props.index}`
      });
    };
    var onClickDeleteItem = function onClickDeleteItem() {
      handleTrackingNotificatiOnPress("Delete One");
      swipeRef.current.close();
      props == null || props.onDeleteNotificationItem(notificationItem);
    };
    var _renderRightActions = function renderRightActions(_progress, _dragX, onClickDeleteItem) {
      var _props$testID = props.testID,
        testID = _props$testID === undefined ? "ItemNotification" : _props$testID,
        _props$accessibilityL = props.accessibilityLabel,
        accessibilityLabel = _props$accessibilityL === undefined ? "ItemNotification" : _props$accessibilityL;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: onClickDeleteItem,
        style: styles.wrapRightAction,
        testID: `${testID}__TouchableDelete`,
        accessibilityLabel: `${accessibilityLabel}__TouchableDelete`,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.rightAction,
          children: [(0, _jsxRuntime.jsx)(_icons.DeleteRed, {}), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Bold",
            style: {
              color: _color.color.palette.baseRed
            },
            children: "Delete"
          })]
        })
      });
    };
    var handleDescriptionForFlightItem = function handleDescriptionForFlightItem() {
      switch (notificationItem == null ? undefined : notificationItem.category) {
        case _constants.NOTIFICATION_TYPES.FLIGHTS:
          if (!_lodash.default.isEmpty(notificationItem == null ? undefined : notificationItem.message)) {
            return (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
              style: styles.descriptionStyles,
              value: `<p>${notificationItem == null ? undefined : notificationItem.message}</p>`,
              numberOfLines: 3
            });
          }
          break;
        case _constants.NOTIFICATION_TYPES.ADVISORIES:
          return (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: (0, _helper.formatTextDescriptionAdvisories)(notificationItem == null ? undefined : notificationItem.message),
            style: styles.descriptionStyles,
            numberOfLines: 3
          });
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_EPIC:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_SERVICES:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_TRAVEL:
        case _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_ADVISORIES:
          return (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: notificationItem == null ? undefined : notificationItem.message,
            style: styles.descriptionStyles,
            numberOfLines: 2
          });
        case _constants.NOTIFICATION_TYPES.L1_ADVISORIES:
        case _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT:
          if (!_lodash.default.isEmpty(notificationItem == null ? undefined : notificationItem.message)) {
            return (0, _jsxRuntime.jsx)(_htmlRichtext.HtmlRichtext, {
              style: styles.descriptionStyles,
              value: `<p>${notificationItem == null ? undefined : notificationItem.message}</p>`,
              numberOfLines: 2
            });
          }
          break;
        default:
          return (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            text: notificationItem == null ? undefined : notificationItem.message,
            style: styles.descriptionStyles,
            numberOfLines: 3
          });
      }
    };
    return (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.GestureHandlerRootView, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        onPress: handlePressCard,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.backgroundCard,
          children: (0, _jsxRuntime.jsx)(_reactNativeGestureHandler.Swipeable, {
            ref: swipeRef,
            renderRightActions: function renderRightActions(progress, dragX) {
              return _renderRightActions(progress, dragX, onClickDeleteItem);
            },
            onSwipeableWillOpen: function onSwipeableWillOpen() {
              return handleSwipeableWillOpen();
            },
            onSwipeableWillClose: function onSwipeableWillClose() {
              return setIsSwipe(false);
            },
            onSwipeableOpen: function onSwipeableOpen() {
              return handleOnSwipeableOpen(props == null ? undefined : props.index);
            },
            children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: isSwipe ? styles.wrapItemActive : styles.wrapItem,
              children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: styles.wrapLeftContent,
                children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: leftContentStyle,
                  children: handleIconForLeftSection(notificationItem == null ? undefined : notificationItem.category)
                }), showDot()]
              }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: styles.wrapRightContent,
                children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
                  style: styles.rowFlexSpaceBetween,
                  children: [(0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption2Bold",
                    style: Object.assign({
                      color: isUrgent(notificationItem == null ? undefined : notificationItem.label) || isUrgent(notificationItem == null ? undefined : notificationItem.category) ? _color.color.palette.baseRed : _color.color.palette.lightPurple
                    }, styles.categoryStyles),
                    numberOfLines: 1,
                    children: !_lodash.default.isEmpty(notificationItem == null ? undefined : notificationItem.label) && (notificationItem == null ? undefined : notificationItem.label)
                  }), (0, _jsxRuntime.jsx)(_text.Text, {
                    preset: "caption2Bold",
                    style: styles.dateStyles,
                    children: renderNotificationDate
                  })]
                }), !_lodash.default.isEmpty(notificationItem == null ? undefined : notificationItem.title) && !listCategoryNoTitle.includes(notificationItem == null ? undefined : notificationItem.category) && (0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "bodyTextBold",
                  style: styles.titleStyles,
                  numberOfLines: titleNumberOfLines,
                  children: notificationItem == null ? undefined : notificationItem.title
                }), handleDescriptionForFlightItem()]
              })]
            })
          })
        }), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
          ref: alertApp
        })]
      })
    });
  };
  var commonStyleText = {
    fontFamily: "Lato",
    fontStyle: "normal"
  };
  var styles = _reactNative2.StyleSheet.create({
    backgroundCard: {
      backgroundColor: _color.color.palette.lightestRed
    },
    categoryStyles: Object.assign({
      width: "85%"
    }, commonStyleText, {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      textTransform: "uppercase"
    }),
    dateStyles: Object.assign({
      color: _color.color.palette.midGrey
    }, commonStyleText, {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: 'normal'
      })
    }),
    descriptionStyles: Object.assign({}, _text.presets.caption1Regular, {
      color: _color.color.palette.almostBlackGrey,
      marginTop: 4
    }),
    dot: {
      alignItems: "center",
      backgroundColor: _color.color.palette.lightPurple,
      borderColor: _color.color.palette.whiteGrey,
      borderRadius: 7,
      borderWidth: 2,
      height: 14,
      justifyContent: "center",
      left: -3,
      overflow: "hidden",
      position: "absolute",
      top: -2,
      width: 14
    },
    leftContent: {
      alignItems: "center",
      borderRadius: 12,
      height: 40,
      justifyContent: "center",
      width: 40,
      borderWidth: 1,
      borderColor: _color.color.palette.lighterGrey
    },
    l2AnnouncementIcon: {
      width: 48,
      height: 48,
      resizeMode: "contain"
    },
    rightAction: {
      alignItems: "center"
    },
    rowFlexSpaceBetween: {
      alignItems: "flex-start",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    titleStyles: Object.assign({
      color: _color.color.palette.almostBlackGrey,
      marginTop: 4
    }, commonStyleText, {
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: 'normal'
      })
    }),
    wrapItem: {
      backgroundColor: _color.color.palette.almostWhiteGrey,
      flexDirection: "row",
      justifyContent: "center",
      paddingBottom: 14,
      paddingLeft: 18,
      paddingRight: 24,
      paddingTop: 10
    },
    wrapItemActive: {
      backgroundColor: _color.color.palette.almostWhiteGrey,
      borderBottomRightRadius: 16,
      borderTopRightRadius: 16,
      flexDirection: "row",
      justifyContent: "center",
      paddingBottom: 14,
      paddingLeft: 16,
      paddingRight: 24,
      paddingTop: 10
    },
    wrapLeftContent: {
      width: "18%"
    },
    wrapRightAction: {
      alignItems: "center",
      backgroundColor: _color.color.palette.lightestRed,
      justifyContent: "center",
      margin: 0,
      paddingHorizontal: 20,
      width: 90
    },
    wrapRightContent: {
      width: "82%"
    },
    iconNoti: {
      width: 20,
      height: 20
    }
  });
  var _default = exports.default = ItemNotificationV2;
