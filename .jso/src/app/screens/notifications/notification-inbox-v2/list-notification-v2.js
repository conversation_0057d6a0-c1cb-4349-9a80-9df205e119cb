  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ListNotificationV2 = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _itemNotificationV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  /* eslint-disable @typescript-eslint/no-empty-function */

  var COMPONENT_NAME = "ListNotification";
  var ListHeader = function ListHeader(_ref) {
    var header = _ref.header;
    if (!header) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.listHeaderEmpty
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.wrapHeaderList,
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.wrapTextDate,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: styles.textDate,
          children: header
        })
      })
    });
  };
  var ListNotificationV2 = exports.ListNotificationV2 = function ListNotificationV2(props) {
    var notificationPayload = props.notificationPayload,
      onDeleteNotificationItem = props.onDeleteNotificationItem,
      filterCategoriesRef = props.filterCategoriesRef;
    return (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
      data: notificationPayload == null ? undefined : notificationPayload.notificationData,
      showsVerticalScrollIndicator: false,
      renderItem: function renderItem(_ref2) {
        var item = _ref2.item,
          index = _ref2.index;
        return (0, _jsxRuntime.jsx)(_itemNotificationV.default, {
          data: item,
          index: index,
          previousRef: props == null ? undefined : props.previousRef,
          filterCategoriesRef: filterCategoriesRef,
          onDeleteNotificationItem: onDeleteNotificationItem,
          testID: `${COMPONENT_NAME}__ItemNotification__${index}`,
          accessibilityLabel: `${COMPONENT_NAME}__ItemNotification__${index}`
        });
      },
      keyExtractor: function keyExtractor(_, index) {
        return `flat_list_item_notification_${index}`;
      },
      ListHeaderComponent: (0, _jsxRuntime.jsx)(ListHeader, {
        header: notificationPayload == null ? undefined : notificationPayload.dateNotification
      }),
      contentContainerStyle: styles.containerList
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerList: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    textDate: {
      color: _theme.color.palette.darkestGrey,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      })
    },
    listHeaderEmpty: {
      marginTop: 24
    },
    wrapHeaderList: {
      paddingHorizontal: 16,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    wrapTextDate: {
      marginTop: 24
    },
    viewEmpty: {
      flex: 1
    }
  });
