  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.FilterListNoti = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _gestureHandler = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FilterListNoti = exports.FilterListNoti = _react.default.memo(function (props) {
    var addCategory = props.addCategory,
      removeCategory = props.removeCategory,
      disable = props.disable,
      filterCategoriesRef = props.filterCategoriesRef;
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      filter = _useState2[0],
      setFilter = _useState2[1];
    var onClickFilter = (0, _react.useCallback)(function (index) {
      var filterValue = (0, _toConsumableArray2.default)(filter);
      if (filterValue[index].active === false) {
        addCategory(filterValue[index]);
      } else {
        removeCategory(filterValue[index]);
      }
      filterValue[index].active = !filter[index].active;
      filterCategoriesRef.current = filterValue;
      setFilter(filterValue);
    }, [filter]);
    var clearFilter = (0, _react.useCallback)(function () {
      setFilter([{
        id: 1,
        title: "notification.filterNotificationInboxV2.Advisories",
        active: false,
        dataFilter: [_constants.NOTIFICATION_TYPES.ADVISORIES, _constants.NOTIFICATION_TYPES.L1_ADVISORIES, _constants.NOTIFICATION_TYPES.L2_ANNOUNCEMENT, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_ADVISORIES]
      }, {
        id: 2,
        title: "notification.filterNotificationInboxV2.Travel",
        active: false,
        dataFilter: [_constants.NOTIFICATION_TYPES.FLIGHTS, _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_TRAVEL]
      }, {
        id: 3,
        title: "notification.filterNotificationInboxV2.Marketing",
        active: false,
        dataFilter: [_constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS]
      }, {
        id: 4,
        title: "notification.filterNotificationInboxV2.Services",
        active: false,
        dataFilter: [_constants.NOTIFICATION_TYPES.RETRO_CLAIM, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_EPIC, _constants.NOTIFICATION_TYPES.EVENTS_PROMOTIONS_SERVICES]
      }]);
    }, [_constants.NOTIFICATION_TYPES]);
    (0, _react.useEffect)(function () {
      clearFilter();
      return function () {
        clearFilter();
      };
    }, []);
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
        horizontal: true,
        showsHorizontalScrollIndicator: false,
        children: (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.viewRow,
          children: filter == null ? undefined : filter.map(function (item, index) {
            return (0, _jsxRuntime.jsxs)(_gestureHandler.TouchableOpacity, {
              style: item != null && item.active ? styles.viewItemActive : styles.viewItemInActive,
              onPress: function onPress() {
                return onClickFilter(index);
              },
              disabled: disable,
              children: [(0, _jsxRuntime.jsx)(_text.Text, {
                preset: "bodyTextBold",
                tx: item == null ? undefined : item.title,
                style: item != null && item.active ? styles.txtActive : styles.txtInActive
              }), (item == null ? undefined : item.active) && (0, _jsxRuntime.jsx)(_icons.CloseFilterMultipleNoti, {
                width: "8",
                height: "8"
              })]
            }, item == null ? undefined : item.id);
          })
        })
      })
    });
  });
  var styles = _reactNative.StyleSheet.create({
    container: {
      width: '100%',
      marginTop: 14,
      paddingBottom: 8,
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    viewRow: {
      flex: 1,
      flexDirection: 'row',
      paddingHorizontal: 8
    },
    viewItemInActive: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      marginLeft: 8,
      borderColor: _theme.color.palette.lighterGrey
    },
    viewItemActive: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 10,
      paddingVertical: 6,
      borderRadius: 99,
      borderWidth: 1,
      marginLeft: 8,
      borderColor: _theme.color.palette.purpleD5BBEA,
      backgroundColor: _theme.color.palette.lightestPurple
    },
    txtInActive: {
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.darkestGrey,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      })
    },
    txtActive: {
      marginRight: 6,
      fontSize: 14,
      lineHeight: 18,
      color: _theme.color.palette.lightPurple,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      })
    }
  });
