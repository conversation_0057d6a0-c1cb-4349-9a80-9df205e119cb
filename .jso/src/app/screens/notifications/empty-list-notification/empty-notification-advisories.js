  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EmptyNotificationAdvisories = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _enum = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var TypeNotifications = /*#__PURE__*/function (TypeNotifications) {
    TypeNotifications["onNotification"] = "onNotification";
    TypeNotifications["offNotification"] = "offNotification";
    return TypeNotifications;
  }(TypeNotifications || {});
  var COMPONENT_NAME = "EmptyNotificationAdvisories";
  var EmptyNotificationAdvisories = exports.EmptyNotificationAdvisories = function EmptyNotificationAdvisories(_ref) {
    var pressAction = _ref.pressAction;
    var settingNotificationPayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.settingNotificationPayload);
    var isOnNotification = settingNotificationPayload == null ? undefined : settingNotificationPayload[_enum.SettingNotificationID.EVENT_STAY_VILIGANT];
    var templateConfig = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, TypeNotifications.onNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationAdvisories, {}),
      title: (0, _i18n.translate)("emptyNotification.advisories.title"),
      description: (0, _i18n.translate)("emptyNotification.advisories.on_notification.description"),
      firstAction: "",
      firstActionPress: false
    }), TypeNotifications.offNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationAdvisoriesOff, {}),
      title: (0, _i18n.translate)("emptyNotification.advisories.title"),
      description: (0, _i18n.translate)("emptyNotification.advisories.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.advisories.off_notification.turnOnNotificationForAdvisories"),
      firstActionPress: pressAction
    });
    var configScreen = templateConfig[isOnNotification ? TypeNotifications.onNotification : TypeNotifications.offNotification];
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: configScreen == null ? undefined : configScreen.icon,
      title: configScreen == null ? undefined : configScreen.title,
      description: configScreen == null ? undefined : configScreen.description,
      testID: `${COMPONENT_NAME}__EmptyNotificationAdvisories`,
      accessibilityLabel: `${COMPONENT_NAME}__EmptyNotificationAdvisories`,
      firstAction: configScreen == null ? undefined : configScreen.firstAction,
      firstActionPress: configScreen == null ? undefined : configScreen.firstActionPress
    });
  };
