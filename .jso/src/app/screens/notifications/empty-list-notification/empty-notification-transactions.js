  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EmptyNotificationTransactions = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var COMPONENT_NAME = "EmptyNotificationTransactions";
  var EmptyNotificationTransactions = exports.EmptyNotificationTransactions = function EmptyNotificationTransactions() {
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationTransactions, {}),
      title: (0, _i18n.translate)("emptyNotification.transactions.title"),
      description: (0, _i18n.translate)("emptyNotification.transactions.on_notification.description"),
      testID: `${COMPONENT_NAME}__EmptyNotificationTransactions`,
      accessibilityLabel: `${COMPONENT_NAME}__EmptyNotificationTransactions`
    });
  };
