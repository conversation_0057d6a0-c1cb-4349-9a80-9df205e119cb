  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EmptyNotificationEventPromotions = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _enum = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var TypeNotifications = /*#__PURE__*/function (TypeNotifications) {
    TypeNotifications["onNotification"] = "onNotification";
    TypeNotifications["offNotification"] = "offNotification";
    return TypeNotifications;
  }(TypeNotifications || {});
  var isNotificationOn = function isNotificationOn(configNotification) {
    return (configNotification == null ? undefined : configNotification[_enum.SettingNotificationID.EVENT_SHOPPING_DEALS]) && (configNotification == null ? undefined : configNotification[_enum.SettingNotificationID.EVENT_FANDB_PROMOTIONS]) && (configNotification == null ? undefined : configNotification[_enum.SettingNotificationID.EVENT_LATEST_EVENTS]) && (configNotification == null ? undefined : configNotification[_enum.SettingNotificationID.EVENT_CHANGI_REWARDS_MEMBER_EXCLUSIVES]);
  };
  var COMPONENT_NAME = "EmptyNotificationAll";
  var EmptyNotificationEventPromotions = exports.EmptyNotificationEventPromotions = function EmptyNotificationEventPromotions() {
    var navigation = (0, _native.useNavigation)();
    var pressFirstAction = function pressFirstAction() {
      navigation.navigate(_constants.NavigationConstants.settingNotificationsScreen);
    };
    var pressOpenExploreChangi = function pressOpenExploreChangi() {
      navigation == null || navigation.navigate(_constants.NavigationConstants.explore, {
        isScrollToExploreChangiSection: true
      });
    };
    var settingNotificationPayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.settingNotificationPayload);
    var isOnNotification = isNotificationOn(settingNotificationPayload);
    var templateConfig = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, TypeNotifications.onNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationEventPromotion, {}),
      title: (0, _i18n.translate)("emptyNotification.eventPromotions.title"),
      description: (0, _i18n.translate)("emptyNotification.eventPromotions.on_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.eventPromotions.on_notification.exploreOurAttractions"),
      firstActionPress: pressOpenExploreChangi,
      secondAction: "",
      secondActionPress: function secondActionPress() {
        return null;
      }
    }), TypeNotifications.offNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationEventPromotionOff, {}),
      title: (0, _i18n.translate)("emptyNotification.eventPromotions.title"),
      description: (0, _i18n.translate)("emptyNotification.eventPromotions.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.eventPromotions.off_notification.turnOnNotificationForEventPromotions"),
      firstActionPress: pressFirstAction,
      secondAction: (0, _i18n.translate)("emptyNotification.eventPromotions.off_notification.exploreOurAttractions"),
      secondActionPress: pressOpenExploreChangi
    });
    var configScreen = templateConfig[isOnNotification ? TypeNotifications.onNotification : TypeNotifications.offNotification];
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: configScreen == null ? undefined : configScreen.icon,
      title: configScreen == null ? undefined : configScreen.title,
      description: configScreen == null ? undefined : configScreen.description,
      testID: `${COMPONENT_NAME}__EmptyNotificationAdvisories`,
      accessibilityLabel: `${COMPONENT_NAME}__EmptyNotificationAdvisories`,
      firstAction: configScreen == null ? undefined : configScreen.firstAction,
      firstActionPress: configScreen == null ? undefined : configScreen.firstActionPress,
      secondAction: configScreen == null ? undefined : configScreen.secondAction,
      secondActionPress: configScreen == null ? undefined : configScreen.secondActionPress
    });
  };
