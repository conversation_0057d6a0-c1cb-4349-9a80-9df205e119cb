  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.stylesFirstAction = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var stylesFirstAction = exports.stylesFirstAction = _reactNative.StyleSheet.create({
    emptyNotificationEventPromotions: {
      paddingHorizontal: 51
    },
    emptyNotificationFlights: {
      paddingHorizontal: 86
    },
    turnOffNotificationAdvisories: {
      paddingHorizontal: 30
    },
    turnOffNotificationAll: {
      paddingHorizontal: 60
    },
    turnOffNotificationEventPerks: {
      width: 328
    },
    turnOffNotificationEventPromotions: {
      paddingHorizontal: 17
    },
    turnOffNotificationFlights: {
      paddingHorizontal: 35
    }
  });
