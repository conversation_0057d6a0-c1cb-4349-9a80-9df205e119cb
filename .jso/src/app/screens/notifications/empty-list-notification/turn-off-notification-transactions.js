  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TurnOffNotificationTransactions = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var COMPONENT_NAME = "TurnOffNotificationTransactions";
  var TurnOffNotificationTransactions = exports.TurnOffNotificationTransactions = function TurnOffNotificationTransactions() {
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationTransactionsOff, {}),
      title: (0, _i18n.translate)("emptyNotification.transactions.title"),
      description: (0, _i18n.translate)("emptyNotification.transactions.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.transactions.off_notification.turnOnNotificationForTransactions"),
      testID: `${COMPONENT_NAME}__TurnOffNotificationTransactions`,
      accessibilityLabel: `${COMPONENT_NAME}__TurnOffNotificationTransactions`
    });
  };
