  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EmptyNotificationEventPerks = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[4]);
  var _i18n = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _enum = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  var TypeNotifications = /*#__PURE__*/function (TypeNotifications) {
    TypeNotifications["onNotification"] = "onNotification";
    TypeNotifications["offNotification"] = "offNotification";
    return TypeNotifications;
  }(TypeNotifications || {});
  var isNotificationOn = function isNotificationOn(configNotification) {
    return (configNotification == null ? undefined : configNotification[_enum.SettingNotificationID.EVENT_PERKS]) && (configNotification == null ? undefined : configNotification[_enum.SettingNotificationID.EVENT_REMINDER]);
  };
  var COMPONENT_NAME = "EmptyNotificationAll";
  var EmptyNotificationEventPerks = exports.EmptyNotificationEventPerks = function EmptyNotificationEventPerks() {
    var navigation = (0, _native.useNavigation)();
    var pressFirstAction = function pressFirstAction() {
      navigation.navigate(_constants.NavigationConstants.settingNotificationsScreen);
    };
    var pressOpenExploreChangi = function pressOpenExploreChangi() {
      navigation == null || navigation.navigate(_constants.NavigationConstants.explore, {
        isScrollToExploreChangiSection: true
      });
    };
    var settingNotificationPayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.settingNotificationPayload);
    var isOnNotification = isNotificationOn(settingNotificationPayload);
    var templateConfig = (0, _defineProperty2.default)((0, _defineProperty2.default)({}, TypeNotifications.onNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationEventPromotion, {}),
      title: (0, _i18n.translate)("emptyNotification.eventPerks.title"),
      description: (0, _i18n.translate)("emptyNotification.eventPerks.on_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.eventPerks.on_notification.exploreOurAttractions"),
      firstActionPress: pressOpenExploreChangi,
      firstActionStyles: null,
      secondAction: "",
      secondActionPress: function secondActionPress() {
        return null;
      }
    }), TypeNotifications.offNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationEventPromotionOff, {}),
      title: (0, _i18n.translate)("emptyNotification.eventPerks.title"),
      description: (0, _i18n.translate)("emptyNotification.eventPerks.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.eventPerks.off_notification.turnOnNotification"),
      firstActionPress: pressFirstAction,
      firstActionStyles: {
        width: 328
      },
      secondAction: (0, _i18n.translate)("emptyNotification.eventPerks.off_notification.exploreOurAttractions"),
      secondActionPress: pressOpenExploreChangi
    });
    var configScreen = templateConfig[isOnNotification ? TypeNotifications.onNotification : TypeNotifications.offNotification];
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: configScreen == null ? undefined : configScreen.icon,
      title: configScreen == null ? undefined : configScreen.title,
      description: configScreen == null ? undefined : configScreen.description,
      testID: `${COMPONENT_NAME}__EmptyNotificationPerks`,
      accessibilityLabel: `${COMPONENT_NAME}__EmptyNotificationPerks`,
      firstAction: configScreen == null ? undefined : configScreen.firstAction,
      firstActionStyles: configScreen == null ? undefined : configScreen.firstActionStyles,
      firstActionPress: configScreen == null ? undefined : configScreen.firstActionPress,
      secondAction: configScreen == null ? undefined : configScreen.secondAction,
      secondActionPress: configScreen == null ? undefined : configScreen.secondActionPress
    });
  };
