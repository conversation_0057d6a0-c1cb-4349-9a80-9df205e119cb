  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TurnOffNotificationFlights = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _constants = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var COMPONENT_NAME = "TurnOffNotificationFlights";
  var TurnOffNotificationFlights = exports.TurnOffNotificationFlights = function TurnOffNotificationFlights() {
    var navigation = (0, _native.useNavigation)();
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationFlightOff, {}),
      title: (0, _i18n.translate)("emptyNotification.flights.title"),
      description: (0, _i18n.translate)("emptyNotification.flights.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.flights.off_notification.turnOnNotificationForFlight"),
      firstActionStyles: _styles.stylesFirstAction.turnOffNotificationFlights,
      secondAction: (0, _i18n.translate)("emptyNotification.flights.off_notification.searchFlights"),
      firstActionPress: function firstActionPress() {
        return navigation.navigate(_constants.NavigationConstants.settingNotificationsScreen);
      },
      secondActionPress: function secondActionPress() {
        return navigation.navigate(_constants.NavigationConstants.search, {
          screen: _searchIndex.SearchIndex.flights
        });
      },
      testID: `${COMPONENT_NAME}__TurnOffNotificationFlights`,
      accessibilityLabel: `${COMPONENT_NAME}__TurnOffNotificationFlights`
    });
  };
