  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EmptyNotificationAll = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  var COMPONENT_NAME = "EmptyNotificationAll";
  var EmptyNotificationAll = exports.EmptyNotificationAll = function EmptyNotificationAll() {
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationList, {}),
      title: (0, _i18n.translate)("emptyNotification.all.title"),
      description: (0, _i18n.translate)("emptyNotification.all.on_notification.description"),
      testID: `${COMPONENT_NAME}__EmptyNotificationAll`,
      accessibilityLabel: `${COMPONENT_NAME}__EmptyNotificationAll`
    });
  };
