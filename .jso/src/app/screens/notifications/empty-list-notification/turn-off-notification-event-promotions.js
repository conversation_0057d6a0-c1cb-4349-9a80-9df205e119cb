  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TurnOffNotificationEventPromotions = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _constants = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _styles = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var COMPONENT_NAME = "TurnOffNotificationEventPromotions";
  var TurnOffNotificationEventPromotions = exports.TurnOffNotificationEventPromotions = function TurnOffNotificationEventPromotions() {
    var navigation = (0, _native.useNavigation)();
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationEventPromotionOff, {}),
      title: (0, _i18n.translate)("emptyNotification.eventPromotions.title"),
      description: (0, _i18n.translate)("emptyNotification.eventPromotions.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.eventPromotions.off_notification.turnOnNotificationForEventPromotions"),
      secondAction: (0, _i18n.translate)("emptyNotification.eventPromotions.off_notification.exploreOurAttractions"),
      firstActionStyles: _styles.stylesFirstAction.turnOffNotificationEventPromotions,
      firstActionPress: function firstActionPress() {
        return navigation.navigate(_constants.NavigationConstants.settingNotificationsScreen);
      },
      secondActionPress: function secondActionPress() {
        return navigation.navigate(_constants.NavigationConstants.explore, {
          isScrollToExploreChangiSection: true
        });
      },
      testID: `${COMPONENT_NAME}__TurnOffNotificationEventPromotions`,
      accessibilityLabel: `${COMPONENT_NAME}__TurnOffNotificationEventPromotions`
    });
  };
