  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.TurnOffNotificationAdvisories = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _icons = _$$_REQUIRE(_dependencyMap[2]);
  var _emptyNotification = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  var _native = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _styles = _$$_REQUIRE(_dependencyMap[7]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[8]);
  var COMPONENT_NAME = "TurnOffNotificationAdvisories";
  var TurnOffNotificationAdvisories = exports.TurnOffNotificationAdvisories = function TurnOffNotificationAdvisories() {
    var navigation = (0, _native.useNavigation)();
    return (0, _jsxRuntime.jsx)(_emptyNotification.EmptyNotification, {
      icon: (0, _jsxRuntime.jsx)(_icons.EmptyNotificationAdvisoriesOff, {}),
      title: (0, _i18n.translate)("emptyNotification.advisories.title"),
      description: (0, _i18n.translate)("emptyNotification.advisories.off_notification.description"),
      firstAction: (0, _i18n.translate)("emptyNotification.advisories.off_notification.turnOnNotificationForAdvisories"),
      firstActionStyles: _styles.stylesFirstAction.turnOffNotificationAdvisories,
      testID: `${COMPONENT_NAME}__TurnOffNotificationAdvisories`,
      accessibilityLabel: `${COMPONENT_NAME}__TurnOffNotificationAdvisories`,
      firstActionPress: function firstActionPress() {
        return navigation.navigate(_constants.NavigationConstants.settingNotificationsScreen);
      }
    });
  };
