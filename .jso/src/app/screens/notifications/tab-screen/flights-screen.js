  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.LIST_FLIGHT_NOTIFICATION_STATUS = exports.FlightNotificationScreen = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _listNotification = _$$_REQUIRE(_dependencyMap[6]);
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _styles = _$$_REQUIRE(_dependencyMap[10]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[13]);
  var _validate = _$$_REQUIRE(_dependencyMap[14]);
  var _emptyListNotification = _$$_REQUIRE(_dependencyMap[15]);
  var _helper = _$$_REQUIRE(_dependencyMap[16]);
  var _loadingScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _constants = _$$_REQUIRE(_dependencyMap[20]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[21]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _lodash = _$$_REQUIRE(_dependencyMap[23]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[24]);
  var _deleteToastRef = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[26]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[27]);
  var _errorCloud = _$$_REQUIRE(_dependencyMap[28]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[29]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var FlightNotificationScreen = exports.FlightNotificationScreen = function FlightNotificationScreen() {
    var _dateGroup;
    var isFocused = (0, _native.useIsFocused)();
    var previousRef = _react.default.useRef(null);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("FLIGHT_NOTIFICATION"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var navigation = (0, _native.useNavigation)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var listUserNotications = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.listUserNotications);
    var flightNotificationLoading = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.getUserNotificationsLoading);
    var flightNotificationsInitializing = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.userNotificationsInitializing);
    var settingNotificationPayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.settingNotificationPayload);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.NOTIFICATION_TAB_FLIGHTS),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      errorData = _useTickerbandMaintan.errorData,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance;
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.screenName === _constants.screenTagName.flights;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var listFlightNotification = (0, _react.useMemo)(function () {
      if ((listUserNotications == null ? undefined : listUserNotications.length) === 0) return [];
      return listUserNotications == null ? undefined : listUserNotications.filter(function (item) {
        return [_constants.NOTIFICATION_TYPES.FLIGHTS, _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE].includes(item.category);
      });
    }, [listUserNotications]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("NotificationList_FLights");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("NotificationList_FLights", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    var fetchDataNotificationFlight = function fetchDataNotificationFlight() {
      dispatch(_notificationRedux.default.getUserNotificationsRequest({
        categories: [_constants.NOTIFICATION_TYPES.FLIGHTS, _constants.NOTIFICATION_TYPES.FLIGHTS_APPSCAPADE]
      }));
    };
    var checkInternetAndFetch = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          fetchDataNotificationFlight();
        }
      });
      return function checkInternetAndFetch() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (isFocused) {
        checkInternetAndFetch();
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      var unsubscribe = navigation.addListener("blur", function () {
        dispatch(_notificationRedux.default.resetUserNotificationsList());
      });
      return unsubscribe;
    }, [navigation]);
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref3.apply(this, arguments);
      };
    }();
    if (!(0, _validate.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        testID: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
        accessibilityLabel: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
        onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch3.isConnected;
          if (isConnected) {
            setNoConnection(false);
            fetchDataNotificationFlight();
          }
        }),
        noInternetOverlayStyle: _styles.commonTabStyles.overlayStyle
      });
    }
    if (flightNotificationsInitializing && !flightNotificationLoading) {
      return null;
    }
    if (!(0, _validate.isEmpty)(settingNotificationPayload) && !flightNotificationLoading && (0, _helper.isShowDisabledEmptyState)(settingNotificationPayload, LIST_FLIGHT_NOTIFICATION_STATUS, listFlightNotification) && !(listFlightNotification != null && listFlightNotification.length)) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.commonTabStyles.container,
        children: (0, _jsxRuntime.jsx)(_emptyListNotification.TurnOffNotificationFlights, {})
      });
    }
    if (!flightNotificationLoading && !(listFlightNotification != null && listFlightNotification.length)) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.commonTabStyles.container,
        children: (0, _jsxRuntime.jsx)(_emptyListNotification.EmptyNotificationFlights, {})
      });
    }
    if (isShowMaintenance) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.commonTabStyles.container,
        children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
          skipStatusbar: true,
          style: {
            backgroundColor: "transparent"
          },
          titleStyle: {
            marginTop: 16
          },
          buttonStyle: {
            width: "auto"
          },
          errorData: errorData,
          onPress: fetchTickerbandMaintanance
        })
      });
    }
    var handleDeleteNotificationItem = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (_notificationItem) {
        var _yield$NetInfo$fetch4 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch4.isConnected;
        if (isConnected && !(0, _validate.isEmpty)(_notificationItem)) {
          dispatch(_notificationRedux.default.deleteSingleNotificationRequest(_notificationItem == null ? undefined : _notificationItem.id));
        } else {
          _deleteToastRef.default.showErrorToast();
        }
      });
      return function handleDeleteNotificationItem(_x3) {
        return _ref5.apply(this, arguments);
      };
    }();
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _styles.commonTabStyles.container,
      children: flightNotificationLoading ? (0, _loadingScreen.default)() : (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: function onRefresh() {
            return fetchDataNotificationFlight();
          }
        }),
        showsVerticalScrollIndicator: false,
        children: !!(listFlightNotification != null && listFlightNotification.length) && ((_dateGroup = (0, _helper.dateGroup)(listFlightNotification)) == null ? undefined : _dateGroup.map(function (element, index) {
          return (0, _jsxRuntime.jsx)(_listNotification.ListNotification, {
            index: index,
            previousRef: previousRef,
            notificationPayload: element,
            onDeleteNotificationItem: handleDeleteNotificationItem
          }, index);
        }))
      })
    });
  };
  var COMPONENT_NAME = "FlightNotificationScreen";
  var LIST_FLIGHT_NOTIFICATION_STATUS = exports.LIST_FLIGHT_NOTIFICATION_STATUS = ["FLIGHT_BOARDING", "FLIGHT_CONFIRMED", "FLIGHT_GATE_CLOSTING", "FLIGHT_LANDED", "FLIGHT_STATUS_UPDATE"];
