  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var LoadingView = function LoadingView() {
    var _ref;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: stylesLoading.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: stylesLoading.wrapTextDate,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextBold",
          style: stylesLoading.textDate,
          children: "Today"
        })
      }), (_ref = [1, 2, 3, 4, 5]) == null ? undefined : _ref.map(function (element) {
        return (0, _jsxRuntime.jsx)(ElementLoading, {}, element);
      })]
    });
  };
  var _default = exports.default = LoadingView;
  var ElementLoading = function ElementLoading() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: stylesLoading.content,
      children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
        style: stylesLoading.leftContent,
        children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: stylesLoading.icon
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: stylesLoading.rightContent,
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: Object.assign({}, stylesLoading.commonSection, stylesLoading.sectionOne)
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: Object.assign({}, stylesLoading.commonSection, stylesLoading.sectionTwo)
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: Object.assign({}, stylesLoading.commonSection, stylesLoading.sectionThree)
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: Object.assign({}, stylesLoading.commonSection, stylesLoading.sectionFour)
        })]
      })]
    });
  };
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var stylesLoading = _reactNative.StyleSheet.create({
    commonSection: {
      borderRadius: 4,
      height: 13,
      marginBottom: 8,
      marginTop: 3
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingHorizontal: 16
    },
    content: {
      alignItems: "flex-start",
      flexDirection: "row",
      marginBottom: 28
    },
    icon: {
      borderRadius: 12,
      height: 48,
      width: 48
    },
    leftContent: {
      marginRight: 22,
      width: "13%"
    },
    rightContent: {
      width: "87%"
    },
    sectionFour: {
      width: 160
    },
    sectionOne: {
      width: 35
    },
    sectionThree: {
      width: 239
    },
    sectionTwo: {
      width: 265
    },
    textDate: {
      color: _theme.color.palette.darkestGrey,
      fontFamily: _theme.typography.bold,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      })
    },
    wrapTextDate: {
      marginBottom: 12,
      marginTop: 24
    }
  });
