  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var CartToastRemoveNotification = function CartToastRemoveNotification(props) {
    var toastRef = props.toastRef,
      title = props.title,
      testID = props.testID,
      accessibilityLabel = props.accessibilityLabel;
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastRef,
        style: styles.feedBackToastStyle,
        buttonTextStyle: styles.toastButtonStyle,
        informativeTextStyle: styles.toastTextStyle,
        position: "custom",
        type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA,
        text: title,
        testID: `${testID}__FeedBackToast`,
        accessibilityLabel: `${accessibilityLabel}__FeedBackToast`,
        numberOfLines: 2
      })
    });
  };
  var styles = _reactNative.StyleSheet.create({
    feedBackToastStyle: {
      bottom: 40,
      width: "95%"
    },
    toastButtonStyle: {
      color: _theme.color.palette.lighterPurple,
      width: "35%"
    },
    toastTextStyle: {
      color: _theme.color.palette.whiteGrey,
      flex: 1,
      fontSize: 16,
      lineHeight: 20
    }
  });
  var _default = exports.default = CartToastRemoveNotification;
