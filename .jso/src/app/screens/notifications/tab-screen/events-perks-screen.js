  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.EventAndPerkNotificationScreen = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _listNotification = _$$_REQUIRE(_dependencyMap[6]);
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _styles = _$$_REQUIRE(_dependencyMap[10]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[12]);
  var _validate = _$$_REQUIRE(_dependencyMap[13]);
  var _emptyListNotification = _$$_REQUIRE(_dependencyMap[14]);
  var _helper = _$$_REQUIRE(_dependencyMap[15]);
  var _loadingScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _adobe = _$$_REQUIRE(_dependencyMap[17]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _constants = _$$_REQUIRE(_dependencyMap[19]);
  var _errorUnplannedMaintenance = _$$_REQUIRE(_dependencyMap[20]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _lodash = _$$_REQUIRE(_dependencyMap[22]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[23]);
  var _deleteToastRef = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[25]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[26]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "EventAndPerkNotificationScreen";
  var EventAndPerkNotificationScreen = exports.EventAndPerkNotificationScreen = function EventAndPerkNotificationScreen() {
    var _dateGroup;
    var isFocused = (0, _native.useIsFocused)();
    var previousRef = _react.default.useRef(null);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("EVENT_AND_PERK_NOTIFICATION"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var listUserNotifications = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.listUserNotications);
    var isLoading = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.getUserNotificationsLoading);
    var isInitializing = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.userNotificationsInitializing);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var screenMaintenanceObj = (0, _react.useMemo)(function () {
      return null;
    }, [listErrorMaintenance]);
    var listNotifications = (0, _react.useMemo)(function () {
      if ((listUserNotifications == null ? undefined : listUserNotifications.length) === 0) return [];
      var filters = [_constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS];
      return listUserNotifications == null ? undefined : listUserNotifications.filter(function (item) {
        return filters.includes(item.category);
      });
    }, [listUserNotifications]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("NotificationList_Events&Perks");
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      checkInternetAndFetch();
      (0, _adobe.commonTrackingScreen)("NotificationList_Events&Perks", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      return function () {
        dispatch(_notificationRedux.default.resetUserNotificationsList());
      };
    }, []));
    var fetchDataNotification = function fetchDataNotification() {
      dispatch(_notificationRedux.default.getUserNotificationsRequest({
        categories: [_constants.NOTIFICATION_TYPES.EVENT_PP_NEW_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_PERK, _constants.NOTIFICATION_TYPES.EVENT_PP_UPCOMING_BOOKING, _constants.NOTIFICATION_TYPES.EVENT_PP_NEW_CREDITS, _constants.NOTIFICATION_TYPES.EVENT_PP_EXPIRING_CREDITS]
      }));
    };
    var checkInternetAndFetch = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          fetchDataNotification();
        }
      });
      return function checkInternetAndFetch() {
        return _ref.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [isFocused]);
    var handleButtonOnPress = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (navigationObj, redirect) {
        var type = navigationObj.type,
          value = navigationObj.value;
        if (!type || !value) return;
        handleNavigation(type, value, redirect);
      });
      return function handleButtonOnPress(_x, _x2) {
        return _ref3.apply(this, arguments);
      };
    }();
    if (!(0, _validate.isEmpty)(screenMaintenanceObj) && screenMaintenanceObj != null && screenMaintenanceObj.enableMode) {
      return (0, _jsxRuntime.jsx)(_errorUnplannedMaintenance.ErrorUnplannedMaintenance, {
        header: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.header,
        subHeader: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.subHeader,
        icon: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.icon,
        buttonLabel: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel,
        buttonLabel2: screenMaintenanceObj == null ? undefined : screenMaintenanceObj.buttonLabel2,
        onFirstButtonPress: function onFirstButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationFirst, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectFirst);
        },
        onSecondButtonPress: function onSecondButtonPress() {
          return handleButtonOnPress(screenMaintenanceObj == null ? undefined : screenMaintenanceObj.navigationSecond, screenMaintenanceObj == null ? undefined : screenMaintenanceObj.redirectSecond);
        },
        testID: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`,
        accessibilityLabel: `${COMPONENT_NAME}__ErrorUnplannedMaintenance`
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        testID: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
        accessibilityLabel: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
        onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch3.isConnected;
          if (isConnected) {
            setNoConnection(false);
            fetchDataNotification();
          }
        }),
        noInternetOverlayStyle: _styles.commonTabStyles.overlayStyle
      });
    }
    if (isInitializing && !isLoading) {
      return null;
    }
    if (!isLoading && !(listNotifications != null && listNotifications.length)) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _styles.commonTabStyles.container,
        children: (0, _jsxRuntime.jsx)(_emptyListNotification.EmptyNotificationEventPerks, {})
      });
    }
    var handleDeleteNotificationItem = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (_notificationItem) {
        var _yield$NetInfo$fetch4 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch4.isConnected;
        if (isConnected && !(0, _validate.isEmpty)(_notificationItem)) {
          dispatch(_notificationRedux.default.deleteSingleNotificationRequest(_notificationItem == null ? undefined : _notificationItem.id));
        } else {
          _deleteToastRef.default.showErrorToast();
        }
      });
      return function handleDeleteNotificationItem(_x3) {
        return _ref5.apply(this, arguments);
      };
    }();
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _styles.commonTabStyles.container,
      children: isLoading ? (0, _loadingScreen.default)() : (0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: function onRefresh() {
            return fetchDataNotification();
          }
        }),
        showsVerticalScrollIndicator: false,
        children: !!(listNotifications != null && listNotifications.length) && ((_dateGroup = (0, _helper.dateGroup)(listNotifications)) == null ? undefined : _dateGroup.map(function (element, index) {
          return (0, _jsxRuntime.jsx)(_listNotification.ListNotification, {
            index: index,
            previousRef: previousRef,
            notificationPayload: element,
            onDeleteNotificationItem: handleDeleteNotificationItem
          }, index);
        }))
      })
    });
  };
