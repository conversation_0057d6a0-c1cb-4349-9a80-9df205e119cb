  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _createClass2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _classCallCheck2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[3]);
  var _DeleteToastRef;
  var DeleteToastRef = exports.default = /*#__PURE__*/(0, _createClass2.default)(function DeleteToastRef() {
    (0, _classCallCheck2.default)(this, DeleteToastRef);
  });
  _DeleteToastRef = DeleteToastRef;
  DeleteToastRef.setDeleteSingleToastRef = function (ref) {
    _DeleteToastRef.deleteSingleToastRef = ref;
  };
  DeleteToastRef.setErrorToastRef = function (ref) {
    _DeleteToastRef.errorToastRef = ref;
  };
  DeleteToastRef.showDeleteSingleToast = function () {
    var _DeleteToastRef$delet;
    var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _feedbackToastProps.DURATION.LENGTH_MAX;
    (_DeleteToastRef$delet = _DeleteToastRef.deleteSingleToastRef) == null || (_DeleteToastRef$delet = _DeleteToastRef$delet.current) == null || _DeleteToastRef$delet.show(duration);
  };
  DeleteToastRef.closeNowDeleteSingleToast = function () {
    var _DeleteToastRef$delet2;
    (_DeleteToastRef$delet2 = _DeleteToastRef.deleteSingleToastRef) == null || (_DeleteToastRef$delet2 = _DeleteToastRef$delet2.current) == null || _DeleteToastRef$delet2.closeNow();
  };
  DeleteToastRef.showErrorToast = function () {
    var _DeleteToastRef$error;
    var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _feedbackToastProps.DURATION.LENGTH_MAX;
    (_DeleteToastRef$error = _DeleteToastRef.errorToastRef) == null || (_DeleteToastRef$error = _DeleteToastRef$error.current) == null || _DeleteToastRef$error.show(duration);
  };
  DeleteToastRef.closeNowErrorToast = function () {
    var _DeleteToastRef$error2;
    (_DeleteToastRef$error2 = _DeleteToastRef.errorToastRef) == null || (_DeleteToastRef$error2 = _DeleteToastRef$error2.current) == null || _DeleteToastRef$error2.closeNow();
  };
