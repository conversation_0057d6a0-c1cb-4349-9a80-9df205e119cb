  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PrivilegesScreen = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _tabBar = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _tabBarButton = _$$_REQUIRE(_dependencyMap[6]);
  var _privilegesList = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _privilegesRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _redeemMoreRewards = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _adobe = _$$_REQUIRE(_dependencyMap[13]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[14]);
  var styles = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[15]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var NUMBER = 5;
  var tabLoading = Array(NUMBER).fill({
    type: _tabBarButton.TabBarButtonType.loading
  });
  var SCREEN_NAME = "PrivilegesScreen";
  var titlePrivilegesCode = "INF1";
  var informativeDescriptionPrivilegesCode = "INF2";
  var informativePrivilegesCode = "INF7";
  var PrivilegesScreen = exports.PrivilegesScreen = function PrivilegesScreen() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var scrollViewRef = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var privileges = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.privileges);
    var isFetching = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.privilegesFetching);
    var privilegesType = (0, _reactRedux.useSelector)(_privilegesRedux.PrivilegesSelectors.privilegesType);
    var informativesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getInformativesCommon);
    var _useState = (0, _react.useState)(NUMBER),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visible = _useState2[0],
      setVisible = _useState2[1];
    var _useState3 = (0, _react.useState)(privilegesType[0]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      selectedType = _useState4[0],
      setSelectedType = _useState4[1];
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Account_Privileges");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Account_Privileges", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    var fetchPrivileges = function fetchPrivileges() {
      dispatch(_privilegesRedux.default.privilegesRequest());
    };
    var filterData = (0, _react.useMemo)(function () {
      if (selectedType && (selectedType == null ? undefined : selectedType.value) !== "All") {
        return privileges.filter(function (x) {
          return x.privilegesType === selectedType.value;
        });
      } else {
        return privileges;
      }
    }, [selectedType, privileges]);
    var title = (0, _react.useMemo)(function () {
      return (0, _lodash.get)(informativesCommon == null ? undefined : informativesCommon.find(function (item) {
        return item.code === titlePrivilegesCode;
      }), "informativeText");
    }, [informativesCommon]);
    var informativePrivileges = (0, _react.useMemo)(function () {
      return informativesCommon == null ? undefined : informativesCommon.find(function (item) {
        return item.code === informativePrivilegesCode;
      });
    }, [informativesCommon]);
    var contentInformativeDescriptionPrivileges = (0, _react.useMemo)(function () {
      return informativesCommon == null ? undefined : informativesCommon.find(function (item) {
        return item.code === informativeDescriptionPrivilegesCode;
      });
    }, [informativesCommon]);
    var label = (0, _lodash.get)(informativePrivileges, "title", "");
    var loadMore = function loadMore() {
      setVisible(visible + NUMBER);
    };
    (0, _react.useEffect)(function () {
      setVisible(NUMBER);
    }, [selectedType]);
    (0, _react.useEffect)(function () {
      fetchPrivileges();
    }, []);
    var handelChangeType = function handelChangeType(value) {
      var _scrollViewRef$curren;
      scrollViewRef == null || (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollTo({
        y: 0,
        animated: true
      });
      setSelectedType(value);
    };
    (0, _react.useEffect)(function () {
      return function () {
        dispatch(_privilegesRedux.default.resetPrivileges());
      };
    }, []);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {}), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.shadowWrapper,
        children: (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.shadowLine,
          children: (0, _jsxRuntime.jsx)(_tabBar.TabBar, {
            tabBarData: isFetching ? tabLoading : privilegesType,
            onTabBarItemSelected: handelChangeType,
            contentStyle: isFetching ? styles.loadingTab : styles.paddingTab
          })
        })
      }), (0, _jsxRuntime.jsxs)(_reactNative.ScrollView, {
        ref: scrollViewRef,
        showsVerticalScrollIndicator: false,
        style: styles.background,
        testID: `${SCREEN_NAME}__ScrollView`,
        accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
        children: [(0, _jsxRuntime.jsx)(_privilegesList.PrivilegesList, {
          visible: visible,
          data: filterData,
          loadMore: loadMore,
          title: title,
          fetchPrivileges: fetchPrivileges,
          informativePrivileges: contentInformativeDescriptionPrivileges,
          testID: `${SCREEN_NAME}__PrivilegesList`,
          accessibilityLabel: `${SCREEN_NAME}__PrivilegesList`
        }), (0, _jsxRuntime.jsx)(_redeemMoreRewards.default, {
          label: label
        })]
      })]
    });
  };
