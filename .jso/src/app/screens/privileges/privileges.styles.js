  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.shadowWrapper = exports.shadowLine = exports.paddingTab = exports.marginUpgrade = exports.marginBottomH4 = exports.loadingTab = exports.height = exports.enjoyText = exports.background = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var shadowLine = exports.shadowLine = {
    backgroundColor: _theme.color.palette.whiteGrey,
    shadowColor: _theme.color.palette.overlayColor,
    elevation: 5,
    shadowOpacity: 0.1,
    shadowOffset: {
      height: 2,
      width: 0
    }
  };
  var paddingTab = exports.paddingTab = {
    paddingLeft: 24
  };
  var shadowWrapper = exports.shadowWrapper = {
    overflow: "hidden",
    backgroundColor: _theme.color.palette.lightestGrey,
    paddingBottom: 8
  };
  var enjoyText = exports.enjoyText = {
    marginVertical: 30,
    color: _theme.color.palette.almostBlackGrey
  };
  var background = exports.background = {
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var marginBottomH4 = exports.marginBottomH4 = {
    marginBottom: 16
  };
  var marginUpgrade = exports.marginUpgrade = {
    marginVertical: 50
  };
  var height = exports.height = {
    height: 50
  };
  var loadingTab = exports.loadingTab = Object.assign({}, paddingTab, {
    paddingBottom: 5
  });
