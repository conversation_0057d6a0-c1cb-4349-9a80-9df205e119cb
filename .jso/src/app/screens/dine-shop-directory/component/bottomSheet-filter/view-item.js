  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.ViewItem = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _checkbox = _$$_REQUIRE(_dependencyMap[4]);
  var _Chip = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _color = _$$_REQUIRE(_dependencyMap[7]);
  var _typography = _$$_REQUIRE(_dependencyMap[8]);
  var _icons = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[10]);
  var _dineShopDirectoryUntil = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var myParamsChipUI = ["Area", "Availability", "Category", "Dietary Options"];
  var myParamsCollapse = ["Availability", "Dietary Options", "Dine Category & Cuisine", "Shop Category", "Membership & Payment"];
  var ViewItem = exports.ViewItem = _react.default.memo(function (_ref) {
    var data = _ref.data,
      item = _ref.item,
      index = _ref.index,
      handleOnClickItem = _ref.handleOnClickItem,
      handleClearSubItem = _ref.handleClearSubItem,
      handleClearLocation = _ref.handleClearLocation,
      handleClearCategory = _ref.handleClearCategory;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      showCollapse = _useState2[0],
      setShowCollapse = _useState2[1];
    var renderHeaderTop = function renderHeaderTop() {
      var _data$, _data$2;
      var dataArea = (_data$ = data[0]) == null ? undefined : _data$.childTags;
      var dataLocation = (_data$2 = data[1]) == null ? undefined : _data$2.childTags;
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.header,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewRowHeader,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.title,
            children: (item == null ? undefined : item.tagTitle) === "Area" ? "Location" : item == null ? undefined : item.tagTitle
          }), ((0, _dineShopDirectoryUntil.hasActiveTag)(dataArea) || (0, _dineShopDirectoryUntil.hasActiveTag)(dataLocation)) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonClear,
            onPress: handleClearLocation,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "common.clear",
              style: styles.txtClear
            })
          })]
        })
      });
    };
    var renderHeaderCategory = function renderHeaderCategory() {
      var _data$3;
      var dataCategory = (_data$3 = data[2]) == null ? undefined : _data$3.childTags;
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.header,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewRowHeader,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.title,
            children: (item == null ? undefined : item.tagTitle) === "Area" ? "Location" : item == null ? undefined : item.tagTitle
          }), (0, _dineShopDirectoryUntil.hasActiveTag)(dataCategory) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonClear,
            onPress: handleClearCategory,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "common.clear",
              style: styles.txtClear
            })
          })]
        })
      });
    };
    var renderHeader = function renderHeader() {
      var stringMatch = myParamsCollapse.includes(item.tagTitle);
      if ((item == null ? undefined : item.tagTitle) === "Location") return null;
      if ((item == null ? undefined : item.tagTitle) === "Area") return renderHeaderTop();
      if ((item == null ? undefined : item.tagTitle) === "Category") return renderHeaderCategory();
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: [!showCollapse ? styles.headerCollapse : styles.header],
        onPress: function onPress() {
          return setShowCollapse(!showCollapse);
        },
        disabled: !stringMatch,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.viewRowHeader,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: [styles.title, {
              minWidth: (item == null ? undefined : item.tagTitle) === "Shop Category" ? "30%" : 0
            }],
            children: (item == null ? undefined : item.tagTitle) === "Area" ? "Location" : item == null ? undefined : item.tagTitle
          }), (0, _dineShopDirectoryUntil.hasActiveTag)(item == null ? undefined : item.childTags) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: styles.buttonClear,
            onPress: handleClearSubItem,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "common.clear",
              style: styles.txtClear
            })
          })]
        }), stringMatch && (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: showCollapse ? (0, _jsxRuntime.jsx)(_icons.AccordionUp, {}) : (0, _jsxRuntime.jsx)(_icons.AccordionDown, {})
        })]
      });
    };
    var renderCheckBoxList = function renderCheckBoxList(childTag) {
      var isAll = (childTag == null ? undefined : childTag.tagTitle) === "All";
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        children: [(0, _jsxRuntime.jsx)(_checkbox.Checkbox, {
          value: !!childTag.isActive,
          onToggle: function onToggle() {
            handleOnClickItem(isAll ? "ALL" : childTag == null ? undefined : childTag.tagName);
          },
          outlineStyle: styles.outlineStyle,
          fillStyle: styles.fillStyle,
          text: childTag.tagTitle,
          icon: (0, _jsxRuntime.jsx)(_icons.Check, {
            fill: _color.color.palette.whiteGrey,
            width: 16,
            height: 16
          }),
          textStyle: styles.txtCheckBox
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.lineGrey
        })]
      }, childTag == null ? undefined : childTag.tagTitle);
    };
    var renderColorIcon = function renderColorIcon(active) {
      return active ? _color.color.palette.lightPurple : _color.color.palette.darkestGrey;
    };
    var renderIconChip = function renderIconChip(childTag, active) {
      switch (childTag.tagTitle) {
        case "Public Area":
          return (0, _jsxRuntime.jsx)(_icons.PublicAreaIcon, {
            width: 16,
            height: 16,
            color: renderColorIcon(active)
          });
        case "Transit Area":
          return active ? (0, _jsxRuntime.jsx)(_icons.TransitAreaFillIcon, {
            width: 16,
            height: 16,
            color: _color.color.palette.lightPurple
          }) : (0, _jsxRuntime.jsx)(_icons.TransitAreaIcon, {
            width: 16,
            height: 16
          });
        case "Dine":
          return (0, _jsxRuntime.jsx)(_icons.DineFilter, {
            width: 16,
            height: 16,
            color: renderColorIcon(active)
          });
        case "Shop":
          return (0, _jsxRuntime.jsx)(_icons.ShopFilter, {
            width: 16,
            height: 16,
            color: renderColorIcon(active)
          });
        default:
          return null;
      }
    };
    var renderChip = function renderChip(childTag) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: styles.chipsContainer,
        children: (0, _jsxRuntime.jsx)(_Chip.Chip, {
          icon: renderIconChip(childTag, !!childTag.isActive),
          isActive: !!childTag.isActive,
          onClick: function onClick() {
            return handleOnClickItem(childTag == null ? undefined : childTag.tagName);
          },
          text: childTag.tagTitle
        })
      }, childTag == null ? undefined : childTag.tagTitle);
    };
    var renderDataFilter = function renderDataFilter() {
      var _item$childTags;
      if (!item.childTags) {
        return null;
      }
      return (_item$childTags = item.childTags) == null ? undefined : _item$childTags.map(function (childTag, ind) {
        var stringMatch = myParamsChipUI.includes(item.tagTitle);
        return stringMatch ? renderChip(childTag) : renderCheckBoxList(childTag);
      });
    };
    var getContainerDineFilterStyle = function getContainerDineFilterStyle(item) {
      var stringMatch = myParamsChipUI.includes(item.tagTitle);
      return stringMatch ? styles.viewRow : styles.viewColumn;
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: {
        marginTop: index === 1 ? -34 : 0
      },
      children: [renderHeader(), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: getContainerDineFilterStyle(item),
        children: (showCollapse || item.tagTitle === "Category" || item.tagTitle === "Location" || item.tagTitle === "Area") && (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
          children: renderDataFilter()
        })
      })]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    header: {
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 12
    },
    headerCollapse: {
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      borderBottomWidth: 1,
      borderColor: _color.color.palette.lighterGrey,
      paddingBottom: 16,
      marginTop: -34
    },
    title: {
      fontFamily: _typography.typography.black,
      color: _color.color.palette.almostBlackGrey,
      fontSize: 16,
      fontWeight: _reactNative2.Platform.select({
        ios: "900",
        android: "normal"
      }),
      lineHeight: 20,
      textAlignVertical: "center"
    },
    chipsContainer: {
      marginRight: 8,
      marginBottom: 8
    },
    viewRow: {
      flexDirection: "row",
      flexWrap: "wrap",
      width: "100%"
    },
    viewRowHeader: {
      flexDirection: "row",
      alignItems: "center"
    },
    viewColumn: {
      width: "100%"
    },
    lineGrey: {
      borderBottomColor: _color.color.palette.lighterGrey,
      borderBottomWidth: 1,
      marginVertical: 12
    },
    outlineStyle: {
      width: 16,
      height: 16,
      borderRadius: 4,
      borderColor: _color.color.palette.midGrey,
      borderWidth: 1
    },
    fillStyle: {
      width: 16,
      height: 16,
      borderRadius: 4
    },
    txtClear: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.lightPurple,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16
    },
    buttonClear: {
      marginLeft: 8
    },
    txtCheckBox: {
      fontFamily: _typography.typography.regular,
      color: _color.color.palette.almostBlackGrey,
      fontSize: 14,
      fontWeight: _reactNative2.Platform.select({
        ios: "400",
        android: "normal"
      }),
      lineHeight: 18
    }
  });
