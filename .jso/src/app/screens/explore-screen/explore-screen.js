  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _latestHappenings = _$$_REQUIRE(_dependencyMap[10]);
  var _shortcutLink = _$$_REQUIRE(_dependencyMap[11]);
  var _sections = _$$_REQUIRE(_dependencyMap[12]);
  var _homepageMasthead = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _exploreContainer = _$$_REQUIRE(_dependencyMap[14]);
  var _tabBarContainer = _$$_REQUIRE(_dependencyMap[15]);
  var _exploreRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[16]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[17]);
  var _exploreContainerProps = _$$_REQUIRE(_dependencyMap[18]);
  var _exploreCategories = _$$_REQUIRE(_dependencyMap[19]);
  var _navigators = _$$_REQUIRE(_dependencyMap[20]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[21]);
  var _constants = _$$_REQUIRE(_dependencyMap[22]);
  var _exploreHelper = _$$_REQUIRE(_dependencyMap[23]);
  var _cartToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _text = _$$_REQUIRE(_dependencyMap[25]);
  var _i18n = _$$_REQUIRE(_dependencyMap[26]);
  var _icons = _$$_REQUIRE(_dependencyMap[27]);
  var _lodash = _$$_REQUIRE(_dependencyMap[28]);
  var _reactNativeGeolocationService = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _nativeAuthRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[30]));
  var _error = _$$_REQUIRE(_dependencyMap[31]);
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[32]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _exploreScreenStyles = _$$_REQUIRE(_dependencyMap[34]);
  var _shortcutLink2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[35]));
  var _profileRedux = _$$_REQUIRE(_dependencyMap[36]);
  var _justForYouCarouselScreen = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[37]));
  var _native = _$$_REQUIRE(_dependencyMap[38]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[39]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[40]);
  var _exploreChangiModalFilter = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[41]));
  var _modalSwipe = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[42]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[43]);
  var _utils = _$$_REQUIRE(_dependencyMap[44]);
  var _grantPermissionModal = _$$_REQUIRE(_dependencyMap[45]);
  var _adobe = _$$_REQUIRE(_dependencyMap[46]);
  var _store = _$$_REQUIRE(_dependencyMap[47]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[48]);
  var _changipay = _$$_REQUIRE(_dependencyMap[49]);
  var _forYouRedux = _$$_REQUIRE(_dependencyMap[50]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[51]);
  var _analytics = _$$_REQUIRE(_dependencyMap[52]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[53]);
  var _homepageMasthead2 = _$$_REQUIRE(_dependencyMap[54]);
  var _exploreLanding = _$$_REQUIRE(_dependencyMap[55]);
  var _envParams = _$$_REQUIRE(_dependencyMap[56]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[57]));
  var _exploreStaffPerk = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[58]));
  var _storage = _$$_REQUIRE(_dependencyMap[59]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[60]);
  var _pageConfigRedux = _$$_REQUIRE(_dependencyMap[61]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[62]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[63]));
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[64]);
  var _grantPermissionModalOldVersion = _$$_REQUIRE(_dependencyMap[65]);
  var _monarchOverlayControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[66]));
  var _monarchOverlay = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[67]));
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[68]);
  var _qualtrics = _$$_REQUIRE(_dependencyMap[69]);
  var _notificationRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[70]));
  var _shopRedux = _$$_REQUIRE(_dependencyMap[71]);
  var _tickerBand = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[72]));
  var _bottomTabs = _$$_REQUIRE(_dependencyMap[73]);
  var _account = _$$_REQUIRE(_dependencyMap[74]);
  var _bottomSheetError = _$$_REQUIRE(_dependencyMap[75]);
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[76]);
  var _scrollBuddy = _$$_REQUIRE(_dependencyMap[77]);
  var _exploreScreen = _$$_REQUIRE(_dependencyMap[78]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[79]));
  var _explore = _$$_REQUIRE(_dependencyMap[80]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[81]);
  var _useAppRating = _$$_REQUIRE(_dependencyMap[82]);
  var _panResponder = _$$_REQUIRE(_dependencyMap[83]);
  var _suspend = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[84]));
  var _authentication = _$$_REQUIRE(_dependencyMap[85]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[86]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[87]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[88]);
  var _aemGroupTwo = _$$_REQUIRE(_dependencyMap[89]);
  var _playpass = _$$_REQUIRE(_dependencyMap[90]);
  var _playpass2 = _$$_REQUIRE(_dependencyMap[91]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[92]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var MAX_ITEM_SHOW_SEE_MORE = 6;
  var SCREEN_NAME = "ExploreScreen";
  var TYPES = _constants.USER_TYPES.TYPES,
    VALUES = _constants.USER_TYPES.VALUES;
  var USER_TYPE_FLY_AIRPORTVISIT = TYPES.FLY_AIRPORTVISIT,
    USER_TYPE_FLY_HOME = TYPES.FLY_HOME,
    USER_TYPE_FLY_AIRPORT = TYPES.FLY_AIRPORT,
    USER_TYPE_GENERAL = TYPES.GENERAL,
    USER_TYPE_MONARC = TYPES.MONARC;
  var USER_TYPE_VALUE_UNDEFINED = VALUES.UNDEFINED,
    USER_TYPE_VALUE_FLY_AIRPORTVISIT = VALUES.FLY_AIRPORTVISIT,
    USER_TYPE_VALUE_FLY_HOME = VALUES.FLY_HOME,
    USER_TYPE_VALUE_FLY_AIRPORT = VALUES.FLY_AIRPORT,
    USER_TYPE_VALUE_GENERAL = VALUES.GENERAL,
    USER_TYPE_VALUE_MONARC = VALUES.MONARC;
  var wrapOrderExplore = {
    marginTop: 24
  };
  var ExploreComponentTypes = /*#__PURE__*/function (ExploreComponentTypes) {
    ExploreComponentTypes["timelineTile"] = "timelineTile";
    ExploreComponentTypes["shortcutLink"] = "shortcutLink";
    ExploreComponentTypes["latestHappening"] = "latestHappening";
    ExploreComponentTypes["justForYou"] = "justForYou";
    ExploreComponentTypes["staffPerkSwimlane"] = "staffPerkSwimlane";
    return ExploreComponentTypes;
  }(ExploreComponentTypes || {});
  var checkMonarchOverlay = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (isFinishedLoading, rewardsData, userProfile, isFocused) {
      var ifTrue = (0, _utils.handleCondition)(isFinishedLoading && isFocused, true, false);
      if (ifTrue) {
        var _rewardsData$reward, _rewardsData$reward2;
        var isMonarchTier = (0, _utils.handleCondition)((rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null || (_rewardsData$reward = _rewardsData$reward.currentTierInfo) == null ? undefined : _rewardsData$reward.replace(" ", "")) === _changiRewardsMemberCard.Tier.Monarch || (rewardsData == null || (_rewardsData$reward2 = rewardsData.reward) == null || (_rewardsData$reward2 = _rewardsData$reward2.currentTierInfo) == null ? undefined : _rewardsData$reward2.replace(" ", "")) === _changiRewardsMemberCard.Tier.StaffMonarch, true, false);
        var listUserIdShowedMonarch = yield (0, _storage.load)(_storageKey.StorageKey.monarchOveylayUserID);
        if (isMonarchTier && userProfile && !(listUserIdShowedMonarch != null && listUserIdShowedMonarch.includes(userProfile == null ? undefined : userProfile.id))) {
          _monarchOverlayControler.default.showOverlay();
        }
      }
    });
    return function checkMonarchOverlay(_x, _x2, _x3, _x4) {
      return _ref.apply(this, arguments);
    };
  }();
  var useCheckSurveyOpen = function useCheckSurveyOpen(isTriggerPopupSuccess, appSettingData, currentForceUpdateFlow) {
    (0, _react.useEffect)(function () {
      if (isTriggerPopupSuccess) {
        (0, _qualtrics.checkSurvey)(appSettingData, !!currentForceUpdateFlow.current);
      }
    }, [isTriggerPopupSuccess]);
  };
  var SCROLL_BUDDY_THRESHOLD = 30;
  var ExploreScreen = function ExploreScreen(_ref2) {
    var _get, _rewardsData$reward3, _myTravelFlightsPaylo2;
    var route = _ref2.route,
      navigation = _ref2.navigation;
    var _ref3 = Object.assign({}, (route == null ? undefined : route.params) || {}),
      _ref3$isScrollToExplo = _ref3.isScrollToExploreChangiSection,
      isScrollToExploreChangiSection = _ref3$isScrollToExplo === undefined ? false : _ref3$isScrollToExplo,
      _ref3$section = _ref3.section,
      exploreSection = _ref3$section === undefined ? "" : _ref3$section,
      _ref3$initialFilterDa = _ref3.initialFilterDate,
      initialFilterDate = _ref3$initialFilterDa === undefined ? "" : _ref3$initialFilterDa,
      _ref3$initialFilterLo = _ref3.initialFilterLocation,
      initialFilterLocation = _ref3$initialFilterLo === undefined ? "" : _ref3$initialFilterLo;
    var dispatch = (0, _reactRedux.useDispatch)();
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var timeoutSetShowScrollBuddy = (0, _react.useRef)(null);
    var toastRef = (0, _react.useRef)(null);
    var scrollViewRef = (0, _react.useRef)(null);
    var scrollOffsetRef = (0, _react.useRef)(0);
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewExplorePlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;

    // ANIMATED and REF
    var offsetY = (0, _reactNativeReanimated.useSharedValue)(0);
    var isBegindScroll = (0, _reactNativeReanimated.useSharedValue)(false);
    var scrollOffsets = (0, _reactNativeReanimated.useSharedValue)({});
    var exploreChangiTabHeight = (0, _reactNativeReanimated.useSharedValue)(0);
    var scrollBuddyOpacity = (0, _reactNativeReanimated.useSharedValue)(1);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isModalVisible = _useState2[0],
      setModalVisible = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      animateModal = _useState4[0],
      setanimateModal = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      showFilterModal = _useState6[0],
      setShowFilterModal = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isShowCardToast = _useState8[0],
      setIsShowCardToast = _useState8[1];
    var _useState9 = (0, _react.useState)(initialFilterDate),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      filterDate = _useState0[0],
      setFilterDate = _useState0[1];
    var _useState1 = (0, _react.useState)({}),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      checkedLocationState = _useState10[0],
      setCheckedLocationState = _useState10[1];
    var announcementPayload = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.announcementPayload);
    var indexItemExploreChangi = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.indexItemExploreChangi);
    var offsetItemExploreChangi = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.offsetItemExploreChangi);
    var exploreDataPayload = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreData);
    var _exploreDataPayload = Object.assign({}, exploreDataPayload),
      isExploreDataLoading = _exploreDataPayload.isLoading,
      isExploreDataError = _exploreDataPayload.hasError;
    var hasMoreExploreData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.hasMoreExploreData);
    var selectedExploreCategory = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.selectedExploreCategory);
    var isEventsCategorySelected = (0, _exploreRedux.findSelectedCategory)(selectedExploreCategory, _exploreCategories.ExploreCategoryEnum.events);
    var isPkgCode = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.isPkgCode);
    var campaignCodeHappening = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.campaignCodeHappening);
    var pkgCodeLastedHappening = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.pkgCodeLastedHappening);
    var shortcutLinksExploreData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.shortcutLinksExploreData);
    var playPassUrlPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassUrlPayload("FAQupload"));
    var _usePlayPassUrl = (0, _screenHook.usePlayPassUrl)("viewExplorePlayPass"),
      loadingExplorePlayPass = _usePlayPassUrl.loading;
    var _usePlayPassUrl2 = (0, _screenHook.usePlayPassUrl)("viewCartPlayPass"),
      loadingCartPlayPass = _usePlayPassUrl2.loading;
    var _useState11 = (0, _react.useState)(0),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      defaultScrollOffset = _useState12[0],
      setDefaultScrollOffset = _useState12[1];
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var finishBiometric = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.finishBiometric);
    var _useHandleScroll = (0, _navigators.useHandleScroll)(),
      handleScroll = _useHandleScroll.handleScroll,
      scrollDirection = _useHandleScroll.scrollDirection;
    var userProfile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var dataExplorePageConfiguration = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.explorePagePayload);
    var scrollBuddyData = (0, _reactRedux.useSelector)(_aemGroupTwo.AemGroupTwoSelectors.scrollBuddyPayload);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var pageLevelExploreScreen = (_get = (0, _lodash.get)(dataExplorePageConfiguration, "list", [])) == null ? undefined : _get.filter(function (e) {
      return ExploreComponentTypes[e == null ? undefined : e.sectionComponent];
    });
    var configPage = (0, _utils.handleCondition)(!(0, _lodash.isEmpty)(pageLevelExploreScreen), pageLevelExploreScreen, _exploreLanding.defaultExplorePageConfiguration);
    var isGetRatingSuccess = (0, _reactRedux.useSelector)(_pageConfigRedux.PageConfigSelectors.getRatingSuccess);
    var _useState13 = (0, _react.useState)(false),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      staffPerkRequest = _useState14[0],
      setStaffPerkRequest = _useState14[1];
    var latestHappeningsRequest = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.latestHappeningsRequest);
    var upComingFlightFetching = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.upComingFlightFetching);
    var _useState15 = (0, _react.useState)(false),
      _useState16 = (0, _slicedToArray2.default)(_useState15, 2),
      justForYouCarouselFetching = _useState16[0],
      setJustForYouDataFetching = _useState16[1];
    var isTriggerPopupSuccess = (0, _reactRedux.useSelector)(_notificationRedux.NotificationSelectors.isTriggerPopupSuccess);
    var recommendedProductsFetching = (0, _reactRedux.useSelector)(_shopRedux.ShopSelectors.recommendedProductsFetching);
    var dataErrorMaintenanceConfig = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE));
    var listErrorMaintenance = (0, _lodash.get)(dataErrorMaintenanceConfig, "data.list", []);
    var sectionMaintenanceObj = (0, _react.useMemo)(function () {
      var data = listErrorMaintenance == null ? undefined : listErrorMaintenance.find(function (obj) {
        return obj.sectionName === _constants.sectionTagName.exploreChangi;
      });
      return data || {};
    }, [listErrorMaintenance]);
    var _useState17 = (0, _react.useState)(false),
      _useState18 = (0, _slicedToArray2.default)(_useState17, 2),
      showGrantPermissionFlow = _useState18[0],
      setShowGrantPermissionFlow = _useState18[1];
    var mastHeadsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.mastHeads");
    var tier = isLoggedIn ? rewardsData == null || (_rewardsData$reward3 = rewardsData.reward) == null || (_rewardsData$reward3 = _rewardsData$reward3.currentTierInfo) == null ? undefined : _rewardsData$reward3.replace(" ", "") : "Non-logged-in";
    var imageHomepageAEM = (0, _homepageMasthead2.mappingTierCode)(tier, mastHeadsAEM);
    var errorAEM = dataCommonAEM == null ? undefined : dataCommonAEM.error;
    var isFocused = (0, _native.useIsFocused)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("EXPLORE_SCREEN"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useState19 = (0, _react.useState)(null),
      _useState20 = (0, _slicedToArray2.default)(_useState19, 2),
      shortcutLinksProcessed = _useState20[0],
      setShortcutLinksProcessed = _useState20[1];
    var _useState21 = (0, _react.useState)(false),
      _useState22 = (0, _slicedToArray2.default)(_useState21, 2),
      mapRMFlag = _useState22[0],
      setMapRMFlag = _useState22[1];
    var shortcutLinkStyles = {
      paddingBottom: _reactNative2.Platform.OS === "ios" ? inset.bottom : 0
    };
    var refScreen = (0, _react.useRef)(0);
    var loadingBookingData = (0, _playpass.usePlayPassStore)(function (state) {
      return state.playPassStickyCartLoading;
    });
    var playPassStickyCart = (0, _playpass.usePlayPassStore)(function (state) {
      return state.playPassStickyCart;
    });
    var _ref4 = playPassStickyCart || {},
      timerTs = _ref4.timerTs;
    var _useContext = (0, _react.useContext)(_account.AccountContext),
      nativeOnboardingScreen = _useContext.nativeOnboardingScreen;
    var isNativeOnboardingScreen = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.NATIVE_ONBOARDING_SCREEN, nativeOnboardingScreen);
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      loadingGetConfig = _useGetConfigurationP.loadingGetConfig,
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiPay = _useGetConfigurationP.notifyDisableChangiPay;

    // STATE
    var currentScrollOffset = (0, _react.useMemo)(function () {
      var _scrollOffsets$value;
      return (_scrollOffsets$value = scrollOffsets.value) == null ? undefined : _scrollOffsets$value[selectedExploreCategory];
    }, [selectedExploreCategory]);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.FLY_EXPLORE_SWIMLANE),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      tickerBand = _useTickerbandMaintan.tickerBand,
      tickerBandDescription = _useTickerbandMaintan.tickerBandDescription,
      tickerBandButtonText = _useTickerbandMaintan.tickerBandButtonText,
      onPressCTA = _useTickerbandMaintan.onPressCTA,
      onCloseTickerBand = _useTickerbandMaintan.onCloseTickerBand;
    var bottomTabHeight = (0, _bottomTabs.useBottomTabBarHeight)();
    var _useContext2 = (0, _react.useContext)(_explore.ExploreContext),
      exploreScrollBuddyFlag = _useContext2.exploreScrollBuddyFlag,
      exploreStaffPerksFlag = _useContext2.exploreStaffPerksFlag,
      exploreJustForYouFlag = _useContext2.exploreJustForYouFlag;
    var _useContext3 = (0, _react.useContext)(_panResponder.PanResponderContext),
      triggerShowAppRatingRef = _useContext3.triggerShowAppRatingRef;
    var isExistScrollBuddy = (0, _remoteConfig.isFlagOnCondition)(exploreScrollBuddyFlag);
    var isExploreStaffPerksFlag = (0, _remoteConfig.isFlagOnCondition)(exploreStaffPerksFlag);
    var isJustForYouFlagOn = (0, _remoteConfig.isFlagOnCondition)(exploreJustForYouFlag);
    var isDisplayTickerBand = (0, _react.useMemo)(function () {
      var _myTravelFlightsPaylo;
      return !(0, _lodash.isEmpty)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) && (myTravelFlightsPayload == null || (_myTravelFlightsPaylo = myTravelFlightsPayload.getMyTravelFlightDetails) == null ? undefined : _myTravelFlightsPaylo.length) > 0 && isShowMaintenance;
    }, [myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails, myTravelFlightsPayload == null || (_myTravelFlightsPaylo2 = myTravelFlightsPayload.getMyTravelFlightDetails) == null ? undefined : _myTravelFlightsPaylo2.length, isShowMaintenance]);
    var onExploreTabLayout = (0, _react.useCallback)(function (e) {
      exploreChangiTabHeight.value = e.nativeEvent.layout.height;
    }, []);
    var checkInternet = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if ((0, _utils.ifAllTrue)([isLoggedIn, isConnected])) {
          dispatch(_nativeAuthRedux.default.nativeAuthTokenVerifyRequest());
        }
      });
      return function checkInternet() {
        return _ref5.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      var seconds = _momentTimezone.default.duration(_momentTimezone.default.tz(timerTs, "Asia/Singapore").utc().diff((0, _momentTimezone.default)().utc())).asSeconds();
      if (seconds > 0) {
        setIsShowCardToast(true);
      } else {
        setIsShowCardToast(false);
      }
    }, [timerTs]);
    var checkListTrigger = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* () {
        var isParticipatedCmWebview = yield (0, _storage.load)(_storageKey.StorageKey.isParticipatedCmWebview);
        var isParticipatedInAppFolio = yield (0, _storage.load)(_storageKey.StorageKey.isParticipatedInAppFolio);
        return (0, _utils.ifOneTrue)([isParticipatedCmWebview, isParticipatedInAppFolio]);
      });
      return function checkListTrigger() {
        return _ref6.apply(this, arguments);
      };
    }();
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      if (scrollOffsetRef.current > SCROLL_BUDDY_THRESHOLD || loadingBookingData) {
        (0, _reactNativeReanimated.runOnJS)(hideScrollBuddy)();
      } else {
        (0, _reactNativeReanimated.runOnJS)(displayScrollBuddy)();
      }
      return function () {
        (0, _reactNativeReanimated.runOnJS)(hideScrollBuddy)();
      };
    }, [loadingBookingData]));
    var _useTriggerAppRating = (0, _useAppRating.useTriggerAppRating)(_useAppRating.AppRatingSession.ExploreScreen),
      handleResetInactivityTimeout = _useTriggerAppRating.handleResetInactivityTimeout;
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        checkInternet();
      });
    }, [isLoggedIn]));

    // Get AEM L3 page
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        handleResetInactivityTimeout();
      });
    }, []));
    var handleTrackingShowRatingPopup = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* () {
        var _store$getState, _route$params;
        var listTrigger = yield checkListTrigger();
        var _store$getState$pageC = (_store$getState = _store.store.getState()) == null ? undefined : _store$getState.pageConfigReducer,
          _store$getState$pageC2 = _store$getState$pageC.authTokenAgeHours,
          authTokenAgeHours = _store$getState$pageC2 === undefined ? "48" : _store$getState$pageC2,
          _store$getState$pageC3 = _store$getState$pageC.ratingPopupDays,
          ratingPopupDays = _store$getState$pageC3 === undefined ? "120" : _store$getState$pageC3;
        var checkAuthTokenAndcadence = (0, _utils.ifAllTrue)([!(0, _lodash.isEmpty)(authTokenAgeHours), !(0, _lodash.isEmpty)(ratingPopupDays), isGetRatingSuccess]);
        if ((0, _utils.ifAllTrue)([!(route != null && (_route$params = route.params) != null && _route$params.isOpenApp), listTrigger, (0, _authentication.checkLoginState)(), checkAuthTokenAndcadence])) {
          (0, _screenHelper.handleShowPopupRating)(Number(authTokenAgeHours), Number(ratingPopupDays));
        }
      });
      return function handleTrackingShowRatingPopup() {
        return _ref7.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      if (triggerShowAppRatingRef != null && triggerShowAppRatingRef.current) {
        triggerShowAppRatingRef.current[_useAppRating.AppRatingSession.OldLogic] = handleTrackingShowRatingPopup;
      } else {
        triggerShowAppRatingRef.current = (0, _defineProperty2.default)({}, _useAppRating.AppRatingSession.OldLogic, handleTrackingShowRatingPopup);
      }
    }, [handleTrackingShowRatingPopup]);
    (0, _screenHook.useAdobeTargetHandle)({
      isFlagOn: isJustForYouFlagOn
    });

    // Handle app rating popup logic
    (0, _useAppRating.useAppRatingOnSaveFlightExploreScreen)({
      route: route,
      triggerShowAppRatingRef: triggerShowAppRatingRef
    });
    (0, _react.useEffect)(function () {
      (0, _analytics.analyticsLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.LANDING);
      (0, _analytics.dtACtionLogEvent)(_analytics.ANALYTICS_LOG_EVENT_NAME.LANDING);
      (0, _analytics.dtBizEvent)(SCREEN_NAME, _analytics.ANALYTICS_LOG_EVENT_NAME.LANDING, 'App-Event', {});
    }, []);
    var requestLocationPermission = function requestLocationPermission() {
      if (_reactNative2.Platform.OS === "ios") {
        _reactNativeGeolocationService.default.requestAuthorization("whenInUse");
      } else {
        (0, _reactNativePermissions.request)(_reactNativePermissions.PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
      }
    };
    var handleDeepLink = /*#__PURE__*/function () {
      var _ref9 = (0, _asyncToGenerator2.default)(function* (_ref8) {
        var url = _ref8.url;
        if (url != null && url.startsWith(_constants.APP_DEEPLINKS.LOCATION_PERMISSION)) {
          requestLocationPermission();
        }
      });
      return function handleDeepLink(_x5) {
        return _ref9.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      var linkingEvent = _reactNative2.Linking.addEventListener("url", handleDeepLink);
      _reactNative2.Linking.getInitialURL().then(function (url) {
        handleDeepLink({
          url: url
        });
      });
      return function () {
        linkingEvent == null || linkingEvent.remove();
      };
    }, []);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Home_Page");
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      _reactNative2.InteractionManager.runAfterInteractions(function () {
        (0, _adobe.commonTrackingScreen)("Home_Page", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return function () {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          navigation.setParams({
            isOpenApp: false
          });
          clearTimeout(timeoutSetShowScrollBuddy.current);
        });
      };
    }, []));
    var loadShortcutLinks = _react.default.useCallback(function () {
      dispatch(_exploreRedux.default.shortcutLinksExplorePreRequest());
      _reactNativeGeolocationService.default.getCurrentPosition(function (position) {
        getShortcutLinkExplorer(position);
      }, function (error) {
        getShortcutLinkExplorer(null);
      }, {
        enableHighAccuracy: false,
        timeout: 10000,
        maximumAge: 5000
      });
    }, [isLoggedIn, tier]);
    (0, _react.useEffect)(function () {
      if (isLoggedIn && !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading) && myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails && !(0, _lodash.isEmpty)(userProfile == null ? undefined : userProfile.email)) {
        var isPlayPassBookingFinished = (0, _mmkvStorage.getPlayPassBookingFinished)();
        if (isPlayPassBookingFinished) {
          _reactNative2.InteractionManager.runAfterInteractions(function () {
            (0, _playpass2.getUpcomingEventRequest)();
          });
        }
      }
    }, [myTravelFlightsPayload, isLoggedIn, userProfile == null ? undefined : userProfile.email]);
    var onReLoadUpComingEvent = function onReLoadUpComingEvent() {
      dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsRequest(userProfile == null ? undefined : userProfile.email));
    };
    var getShortcutLinkExplorer = function getShortcutLinkExplorer(location) {
      var _location$coords, _location$coords2;
      var params = {
        latitude: (location == null || (_location$coords = location.coords) == null ? undefined : _location$coords.latitude) || "",
        longitude: (location == null || (_location$coords2 = location.coords) == null ? undefined : _location$coords2.longitude) || "",
        isViewMore: false
      };
      dispatch(_exploreRedux.default.shortcutLinksExploreRequest(params));
    };
    (0, _react.useEffect)(function () {
      if (!isNativeOnboardingScreen) {
        (0, _mmkvStorage.setAllInitialPromptsAreDone)(true);
        dispatch(_notificationRedux.default.toggleL1Advisory(true));
      }
    }, [isNativeOnboardingScreen]);
    (0, _react.useEffect)(function () {
      if (isNativeOnboardingScreen && finishBiometric) {
        dispatch(_notificationRedux.default.toggleL1Advisory(false));
        if (_reactNative2.Platform.OS === "ios") {
          _reactNativeGeolocationService.default.requestAuthorization("whenInUse").then(function () {
            setShowGrantPermissionFlow(true);
          });
        } else {
          (0, _reactNativePermissions.request)(_reactNativePermissions.PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION).then(function () {
            setShowGrantPermissionFlow(true);
          });
        }
      }
      return function () {
        dispatch(_exploreRedux.default.setNoInternetFilter(true));
      };
    }, [finishBiometric, isNativeOnboardingScreen]);
    (0, _react.useEffect)(function () {
      if (isLoggedIn) {
        if (myTravelFlightsPayload != null && myTravelFlightsPayload.done) {
          loadShortcutLinks();
        }
      } else {
        loadShortcutLinks();
      }
    }, [loadShortcutLinks, isLoggedIn, myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.done]);
    (0, _react.useEffect)(function () {
      var isNotFinishedLoading = (0, _utils.ifOneTrue)([isExploreDataLoading, loadingExplorePlayPass, loadingCartPlayPass, loadingGetConfig, staffPerkRequest, shortcutLinksExploreData == null ? undefined : shortcutLinksExploreData.isLoading, latestHappeningsRequest, upComingFlightFetching, justForYouCarouselFetching, recommendedProductsFetching]);
      checkMonarchOverlay(!isNotFinishedLoading, rewardsData, userProfile, isFocused);
      if ((isScrollToExploreChangiSection || exploreSection === "explorechangi") && !isNotFinishedLoading) {
        //scroll to the explore section
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          setTimeout(function () {
            (0, _screenHelper.scrollScreen)(scrollViewRef, refScreen.current, false);
            navigation.setParams({
              isScrollToExploreChangiSection: false,
              section: ""
            });
          }, 200);
        });
      }
    }, [defaultScrollOffset, isScrollToExploreChangiSection, navigation, exploreSection, isExploreDataLoading, loadingExplorePlayPass, loadingCartPlayPass, loadingGetConfig, staffPerkRequest, shortcutLinksExploreData == null ? undefined : shortcutLinksExploreData.isLoading, latestHappeningsRequest, upComingFlightFetching, justForYouCarouselFetching, isFocused, recommendedProductsFetching, refScreen.current]);
    (0, _react.useEffect)(function () {
      dispatch(_nativeAuthRedux.default.saveLegacyUserInfo({
        info: {},
        isLegacy: false
      }));
    }, []);
    var updatePageNumber = _react.default.useCallback(function (event) {
      var contentOffset = event.nativeEvent.contentOffset;
      offsetY.value = contentOffset.y;
      if ((scrollDirection == null ? undefined : scrollDirection.current) === "up") return;
      if ((0, _screenHelper.isCloseToBottom)(event, _exploreContainerProps.SCROLL_END_THRESHOLD)) {
        hasMoreExploreData && dispatch(_exploreRedux.default.exploreDataSetPageNumber());
      }
    }, [isEventsCategorySelected, hasMoreExploreData]);
    var _useCPay = (0, _changipay.useCPay)(),
      openChangiPay = _useCPay.openChangiPay;
    var onChangiPay = /*#__PURE__*/function () {
      var _ref0 = (0, _asyncToGenerator2.default)(function* () {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeWallet, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeWallet, "1"));
        getConfigApp({
          configKey: _constants.AppConfigPermissionTypes.changiappWalletEnabled,
          callbackSuccess: function callbackSuccess() {
            return openChangiPay();
          },
          callbackFailure: function callbackFailure() {
            return notifyDisableChangiPay();
          }
        });
      });
      return function onChangiPay() {
        return _ref0.apply(this, arguments);
      };
    }();
    var displayScrollBuddy = (0, _react.useCallback)(function () {
      scrollBuddyOpacity.value = (0, _reactNativeReanimated.withSpring)(1);
    }, []);
    var hideScrollBuddy = (0, _react.useCallback)(function () {
      scrollBuddyOpacity.value = (0, _reactNativeReanimated.withSpring)(0);
    }, []);
    var onScroll = _react.default.useCallback(function (event) {
      if (event.nativeEvent.contentOffset.y < SCROLL_BUDDY_THRESHOLD) {
        (0, _reactNativeReanimated.runOnJS)(displayScrollBuddy)();
      } else {
        // runOnJS(hideScrollBuddy)()
      }
      handleScroll(event);
      scrollOffsetRef.current = event.nativeEvent.contentOffset.y;
      if (isExploreDataLoading || isExploreDataError) return;
      updatePageNumber(event);
    }, [isExploreDataLoading, isExploreDataError, isEventsCategorySelected, hasMoreExploreData]);
    var onScrollBegin = _react.default.useCallback(function () {
      isBegindScroll.value = true;
    }, []);
    var setScrollOffsetObject = _react.default.useCallback(function (newSelectedExploreCategory) {
      var newValue = scrollOffsets.value;
      newValue[newSelectedExploreCategory] = scrollOffsetRef == null ? undefined : scrollOffsetRef.current;
      scrollOffsets.value = newValue;
    }, []);
    var onScrollTouchEnd = _react.default.useCallback(function () {
      if (isBegindScroll.value) {
        setScrollOffsetObject(selectedExploreCategory);
        isBegindScroll.value = false;
      }
    }, [selectedExploreCategory]);
    var onScrollEnd = _react.default.useCallback(function () {
      setScrollOffsetObject(selectedExploreCategory);
    }, [selectedExploreCategory]);
    var onExploreContainerLayout = _react.default.useCallback(function (layoutOffset) {
      refScreen.current = layoutOffset;
      setDefaultScrollOffset(layoutOffset);
    }, []);
    var shortcutLinksOnPressed = _react.default.useCallback(/*#__PURE__*/function () {
      var _ref1 = (0, _asyncToGenerator2.default)(function* (shortcutLinkDetail) {
        var _shortcutLinksExplore;
        var cappHomeQuicklinksValueToBeSent = shortcutLinkDetail == null ? undefined : shortcutLinkDetail.title;
        var isTrackBaggageItem = (shortcutLinkDetail == null ? undefined : shortcutLinkDetail.title) === "Track Baggage";
        if (isTrackBaggageItem) {
          var UID = yield (0, _screenHelper.getViewerUID)({
            shouldReturnNull: true
          });
          cappHomeQuicklinksValueToBeSent = `${cappHomeQuicklinksValueToBeSent} | ${UID}`;
        }
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeQuicklinks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeQuicklinks, cappHomeQuicklinksValueToBeSent));
        var userTypeFromQuery = shortcutLinksExploreData == null || (_shortcutLinksExplore = shortcutLinksExploreData.data) == null ? undefined : _shortcutLinksExplore.userType;
        var userTypeValue = USER_TYPE_VALUE_UNDEFINED;
        if (userTypeFromQuery === USER_TYPE_FLY_AIRPORTVISIT) {
          userTypeValue = USER_TYPE_VALUE_FLY_AIRPORTVISIT;
        }
        if (userTypeFromQuery === USER_TYPE_FLY_HOME) {
          userTypeValue = USER_TYPE_VALUE_FLY_HOME;
        }
        if (userTypeFromQuery === USER_TYPE_FLY_AIRPORT) {
          userTypeValue = USER_TYPE_VALUE_FLY_AIRPORT;
        }
        if (userTypeFromQuery === USER_TYPE_GENERAL) {
          userTypeValue = USER_TYPE_VALUE_GENERAL;
        }
        if (userTypeFromQuery === USER_TYPE_MONARC) {
          userTypeValue = USER_TYPE_VALUE_MONARC;
        }
        var indexValueToBeSent = (shortcutLinkDetail == null ? undefined : shortcutLinkDetail.index) + 1;
        var trackActionValueToBeSent = `${userTypeValue} | ${shortcutLinkDetail == null ? undefined : shortcutLinkDetail.title} | ${indexValueToBeSent}`;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeQuicklinksPersonalization, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeQuicklinksPersonalization, trackActionValueToBeSent));
        var _ref10 = shortcutLinkDetail || {},
          navigationType = _ref10.navigationType,
          navigationValue = _ref10.navigationValue,
          isSeeMore = _ref10.isSeeMore,
          redirect = _ref10.redirect,
          shortcutLinkType = _ref10.shortcutLinkType,
          tabName = _ref10.tabName,
          playpassCampaignCd = _ref10.playpassCampaignCd,
          playpassPackageCd = _ref10.playpassPackageCd,
          redirectToShowOnListing = _ref10.redirectToShowOnListing;
        if (isSeeMore) {
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
          setModalVisible(true);
          return;
        }
        var isPlaypassShortcutLink = shortcutLinkType === _constants.SHORTCUT_LINK_TYPES.PLAYPASS;
        if (isPlaypassShortcutLink) {
          setModalVisible(false);
          (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          dispatch(_exploreRedux.default.setIndexItemExploreChangi(-1));
          var shouldScrollToExploreChangi = redirectToShowOnListing && (!!playpassCampaignCd || !!playpassPackageCd);
          if (shouldScrollToExploreChangi) {
            if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
              var _scrollViewRef$curren;
              scrollViewRef == null || (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollToEnd({
                animated: true
              });
            } else {
              handleScrollToExploreChangi(tabName, playpassPackageCd, playpassCampaignCd);
            }
            return;
          }
          var shouldRedirectToPlaypassPage = !redirectToShowOnListing && !!playpassPackageCd;
          if (shouldRedirectToPlaypassPage) {
            if (isLoggedIn) {
              getPlayPassUrl(_constants.StateCode.PPEVENT, playpassPackageCd, {
                entryPoint: _exploreItemType.PlayPassEntryPoint.EXPLORE_CHANGI,
                eventName: "exploreShortcutLink"
              });
            } else {
              var _env;
              var params = `?app=3&package_code=${playpassPackageCd}`;
              navigation.navigate(_constants.NavigationConstants.playpassWebview, {
                uri: `${(_env = (0, _envParams.env)()) == null ? undefined : _env.PLAYPASS_URL_NONE_LOGIN}${params}`,
                title: "",
                needBackButton: true,
                needCloseButton: true
              });
            }
            return;
          }
        }
        if (!navigationValue || !navigationType) return;
        setModalVisible(false);
        (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
        if (navigationValue === "exploreChangi") {
          (0, _screenHelper.scrollScreen)(scrollViewRef, defaultScrollOffset, true);
          return;
        }
        setTimeout(function () {
          handleNavigation(navigationType, navigationValue, redirect, {
            redirectFrom: _constants.CM24RedirectSource.QuickLinks
          });
        }, 500);
      });
      return function (_x6) {
        return _ref1.apply(this, arguments);
      };
    }(), [navigation, currentScrollOffset, defaultScrollOffset, isLoggedIn, sectionMaintenanceObj == null ? undefined : sectionMaintenanceObj.enableMode]);
    (0, _react.useEffect)(function () {
      if (isPkgCode && !(0, _lodash.isEmpty)(pkgCodeLastedHappening)) {
        if (pkgCodeLastedHappening === "DEFAULT_CODE") {
          var _exploreDataPayload$e;
          var index = exploreDataPayload == null || (_exploreDataPayload$e = exploreDataPayload.exploreCategoriesData) == null ? undefined : _exploreDataPayload$e.findIndex(function (data) {
            return (data == null ? undefined : data.campaignCode) === campaignCodeHappening;
          });
          if (index === -1) {
            (0, _screenHelper.scrollScreen)(scrollViewRef, defaultScrollOffset, true);
            dispatch(_exploreRedux.default.resetPkgCodeLastedHappening());
          } else {
            dispatch(_exploreRedux.default.setIndexItemExploreChangi(index));
          }
        } else {
          var convertedExploreChangiData = (0, _exploreHelper.convertExploreChangiData)(exploreDataPayload == null ? undefined : exploreDataPayload.exploreCategoriesData);
          var _index = convertedExploreChangiData == null ? undefined : convertedExploreChangiData.findIndex(function (data) {
            return (data == null ? undefined : data.packageCode) === pkgCodeLastedHappening;
          });
          dispatch(_exploreRedux.default.setIndexItemExploreChangi(_index));
        }
      }
    }, [isPkgCode, pkgCodeLastedHappening, campaignCodeHappening]);
    (0, _react.useEffect)(function () {
      if (offsetItemExploreChangi > 0) {
        var _scrollViewRef$curren2;
        scrollViewRef == null || (_scrollViewRef$curren2 = scrollViewRef.current) == null || _scrollViewRef$curren2.scrollTo({
          y: offsetItemExploreChangi + offsetY.value - exploreChangiTabHeight.value,
          animated: true
        });
        dispatch(_exploreRedux.default.resetPkgCodeLastedHappening());
      }
    }, [indexItemExploreChangi, offsetItemExploreChangi]);
    var handleScrollToExploreChangi = function handleScrollToExploreChangi(tabName, packageCode, campaignCode) {
      setFilterDate(initialFilterDate);
      setCheckedLocationState(initialFilterLocation);
      var pkgCode = (0, _lodash.isEmpty)(packageCode) ? "DEFAULT_CODE" : packageCode;
      dispatch(_exploreRedux.default.setCampaignCodeHappening(campaignCode || ""));
      dispatch(_exploreRedux.default.setPkgCodeLastedHappening(pkgCode));
      var category = (0, _lodash.isEmpty)(tabName) ? "All" : tabName;
      (0, _exploreHelper.navigateToTab)(selectedExploreCategory, currentScrollOffset, category, dispatch);
      dispatch(_exploreRedux.default.exploreDataReset());
      dispatch(_exploreRedux.default.resetExploreChangiLocation());
      dispatch(_exploreRedux.default.exploreDataRequest({
        pageNumber: 1,
        category: category,
        categoryCode: [],
        email: userProfile == null ? undefined : userProfile.email,
        date: "",
        locations: [],
        pkgCode: packageCode || ""
      }));
    };
    var onLatestHappeningSelected = (0, _react.useCallback)(function (latestHappeningDetail) {
      var _ref11 = latestHappeningDetail || {},
        packageCode = _ref11.packageCode,
        campaignCode = _ref11.campaignCode,
        redirectToShowOnListing = _ref11.redirectToShowOnListing,
        tabName = _ref11.tabName,
        type = _ref11.type,
        _ref11$title = _ref11.title,
        title = _ref11$title === undefined ? "" : _ref11$title,
        navigationType = _ref11.navigationType,
        navigationValue = _ref11.navigationValue,
        _ref11$sequenceNumber = _ref11.sequenceNumber,
        sequenceNumber = _ref11$sequenceNumber === undefined ? "" : _ref11$sequenceNumber,
        _ref11$fragmentTitle = _ref11.fragmentTitle,
        fragmentTitle = _ref11$fragmentTitle === undefined ? "" : _ref11$fragmentTitle,
        _ref11$redirect = _ref11.redirect,
        redirect = _ref11$redirect === undefined ? {} : _ref11$redirect;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomeLatestHappenings, (0, _defineProperty2.default)((0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomeLatestHappenings, title), _adobe.AdobeTagName.CAppLatestHappeningsDetails, `${sequenceNumber} | ${fragmentTitle} | ${type}`));
      if (navigationType === _navigationType.NavigationTypeEnum.deepLink && [_navigationHelper.NavigationValueDeepLink.gameMain, _navigationHelper.NavigationValueDeepLink.gameAsteroid, _navigationHelper.NavigationValueDeepLink.gameMissionpass, _navigationHelper.NavigationValueDeepLink.gameSkillGame].some(function (val) {
        return val === navigationValue;
      })) {
        handleNavigation(navigationType, navigationValue, {
          aaTag: (0, _utils.joinTexts)([_navigationHelper.NavigationAATag.LatestHappenings, title]),
          isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
          pageSource: _navigationHelper.NavigationPageSource.LatestHappenings
        });
      } else if (type === "CMS") {
        if (!navigationValue || !navigationType) return;
        handleNavigation(navigationType, navigationValue, redirect, {
          redirectFrom: _constants.CM24RedirectSource.LatestHappenings
        });
      } else {
        dispatch(_exploreRedux.default.setIndexItemExploreChangi(-1));
        if (!redirectToShowOnListing) {
          if (isLoggedIn) {
            dispatch(_exploreRedux.default.setIsPlaypassItemClicked(true));
            getPlayPassUrl(_constants.StateCode.PPEVENT, packageCode, {
              entryPoint: _exploreItemType.PlayPassEntryPoint.EXPLORE_CHANGI,
              eventName: "latestHappeningDetail"
            });
          } else {
            var _env2;
            var params = `?app=3&package_code=${packageCode}`;
            navigation.navigate(_constants.NavigationConstants.playpassWebview, {
              uri: `${(_env2 = (0, _envParams.env)()) == null ? undefined : _env2.PLAYPASS_URL_NONE_LOGIN}${params}`,
              title: "",
              needBackButton: true,
              needCloseButton: true
            });
          }
          return null;
        } else {
          dispatch(_airportLandingRedux.default.setPlayPassEntryPoint(_exploreItemType.PlayPassEntryPoint.LATEST_HAPPENING));
          if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
            var _scrollViewRef$curren3;
            scrollViewRef == null || (_scrollViewRef$curren3 = scrollViewRef.current) == null || _scrollViewRef$curren3.scrollToEnd({
              animated: true
            });
          } else {
            handleScrollToExploreChangi(tabName, packageCode, campaignCode);
          }
          return null;
        }
      }
    }, [dispatch, isLoggedIn, sectionMaintenanceObj, getPlayPassUrl]);
    var onClosedSheet = function onClosedSheet() {
      setModalVisible(false);
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
    };
    (0, _react.useEffect)(function () {
      var fetchAtomRMConfig = function fetchAtomRMConfig() {
        var mapFlagEnable = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ATOMS_MAP);
        setMapRMFlag(mapFlagEnable);
      };
      fetchAtomRMConfig();
    }, []);
    var _useContext4 = (0, _react.useContext)(_panResponder.PanResponderContext),
      currentForceUpdateFlow = _useContext4.currentForceUpdateFlow;
    useCheckSurveyOpen(isTriggerPopupSuccess, (0, _envParams.env)(), currentForceUpdateFlow);
    (0, _react.useEffect)(function () {
      var _shortcutLinksExplore2;
      var links = shortcutLinksExploreData == null || (_shortcutLinksExplore2 = shortcutLinksExploreData.data) == null ? undefined : _shortcutLinksExplore2.shortcutLinks;
      if (!(0, _lodash.isEmpty)(links)) {
        if (!mapRMFlag) {
          var _links;
          links = (_links = links) == null ? undefined : _links.filter(function (link) {
            return (link == null ? undefined : link.navigationValue) !== "atomMap";
          });
        }
        setShortcutLinksProcessed(links);
      }
    }, [shortcutLinksExploreData, mapRMFlag]);
    var shortcutLinksData = (0, _react.useMemo)(function () {
      if (shortcutLinksProcessed && Array.isArray(shortcutLinksProcessed) && (shortcutLinksProcessed == null ? undefined : shortcutLinksProcessed.length) > MAX_ITEM_SHOW_SEE_MORE) {
        var seeMore = {
          icon: _icons.SeeMore,
          title: (0, _i18n.translate)("exploreScreen.seeMore"),
          isSeeMore: true,
          isCustomIcon: true
        };
        return [].concat((0, _toConsumableArray2.default)(shortcutLinksProcessed.slice(0, MAX_ITEM_SHOW_SEE_MORE)), [seeMore]);
      }
      return shortcutLinksProcessed;
    }, [shortcutLinksProcessed]);
    var _playPassItemOnPressed = function playPassItemOnPressed(eventDetailsItem) {
      var _ref12 = eventDetailsItem || "",
        navigationType = _ref12.navigationType,
        navigationValue = _ref12.navigationValue,
        redirect = _ref12.redirect;
      if (!navigationValue) return;
      handleNavigation(navigationType, navigationValue, redirect);
    };
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      return function () {
        _reactNative2.InteractionManager.runAfterInteractions(function () {
          setTimeout(function () {
            setShowFilterModal(false);
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          }, 300);
        });
      };
    }, []));
    var scrollBuddyOnPress = function scrollBuddyOnPress() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassLandingTiles, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassLandingTiles, "Scroll buddy"));
      // Handle deep-link logic
      var navigationData = scrollBuddyData == null ? undefined : scrollBuddyData.navigation;
      if (navigationData) {
        handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, [_navigationHelper.NavigationValueDeepLink.gameMain, _navigationHelper.NavigationValueDeepLink.gameAsteroid, _navigationHelper.NavigationValueDeepLink.gameMissionpass].some(function (val) {
          return val === (navigationData == null ? undefined : navigationData.value);
        }) ? {
          aaTag: _navigationHelper.NavigationAATag.ScrollBuddy,
          isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
          pageSource: _navigationHelper.NavigationPageSource.ScrollBuddy
        } : undefined);
        return;
      }
      var category = (scrollBuddyData == null ? undefined : scrollBuddyData.category) || "All";
      if (!(0, _lodash.isEmpty)(sectionMaintenanceObj) && sectionMaintenanceObj != null && sectionMaintenanceObj.enableMode) {
        var _scrollViewRef$curren4;
        scrollViewRef == null || (_scrollViewRef$curren4 = scrollViewRef.current) == null || _scrollViewRef$curren4.scrollToEnd({
          animated: true
        });
      } else {
        handleScrollToExploreChangi(category, "", "");
      }
    };
    var renderScrollBuddyComponent = (0, _react.useMemo)(function () {
      var _announcementPayload$;
      var validAnnouncementLength = announcementPayload == null || announcementPayload.filter == null || (_announcementPayload$ = announcementPayload.filter(function (item) {
        var _item$extraJsonData;
        return item == null || (_item$extraJsonData = item.extraJsonData) == null || (_item$extraJsonData = _item$extraJsonData.screens) == null || _item$extraJsonData.some == null ? undefined : _item$extraJsonData.some(function (scr) {
          return (scr == null ? undefined : scr.tagName) === _constants.NotificationL1Page.Explore;
        });
      })) == null ? undefined : _announcementPayload$.length;
      if (isShowCardToast || !isExistScrollBuddy || loadingBookingData || validAnnouncementLength) {
        return null;
      }
      return (0, _jsxRuntime.jsx)(_scrollBuddy.ScrollBuddy, {
        opacity: scrollBuddyOpacity,
        scrollBuddyImage: scrollBuddyData == null ? undefined : scrollBuddyData.image,
        onPress: scrollBuddyOnPress
      });
    }, [isShowCardToast, isExistScrollBuddy, scrollBuddyData, loadingBookingData, JSON.stringify(announcementPayload)]);
    var grantPermissionContent = (0, _react.useCallback)(function () {
      return (0, _utils.handleCondition)(_reactNative2.Platform.OS === "android", function () {
        var androidLevel = _reactNativeDeviceInfo.default.getSystemVersion();
        return (0, _utils.handleCondition)(Number(androidLevel) < 13, (0, _jsxRuntime.jsx)(_grantPermissionModalOldVersion.GrantPermissionModalOldVersion, {}), (0, _jsxRuntime.jsx)(_grantPermissionModal.GrantPermissionModal, {}));
      }(), (0, _jsxRuntime.jsx)(_grantPermissionModal.GrantPermissionModal, {}));
    }, []);
    var onShowFilterModal = (0, _react.useCallback)(function () {
      (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(true);
      setShowFilterModal(true);
    }, []);
    (0, _exploreScreen.useBottomSheetError)({
      route: route
    });
    return (0, _jsxRuntime.jsx)(_suspend.default, {
      freeze: !isFocused,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        testID: "ExploreScreen",
        style: _exploreScreenStyles.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
          translucent: true,
          backgroundColor: "transparent",
          barStyle: (imageHomepageAEM == null ? undefined : imageHomepageAEM.background) === "Dark" || errorAEM ? "light-content" : "dark-content"
        }), isDisplayTickerBand && (0, _jsxRuntime.jsx)(_tickerBand.default, {
          urgent: false,
          title: tickerBand,
          description: tickerBandDescription,
          buttonText: tickerBandButtonText,
          onCTAPress: onPressCTA,
          onClose: function onClose() {
            return onCloseTickerBand();
          },
          tickerStyle: {
            paddingTop: 50
          }
        }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          onScroll: onScroll,
          onScrollEndDrag: updatePageNumber,
          onScrollBeginDrag: onScrollBegin,
          onMomentumScrollEnd: onScrollEnd,
          onTouchEnd: onScrollTouchEnd,
          scrollEventThrottle: 0.1,
          ref: scrollViewRef,
          showsVerticalScrollIndicator: false,
          stickyHeaderIndices: [2],
          testID: `${SCREEN_NAME}__ScrollView`,
          accessibilityLabel: `${SCREEN_NAME}__ScrollView`,
          contentContainerStyle: {
            paddingBottom: bottomTabHeight
          },
          children: [(0, _jsxRuntime.jsx)(_homepageMasthead.default, {
            testID: `${SCREEN_NAME}__HomePageMasthead`,
            accessibilityLabel: `${SCREEN_NAME}__HomePageMasthead`,
            onChangiPayPressed: onChangiPay,
            onReLoadUpComingEvent: onReLoadUpComingEvent
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: wrapOrderExplore,
            children: configPage == null ? undefined : configPage.map(function (element, index) {
              var needSeparator = (0, _lodash.size)(configPage) - 1 !== index;
              var isLastItem = (0, _lodash.size)(configPage) === index + 1;
              switch (element == null ? undefined : element.sectionComponent) {
                case ExploreComponentTypes.shortcutLink:
                  return shortcutLinksExploreData != null && shortcutLinksExploreData.hasError ? (0, _jsxRuntime.jsx)(_error.ErrorComponent, {
                    type: _error.ErrorComponentType.standard,
                    onPressed: function onPressed() {
                      return loadShortcutLinks();
                    },
                    style: _exploreScreenStyles.styles.errorComponent,
                    testID: `${SCREEN_NAME}__ErrorComponent`,
                    accessibilityLabel: `${SCREEN_NAME}__ErrorComponent`
                  }, `${element == null ? undefined : element.sectionComponent}-${index}`) : (0, _jsxRuntime.jsx)(_sections.ShortCutLinks, {
                    data: shortcutLinksData,
                    type: (0, _utils.handleCondition)(shortcutLinksExploreData == null ? undefined : shortcutLinksExploreData.isLoading, _shortcutLink.ShortcutLinkType.loading, _shortcutLink.ShortcutLinkType.default),
                    onPressed: shortcutLinksOnPressed,
                    testID: `${SCREEN_NAME}__ShortCutLinksExplore`,
                    accessibilityLabel: `${SCREEN_NAME}__ShortCutLinksExplore`,
                    useSeparator: needSeparator,
                    isLastItem: isLastItem
                  }, `${element == null ? undefined : element.sectionComponent}-${index}`);
                case ExploreComponentTypes.timelineTile:
                  return (
                    // <UpComingFlightSection
                    //   useSeparator={needSeparator}
                    //   isLastItem={isLastItem}
                    //   key={`${element?.sectionComponent}-${index}`}
                    // />
                    (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {})
                  );
                case ExploreComponentTypes.latestHappening:
                  return (0, _jsxRuntime.jsx)(_latestHappenings.LatestHappenings, {
                    onSelected: onLatestHappeningSelected,
                    testID: `${SCREEN_NAME}__ShortCutLinksExplore`,
                    accessibilityLabel: `${SCREEN_NAME}__ShortCutLinksExplore`,
                    useSeparator: needSeparator,
                    isLastItem: isLastItem
                  }, `${element == null ? undefined : element.sectionComponent}-${index}`);
                case ExploreComponentTypes.justForYou:
                  return (0, _jsxRuntime.jsx)(_justForYouCarouselScreen.default, {
                    testID: `${SCREEN_NAME}__JustForYouCarousel`,
                    accessibilityLabel: `${SCREEN_NAME}__JustForYouCarousel`,
                    useSeparator: needSeparator,
                    isLastItem: isLastItem,
                    setJustForYouDataFetching: setJustForYouDataFetching
                  }, `${element == null ? undefined : element.sectionComponent}-${index}`);
                case ExploreComponentTypes.staffPerkSwimlane:
                  return (0, _jsxRuntime.jsx)(_exploreStaffPerk.default, {
                    navigation: navigation,
                    useSeparator: needSeparator,
                    isLastItem: isLastItem,
                    setStaffPerkRequest: setStaffPerkRequest,
                    isExploreStaffPerksFlag: isExploreStaffPerksFlag
                  }, `${element == null ? undefined : element.sectionComponent}-${index}`);
                default:
                  return null;
              }
            })
          }), (0, _jsxRuntime.jsx)(_tabBarContainer.TabBarContainer, {
            scrollOffset: currentScrollOffset,
            testID: `${SCREEN_NAME}__ShortCutLinksExplore`,
            accessibilityLabel: `${SCREEN_NAME}__ShortCutLinksExplore`,
            setHeigtExploreChangiTab: onExploreTabLayout,
            showFilterModal: onShowFilterModal
          }), (0, _jsxRuntime.jsx)(_exploreContainer.ExploreContainer, {
            scrollViewRef: scrollViewRef,
            onExploreContainerLayout: onExploreContainerLayout,
            playPassItemOnPressed: function playPassItemOnPressed(data) {
              return _playPassItemOnPressed(data);
            },
            showFilterModal: onShowFilterModal,
            heigtExploreChangiTab: exploreChangiTabHeight,
            testID: `${SCREEN_NAME}__ShortCutLinksExplore`,
            accessibilityLabel: `${SCREEN_NAME}__ShortCutLinksExplore`
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _exploreScreenStyles.styles.paddingStyle
          })]
        }), (0, _jsxRuntime.jsx)(_modalSwipe.default, {
          modalVisible: isModalVisible,
          PressToanimate: animateModal,
          ContentModal: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _exploreScreenStyles.styles.containerContent,
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _exploreScreenStyles.styles.bottomSheetStyle,
              children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: _exploreScreenStyles.styles.bottomSheetStyle,
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  preset: "h2",
                  style: _exploreScreenStyles.styles.titleBottomSheet,
                  children: (0, _i18n.translate)("exploreScreen.exploreChangi")
                }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                  style: [_exploreScreenStyles.styles.shortcutLinkContainer, shortcutLinkStyles],
                  children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
                    data: shortcutLinksProcessed,
                    renderItem: function renderItem(_ref13) {
                      var item = _ref13.item,
                        index = _ref13.index;
                      var updatedItem = Object.assign({}, item, {
                        index: index,
                        shortcutLinkType: item == null ? undefined : item.type
                      });
                      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
                        style: _exploreScreenStyles.styles.itemShortcutLinkView,
                        children: (0, _jsxRuntime.jsx)(_shortcutLink2.default, Object.assign({
                          type: shortcutLinksExploreData != null && shortcutLinksExploreData.isLoading ? _shortcutLink.ShortcutLinkType.loading : _shortcutLink.ShortcutLinkType.default
                        }, updatedItem, {
                          onPressed: shortcutLinksOnPressed
                        }))
                      });
                    },
                    horizontal: false,
                    contentContainerStyle: _exploreScreenStyles.styles.swipeModalContentContainerStyle,
                    scrollEnabled: true,
                    showsVerticalScrollIndicator: false,
                    keyExtractor: function keyExtractor(_, index) {
                      return index.toString();
                    },
                    numColumns: 3,
                    testID: `${SCREEN_NAME}__FlatListData`,
                    accessibilityLabel: `${SCREEN_NAME}__FlatListData`,
                    ItemSeparatorComponent: function ItemSeparatorComponent() {
                      return (0, _jsxRuntime.jsx)(SeparatorComponent, {});
                    }
                  })
                })]
              })
            })
          }),
          HeaderStyle: _exploreScreenStyles.styles.headerContent,
          HeaderContent: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _exploreScreenStyles.styles.containerHeader,
            children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _exploreScreenStyles.styles.swipeModalHeaderContent,
              children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                onPress: onClosedSheet,
                children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                  style: _exploreScreenStyles.styles.iconCloseModalStyle,
                  source: _icons.ArrowDownWidth,
                  resizeMode: "contain"
                })
              })
            })
          }),
          MainContainerModal: _exploreScreenStyles.styles.mainContainerModal,
          onClose: function onClose() {
            setModalVisible(false);
            setanimateModal(false);
            (0, _mmkvStorage.setIsShowModalCheckRatingPopup)(false);
          }
        }), renderScrollBuddyComponent, isLoggedIn && (0, _jsxRuntime.jsx)(_cartToast.default, {
          toastRef: toastRef,
          testID: `${SCREEN_NAME}__CartToast`,
          accessibilityLabel: `${SCREEN_NAME}__CartToast`,
          setIsShowCardToast: setIsShowCardToast
        }), (0, _jsxRuntime.jsx)(_exploreChangiModalFilter.default, {
          showFilterModal: showFilterModal,
          setShowFilterModal: setShowFilterModal,
          filterDate: filterDate,
          setFilterDate: setFilterDate,
          checkedLocationState: checkedLocationState,
          setCheckedLocationState: setCheckedLocationState,
          initialFilterLocation: initialFilterLocation,
          testID: `${SCREEN_NAME}__ModalFilter`,
          accessibilityLabel: `${SCREEN_NAME}__ModalFilter`
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: isExploreDataLoading || loadingExplorePlayPass || loadingCartPlayPass || loadingGetConfig
        }), showGrantPermissionFlow && grantPermissionContent(), (0, _jsxRuntime.jsx)(_monarchOverlay.default, {}), (0, _jsxRuntime.jsx)(_bottomSheetError.BottomSheetError, {
          icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
          visible: Boolean(playPassUrlPayload == null ? undefined : playPassUrlPayload.error) && isFocused,
          title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
          errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
          onClose: function onClose() {
            dispatch(_airportLandingRedux.default.getPlayPassUrlReset("FAQupload"));
          },
          onButtonPressed: function onButtonPressed() {
            dispatch(_airportLandingRedux.default.getPlayPassUrlReset("FAQupload"));
          },
          buttonText: (0, _i18n.translate)("subscription.close"),
          testID: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong__Playpass`,
          accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong__Playpass`
        })]
      })
    });
  };
  var SeparatorComponent = function SeparatorComponent() {
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _exploreScreenStyles.styles.separatorStyle
    });
  };
  var _default = exports.default = _react.default.memo(ExploreScreen);
