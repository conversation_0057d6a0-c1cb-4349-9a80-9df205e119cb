  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _exploreRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[15]);
  var _exploreHelper = _$$_REQUIRE(_dependencyMap[16]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _bottomSheetError = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[20]));
  var _i18n = _$$_REQUIRE(_dependencyMap[21]);
  var _icons = _$$_REQUIRE(_dependencyMap[22]);
  var _bottomNavigator = _$$_REQUIRE(_dependencyMap[23]);
  var _animatedFeedbackToast = _$$_REQUIRE(_dependencyMap[24]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[25]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _envParams = _$$_REQUIRE(_dependencyMap[27]);
  var _queries = _$$_REQUIRE(_dependencyMap[28]);
  var _firebase = _$$_REQUIRE(_dependencyMap[29]);
  var _playpass = _$$_REQUIRE(_dependencyMap[30]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[31]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var feedBackToastStyle = function feedBackToastStyle(isUpdateStyle) {
    return {
      width: "95%",
      bottom: handleBottom(isUpdateStyle)
    };
  };
  var SCREEN_NAME = "ExploreScreen";
  var handleBottom = function handleBottom(isUpdateStyle) {
    if (isUpdateStyle) {
      if (_reactNative.Platform.OS === "android") {
        return 155;
      }
      return 105;
    }
    return 9;
  };
  var toastButtonStyle = {
    color: _theme.color.palette.lighterPurple,
    width: "35%"
  };
  var toastTextStyle = {
    color: _theme.color.palette.whiteGrey,
    flex: 1,
    fontSize: 16,
    lineHeight: 20
  };
  var CartToast = function CartToast(props) {
    var _props$testID = props.testID,
      testID = _props$testID === undefined ? "CartToast" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "CartToast" : _props$accessibilityL,
      toastRef = props.toastRef,
      setIsShowCardToast = props.setIsShowCardToast;
    var navigation = (0, _native.useNavigation)();
    var playPassStickyCart = (0, _playpass.usePlayPassStore)(function (state) {
      return state.playPassStickyCart;
    });
    var playPassStickyCartLoading = (0, _playpass.usePlayPassStore)(function (state) {
      return state.playPassStickyCartLoading;
    });
    var setPlayPassStickyCart = (0, _playpass.usePlayPassStore)(function (state) {
      return state.setPlayPassStickyCart;
    });
    var setPlayPassStickyCartLoading = (0, _playpass.usePlayPassStore)(function (state) {
      return state.setPlayPassStickyCartLoading;
    });
    var exploreDataLoadingError = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.exploreDataLoadingError);
    var _exploreDataLoadingEr = Object.assign({}, exploreDataLoadingError),
      isExploreDataError = _exploreDataLoadingEr.hasError;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isUpdateStyle = _useState2[0],
      setIsUpdateStyle = _useState2[1];
    var toastDuration = _constants.TOAST_MESSAGE_DURATION + 1000;
    var playPassUrlPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getPlayPassUrlPayload("viewCartPlayPass"));
    var _useContext = (0, _react.useContext)(_bottomNavigator.BottomNavContext),
      bottomTabsPosition = _useContext.bottomTabsPosition;
    var setDisplayCartToast = (0, _react.useCallback)(function (show) {
      setIsShowCardToast(show);
    }, [setIsShowCardToast]);
    (0, _react.useEffect)(function () {
      if (isExploreDataError) {
        setIsUpdateStyle(true);
        setTimeout(function () {
          setIsUpdateStyle(false);
        }, toastDuration);
      }
    }, [isExploreDataError]);
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewCartPlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var _ref = playPassStickyCart || {},
      cartId = _ref.cartId,
      timerTs = _ref.timerTs;
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var messagesCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var _ref2 = profilePayload || {},
      email = _ref2.email;
    var isEmptyData = playPassStickyCartLoading || !timerTs || !playPassStickyCart;
    var getPendingBookingRequest = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var dtAction = (0, _firebase.dtManualActionEvent)(_firebase.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_PENDING_PLAYPASS_BOOKING);
        dtAction.reportStringValue("function", "getPendingBookingRequest");
        var input = {
          userName: email,
          scope: "pending"
        };
        try {
          var _env, _env2, _response$data;
          var response = yield (0, _request.default)({
            url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
            method: "post",
            data: (0, _awsAmplify.graphqlOperation)(_queries.getAllBookingsQueryV2, {
              input: input
            }),
            parameters: {},
            headers: {
              "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
            }
          });
          dtAction.reportStringValue("status", "success");
          var data = (response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null || (_response$data = _response$data.getPlaypassBookings_v2) == null || (_response$data = _response$data.data) == null ? undefined : _response$data[0]) || null;
          setPlayPassStickyCart(data);
        } catch (error) {
          dtAction.reportStringValue("status", "failed");
          setPlayPassStickyCart(null);
        } finally {
          dtAction.leaveAction();
        }
      });
      return function getPendingBookingRequest() {
        return _ref3.apply(this, arguments);
      };
    }();
    var getPendingBookingsRequest = (0, _react.useCallback)(function () {
      if (email) {
        setPlayPassStickyCartLoading();
        getPendingBookingRequest();
      } else {
        setPlayPassStickyCart(null);
      }
    }, [email]);
    var _useCartTimer = (0, _exploreHelper.useCartTimer)({
        isEmptyData: isEmptyData,
        timerTs: timerTs,
        toastRefCurrent: toastRef == null ? undefined : toastRef.current,
        loading: playPassStickyCartLoading,
        fetchData: getPendingBookingsRequest
      }),
      secondsTimerValue = _useCartTimer.secondsTimerValue;
    var timerValue = (0, _exploreHelper.secondsTimer)(secondsTimerValue);
    var _useMemo = (0, _react.useMemo)(function () {
        var informativeCart = messagesCommon == null ? undefined : messagesCommon.find(function (item) {
          return item.code === "MSG127";
        });
        var newTitle = (0, _lodash.get)(informativeCart, "informativeText", "");
        var firstButton = (0, _lodash.get)(informativeCart, "firstButton");
        var secondButton = (0, _lodash.get)(informativeCart, "secondButton", "");
        var newButtonText = firstButton || secondButton;
        return {
          title: newTitle,
          buttonText: newButtonText
        };
      }, [messagesCommon]),
      title = _useMemo.title,
      buttonText = _useMemo.buttonText;
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        _reactNative.InteractionManager.runAfterInteractions(function () {
          getPendingBookingsRequest();
        });
      });
      return unsubscribeFocus;
    }, [email, navigation]);
    (0, _react.useEffect)(function () {
      var unsubscribeBlur = navigation.addListener("blur", function () {
        var _toastRef$current;
        toastRef == null || (_toastRef$current = toastRef.current) == null || _toastRef$current.closeNow();
      });
      return unsubscribeBlur;
    }, [navigation]);
    var onGoToCart = (0, _react.useCallback)(function () {
      if (cartId) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppHomePlaypassStickyCart, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppHomePlaypassStickyCart, `Sticky Cart | ${buttonText}`));
        getPlayPassUrl(_constants.StateCode.PPCART, cartId, {
          entryPoint: _exploreItemType.PlayPassEntryPoint.CHANGI_STICKY_CART,
          cardID: cartId
        });
      }
    }, [cartId]);
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_animatedFeedbackToast.AnimatedFeedBackToast, {
        ref: toastRef,
        style: feedBackToastStyle(isUpdateStyle),
        buttonTextStyle: toastButtonStyle,
        informativeTextStyle: toastTextStyle,
        position: "custom",
        type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA,
        text: title ? title.replace("<MM:SS>", timerValue) : "",
        buttonText: buttonText,
        onPress: onGoToCart,
        testID: `${testID}__FeedBackToast`,
        accessibilityLabel: `${accessibilityLabel}__FeedBackToast`,
        numberOfLines: 2,
        positionBottom: bottomTabsPosition,
        setDisplayCartToast: setDisplayCartToast
      }), (0, _jsxRuntime.jsx)(_bottomSheetError.default, {
        icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
        visible: Boolean(playPassUrlPayload == null ? undefined : playPassUrlPayload.error),
        title: (0, _i18n.translate)("popupError.somethingWrongOneline"),
        errorMessage: (0, _i18n.translate)("popupError.networkErrorMessage"),
        onClose: function onClose() {
          dispatch(_airportLandingRedux.default.getPlayPassUrlReset("viewCartPlayPass"));
        },
        onButtonPressed: function onButtonPressed() {
          dispatch(_airportLandingRedux.default.getPlayPassUrlReset("viewCartPlayPass"));
        },
        buttonText: (0, _i18n.translate)("subscription.close"),
        testID: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong`,
        accessibilityLabel: `${SCREEN_NAME}__BottomSheetErrorSomethingWrong`
      })]
    });
  };
  var _default = exports.default = _react.default.memo(CartToast);
