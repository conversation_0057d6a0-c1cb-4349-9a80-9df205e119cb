  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.buttonTitleStyle = exports.buttonTitleDisabledStyle = exports.buttonGradientStyle = exports.buttonGradientDisabledColors = exports.buttonGradientColors = exports.buttonContainerStyle = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width;
  var buttonGradientStyle = exports.buttonGradientStyle = {
    justifyContent: "center",
    borderRadius: 40,
    borderWidth: 0,
    width: width - 96,
    height: 44,
    borderColor: "transparent",
    marginTop: 24
  };
  var buttonGradientColors = exports.buttonGradientColors = [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End];
  var buttonGradientDisabledColors = exports.buttonGradientDisabledColors = [_theme.color.palette.greyCCCCCC, _theme.color.palette.greyCCCCCC];
  var buttonContainerStyle = exports.buttonContainerStyle = {
    margin: 0,
    padding: 0,
    borderWidth: 0
  };
  var buttonTitleStyle = exports.buttonTitleStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostWhiteGrey,
    textAlign: "center"
  });
  var buttonTitleDisabledStyle = exports.buttonTitleDisabledStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.darkGrey999,
    textAlign: "center"
  });
