  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.systemFonts = exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _adobe = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _yourReward = _$$_REQUIRE(_dependencyMap[9]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativeRenderHtml = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _walletRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[14]);
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _backgrounds = _$$_REQUIRE(_dependencyMap[16]);
  var _lodash = _$$_REQUIRE(_dependencyMap[17]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[18]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[20]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[21]);
  var _htmlContent = _$$_REQUIRE(_dependencyMap[22]);
  var _termAndCondition = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _contentRedemptionMode = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _contentForSpecialPerk = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _constants = _$$_REQUIRE(_dependencyMap[27]);
  var _theme = _$$_REQUIRE(_dependencyMap[28]);
  var _native = _$$_REQUIRE(_dependencyMap[29]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[30]);
  var _utils = _$$_REQUIRE(_dependencyMap[31]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[32]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var systemFonts = exports.systemFonts = [].concat((0, _toConsumableArray2.default)(_reactNativeRenderHtml.defaultSystemFonts), ["Lato-Regular", "Lato-Bold"]);
  var SCREEN_NAME = "YourReward";
  var YourRewardScreen = function YourRewardScreen(_ref) {
    var navigation = _ref.navigation,
      route = _ref.route;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _route$params = route == null ? undefined : route.params,
      item = _route$params.item,
      _route$params$onGoBac = _route$params.onGoBack,
      onGoBack = _route$params$onGoBac === undefined ? undefined : _route$params$onGoBac;
    var toastRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setIsConnected = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isShowError = _useState4[0],
      setIsShowError = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isDisableScrollVertical = _useState6[0],
      setIsDisableScrollVertical = _useState6[1];
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var walletMyDetailPerksFetching = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyDetailPerksFetching);
    var walletMyDetailPerks = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyDetailPerks);
    var walletMyDetailPerksError = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyDetailPerksError);
    var _ref2 = walletMyDetailPerks != null ? walletMyDetailPerks : {},
      subDescription = _ref2.subDescription,
      subDescription2 = _ref2.subDescription2,
      terms = _ref2.terms,
      voucher_type_description = _ref2.voucher_type_description;
    var refCallbackInternetErrorOverlay = (0, _react.useRef)({
      callback: null
    });
    var refCallbackErrorOverlay = (0, _react.useRef)({
      callback: null
    });
    var perkTitle = (0, _react.useMemo)(function () {
      return (0, _utils.toNumber)(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular;
    }, [item == null ? undefined : item.token_qty, item == null ? undefined : item.token_name_plural, item == null ? undefined : item.token_name_singular]);
    var isGamificationPrize = (item == null ? undefined : item.perk_type) === _constants.PerkType.GamificationPrize;
    var redemptionMode = isGamificationPrize ? item == null ? undefined : item.redemptionMode : item == null ? undefined : item.redemption_mode;
    var detailsData = (0, _react.useMemo)(function () {
      if (isGamificationPrize) {
        return {
          description: subDescription,
          termsAndConditions: subDescription2
        };
      }
      return {
        description: voucher_type_description,
        termsAndConditions: terms
      };
    }, [isGamificationPrize, subDescription, subDescription2, voucher_type_description, terms]);
    (0, _react.useEffect)(function () {
      return function () {
        dispatch(_walletRedux.default.resetWalletMyDetailPerks());
      };
    }, []);
    var _fetchData = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        if (!isConnectedNetInfo) {
          refCallbackInternetErrorOverlay.current.callback = _fetchData;
          setIsConnected(false);
        } else {
          var _route$params2;
          var params = isGamificationPrize ? {
            perk_type: item == null ? undefined : item.perk_type,
            gamification_prize_id: item == null ? undefined : item.id
          } : {
            perk_type: (item == null ? undefined : item.perk_type) || _constants.PerkType.ChangiRewardPerk,
            token_type: "",
            package_cd: "",
            campaign_cd: item == null ? undefined : item.perk_type,
            card_no: profilePayload == null ? undefined : profilePayload.cardNo,
            voucher_type_code: item == null ? undefined : item.perk_code,
            voucher_valid_from: item == null ? undefined : item.valid_from,
            voucher_valid_to: item == null ? undefined : item.expiry_dt
          };
          setIsConnected(true);
          if (route != null && (_route$params2 = route.params) != null && _route$params2.changi_reward_detail) {
            var _route$params3;
            params.changi_reward_detail = route == null || (_route$params3 = route.params) == null ? undefined : _route$params3.changi_reward_detail;
          }
          dispatch(_walletRedux.default.walletMyDetailPerksRequest(Object.assign({}, params)));
        }
      });
      return function fetchData() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(`Perk_Detail_${perkTitle}`);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(`Perk_Detail_${perkTitle}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
        _fetchData();
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(walletMyDetailPerksError)) {
        setIsShowError(true);
        refCallbackErrorOverlay.current.callback = function () {
          hideErrorOverlay == null || hideErrorOverlay();
          _fetchData == null || _fetchData();
        };
      }
    }, [walletMyDetailPerksError]);
    var showRewardCard = function showRewardCard() {
      _changiEcardControler.default.showModal(navigation);
    };
    var goBack = function goBack() {
      if (onGoBack) {
        onGoBack();
      }
      navigation.goBack();
    };
    var showInternetOverlay = function showInternetOverlay(callback) {
      refCallbackInternetErrorOverlay.current.callback = callback;
      setIsConnected(false);
    };
    var hideInternetOverlay = function hideInternetOverlay() {
      refCallbackInternetErrorOverlay.current.callback = null;
      setIsConnected(true);
    };
    var showErrorOverlay = function showErrorOverlay(callback) {
      refCallbackErrorOverlay.current.callback = callback;
      setIsShowError(true);
    };
    var hideErrorOverlay = function hideErrorOverlay() {
      refCallbackErrorOverlay.current.callback = null;
      setIsShowError(false);
    };
    var handleContent = function handleContent() {
      if (item != null && item.cr_vouchers_campaign) {
        return (0, _jsxRuntime.jsx)(_contentForSpecialPerk.default, {
          walletMyDetailPerks: walletMyDetailPerks,
          showRewardCard: showRewardCard,
          item: item
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_react.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_contentRedemptionMode.default, {
            redemptionMode: redemptionMode,
            data: walletMyDetailPerks,
            showRewardCard: showRewardCard,
            item: item,
            showErrorOverlay: showErrorOverlay,
            hideErrorOverlay: hideErrorOverlay,
            showInternetOverlay: showInternetOverlay,
            hideInternetOverlay: hideInternetOverlay,
            loading: false,
            onSwipeHorizontal: onSwipeHorizontal,
            toastRef: toastRef
          }), (detailsData == null ? undefined : detailsData.description) && (0, _jsxRuntime.jsx)(_reactNative.View, {
            style: _yourReward.styles.voucherDescription,
            children: (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
              source: {
                html: (0, _htmlContent.formatHtmlContent)(detailsData == null ? undefined : detailsData.description)
              },
              tagsStyles: _yourReward.tagStyle,
              contentWidth: _yourReward.CONTENT_WIDTH,
              systemFonts: systemFonts
            })
          }), (0, _jsxRuntime.jsx)(_termAndCondition.default, {
            redemptionMode: redemptionMode,
            term: (0, _htmlContent.formatHtmlContent)(detailsData == null ? undefined : detailsData.termsAndConditions)
          })]
        });
      }
    };
    var onSwipeHorizontal = function onSwipeHorizontal(state) {
      setIsDisableScrollVertical(state);
    };
    var onBackPress = (0, _react.useCallback)(function () {
      if (onGoBack) {
        onGoBack();
        return false;
      }
      return true;
    }, [onGoBack]);
    (0, _react.useEffect)(function () {
      if (onGoBack) {
        navigation.setOptions({
          gestureEnabled: false
        });
      }
    }, [onGoBack]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var subscription = _reactNative.BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return function () {
        return subscription.remove();
      };
    }, [onGoBack]));
    if (walletMyDetailPerksFetching) {
      return (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _yourReward.styles.container,
        children: (0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
          showsVerticalScrollIndicator: false,
          contentContainerStyle: _yourReward.styles.contentContainerStyle,
          children: (0, _jsxRuntime.jsxs)(_baseImageBackground.default, {
            source: _backgrounds.BackgroundPlayPass,
            imageStyle: _yourReward.styles.backgroundImage,
            resizeMode: "cover",
            children: [(0, _jsxRuntime.jsx)(_contentRedemptionMode.default, {
              redemptionMode: redemptionMode,
              data: walletMyDetailPerks,
              showRewardCard: showRewardCard,
              item: item,
              showErrorOverlay: showErrorOverlay,
              hideErrorOverlay: hideErrorOverlay,
              showInternetOverlay: showInternetOverlay,
              hideInternetOverlay: hideInternetOverlay,
              loading: true,
              onSwipeHorizontal: onSwipeHorizontal
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _yourReward.styles.shimmerPlaceholderLine1Style
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _yourReward.styles.shimmerPlaceholderLine2Style
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _theme.color.shimmerPlacholderColor,
              shimmerStyle: _yourReward.styles.shimmerPlaceholderLine2Style
            })]
          })
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _yourReward.styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.ScrollView, {
        showsVerticalScrollIndicator: false,
        contentContainerStyle: _yourReward.styles.contentContainerStyle,
        scrollEnabled: !isDisableScrollVertical,
        children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
          source: _backgrounds.BackgroundPlayPass,
          imageStyle: _yourReward.styles.backgroundImage,
          resizeMode: "cover",
          children: handleContent()
        })
      }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: true,
        headerBackgroundColor: "transparent",
        visible: !isConnected,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: function onReload() {
          var _refCallbackInternetE;
          return (_refCallbackInternetE = refCallbackInternetErrorOverlay.current) == null || _refCallbackInternetE.callback == null ? undefined : _refCallbackInternetE.callback();
        },
        onBack: goBack
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        headerText: perkTitle,
        hideScreenHeader: false,
        visible: isShowError,
        onReload: function onReload() {
          var _refCallbackErrorOver;
          return (_refCallbackErrorOver = refCallbackErrorOverlay.current) == null || _refCallbackErrorOver.callback == null ? undefined : _refCallbackErrorOver.callback();
        },
        onBack: goBack,
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
        ignoreShowNoInternet: true
      }), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
        ref: toastRef,
        style: _yourReward.styles.feedBackToastStyle,
        position: "custom",
        textStyle: _yourReward.styles.toastTextStyle,
        type: _feedbackToast.FeedBackToastType.smallFeedBack,
        text: "Copied"
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: walletMyDetailPerksFetching
      })]
    });
  };
  var _default = exports.default = YourRewardScreen;
