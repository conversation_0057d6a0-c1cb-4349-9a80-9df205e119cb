  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _utils = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _radio = _$$_REQUIRE(_dependencyMap[11]);
  var _i18n = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var YourRewardRedemptionOutlet = function YourRewardRedemptionOutlet(_ref) {
    var isGamificationPrize = _ref.isGamificationPrize,
      listOutlet = _ref.listOutlet,
      onSelect = _ref.onSelect;
    var outletSize = (0, _lodash.size)(listOutlet);
    if (outletSize === 0) return null;
    var isSingleOutlet = outletSize === 1;
    var _useState = (0, _react.useState)((0, _utils.handleCondition)(isSingleOutlet, listOutlet, null)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      selectedOutlet = _useState2[0],
      setSelectedOutlet = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isModalVisible = _useState4[0],
      setIsModalVisible = _useState4[1];
    var showBottomSheet = function showBottomSheet() {
      return setIsModalVisible(true);
    };
    var closeBottomSheet = function closeBottomSheet() {
      return setIsModalVisible(false);
    };
    (0, _react.useEffect)(function () {
      if (isSingleOutlet) {
        if (isGamificationPrize) {
          setSelectedOutlet(listOutlet == null ? undefined : listOutlet[0]);
        }
        onSelect(listOutlet == null ? undefined : listOutlet[0]);
      }
    }, [isSingleOutlet]);
    var selectOutlet = function selectOutlet(item) {
      setSelectedOutlet(item);
      onSelect(item);
      closeBottomSheet();
    };
    var handleDynamicHeight = function handleDynamicHeight() {
      var baseSize = 60;
      var itemSize = 100;
      var listSize = listOutlet ? listOutlet.length : 0;
      var _Dimensions$get = _reactNative2.Dimensions.get("screen"),
        height = _Dimensions$get.height;
      var modalSize = listSize * itemSize + baseSize;
      if (height * 0.9 <= modalSize) return height * 0.9;
      return modalSize;
    };
    var outletLine = function outletLine(item) {
      var isChecked = `${item == null ? undefined : item.outlet_code}-${item == null ? undefined : item.outlet_name}` === `${selectedOutlet == null ? undefined : selectedOutlet.outlet_code}-${selectedOutlet == null ? undefined : selectedOutlet.outlet_name}`;
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: _styles.styles.wrapOutletLine,
        onPress: function onPress() {
          return selectOutlet(item);
        },
        children: [(0, _jsxRuntime.jsx)(_radio.Radio, {
          isChecked: isChecked,
          onChecked: function onChecked() {
            return selectOutlet(item);
          },
          style: _styles.styles.radioStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: item == null ? undefined : item.outlet_name,
          style: (0, _utils.handleCondition)(isChecked, _styles.styles.textOutletLineCheckedStyle, _styles.styles.textOutletLineStyle)
        })]
      });
    };
    if (isSingleOutlet && !isGamificationPrize) {
      var _listOutlet$;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.singleOutletContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "yourReward.redemptionLocation",
          style: _styles.styles.redemptionLocationTextStyle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: listOutlet == null || (_listOutlet$ = listOutlet[0]) == null ? undefined : _listOutlet$.outlet_name,
          style: _styles.styles.redemptionLocationDescriptionStyle
        })]
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.singleOutletContainer,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "yourReward.redemptionLocation",
        style: _styles.styles.redemptionLocationTextStyle
      }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        disabled: isGamificationPrize,
        onPress: showBottomSheet,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.wrapDropdownSelection,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: (0, _utils.handleCondition)(!selectedOutlet, (0, _i18n.translate)("yourReward.chooseALocation"), selectedOutlet == null ? undefined : selectedOutlet.outlet_name),
            style: (0, _utils.handleCondition)(selectedOutlet, _styles.styles.outletNameTextStyle, _styles.styles.placeHolderOutletNameTextStyle),
            numberOfLines: 1
          }), !isGamificationPrize && (0, _jsxRuntime.jsx)(_icons.ArrowDownGrey, {})]
        })
      }), (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
        isModalVisible: isModalVisible,
        onClosedSheet: closeBottomSheet,
        animationInTiming: 300,
        animationOutTiming: 300,
        containerStyle: Object.assign({}, _styles.styles.bottomSheetStyle, {
          height: handleDynamicHeight()
        }),
        stopDragCollapse: true,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.styles.wrapHeaderBottomSheet,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "yourReward.redemptionLocation",
            style: _styles.styles.textHeaderBottomSheetStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: closeBottomSheet,
            children: (0, _jsxRuntime.jsx)(_icons.CrossPurple, {})
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          data: listOutlet,
          renderItem: function renderItem(_ref2) {
            var item = _ref2.item;
            return outletLine(item);
          },
          keyExtractor: function keyExtractor(item) {
            return item == null ? undefined : item.outlet_code;
          },
          showsVerticalScrollIndicator: false,
          ItemSeparatorComponent: function ItemSeparatorComponent() {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.styles.separatorItemStyle
            });
          }
        })]
      })]
    });
  };
  var _default = exports.default = YourRewardRedemptionOutlet;
