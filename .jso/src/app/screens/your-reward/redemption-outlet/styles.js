  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bottomSheetStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 328,
      paddingHorizontal: 24
    },
    outletNameTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      width: "90%"
    }),
    placeHolderOutletNameTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.midGrey
    }),
    radioStyle: {
      marginBottom: 0
    },
    redemptionLocationDescriptionStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey
    }),
    redemptionLocationTextStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 10
    }),
    separatorItemStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1
    },
    singleOutletContainer: {
      marginTop: 34,
      width: "100%"
    },
    textHeaderBottomSheetStyle: Object.assign({}, _text.presets.subTitleBold, {
      flex: 1,
      textAlign: "center"
    }),
    textOutletLineCheckedStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 12,
      textAlignVertical: "center",
      width: "90%"
    }),
    textOutletLineStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 12,
      textAlignVertical: "center",
      width: "90%"
    }),
    wrapDropdownSelection: {
      alignItems: "center",
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      height: 44,
      justifyContent: "space-between",
      paddingHorizontal: 16
    },
    wrapHeaderBottomSheet: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      marginHorizontal: 16,
      paddingVertical: 20,
      width: "100%"
    },
    wrapOutletLine: {
      alignItems: "center",
      flexDirection: "row",
      paddingVertical: 18
    }
  });
