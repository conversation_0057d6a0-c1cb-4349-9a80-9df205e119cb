  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tagStyle2 = exports.tagStyle = exports.styles = exports.CONTENT_WIDTH = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var CONTENT_WIDTH = exports.CONTENT_WIDTH = width - 48;
  var styles = exports.styles = _reactNative.StyleSheet.create({
    backgroundImage: {
      borderBottomLeftRadius: 40,
      height: 228,
      position: "absolute",
      width: width
    },
    bottomSheetCustomContentStyle: {
      paddingHorizontal: 24,
      paddingTop: 24,
      width: "100%"
    },
    bottomSheetCustomMessageStyle: {
      paddingHorizontal: 0,
      textAlign: "center"
    },
    bottomSheetCustomTitleStyle: {
      letterSpacing: 0.6,
      paddingHorizontal: 0,
      textAlign: "center"
    },
    cardContentPremium: Object.assign({
      alignItems: "center",
      alignSelf: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      margin: 24,
      padding: 24,
      width: CONTENT_WIDTH
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        borderRadius: 20,
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        borderRadius: 20,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    cardContentPremiumWithLoading: {
      height: 266,
      paddingHorizontal: 63,
      paddingVertical: 53
    },
    container: {
      flex: 1
    },
    contentContainerStyle: {
      paddingBottom: 20
    },
    descriptionTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.darkestGrey,
      flex: 1,
      marginBottom: 40,
      textAlign: "center"
    }),
    disclaimerTextDescriptionStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    }),
    disclaimerTextTitleStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 0,
      lineHeight: 22,
      marginLeft: 4,
      textAlign: "center",
      textTransform: "none"
    }),
    feedBackToastStyle: {
      bottom: 40
    },
    imageVoucher: {
      borderRadius: 16,
      height: 116,
      marginVertical: 16,
      width: 190
    },
    loadingImageContentStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      borderRadius: 4,
      height: 160,
      width: 200
    },
    markAsUsedBtn: {
      alignItems: "center",
      borderColor: _theme.color.palette.lightPurple,
      borderRadius: 60,
      borderWidth: 2,
      justifyContent: "center",
      paddingVertical: 10,
      width: "100%"
    },
    markAsUsedBtnInActive: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 60,
      borderWidth: 2,
      justifyContent: "center",
      paddingVertical: 10,
      width: "100%"
    },
    markAsUsedTextBtn: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      lineHeight: 24,
      textAlignVertical: "center"
    }),
    markAsUsedTextBtnInActive: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.darkGrey,
      lineHeight: 24,
      textAlignVertical: "center"
    }),
    overLayLoading: {
      height: height,
      position: "absolute",
      width: width,
      zIndex: 9999999
    },
    promoCodeText: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center",
      width: "90%"
    }),
    quantityText: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    shimmerPlaceholderLine1Style: {
      borderRadius: 4,
      height: 16,
      marginBottom: 16,
      marginLeft: 24,
      width: CONTENT_WIDTH * 0.35
    },
    shimmerPlaceholderLine2Style: {
      borderRadius: 4,
      height: 16,
      marginBottom: 16,
      marginLeft: 24,
      width: CONTENT_WIDTH
    },
    showRewardsCardBtn: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightPurple,
      borderColor: _theme.color.palette.lightPurple,
      borderRadius: 60,
      borderWidth: 2,
      justifyContent: "center",
      paddingVertical: 10,
      width: "100%"
    },
    showRewardsCardTextBtn: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 24,
      textAlignVertical: "center"
    }),
    subHeading: Object.assign({}, _text.presets.subTitleBold, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center"
    }),
    titleTermCondition: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 0.06,
      lineHeight: 22,
      marginBottom: 24
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    usedText: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      marginTop: 24
    }),
    validDateText: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey,
      marginBottom: 16
    }),
    validTillDateText: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    voucherDescription: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.almostBlackGrey,
      lineHeight: 20,
      marginHorizontal: 24,
      textAlignVertical: "center"
    }),
    wrapDisclaimerSection: {
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      borderTopColor: _theme.color.palette.lighterGrey,
      borderTopWidth: 1,
      marginBottom: 24,
      paddingVertical: 16,
      width: "100%"
    },
    wrapDisclaimerSectionBottomLine: {
      borderTopColor: _theme.color.palette.lighterGrey,
      borderTopWidth: 1,
      marginBottom: 24,
      width: "100%"
    },
    wrapDisclaimerText: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginBottom: 8
    },
    wrapPromoCode: {
      alignItems: "center",
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 12,
      borderWidth: 1,
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 16,
      padding: 12,
      width: width - 96
    },
    wrapQty: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 8,
      height: 26,
      justifyContent: "center",
      marginRight: 8,
      paddingHorizontal: 8,
      paddingVertical: 4,
      width: 48
    },
    wrapTermAndCondition: {
      marginHorizontal: 24,
      marginTop: 50
    },
    wrapValidTill: {
      alignItems: "center",
      flexDirection: "row",
      marginBottom: 24
    }
  });
  var tagStyle2 = exports.tagStyle2 = {
    ul: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      paddingLeft: 11,
      color: _theme.color.palette.almostBlackGrey
    }),
    ol: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      color: _theme.color.palette.almostBlackGrey
    }),
    li: Object.assign({}, _text.presets.bodyTextRegular, {
      lineHeight: 20,
      fontSize: 16,
      padding: 0,
      margin: 0,
      marginBottom: 5,
      color: _theme.color.palette.almostBlackGrey,
      paddingLeft: 5
    }),
    p: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      marginBottom: 10,
      color: _theme.color.palette.almostBlackGrey
    })
  };
  var tagStyle = exports.tagStyle = {
    ul: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      paddingLeft: 11,
      color: _theme.color.palette.darkestGrey
    }),
    ol: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      color: _theme.color.palette.darkestGrey
    }),
    li: Object.assign({}, _text.presets.bodyTextRegular, {
      lineHeight: 20,
      fontSize: 16,
      padding: 0,
      margin: 0,
      marginBottom: 5,
      color: _theme.color.palette.darkestGrey,
      paddingLeft: 5
    }),
    p: Object.assign({}, _text.presets.bodyTextRegular, {
      margin: 0,
      lineHeight: 20,
      fontSize: 16,
      marginBottom: 10,
      color: _theme.color.palette.darkestGrey
    })
  };
