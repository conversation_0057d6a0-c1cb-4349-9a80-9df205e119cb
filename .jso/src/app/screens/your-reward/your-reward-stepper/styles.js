  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    container: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center"
    },
    quantityTextStyle: Object.assign({}, _text.presets.h3, {
      color: _theme.color.palette.almostBlackGrey,
      marginHorizontal: 16
    }),
    titleStepperStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      flex: 1
    }),
    wrapStepperComponentStyle: {
      alignItems: "center",
      flexDirection: "row",
      marginLeft: 8
    }
  });
