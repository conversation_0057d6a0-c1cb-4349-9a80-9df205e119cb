  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.YourRewardStepper = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _utils = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var YourRewardStepperComponent = function YourRewardStepperComponent(_ref, ref) {
    var title = _ref.title,
      maxQuantity = _ref.maxQuantity,
      disabled = _ref.disabled;
    var _useState = (0, _react.useState)((0, _utils.handleCondition)(maxQuantity, 1, 0)),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      quantity = _useState2[0],
      setQuantity = _useState2[1];
    var increaseQuantity = function increaseQuantity() {
      var newQty = quantity + 1;
      setQuantity(newQty);
    };
    var decreaseQuantity = function decreaseQuantity() {
      var newQty = quantity - 1;
      setQuantity(newQty);
    };
    var disableMinus = function disableMinus() {
      if (disabled) return true;
      if (quantity <= 0 || quantity === 1) return true;
      return false;
    };
    var disablePlus = function disablePlus() {
      if (disabled) return true;
      if (quantity === (0, _utils.handleCondition)(maxQuantity, maxQuantity, 0)) return true;
      return false;
    };
    var getCurrentQuantity = function getCurrentQuantity() {
      return quantity;
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        getCurrentQuantity: getCurrentQuantity
      };
    });
    var isDisableMinus = disableMinus();
    var isDisablePlus = disablePlus();
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _styles.styles.container,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        text: title,
        style: _styles.styles.titleStepperStyle
      }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _styles.styles.wrapStepperComponentStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          disabled: isDisableMinus,
          onPress: decreaseQuantity,
          children: (0, _utils.handleCondition)(isDisableMinus, (0, _jsxRuntime.jsx)(_icons.MinusCircleDisabled, {}), (0, _jsxRuntime.jsx)(_icons.MinusCircle, {}))
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: quantity.toString(),
          style: _styles.styles.quantityTextStyle
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          disabled: isDisablePlus,
          onPress: increaseQuantity,
          children: (0, _utils.handleCondition)(isDisablePlus, (0, _jsxRuntime.jsx)(_icons.PlusCircleDisabled, {}), (0, _jsxRuntime.jsx)(_icons.PlusCircle, {}))
        })]
      })]
    });
  };
  var YourRewardStepper = exports.YourRewardStepper = (0, _react.forwardRef)(YourRewardStepperComponent);
