  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PopUpConfirm = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _aemRedux = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeModal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var backgroundGrey95 = "rgba(242,242,242,1)";
  var backgroundGrey7 = "rgba(18,18,18,0.8)";
  var systemTextColor = "#007AFF";
  var separateColor = "#3C3C435C";
  var androidBackground = "#F3F4F9";
  var androidTitleColor = "#001E2F";
  var systemTextAndroidColor = "#1B72C0";
  var PopUpConfirmContent = function PopUpConfirmContent(_ref, ref) {
    var onConfirm = _ref.onConfirm,
      onCancel = _ref.onCancel;
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var MSG87 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG87";
    });
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visibleModal = _useState2[0],
      setVisibleModal = _useState2[1];
    var hidePopup = function hidePopup() {
      return setVisibleModal(false);
    };
    var showPopup = function showPopup() {
      return setVisibleModal(true);
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        showPopup: showPopup,
        hidePopup: hidePopup
      };
    });
    var handleContentByPlatform = function handleContentByPlatform() {
      if (_reactNative2.Platform.OS === "ios") {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.iosPopupContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.iosMessageWrapperStyle,
            children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.iosTextTitleStyle,
              children: MSG87 == null ? undefined : MSG87.title
            }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.iosTextDescriptionStyle,
              children: MSG87 == null ? undefined : MSG87.message
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.iosSeparateDividerStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onConfirm,
            activeOpacity: 0.5,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.iosFirstButtonTextStyle,
              children: MSG87 == null ? undefined : MSG87.firstButton
            })
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.iosSeparateDividerStyle
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onCancel,
            activeOpacity: 0.5,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.iosSecondButtonTextStyle,
              children: MSG87 == null ? undefined : MSG87.secondButton
            })
          })]
        });
      }
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.androidPopupContainer,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.androidMessageWrapperStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.androidTextTitleStyle,
            children: MSG87 == null ? undefined : MSG87.title
          }), (0, _jsxRuntime.jsx)(_reactNative.Text, {
            style: styles.androidTextDescriptionStyle,
            children: MSG87 == null ? undefined : MSG87.message
          })]
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.androidWrapButtonStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onCancel,
            activeOpacity: 0.5,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.androidSecondButtonTextStyle,
              children: MSG87 == null ? undefined : MSG87.secondButton
            })
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: onConfirm,
            activeOpacity: 0.5,
            children: (0, _jsxRuntime.jsx)(_reactNative.Text, {
              style: styles.androidFirstButtonTextStyle,
              children: MSG87 == null ? undefined : MSG87.firstButton
            })
          })]
        })]
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNativeModal.default, {
      isVisible: visibleModal,
      animationIn: "fadeIn",
      animationOut: "fadeOut",
      backdropColor: backgroundGrey7,
      children: handleContentByPlatform()
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    androidFirstButtonTextStyle: {
      color: systemTextAndroidColor,
      fontSize: 14,
      fontFamily: _theme.typography.bold,
      lineHeight: 20,
      textAlign: "left"
    },
    androidMessageWrapperStyle: {
      padding: 24
    },
    androidPopupContainer: {
      alignSelf: "center",
      backgroundColor: androidBackground,
      borderRadius: 28,
      width: 312
    },
    androidSecondButtonTextStyle: {
      color: systemTextAndroidColor,
      fontSize: 14,
      fontFamily: _theme.typography.bold,
      lineHeight: 20,
      textAlign: "left"
    },
    androidTextDescriptionStyle: {
      color: androidTitleColor,
      fontSize: 13,
      lineHeight: 16,
      textAlign: "center"
    },
    androidTextTitleStyle: {
      color: androidTitleColor,
      fontSize: 24,
      lineHeight: 32,
      marginBottom: 16,
      textAlign: "center"
    },
    androidWrapButtonStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between",
      paddingBottom: 24,
      paddingLeft: 33,
      paddingRight: 24
    },
    iosFirstButtonTextStyle: {
      color: systemTextColor,
      fontSize: 17,
      fontWeight: "600",
      lineHeight: 22,
      paddingBottom: 10,
      paddingTop: 12,
      textAlign: "center",
      textAlignVertical: "center"
    },
    iosMessageWrapperStyle: {
      paddingHorizontal: 16,
      paddingVertical: 19
    },
    iosPopupContainer: {
      alignSelf: "center",
      backgroundColor: backgroundGrey95,
      borderRadius: 14,
      width: 270
    },
    iosSecondButtonTextStyle: {
      color: systemTextColor,
      fontSize: 17,
      fontWeight: "400",
      lineHeight: 22,
      paddingBottom: 10,
      paddingTop: 12,
      textAlign: "center",
      textAlignVertical: "center"
    },
    iosSeparateDividerStyle: {
      backgroundColor: separateColor,
      height: 1
    },
    iosTextDescriptionStyle: {
      color: _theme.color.palette.black,
      fontSize: 13,
      fontWeight: "400",
      lineHeight: 16,
      textAlign: "center",
      textAlignVertical: "center"
    },
    iosTextTitleStyle: {
      color: _theme.color.palette.black,
      fontSize: 17,
      fontWeight: "600",
      lineHeight: 22,
      marginBottom: 2,
      textAlign: "center",
      textAlignVertical: "center"
    }
  });
  var PopUpConfirm = exports.PopUpConfirm = (0, _react.forwardRef)(PopUpConfirmContent);
