  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _lodash = _$$_REQUIRE(_dependencyMap[10]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _clipboard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _yourReward = _$$_REQUIRE(_dependencyMap[13]);
  var _utils = _$$_REQUIRE(_dependencyMap[14]);
  var _myPerksList = _$$_REQUIRE(_dependencyMap[15]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[17]);
  var _yourRewardStepper = _$$_REQUIRE(_dependencyMap[18]);
  var _i18n = _$$_REQUIRE(_dependencyMap[19]);
  var _redemptionOutlet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _swipeToRedeemButton = _$$_REQUIRE(_dependencyMap[21]);
  var _popUpConfirm = _$$_REQUIRE(_dependencyMap[22]);
  var _constants = _$$_REQUIRE(_dependencyMap[23]);
  var _native = _$$_REQUIRE(_dependencyMap[24]);
  var _walletSaga = _$$_REQUIRE(_dependencyMap[25]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _bottomSheetConfirm = _$$_REQUIRE(_dependencyMap[27]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[30]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[31]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "ContentRedemptionMode";
  var ContentRedemptionMode = function ContentRedemptionMode(_ref) {
    var _ref$loading = _ref.loading,
      loading = _ref$loading === undefined ? false : _ref$loading,
      redemptionMode = _ref.redemptionMode,
      item = _ref.item,
      data = _ref.data,
      _ref$showRewardCard = _ref.showRewardCard,
      showRewardCard = _ref$showRewardCard === undefined ? function () {
        return null;
      } : _ref$showRewardCard,
      _ref$showInternetOver = _ref.showInternetOverlay,
      showInternetOverlay = _ref$showInternetOver === undefined ? function (_cb) {
        return null;
      } : _ref$showInternetOver,
      _ref$hideInternetOver = _ref.hideInternetOverlay,
      hideInternetOverlay = _ref$hideInternetOver === undefined ? function () {
        return null;
      } : _ref$hideInternetOver,
      _ref$showErrorOverlay = _ref.showErrorOverlay,
      showErrorOverlay = _ref$showErrorOverlay === undefined ? function (_cb) {
        return null;
      } : _ref$showErrorOverlay,
      _ref$hideErrorOverlay = _ref.hideErrorOverlay,
      hideErrorOverlay = _ref$hideErrorOverlay === undefined ? function () {
        return null;
      } : _ref$hideErrorOverlay,
      _ref$onSwipeHorizonta = _ref.onSwipeHorizontal,
      onSwipeHorizontal = _ref$onSwipeHorizonta === undefined ? function (_cb) {
        return null;
      } : _ref$onSwipeHorizonta,
      _ref$toastRef = _ref.toastRef,
      toastRef = _ref$toastRef === undefined ? null : _ref$toastRef;
    var navigation = (0, _native.useNavigation)();
    var content = null;
    // INF
    var informativeCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getInformativesCommon);
    var INF15 = informativeCommon == null ? undefined : informativeCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "INF15";
    });
    // EHR
    var errorCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var EHR311 = errorCommon == null ? undefined : errorCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR3.11";
    });
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var MSG84 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG84";
    });
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var rewardStepperRef = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)({}),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      outletSelected = _useState2[0],
      setOutletSelected = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      visibleBottomSheetError = _useState4[0],
      setVisibleBottomSheetError = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      showConfirmPopup = _useState6[0],
      setShowConfirmPopup = _useState6[1];
    var _useState7 = (0, _react.useState)(false),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isUsedVoucher = _useState8[0],
      setIsUserVoucher = _useState8[1];
    var _useState9 = (0, _react.useState)(""),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      timestampUsed = _useState0[0],
      setTimestampUsed = _useState0[1];
    var popUpConfirmRef = (0, _react.useRef)(null);
    var resetSwipeButton = function resetSwipeButton() {
      return null;
    };
    var isGamificationPrize = (item == null ? undefined : item.perk_type) === _constants.PerkType.GamificationPrize;
    var detailsData = (0, _react.useMemo)(function () {
      if (isGamificationPrize) {
        return {
          disclaimerDescription: data == null ? undefined : data.description,
          eligibleOutlet: [{
            outlet_name: data == null ? undefined : data.eligibleOutlet
          }],
          imageUrl: data == null ? undefined : data.image,
          isUsed: !!(data != null && data.redeemDt),
          promoCode: data == null ? undefined : data.promoCode,
          quantity: data == null ? undefined : data.qty,
          title: data == null ? undefined : data.title,
          validTillMsg: (0, _i18n.translate)("vouchersPrizesRedemptionsScreen.validUntil", {
            date: (0, _moment.default)(data == null ? undefined : data.validTill).format("DD MMM YYYY")
          })
        };
      }
      return {
        disclaimerDescription: INF15 == null ? undefined : INF15.informativeText,
        disclaimerTitle: INF15 == null ? undefined : INF15.title,
        eligibleOutlet: data == null ? undefined : data.eligible_outlet,
        imageUrl: data == null ? undefined : data.voucher_url,
        isUsed: data == null ? undefined : data.is_used,
        promoCode: data == null ? undefined : data.remarks,
        quantity: item == null ? undefined : item.token_qty,
        title: data == null ? undefined : data.voucher_type_name,
        validTillMsg: data == null ? undefined : data.valid_datetime_msg
      };
    }, [item == null ? undefined : item.perk_type, item == null ? undefined : item.token_qty, data, INF15 == null ? undefined : INF15.title, INF15 == null ? undefined : INF15.informativeText]);
    var isMarkedAsUsed = (detailsData == null ? undefined : detailsData.isUsed) || isUsedVoucher;
    var onCopy = function onCopy() {
      var _toastRef$current;
      _clipboard.default.setString(detailsData == null ? undefined : detailsData.promoCode);
      toastRef == null || (_toastRef$current = toastRef.current) == null || _toastRef$current.show == null || _toastRef$current.show();
    };
    var handleUsedDate = function handleUsedDate() {
      var date = (0, _moment.default)(timestampUsed).format("DD MMM YYYY, hh:mm A");
      return `Used ${date}`;
    };
    var handleDisabledStatus = function handleDisabledStatus() {
      if ((0, _lodash.size)(detailsData == null ? undefined : detailsData.eligibleOutlet) === 1 || !(0, _lodash.isEmpty)(outletSelected)) return false;
      return true;
    };
    var onSelectOutlet = function onSelectOutlet(outlet) {
      setOutletSelected(outlet);
    };
    var onCancel = function onCancel() {
      var _popUpConfirmRef$curr;
      resetSwipeButton == null || resetSwipeButton();
      popUpConfirmRef == null || (_popUpConfirmRef$curr = popUpConfirmRef.current) == null || _popUpConfirmRef$curr.hidePopup();
    };
    var showBottomSheetError = function showBottomSheetError() {
      setTimeout(function () {
        setVisibleBottomSheetError(true);
      }, 200);
    };
    var _onConfirm = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _popUpConfirmRef$curr2;
        popUpConfirmRef == null || (_popUpConfirmRef$curr2 = popUpConfirmRef.current) == null || _popUpConfirmRef$curr2.hidePopup();
        hideErrorOverlay();
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          var _rewardStepperRef$cur;
          hideInternetOverlay();
          _globalLoadingController.default.showLoading(true);
          var qtyRedeemed = rewardStepperRef == null || (_rewardStepperRef$cur = rewardStepperRef.current) == null ? undefined : _rewardStepperRef$cur.getCurrentQuantity();
          var reqBody = {
            memberId: profilePayload == null ? undefined : profilePayload.id,
            cardNo: profilePayload == null ? undefined : profilePayload.cardNo,
            voucherTypeCode: data == null ? undefined : data.voucher_type_code,
            promoCodeList: data == null ? undefined : data.remarks,
            quantity: qtyRedeemed,
            eligibleOutlet: outletSelected,
            redemptionMode: item == null ? undefined : item.redemption_mode
          };
          if (isGamificationPrize) {
            reqBody = {
              swipeType: "swipe_gamification_prize",
              memberId: profilePayload == null ? undefined : profilePayload.id,
              prizeId: item == null ? undefined : item.id
            };
          }
          var response = yield (0, _walletSaga.redeemYourReward)(reqBody);
          _globalLoadingController.default.hideLoading();
          if ((0, _utils.ifOneTrue)([!response, (response == null ? undefined : response.status) === false, (response == null ? undefined : response.return_status) === 0])) {
            resetSwipeButton == null || resetSwipeButton();
            showBottomSheetError();
          } else if ((0, _utils.ifOneTrue)([(response == null ? undefined : response.status) === true, (response == null ? undefined : response.return_status) === 1])) {
            resetSwipeButton == null || resetSwipeButton();
            navigation.navigate.apply(navigation, (0, _toConsumableArray2.default)([_constants.NavigationConstants.yourRewardRedeemSuccess, {
              data: detailsData,
              qtyRedeemed: qtyRedeemed
            }]));
          } else {
            showErrorOverlay(_onConfirm);
          }
        } else {
          showInternetOverlay(_onConfirm);
        }
      });
      return function onConfirm() {
        return _ref2.apply(this, arguments);
      };
    }();
    var _sendRequestMarkAsUsed = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        hideErrorOverlay();
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (isConnected) {
          hideInternetOverlay();
          var currentTimeStamp = (0, _dateTime.getCurrentTimeSingapore)();
          var eligibleOutletSelected = {};
          var outletSize = (0, _lodash.size)(detailsData == null ? undefined : detailsData.eligibleOutlet);
          if (outletSize === 0) {
            showBottomSheetError();
            return;
          } else if (outletSize === 1) {
            var _detailsData$eligible;
            eligibleOutletSelected = detailsData == null || (_detailsData$eligible = detailsData.eligibleOutlet) == null ? undefined : _detailsData$eligible[0];
          } else {
            var _detailsData$eligible2;
            eligibleOutletSelected = detailsData == null || (_detailsData$eligible2 = detailsData.eligibleOutlet) == null ? undefined : _detailsData$eligible2[0];
          }
          _globalLoadingController.default.showLoading(true);
          var reqBody = {
            memberId: profilePayload == null ? undefined : profilePayload.id,
            cardNo: profilePayload == null ? undefined : profilePayload.cardNo,
            voucherTypeCode: data == null ? undefined : data.voucher_type_code,
            promoCodeList: data == null ? undefined : data.remarks,
            quantity: 1,
            eligibleOutlet: eligibleOutletSelected,
            redemptionMode: item == null ? undefined : item.redemption_mode
          };
          if (isGamificationPrize) {
            reqBody = {
              swipeType: "swipe_gamification_prize",
              memberId: profilePayload == null ? undefined : profilePayload.id,
              prizeId: item == null ? undefined : item.id
            };
          }
          var response = yield (0, _walletSaga.redeemYourReward)(reqBody);
          _globalLoadingController.default.hideLoading();
          if ((0, _utils.ifOneTrue)([(response == null ? undefined : response.status) === true, (response == null ? undefined : response.return_status) === 1])) {
            setIsUserVoucher(true);
            setTimestampUsed(currentTimeStamp);
            navigation.setParams({
              needReload: true
            });
          } else if ((0, _utils.ifOneTrue)([!response, (response == null ? undefined : response.status) === false, (response == null ? undefined : response.return_status) === 0])) {
            showBottomSheetError();
          } else {
            showErrorOverlay(_sendRequestMarkAsUsed);
          }
        } else {
          showInternetOverlay(_sendRequestMarkAsUsed);
        }
      });
      return function sendRequestMarkAsUsed() {
        return _ref3.apply(this, arguments);
      };
    }();
    var _onSwipeSuccess = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        onSwipeHorizontal(false);
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (!isConnected) {
          showInternetOverlay(_onSwipeSuccess);
        } else {
          var _popUpConfirmRef$curr3;
          hideInternetOverlay();
          popUpConfirmRef == null || (_popUpConfirmRef$curr3 = popUpConfirmRef.current) == null || _popUpConfirmRef$curr3.showPopup();
        }
      });
      return function onSwipeSuccess() {
        return _ref4.apply(this, arguments);
      };
    }();
    var onSwipeStart = function onSwipeStart() {
      onSwipeHorizontal(true);
    };
    var onSwipeFail = function onSwipeFail() {
      onSwipeHorizontal(false);
    };
    var markAsUsed = function markAsUsed() {
      setShowConfirmPopup(true);
    };
    switch (redemptionMode) {
      case 0:
      case 1:
        content = (0, _jsxRuntime.jsxs)(_react.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "subTitleBold",
            style: _yourReward.styles.subHeading,
            text: detailsData == null ? undefined : detailsData.title,
            numberOfLines: 2
          }), (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: detailsData == null ? undefined : detailsData.imageUrl
            },
            style: _yourReward.styles.imageVoucher
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _yourReward.styles.wrapValidTill,
            children: [((detailsData == null ? undefined : detailsData.quantity) || (detailsData == null ? undefined : detailsData.quantity) === 0) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _yourReward.styles.wrapQty,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: `${detailsData == null ? undefined : detailsData.quantity}`,
                style: _yourReward.styles.quantityText
              })
            }), !(0, _lodash.isEmpty)(detailsData == null ? undefined : detailsData.validTillMsg) && (0, _jsxRuntime.jsx)(_text.Text, {
              style: _yourReward.styles.validTillDateText,
              text: detailsData == null ? undefined : detailsData.validTillMsg,
              numberOfLines: 1
            })]
          }), detailsData != null && detailsData.disclaimerDescription || !isGamificationPrize ? (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _yourReward.styles.wrapDisclaimerSection,
            children: [(detailsData == null ? undefined : detailsData.disclaimerTitle) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _yourReward.styles.wrapDisclaimerText,
              children: [(0, _jsxRuntime.jsx)(_icons.Info, {
                width: 24,
                height: 24
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                text: detailsData == null ? undefined : detailsData.disclaimerTitle,
                style: _yourReward.styles.disclaimerTextTitleStyle
              })]
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: detailsData == null ? undefined : detailsData.disclaimerDescription,
              style: _yourReward.styles.disclaimerTextDescriptionStyle
            })]
          }) : (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _yourReward.styles.wrapDisclaimerSectionBottomLine
          }), (0, _jsxRuntime.jsx)(_yourRewardStepper.YourRewardStepper, {
            title: (0, _i18n.translate)("yourReward.quantity"),
            maxQuantity: detailsData == null ? undefined : detailsData.quantity,
            ref: rewardStepperRef,
            disabled: isGamificationPrize
          }), (0, _jsxRuntime.jsx)(_redemptionOutlet.default, {
            isGamificationPrize: isGamificationPrize,
            listOutlet: detailsData == null ? undefined : detailsData.eligibleOutlet,
            onSelect: onSelectOutlet
          }), (0, _jsxRuntime.jsx)(_swipeToRedeemButton.SwipeButton, {
            disabled: handleDisabledStatus(),
            forceReset: function forceReset(reset) {
              resetSwipeButton = reset;
            },
            title: (0, _i18n.translate)("yourReward.swipeToUse"),
            onSwipeSuccess: _onSwipeSuccess,
            onSwipeStart: onSwipeStart,
            onSwipeFail: onSwipeFail,
            testID: `${COMPONENT_NAME}__SwipeButton`,
            accessibilityLabel: `${COMPONENT_NAME}__SwipeButton`
          }), (0, _jsxRuntime.jsx)(_popUpConfirm.PopUpConfirm, {
            ref: popUpConfirmRef,
            onCancel: onCancel,
            onConfirm: _onConfirm
          }), (0, _jsxRuntime.jsx)(_bottomSheetConfirm.BottomSheetConfirm, {
            visible: visibleBottomSheetError,
            hideOnConfirm: true,
            title: EHR311 == null ? undefined : EHR311.header,
            message: EHR311 == null ? undefined : EHR311.subHeader,
            confirmButtonText: EHR311 == null ? undefined : EHR311.buttonLabel,
            hasCancel: false,
            height: 283,
            onConfirm: function onConfirm() {
              setVisibleBottomSheetError(false);
            },
            onHide: function onHide() {
              setVisibleBottomSheetError(false);
            },
            testID: `${COMPONENT_NAME}BottomSheetConfirmErrorSwipe`,
            icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
            customContentStyle: _yourReward.styles.bottomSheetCustomContentStyle,
            customTitleStyle: _yourReward.styles.bottomSheetCustomTitleStyle,
            customMessageStyle: _yourReward.styles.bottomSheetCustomMessageStyle
          })]
        });
        break;
      case 2:
        content = (0, _jsxRuntime.jsxs)(_react.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "subTitleBold",
            style: _yourReward.styles.subHeading,
            text: detailsData == null ? undefined : detailsData.title,
            numberOfLines: 2
          }), (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: detailsData == null ? undefined : detailsData.imageUrl
            },
            style: _yourReward.styles.imageVoucher
          }), !(0, _lodash.isEmpty)(detailsData == null ? undefined : detailsData.validTillMsg) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: _yourReward.styles.validDateText,
            text: detailsData == null ? undefined : detailsData.validTillMsg,
            numberOfLines: 1
          }), !(0, _lodash.isEmpty)(detailsData == null ? undefined : detailsData.promoCode) && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _yourReward.styles.wrapPromoCode,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              style: _yourReward.styles.promoCodeText,
              text: detailsData == null ? undefined : detailsData.promoCode,
              numberOfLines: 1
            }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onCopy,
              children: (0, _jsxRuntime.jsx)(_icons.Copy, {
                width: 20,
                height: 20
              })
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: (0, _utils.handleCondition)(isMarkedAsUsed, _yourReward.styles.markAsUsedBtnInActive, _yourReward.styles.markAsUsedBtn),
            disabled: isMarkedAsUsed,
            onPress: markAsUsed,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: (0, _utils.handleCondition)(isMarkedAsUsed, "yourReward.used", "yourReward.markAsUsed"),
              style: (0, _utils.handleCondition)(isMarkedAsUsed, _yourReward.styles.markAsUsedTextBtnInActive, _yourReward.styles.markAsUsedTextBtn)
            })
          }), (0, _utils.handleCondition)(isMarkedAsUsed && timestampUsed, (0, _jsxRuntime.jsx)(_text.Text, {
            text: handleUsedDate(),
            style: _yourReward.styles.usedText,
            numberOfLines: 1
          }), null), (0, _jsxRuntime.jsx)(_bottomSheetConfirm.BottomSheetConfirm, {
            visible: visibleBottomSheetError,
            hideOnConfirm: true,
            title: EHR311 == null ? undefined : EHR311.header,
            message: EHR311 == null ? undefined : EHR311.subHeader,
            confirmButtonText: EHR311 == null ? undefined : EHR311.buttonLabel,
            hasCancel: false,
            height: 283,
            onConfirm: function onConfirm() {
              setVisibleBottomSheetError(false);
            },
            onHide: function onHide() {
              setVisibleBottomSheetError(false);
            },
            testID: `${COMPONENT_NAME}BottomSheetConfirmErrorSwipe`,
            icon: (0, _jsxRuntime.jsx)(_icons.InfoRed, {}),
            customContentStyle: _yourReward.styles.bottomSheetCustomContentStyle,
            customTitleStyle: _yourReward.styles.bottomSheetCustomTitleStyle,
            customMessageStyle: _yourReward.styles.bottomSheetCustomMessageStyle
          }), (0, _jsxRuntime.jsx)(_bottomSheetConfirm.BottomSheetConfirm, {
            visible: showConfirmPopup,
            hideOnConfirm: true,
            title: MSG84 == null ? undefined : MSG84.title,
            message: MSG84 == null ? undefined : MSG84.message,
            confirmButtonText: MSG84 == null ? undefined : MSG84.firstButton,
            cancelButtonText: MSG84 == null ? undefined : MSG84.secondButton,
            height: 345,
            onConfirm: function onConfirm() {
              setShowConfirmPopup(false);
              _sendRequestMarkAsUsed();
            },
            onHide: function onHide() {
              setShowConfirmPopup(false);
            },
            testID: `${COMPONENT_NAME}BottomSheetConfirmMarkAsUsed`,
            icon: (0, _jsxRuntime.jsx)(_icons.Voucher, {})
          })]
        });
        break;
      case 3:
      case _myPerksList.PerkCodes.ignoreFeatureFlag:
        content = (0, _jsxRuntime.jsxs)(_react.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "subTitleBold",
            style: _yourReward.styles.subHeading,
            text: detailsData == null ? undefined : detailsData.title,
            numberOfLines: 2
          }), (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: detailsData == null ? undefined : detailsData.imageUrl
            },
            style: _yourReward.styles.imageVoucher
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _yourReward.styles.wrapValidTill,
            children: [((detailsData == null ? undefined : detailsData.quantity) || (detailsData == null ? undefined : detailsData.quantity) === 0) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _yourReward.styles.wrapQty,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                text: `${detailsData == null ? undefined : detailsData.quantity}`,
                style: _yourReward.styles.quantityText
              })
            }), !(0, _lodash.isEmpty)(detailsData == null ? undefined : detailsData.validTillMsg) && (0, _jsxRuntime.jsx)(_text.Text, {
              style: _yourReward.styles.validTillDateText,
              text: detailsData == null ? undefined : detailsData.validTillMsg,
              numberOfLines: 1
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            style: _yourReward.styles.showRewardsCardBtn,
            onPress: showRewardCard,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              tx: "yourReward.showRewardCard",
              style: _yourReward.styles.showRewardsCardTextBtn
            })
          })]
        });
        break;
    }
    if (loading) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_yourReward.styles.cardContentPremium, _yourReward.styles.cardContentPremiumWithLoading],
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _yourReward.styles.loadingImageContentStyle
        })
      });
    }
    return content ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: _yourReward.styles.cardContentPremium,
      children: content
    }) : null;
  };
  var _default = exports.default = ContentRedemptionMode;
