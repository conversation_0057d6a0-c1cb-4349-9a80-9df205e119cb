  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _myPerksList = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeRenderHtml = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _yourReward = _$$_REQUIRE(_dependencyMap[5]);
  var _yourReward2 = _$$_REQUIRE(_dependencyMap[6]);
  var _contentRedemptionMode = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _termAndCondition = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var ContentForSpecialPerk = function ContentForSpecialPerk(_ref) {
    var walletMyDetailPerks = _ref.walletMyDetailPerks,
      showRewardCard = _ref.showRewardCard,
      item = _ref.item;
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_contentRedemptionMode.default, {
        redemptionMode: _myPerksList.PerkCodes.ignoreFeatureFlag,
        data: walletMyDetailPerks,
        showRewardCard: showRewardCard,
        item: item
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _yourReward2.styles.voucherDescription,
        children: (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
          source: {
            html: item == null ? undefined : item.description
          },
          tagsStyles: _yourReward2.tagStyle2,
          contentWidth: _yourReward2.CONTENT_WIDTH,
          systemFonts: _yourReward.systemFonts
        })
      }), (0, _jsxRuntime.jsx)(_termAndCondition.default, {
        redemptionMode: _myPerksList.PerkCodes.ignoreFeatureFlag,
        term: item == null ? undefined : item.terms_and_conditions
      })]
    });
  };
  var _default = exports.default = ContentForSpecialPerk;
