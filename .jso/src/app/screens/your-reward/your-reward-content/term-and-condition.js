  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _lodash = _$$_REQUIRE(_dependencyMap[4]);
  var _yourReward = _$$_REQUIRE(_dependencyMap[5]);
  var _myPerksList = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeRenderHtml = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _yourReward2 = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  var TermAndCondition = function TermAndCondition(_ref) {
    var redemptionMode = _ref.redemptionMode,
      term = _ref.term;
    var allowTypes = [0, 1, 2, 3, _myPerksList.PerkCodes.ignoreFeatureFlag];
    var allowRender = allowTypes.includes(redemptionMode) && !(0, _lodash.isEmpty)(term);
    if (allowRender) {
      return (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _yourReward.styles.wrapTermAndCondition,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "yourReward.termAndCondition",
          style: _yourReward.styles.titleTermCondition
        }), (0, _jsxRuntime.jsx)(_reactNativeRenderHtml.default, {
          source: {
            html: term
          },
          tagsStyles: _yourReward.tagStyle,
          contentWidth: _yourReward.CONTENT_WIDTH,
          systemFonts: _yourReward2.systemFonts
        })]
      });
    }
    return null;
  };
  var _default = exports.default = TermAndCondition;
