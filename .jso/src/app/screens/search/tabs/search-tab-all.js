  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.toastTextStyle = exports.toastButtonStyle = exports.feedBackToastStyle = exports.SearchTabAll = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _native = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _get2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _isArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _isEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _size2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _searchRecentSection = _$$_REQUIRE(_dependencyMap[15]);
  var _searchRecentSection2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _envParams = _$$_REQUIRE(_dependencyMap[19]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[21]);
  var _scanButtonSearch = _$$_REQUIRE(_dependencyMap[22]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[23]);
  var _text = _$$_REQUIRE(_dependencyMap[24]);
  var _theme = _$$_REQUIRE(_dependencyMap[25]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[26]);
  var _icons = _$$_REQUIRE(_dependencyMap[27]);
  var _tenantListingHorizontal = _$$_REQUIRE(_dependencyMap[28]);
  var _menuOption = _$$_REQUIRE(_dependencyMap[29]);
  var _flightListingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _flightProps = _$$_REQUIRE(_dependencyMap[31]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[32]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[33]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[34]);
  var _constants = _$$_REQUIRE(_dependencyMap[35]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[36]);
  var _translate = _$$_REQUIRE(_dependencyMap[37]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[38]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[39]);
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[40]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[41]);
  var _adobe = _$$_REQUIRE(_dependencyMap[42]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[43]);
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[44]));
  var _storage = _$$_REQUIRE(_dependencyMap[45]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[46]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[47]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[48]);
  var _facilitiesAndServiceHorizontal = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[50]);
  var _eventCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[51]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[52]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[53]);
  var _utils = _$$_REQUIRE(_dependencyMap[54]);
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[55]));
  var _searchYouMayAlsoLike = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[56]));
  var _searchIndex = _$$_REQUIRE(_dependencyMap[57]);
  var _recentSearchHook = _$$_REQUIRE(_dependencyMap[58]);
  var _searchTabFlights = _$$_REQUIRE(_dependencyMap[59]);
  var _noResults = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[60]));
  var _flightInformationDisclaimer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[61]));
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[62]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[63]);
  var _firebase = _$$_REQUIRE(_dependencyMap[64]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[65]);
  var _saveFlightTraveOptionWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[66]));
  var _useModal3 = _$$_REQUIRE(_dependencyMap[67]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[68]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[69]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var planeCitiesIcon = {
    color: _theme.color.palette.darkestGrey
  };
  var planeAirlineIcon = {
    color: _theme.color.palette.lightPurple
  };
  var SCREEN_NAME = "SearchAll__";
  var feedBackToastStyle = exports.feedBackToastStyle = {
    bottom: 80,
    paddingHorizontal: 16,
    width: "100%"
  };
  var toastButtonStyle = exports.toastButtonStyle = Object.assign({}, _text.presets.textLink, {
    fontWeight: "normal",
    color: _theme.color.palette.lightBlue,
    alignItems: "flex-end"
  });
  var toastTextStyle = exports.toastTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.whiteGrey,
    width: "80%"
  });
  var getNavigateDirectionToSearchFlightsTab = function getNavigateDirectionToSearchFlightsTab(filteredFlightsSearchResultData) {
    var arrivalFlightElement = filteredFlightsSearchResultData == null ? undefined : filteredFlightsSearchResultData.find(function (i) {
      return (i == null ? undefined : i.direction) === _flightProps.FlightDirection.arrival;
    });
    return !!arrivalFlightElement ? _searchTabFlights.RedirectTab.ArrivalTab : _searchTabFlights.RedirectTab.DepartureTab;
  };
  var SearchTabAll = exports.SearchTabAll = function SearchTabAll(_ref) {
    var _keyword$trim2, _popularSearchKeyword, _saveFlightContent, _saveFlightContent2, _saveFlightContent3, _saveFlightContent4, _insertFlightPayload$6, _saveFlightContent5;
    var navigation = _ref.navigation,
      setModule = _ref.setModule;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      sourcePage = _useContext.sourcePage,
      trackingSearchResultClick = _useContext.trackingSearchResultClick,
      isPressSearchKey = _useContext.isPressSearchKey,
      setIsPressSearchKey = _useContext.setIsPressSearchKey;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useModal = (0, _useModal3.useModal)("saveConnectingFlightSearchAll"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var _useRecentSearch = (0, _recentSearchHook.useRecentSearch)(),
      addNewRecentSearchKeyword = _useRecentSearch.addNewRecentSearchKeyword;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var messageCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var searchAllLoading = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchAllLoading);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingLocal = _useState4[0],
      setLoadingLocal = _useState4[1];
    var searchAllResult = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchAllResult);
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload);
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload);
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isGoToListingFlight = _useState6[0],
      setIsGoToListingFlight = _useState6[1];
    var isDepartureData = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.isDepartureData);
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var alertApp = (0, _react.useRef)(null);
    var toastForSavedFlight = (0, _react.useRef)(null);
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var msg47 = messageCommonAEM == null ? undefined : messageCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var msg58 = messageCommonAEM == null ? undefined : messageCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg48 = messageCommonAEM == null ? undefined : messageCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var ehr12 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR12";
    });
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewExplorePlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var _useState7 = (0, _react.useState)(_flightDetail.TravelOption.iAmTravelling),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      selectedTravelOption = _useState8[0],
      setSelectedTravelOption = _useState8[1];
    var _useModal2 = (0, _useModal3.useModal)("saveFlightTravelOptionSearchAll"),
      isModalVisibleOption = _useModal2.isModalVisible,
      openModalOption = _useModal2.openModal,
      closeModalOption = _useModal2.closeModal;
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      loadingSaveFlight = _useState0[0],
      setLoadingSaveFlight = _useState0[1];
    var _useState1 = (0, _react.useState)(null),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      saveFlightPayload = _useState10[0],
      setSaveFlightPayload = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      showCalendarModal = _useState12[0],
      setShowCalendarModal = _useState12[1];
    var connectingFlightPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.connectingFlightPayload);
    var sectionTypeNeedCheck = [_searchIndex.GROUP_TYPE.facilities, _searchIndex.GROUP_TYPE.attractions];
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.GLOBAL_FLIGHT_SEARCH),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SEARCH_TAB_ALL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var handlePaddingBottom = function handlePaddingBottom() {
      if (_reactNative2.Platform.OS === "android") {
        return 24;
      }
      return 20;
    };
    var paddingScanBtn = (0, _react.useMemo)(function () {
      return {
        paddingBottom: handlePaddingBottom(),
        bottom: 0,
        elevation: 4,
        backgroundColor: "transparent"
      };
    }, []);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Search_All");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Search_All", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
      return function () {
        dispatch(_searchRedux.default.searchAllReset());
      };
    }, []);
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$, _env;
      var timeStamp = new Date().getTime();
      var insertFlightSuccessCondition = (0, _utils.ifOneTrue)([insertFlightPayload == null || (_insertFlightPayload$ = insertFlightPayload.insertFlightData) == null ? undefined : _insertFlightPayload$.success, insertFlightPayload == null ? undefined : insertFlightPayload.recordExist]);
      var addReturnPopupCondition = (0, _utils.ifOneTrue)([(0, _mmkvStorage.getLastSavedFlightTime)() + ((_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp, (0, _size2.default)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) === 1]);
      if (insertFlightSuccessCondition) {
        if ((0, _utils.ifAllTrue)([insertFlightPayload == null ? undefined : insertFlightPayload.isInsertSuccessfully, isFocused])) {
          var _insertFlightPayload$2;
          (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
            data: insertFlightPayload,
            isSuccess: true,
            tag: _adobe.AdobeTagName.CAppSaveFlight,
            flyProfile: "flying",
            pageName: _adobe.AdobeTagName.CAppSearchResult,
            isSaveFlight: true
          });
          if (addReturnPopupCondition && insertFlightPayload != null && (_insertFlightPayload$2 = insertFlightPayload.flightData) != null && _insertFlightPayload$2.isPassenger) {
            openModal();
            (0, _mmkvStorage.setLastSavedFlightTime)(0);
          } else {
            var _toastForRemoveFlight, _toastForSavedFlight$;
            closeModalOption();
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
            toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.closeNow();
            toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.show(_feedbackToastProps.DURATION.LENGTH_LONG);
            (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
          }
        }
      }
    }, [insertFlightPayload]);
    (0, _react.useEffect)(function () {
      return function () {
        if (isFocused) {
          dispatch(_searchRedux.default.searchAllReset());
        }
      };
    }, [keyword, isFocused]);
    var sectionListData = (0, _react.useMemo)(function () {
      var _searchAllResult$sear, _attractionsSection$d;
      var attractionsSection = searchAllResult == null || (_searchAllResult$sear = searchAllResult.searchData) == null ? undefined : _searchAllResult$sear.find(function (section) {
        return section.title === _searchIndex.GROUP_TYPE.attractions;
      });
      if (attractionsSection && attractionsSection != null && (_attractionsSection$d = attractionsSection.data) != null && _attractionsSection$d.length) {
        attractionsSection.data = [attractionsSection.data];
      }
      return searchAllResult == null ? undefined : searchAllResult.searchData;
    }, [searchAllResult]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        dispatch(_searchRedux.default.sendSearchKeywordCollection(_searchIndex.SearchIndex.all, sourcePage));
        dispatch(_searchRedux.default.resetListYouMayAlsoLikeData());
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      if (isFocused) {
        setModule(_searchIndex.SearchIndex.all);
      }
      if (keyword && isFocused) {
        searchAll(keyword);
        dispatch(_searchRedux.default.setViewAllFlight(false));
      }
    }, [keyword, isFocused]);
    (0, _react.useEffect)(function () {
      if (isPressSearchKey) {
        searchAll(keyword);
        setIsPressSearchKey(false);
      }
    }, [isPressSearchKey]);
    var searchAll = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (newKeyword) {
        var textSearch = newKeyword == null ? undefined : newKeyword.trim();
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (!isConnected) {
          setLoadingLocal(true);
          setTimeout(function () {
            setNoConnection(true);
            setLoadingLocal(false);
          }, 1000);
        } else {
          setNoConnection(false);
          if (!textSearch || (textSearch == null ? undefined : textSearch.length) < 2) {
            return;
          }
          dispatch(_searchRedux.default.searchAllRequest(textSearch, myTravelFlightsPayload));
        }
      });
      return function searchAll(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var onReloadData = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (isConnected) {
          setNoConnection(false);
          searchAll(keyword);
        } else {
          setNoConnection(true);
        }
      });
      return function onReloadData() {
        return _ref4.apply(this, arguments);
      };
    }();
    var handleTenantOnPress = function handleTenantOnPress(item) {
      if (item.sectionType === _searchIndex.GROUP_TYPE.dines) {
        navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
          tenantId: item == null ? undefined : item.id,
          name: item == null ? undefined : item.name
        });
      } else {
        navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
          tenantId: item == null ? undefined : item.id,
          name: item == null ? undefined : item.name
        });
      }
    };
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var handleMessage58 = function handleMessage58(message, flyItem) {
      if (message) {
        var _flyItem$flightStatus, _status;
        var status = flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var onRemoveFlight = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* (payload) {
        var item = payload == null ? undefined : payload.item;
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: item == null ? undefined : item.flightNumber,
          flightScheduledDate: item == null ? undefined : item.scheduledDate,
          flightDirection: item == null ? undefined : item.direction
        };
        _reactNative2.Alert.alert((msg48 == null ? undefined : msg48.title) || (0, _translate.translate)("flightLanding.areYouSure"), msg48 != null && msg48.message ? handleMessage48(msg48 == null ? undefined : msg48.message, item == null ? undefined : item.flightNumber, item == null ? undefined : item.destinationPlace) : `${(0, _translate.translate)("flightLanding.removeMessage1")} ${item == null ? undefined : item.flightNumber} ${(0, _translate.translate)("flightLanding.to")} ${item == null ? undefined : item.destinationPlace} ${(0, _translate.translate)("flightLanding.removeMessage2")}`, [{
          text: (msg48 == null ? undefined : msg48.firstButton) || (0, _translate.translate)("flightLanding.cancel")
        }, {
          text: (msg48 == null ? undefined : msg48.secondButton) || (0, _translate.translate)("flightLanding.remove"),
          style: "cancel",
          onPress: function onPress() {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyRemoveFlight, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyRemoveFlight, "1"));
            var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-search-all-unsave`);
            dtAction.reportStringValue("flight-search-all-unsave-press-flightNumber", `${item == null ? undefined : item.flightNumber}`);
            dtAction.reportStringValue("flight-search-all-unsave-press-scheduledDate", `${item == null ? undefined : item.scheduledDate}`);
            dtAction.reportStringValue("flight-search-all-unsave-press-direction", `${item == null ? undefined : item.direction}`);
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightRequest(data, payload));
            dtAction.leaveAction();
          }
        }]);
      });
      return function onRemoveFlight(_x2) {
        return _ref5.apply(this, arguments);
      };
    }();
    var notAbleToSaveAlert = function notAbleToSaveAlert(flyItem) {
      var _flyItem$flightStatus2, _alertApp$current;
      var temp = flyItem == null || (_flyItem$flightStatus2 = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus2.split(" ");
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message, flyItem) || `${(0, _translate.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _translate.translate)("flightLanding.has")} ${status} ${(0, _translate.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _translate.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _translate.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp2.AlertTypes.ALERT
      });
    };
    var checkFlightCanSave = function checkFlightCanSave(statusTag, item) {
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      var priorityTime = (item == null ? undefined : item.actualTimestamp) || (item == null ? undefined : item.estimatedTimestamp) || `${item == null ? undefined : item.flightDate} ${item == null ? undefined : item.timeOfFlight}`;
      var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
      var flightTime = (0, _momentTimezone.default)(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
        case /landed/gim.test(status) && (0, _momentTimezone.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") < currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
          return false;
        default:
          return true;
      }
    };
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully && isFocused) {
        var _toastForSavedFlight$2, _toastForRemoveFlight2;
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: removeFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppRemoveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppSearchResult,
          isSaveFlight: false
        });
        toastForSavedFlight == null || (_toastForSavedFlight$2 = toastForSavedFlight.current) == null || _toastForSavedFlight$2.closeNow();
        toastForRemoveFlight == null || (_toastForRemoveFlight2 = toastForRemoveFlight.current) == null || _toastForRemoveFlight2.show(_feedbackToastProps.DURATION.LENGTH_SHORT);
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      }
    }, [removeFlightPayload]);
    var getFlightDirection = (0, _react.useMemo)(function () {
      var _saveFlightPayload$it;
      if ((0, _isEmpty2.default)(saveFlightPayload)) return _flightProps.FlightDirection.departure;
      return saveFlightPayload == null || (_saveFlightPayload$it = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it.direction;
    }, [saveFlightPayload]);
    (0, _react.useEffect)(function () {
      if (!(0, _isEmpty2.default)(saveFlightPayload)) {
        var _saveFlightPayload$it2;
        setSelectedTravelOption((saveFlightPayload == null || (_saveFlightPayload$it2 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it2.direction) === _flightProps.FlightDirection.departure ? _flightDetail.TravelOption.iAmTravelling : _flightDetail.TravelOption.iAmPicking);
      }
    }, [saveFlightPayload]);
    (0, _react.useEffect)(function () {
      if (insertFlightPayload != null && insertFlightPayload.errorFlag) {
        setLoadingSaveFlight(false);
        closeModalOption();
      }
    }, [insertFlightPayload]);
    var onSaveFlight = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (payload, isSaved) {
        var _payload$item;
        if (isSaved && isLoggedIn) {
          return onRemoveFlight(payload);
        }
        if (!checkFlightCanSave(payload == null || (_payload$item = payload.item) == null ? undefined : _payload$item.flightStatus, payload == null ? undefined : payload.item)) {
          return notAbleToSaveAlert(payload == null ? undefined : payload.item);
        }
        setSaveFlightPayload(payload);
        if (isLoggedIn) {
          openModalOption();
        } else {
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
            callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
              openModalOption();
            },
            callBackAfterLoginCancel: function callBackAfterLoginCancel() {
              return null;
            }
          });
        }
      });
      return function onSaveFlight(_x3, _x4) {
        return _ref6.apply(this, arguments);
      };
    }();
    var savedFlightOnPress = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* () {
        var _saveFlightPayload$it3, _saveFlightPayload$it4, _saveFlightPayload$it5;
        setLoadingSaveFlight(true);
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: saveFlightPayload == null || (_saveFlightPayload$it3 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it3.flightNumber,
          flightScheduledDate: saveFlightPayload == null || (_saveFlightPayload$it4 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it4.scheduledDate,
          flightDirection: saveFlightPayload == null || (_saveFlightPayload$it5 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it5.direction,
          // check param
          flightPax: selectedTravelOption === _flightDetail.TravelOption.iAmTravelling
        };
        if (isLoggedIn) {
          var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-search-all-save`);
          dtAction.reportStringValue("flight-search-all-save-press-flightNumber", `${data.flightNumber}`);
          dtAction.reportStringValue("flight-search-all-save-press-scheduledDate", `${data.flightScheduledDate}`);
          dtAction.reportStringValue("flight-search-all-save-press-flightDirection", `${data.flightDirection}`);
          dtAction.reportStringValue("flight-search-all-save-press-isPassenger", String(data == null ? undefined : data.flightPax));
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, saveFlightPayload));
          dtAction.leaveAction();
        } else {
          navigation.navigate(_constants.NavigationConstants.authScreen);
        }
      });
      return function savedFlightOnPress() {
        return _ref7.apply(this, arguments);
      };
    }();
    var onPressSections = function onPressSections(item, section, sectionTitle) {
      var screenDirection = isDepartureData ? _searchTabFlights.RedirectTab.DepartureTab : _searchTabFlights.RedirectTab.ArrivalTab;
      trackingSearchResultClick(item == null ? undefined : item.name, _searchIndex.SearchIndex.all, getGlobalIndexItem(section, item, sectionTitle));
      addNewRecentSearchKeyword(item == null ? undefined : item.name);
      navigation.navigate(_searchIndex.SearchIndex.flights, {
        screen: screenDirection
      });
      dispatch(_searchRedux.default.setSearchKeyword(item == null ? undefined : item.name));
    };
    var onPressItemFacilitiesService = function onPressItemFacilitiesService(item, section, sectionTitle) {
      var type = (0, _get2.default)(item, "navigation.type");
      var value = (0, _get2.default)(item, "navigation.value");
      var redirect = (0, _get2.default)(item, "redirect", {});
      var itemId = '';
      if (!type || !value) return;
      if ((item == null ? undefined : item.sectionType) === _searchIndex.GROUP_TYPE.attractions) {
        itemId = item == null ? undefined : item.id;
      }
      if ((item == null ? undefined : item.sectionType) === _searchIndex.GROUP_TYPE.facilities) {
        itemId = item == null ? undefined : item.contentId;
      }
      trackingSearchResultClick(item == null ? undefined : item.title, _searchIndex.SearchIndex.all, getGlobalIndexItem(section, item, sectionTitle));
      handleNavigation(type, value, redirect);
    };
    var onPressEventCard = function onPressEventCard(eventDetailsItem, section, sectionTitle) {
      var _env2;
      trackingSearchResultClick(eventDetailsItem == null ? undefined : eventDetailsItem.package_name, _searchIndex.SearchIndex.all, getGlobalIndexItem(section, eventDetailsItem, sectionTitle));
      if (isLoggedIn) {
        getPlayPassUrl(_constants.StateCode.PPEVENT, eventDetailsItem == null ? undefined : eventDetailsItem.code);
        return;
      }
      var params = `?app=3&package_code=${eventDetailsItem == null ? undefined : eventDetailsItem.code}`;
      navigation.navigate(_constants.NavigationConstants.playpassWebview, {
        uri: `${(_env2 = (0, _envParams.env)()) == null ? undefined : _env2.PLAYPASS_URL_NONE_LOGIN}${params}`,
        title: "",
        needBackButton: true,
        needCloseButton: true
      });
    };
    var getGlobalIndexItem = function getGlobalIndexItem(section, item, typeSection) {
      var _section$data;
      // Convert section data
      var sectionDataConvert = (0, _utils.simpleCondition)({
        condition: sectionTypeNeedCheck.includes(typeSection),
        ifValue: (_section$data = section.data) == null ? undefined : _section$data.flatMap(function (data) {
          return data;
        }),
        elseValue: section.data
      });

      // Find the index of the item in the section data
      var itemIndexInSection = sectionDataConvert.indexOf(item);

      // Find the index of the section in the section list
      var sectionIndex = sectionListData == null ? undefined : sectionListData.indexOf(section);

      // Convert section list data
      var sectionListDataConvert = sectionListData == null ? undefined : sectionListData.map(function (item) {
        if (sectionTypeNeedCheck.includes(item.title)) {
          var _item$data;
          return Object.assign({}, item, {
            data: item == null || (_item$data = item.data) == null ? undefined : _item$data.flatMap(function (data) {
              return data;
            })
          });
        }
        return item;
      });

      // Calculate the global index of the item
      var globalIndex = (sectionListDataConvert == null ? undefined : sectionListDataConvert.slice(0, sectionIndex + 1).reduce(function (acc, curr) {
        return acc + curr.data.length;
      }, 0)) - ((sectionDataConvert == null ? undefined : sectionDataConvert.length) - itemIndexInSection);
      return globalIndex;
    };
    var renderTitleView = function renderTitleView(title) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.headerSectionStyle,
            tx: title
          })
        })
      });
    };
    var renderItemResult = function renderItemResult(item, index, section) {
      switch (section.title) {
        case _searchIndex.GROUP_TYPE.airlines:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.itemResultStyle,
            children: (0, _jsxRuntime.jsx)(_menuOption.MenuOption, {
              type: _menuOption.MenuOptionType.default,
              redirection: _menuOption.RedirectType.Internal,
              title: item == null ? undefined : item.name,
              onPress: function onPress() {
                return onPressSections(item, section, section.title);
              },
              iconComponent: (0, _jsxRuntime.jsx)(_icons.Plane, {
                fill: "currentColor",
                style: planeAirlineIcon
              })
            })
          });
        case _searchIndex.GROUP_TYPE.dines:
        case _searchIndex.GROUP_TYPE.shops:
          {
            var getLocationContent = function getLocationContent() {
              return [item == null ? undefined : item.location_display, item == null ? undefined : item.area_display].filter(function (value) {
                return !(0, _isEmpty2.default)(value);
              }).join(` ${(0, _constants.getDotUnicode)()} `);
            };
            var getCategoryContent = function getCategoryContent() {
              var _item$categories;
              if (!(item != null && (_item$categories = item.categories) != null && _item$categories.length)) return '';
              return item.categories.slice(0, 3).map(function (ctg) {
                return ctg == null ? undefined : ctg.tagTitle;
              }).join(', ');
            };
            var getDietaryData = function getDietaryData() {
              var _item$dietary, _item$dietary2;
              if (!(item != null && (_item$dietary = item.dietary) != null && _item$dietary.length)) return undefined;
              var dietaryItem = item == null || (_item$dietary2 = item.dietary) == null ? undefined : _item$dietary2[0];
              var result = {
                content: dietaryItem == null ? undefined : dietaryItem.tagTitle,
                icon: undefined
              };
              switch (dietaryItem == null ? undefined : dietaryItem.tagTitle) {
                case _constants.DINE_DIETARY_TYPE.GLUTEN_FREE:
                case _constants.DINE_DIETARY_TYPE.GLUTEN_FREE_OPTIONS:
                  result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryGlutenFreeOptionsIcon, {});
                  break;
                case _constants.DINE_DIETARY_TYPE.HALAL:
                  result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryHalalIcon, {});
                  break;
                case _constants.DINE_DIETARY_TYPE.VEGAN:
                case _constants.DINE_DIETARY_TYPE.VEGAN_OPTIONS:
                  result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryVeganOptionsIcon, {});
                  break;
                case _constants.DINE_DIETARY_TYPE.VEGETARIAN:
                case _constants.DINE_DIETARY_TYPE.VEGETARIAN_FRIENDLY:
                  result.icon = (0, _jsxRuntime.jsx)(_icons.DietaryVegetarianFriendlyIcon, {});
                  break;
                default:
                  break;
              }
              return result;
            };
            return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: styles.itemResultStyle,
              children: [(0, _jsxRuntime.jsx)(_tenantListingHorizontal.TenantListingHorizontal, Object.assign({}, item, {
                categoryContent: getCategoryContent(),
                dietaryData: getDietaryData(),
                location: getLocationContent(),
                logoUrl: item == null ? undefined : item.logoImage,
                onPressed: function onPressed() {
                  trackingSearchResultClick(item == null ? undefined : item.name, _searchIndex.SearchIndex.all, getGlobalIndexItem(section, item, section.title));
                  handleTenantOnPress(item);
                },
                tenantName: item == null ? undefined : item.title
              })), (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.dividerStyle
              })]
            });
          }
        case _searchIndex.GROUP_TYPE.flights:
          {
            var isSaved = false;
            if (myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails) {
              isSaved = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(function (savedFlight) {
                return savedFlight.flightNumber === item.flightNumber && savedFlight.scheduledDate === item.scheduledDate && (savedFlight.flightDirection || savedFlight.direction) === (item.flightDirection || item.direction);
              }) >= 0;
            }
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: styles.flatListItemStyle,
              children: (0, _jsxRuntime.jsx)(_flightListingCard.default, Object.assign({}, item, {
                isLoggedIn: isLoggedIn,
                onPressed: function onPressed() {
                  var flightNumber = (0, _get2.default)(item, "flightNumber", "");
                  trackingSearchResultClick(flightNumber, _searchIndex.SearchIndex.all, getGlobalIndexItem(section, item, section.title));
                  navigation.navigate("flightDetails", {
                    payload: {
                      item: item,
                      flightNavigationType: _flightProps.FlightNavigationType.FlightAllSearch
                    },
                    direction: item == null ? undefined : item.direction
                  });
                },
                onSaved: function onSaved(isSaved) {
                  onSaveFlight({
                    item: item,
                    flightNavigationType: _flightProps.FlightNavigationType.FlightAllSearch
                  }, isSaved);
                },
                isSaved: isSaved,
                itemIndex: index
              }))
            });
          }
        case _searchIndex.GROUP_TYPE.cities:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.itemResultStyle,
            children: (0, _jsxRuntime.jsx)(_menuOption.MenuOption, {
              type: _menuOption.MenuOptionType.default,
              redirection: _menuOption.RedirectType.Internal,
              title: item == null ? undefined : item.name,
              onPress: function onPress() {
                return onPressSections(item, section, section.title);
              },
              iconComponent: (0, _jsxRuntime.jsx)(_icons.Plane, {
                fill: "currentColor",
                style: planeCitiesIcon
              })
            })
          });
        case _searchIndex.GROUP_TYPE.facilities:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.containerFacilities,
            children: item.map(function (el) {
              return (0, _jsxRuntime.jsx)(_facilitiesAndServiceHorizontal.default, {
                title: el == null ? undefined : el.title,
                onPressed: function onPressed() {
                  return onPressItemFacilitiesService(el, section, section.title);
                },
                imageUrl: (0, _screenHelper.getUriImage)(el == null ? undefined : el.image),
                locationDisplayText: el == null ? undefined : el.locationDisplayText,
                testID: `${SCREEN_NAME}__ItemFacilitiesAndServices__${index}`,
                accessibilityLabel: `${SCREEN_NAME}__ItemFacilitiesAndServices__${index}`
              }, `${SCREEN_NAME}__ItemFacilitiesAndServices__${index}`);
            })
          });
        case _searchIndex.GROUP_TYPE.attractions:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.containerFacilities,
            children: item.map(function (el) {
              return (0, _jsxRuntime.jsx)(_facilitiesAndServiceHorizontal.default, {
                title: el == null ? undefined : el.title,
                onPressed: function onPressed() {
                  return onPressItemFacilitiesService(el, section, section.title);
                },
                imageUrl: (0, _screenHelper.getUriImage)(el == null ? undefined : el.image),
                locationDisplayText: el == null ? undefined : el.locationDisplayText,
                testID: `${SCREEN_NAME}__ItemAttraction__${index}`,
                accessibilityLabel: `${SCREEN_NAME}__ItemAttraction__${index}`
              }, `${SCREEN_NAME}__ItemAttraction__${index}`);
            })
          });
        case _searchIndex.GROUP_TYPE.events:
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.containerEvent,
            children: (0, _jsxRuntime.jsx)(_eventCard.default, {
              title: item == null ? undefined : item.package_name,
              packageCode: item == null ? undefined : item.code,
              imageUrl: item == null ? undefined : item.package_listing_img_url,
              categoryType: _exploreItemType.ExploreItemCategoryEnum.event,
              location: [item == null ? undefined : item.event_location],
              eventStart: item == null ? undefined : item.event_start,
              eventEnd: item == null ? undefined : item.event_end,
              ticketPrices: item == null ? undefined : item.price,
              tokenType: item == null ? undefined : item.token_type,
              onPressed: function onPressed() {
                return onPressEventCard(item, section, section.title);
              },
              testID: `${SCREEN_NAME}__EventCard`,
              accessibilityLabel: `${SCREEN_NAME}__EventCard`,
              earnCrPointFlg: item == null ? undefined : item.earn_cr_points_flg
            })
          });
        default:
          return null;
      }
    };
    var _renderSectionHeader = function renderSectionHeader(title, sectionData) {
      switch (title) {
        case _searchIndex.GROUP_TYPE.dines:
          return renderTitleView("search.sectionTitle.dine");
        case _searchIndex.GROUP_TYPE.shops:
          return renderTitleView("search.sectionTitle.shop");
        case _searchIndex.GROUP_TYPE.airlines:
          return renderTitleView("search.sectionTitle.airline");
        case _searchIndex.GROUP_TYPE.flights:
          var scheduledDateToShow = function () {
            var selectedScheduledDate;
            sectionData == null || sectionData.forEach(function (item) {
              if (!selectedScheduledDate) {
                selectedScheduledDate = item == null ? undefined : item.scheduledDate;
              } else {
                var isScheduledDateBeforeSelectedOne = (0, _momentTimezone.default)(item == null ? undefined : item.scheduledDate).isBefore(selectedScheduledDate);
                if (isScheduledDateBeforeSelectedOne) {
                  selectedScheduledDate = item == null ? undefined : item.scheduledDate;
                }
              }
            });
            return selectedScheduledDate || new Date();
          }();
          return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
            children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                children: (0, _jsxRuntime.jsx)(_text.Text, {
                  style: styles.headerSectionStyle,
                  text: `${(0, _translate.translate)("search.searchFlights.flightFor")} ${(0, _dateTime.dateToFromNow)(scheduledDateToShow)}`
                })
              })
            }), (0, _jsxRuntime.jsx)(_flightInformationDisclaimer.default, {
              marginLeft: 0,
              marginRight: 0
            })]
          });
        case _searchIndex.GROUP_TYPE.cities:
          return renderTitleView("search.sectionTitle.cities");
        case _searchIndex.GROUP_TYPE.facilities:
          return renderTitleView("search.sectionTitle.facilities");
        case _searchIndex.GROUP_TYPE.attractions:
          return renderTitleView("search.sectionTitle.attractions");
        case _searchIndex.GROUP_TYPE.events:
          return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: Object.assign({}, styles.headerSectionStyle, styles.headerEventSectionStyle),
              tx: "search.sectionTitle.events"
            })
          });
        default:
          return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
      }
    };
    var showFlightAddedFeedBackToastMessage = function showFlightAddedFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForSavedFlight,
        style: feedBackToastStyle,
        textButtonStyle: toastButtonStyle,
        position: "bottom",
        textStyle: toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved"),
        onCallback: function onCallback() {
          return dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        }
      });
    };
    var renderFooterSection = function renderFooterSection(title, isViewMore, isDepartureFightData, sectionData) {
      var flightSearchScreenDirection = isDepartureFightData ? _searchTabFlights.RedirectTab.DepartureTab : _searchTabFlights.RedirectTab.ArrivalTab;
      switch (title) {
        case _searchIndex.GROUP_TYPE.dines:
          if (isViewMore) {
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return navigation.navigate("search", {
                      screen: _searchIndex.SearchIndex.dine
                    });
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.viewAllDine",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        case _searchIndex.GROUP_TYPE.shops:
          if (isViewMore) {
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return navigation.navigate("search", {
                      screen: _searchIndex.SearchIndex.shop
                    });
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.viewAllShop",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        case _searchIndex.GROUP_TYPE.airlines:
        case _searchIndex.GROUP_TYPE.cities:
          if (isViewMore) {
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyViewAll, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyViewAll, "1"));
                    navigation.navigate(_searchIndex.SearchIndex.flights, {
                      screen: flightSearchScreenDirection
                    });
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.viewAllFlight",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        case _searchIndex.GROUP_TYPE.flights:
          if (isViewMore) {
            var navigateFlightsTabScreen = getNavigateDirectionToSearchFlightsTab(sectionData);
            var onPressViewAllResultsInFlights = function onPressViewAllResultsInFlights() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyViewAll, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyViewAll, "1"));
              dispatch(_searchRedux.default.setViewAllFlight(true));
              dispatch(_searchRedux.default.setIsDepartureData(isDepartureFightData));
              navigation.navigate(_searchIndex.SearchIndex.flights, {
                screen: navigateFlightsTabScreen
              });
            };
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: onPressViewAllResultsInFlights,
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.viewAllFlight",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        case _searchIndex.GROUP_TYPE.facilities:
          if (isViewMore) {
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerFacilityAndServiceSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return navigation.navigate("search", {
                      screen: _searchIndex.SearchIndex.airport
                    });
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.Airport",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        case _searchIndex.GROUP_TYPE.attractions:
          if (isViewMore) {
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerFacilityAndServiceSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return navigation.navigate("search", {
                      screen: _searchIndex.SearchIndex.attractions
                    });
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.viewAllAttractions",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        case _searchIndex.GROUP_TYPE.events:
          if (isViewMore) {
            return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: styles.footerEventSectionStyles,
                children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
                  onPress: function onPress() {
                    return navigation.navigate("search", {
                      screen: _searchIndex.SearchIndex.events
                    });
                  },
                  children: (0, _jsxRuntime.jsx)(_text.Text, {
                    tx: "search.viewAllEvents",
                    style: styles.moreTextStyles
                  })
                })
              })
            });
          }
          break;
        default:
          return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {});
      }
    };
    var formatDataSearch = function formatDataSearch(dataSearch) {
      if (isShowMaintenance) {
        return (dataSearch == null ? undefined : dataSearch.filter(function (ele) {
          return ![_searchIndex.GROUP_TYPE.airlines, _searchIndex.GROUP_TYPE.flights, _searchIndex.GROUP_TYPE.cities].includes(ele == null ? undefined : ele.title);
        })) || [];
      }
      return dataSearch || [];
    };
    var checkEmptyData = function checkEmptyData() {
      var _keyword$trim;
      if (!searchAllLoading && (keyword == null || (_keyword$trim = keyword.trim()) == null ? undefined : _keyword$trim.length) > 1 && (0, _isArray2.default)(sectionListData)) {
        var data = formatDataSearch(sectionListData);
        var isEmpty = data.every(function (item) {
          var _item$data2;
          return !(item != null && (_item$data2 = item.data) != null && _item$data2.length);
        });
        return isEmpty;
      }
      return false;
    };
    if (searchAllResult != null && searchAllResult.searchError) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        onReload: onReloadData,
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
        overlayStyle: styles.overlayStyle
      });
    }
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: onReloadData,
        noInternetOverlayStyle: styles.overlayStyle
      });
    }
    var onClosedSheet = function onClosedSheet() {
      if (!loadingSaveFlight) {
        closeModalOption();
      }
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      setSelectedTravelOption(option);
    };
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      setSelectedTravelOption(_flightDetail.TravelOption.iAmTravelling);
    };
    var saveFlightContent = function saveFlightContent() {
      var _insertFlightPayload$3;
      var result = {
        title: msg47 == null ? undefined : msg47.title,
        messageText: (msg47 == null ? undefined : msg47.message) || (0, _translate.translate)("flightDetails.popupConfirmSaveFlight.message"),
        textButtonConfirm: msg47 == null ? undefined : msg47.firstButton,
        textButtonCancel: msg47 == null ? undefined : msg47.secondButton,
        textButtonConnection: ""
      };
      result.title = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.title");
      result.textButtonConnection = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton");
      result.textButtonConfirm = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton");
      result.textButtonCancel = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton");
      if ((insertFlightPayload == null || (_insertFlightPayload$3 = insertFlightPayload.flightData) == null || (_insertFlightPayload$3 = _insertFlightPayload$3.item) == null ? undefined : _insertFlightPayload$3.direction) === _flightProps.FlightDirection.arrival) {
        result.messageText = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.arrivalMessage");
      } else {
        result.messageText = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.departureMessage");
      }
      return result;
    };
    var handleModalConfirmSaveFlightHide = function handleModalConfirmSaveFlightHide() {
      var timeStamp = new Date().getTime();
      (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
      if (isGoToListingFlight) {
        handleGoToListingFlight();
      } else {
        handleNotGoToListingFlight();
      }
    };
    var handleGoToListingFlight = function handleGoToListingFlight() {
      setShowCalendarModal(true);
      setIsGoToListingFlight(false);
    };
    var handleNotGoToListingFlight = function handleNotGoToListingFlight() {
      var _toastForSavedFlight$3;
      toastForSavedFlight == null || (_toastForSavedFlight$3 = toastForSavedFlight.current) == null || _toastForSavedFlight$3.show(_feedbackToastProps.DURATION.LENGTH_LONG);
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
    };
    var handleConnectingFlightOnPress = function handleConnectingFlightOnPress() {
      var _insertFlightPayload$4, _insertFlightPayload$5;
      var connectingFlight = {
        isConnecting: true,
        flightConnecting: Object.assign({}, insertFlightPayload == null || (_insertFlightPayload$4 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$4.item, {
          isPassenger: insertFlightPayload == null || (_insertFlightPayload$5 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$5.isPassenger
        })
      };
      setIsGoToListingFlight(true);
      closeModal();
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlight));
    };
    var handleYmalItemOnPress = function handleYmalItemOnPress(item) {
      var contextData = {
        tabName: (0, _translate.translate)("search.tabTitles.all"),
        keyword: keyword,
        ymalTitle: item.title
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppSearchYAML, contextData);
      navigation.navigate(_constants.NavigationConstants.shopDetailsScreen, {
        tenantId: item == null ? undefined : item.id,
        name: item == null ? undefined : item.title
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
        style: styles.container,
        onPress: _reactNative2.Keyboard.dismiss,
        children: [((0, _isEmpty2.default)(keyword) || (keyword == null || (_keyword$trim2 = keyword.trim()) == null ? undefined : _keyword$trim2.length) < 2) && (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "always",
          children: [(0, _jsxRuntime.jsx)(_searchRecentSection2.default, {
            type: _searchRecentSection.SearchRecentSectionType.default
          }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
            data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
              return (item == null ? undefined : item.searchCategoryTab) === "all";
            })) == null ? undefined : _popularSearchKeyword.popularKeywords,
            searchIndex: _searchIndex.SearchIndex.all
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: [styles.scanBtnView, paddingScanBtn],
          children: (0, _jsxRuntime.jsx)(_scanButtonSearch.ScanButtonSearch, {
            onButtonPressed: function onButtonPressed() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, "1"));
            }
          })
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: searchAllLoading || loadingLocal,
          onPressed: function onPressed() {
            return _reactNative2.Keyboard.dismiss();
          },
          customStyle: Object.assign({}, _reactNative2.Platform.select({
            android: {
              elevation: 0
            }
          }))
        }), !(0, _isEmpty2.default)(keyword) && (keyword == null ? undefined : keyword.length) >= 2 && (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
          onPress: function onPress() {
            return _reactNative2.Keyboard.dismiss();
          },
          children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
            showsVerticalScrollIndicator: false,
            refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
              refreshing: false,
              onRefresh: onReloadData
            }),
            children: [checkEmptyData() ? (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
              onPress: function onPress() {
                return _reactNative2.Keyboard.dismiss();
              },
              children: (0, _jsxRuntime.jsx)(_noResults.default, {
                title: ehr12 == null ? undefined : ehr12.header,
                message: ehr12 == null ? undefined : ehr12.subHeader
              })
            }) : (0, _jsxRuntime.jsx)(_reactNative2.SectionList, {
              onScroll: _reactNative2.Keyboard.dismiss,
              scrollEnabled: true,
              sections: formatDataSearch(sectionListData),
              keyExtractor: function keyExtractor(_item, index) {
                return `${_item == null ? undefined : _item.flightNumber} ${_item == null ? undefined : _item.scheduledDate} ${index.toString()}`;
              },
              renderItem: function renderItem(_ref8) {
                var item = _ref8.item,
                  index = _ref8.index,
                  section = _ref8.section;
                return renderItemResult(item, index, section);
              },
              renderSectionHeader: function renderSectionHeader(renderData) {
                var _renderData$section = renderData.section,
                  title = _renderData$section.title,
                  data = _renderData$section.data;
                if (!data.length) {
                  return null;
                }
                return _renderSectionHeader(title, data);
              },
              renderSectionFooter: function renderSectionFooter(_ref9) {
                var _ref9$section = _ref9.section,
                  title = _ref9$section.title,
                  data = _ref9$section.data,
                  isViewMore = _ref9$section.isViewMore,
                  isDepartureFightData = _ref9$section.isDepartureFightData;
                return renderFooterSection(title, isViewMore, isDepartureFightData, data);
              },
              stickySectionHeadersEnabled: false,
              initialNumToRender: 3,
              onEndReachedThreshold: 0.1,
              showsVerticalScrollIndicator: false,
              contentContainerStyle: styles.sectionContainerStyles,
              testID: `${SCREEN_NAME}SectionList`,
              accessibilityLabel: `${SCREEN_NAME}SectionList`
            }), (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
              itemOnPress: handleYmalItemOnPress
            })]
          })
        }), (0, _jsxRuntime.jsx)(_saveFlightTraveOptionWrap.default, {
          onModalHide: savedFlightTravelOptionsOnModalHide,
          visible: isModalVisibleOption,
          onClosed: onClosedSheet,
          loadingSaveFlight: loadingSaveFlight,
          onBackPressed: onClosedSheet,
          selectedOption: selectedTravelOption,
          savedFlightOnPress: savedFlightOnPress,
          onPress: function onPress(option) {
            return travelOptionTapped(option);
          },
          flightDirection: getFlightDirection
        }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
          imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
          visible: isModalVisible && isFocused,
          title: (_saveFlightContent = saveFlightContent()) == null ? undefined : _saveFlightContent.title,
          messageText: (_saveFlightContent2 = saveFlightContent()) == null ? undefined : _saveFlightContent2.messageText,
          onClose: function onClose() {
            closeModal();
            (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
          },
          onButtonPressed: function onButtonPressed() {
            closeModal();
            setIsGoToListingFlight(true);
          },
          textButtonConfirm: (_saveFlightContent3 = saveFlightContent()) == null ? undefined : _saveFlightContent3.textButtonConfirm,
          textButtonCancel: (_saveFlightContent4 = saveFlightContent()) == null ? undefined : _saveFlightContent4.textButtonCancel,
          onModalHide: handleModalConfirmSaveFlightHide,
          isShowButtonConnection: (insertFlightPayload == null || (_insertFlightPayload$6 = insertFlightPayload.flightData) == null || (_insertFlightPayload$6 = _insertFlightPayload$6.item) == null ? undefined : _insertFlightPayload$6.direction) === _flightProps.FlightDirection.arrival,
          onButtonConnectionPressed: handleConnectingFlightOnPress,
          textButtonConnection: (_saveFlightContent5 = saveFlightContent()) == null ? undefined : _saveFlightContent5.textButtonConnection,
          disableCloseButton: true,
          openPendingModal: true
        }), (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
          isVisible: showCalendarModal,
          filterDate: (0, _momentTimezone.default)((0, _get2.default)(insertFlightPayload, "flightData.item.displayTimestamp")).format("YYYY-MM-DD") || (0, _momentTimezone.default)().format("YYYY-MM-DD"),
          initialMinDate: (0, _momentTimezone.default)((0, _get2.default)(insertFlightPayload, "flightData.item.displayTimestamp")).format("YYYY-MM-DD") || (0, _momentTimezone.default)().format("YYYY-MM-DD"),
          onClosedCalendarModal: function onClosedCalendarModal() {
            var _toastForSavedFlight$4;
            toastForSavedFlight == null || (_toastForSavedFlight$4 = toastForSavedFlight.current) == null || _toastForSavedFlight$4.show(_feedbackToastProps.DURATION.LENGTH_LONG);
            setShowCalendarModal(false);
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
            var connectingFlight = {
              isConnecting: false,
              flightConnecting: null
            };
            dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlight));
            (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
          },
          onDateSelected: function onDateSelected(dateString) {
            var direction = (0, _get2.default)(insertFlightPayload, "flightData.item.direction", "DEP");
            var country = (0, _get2.default)(insertFlightPayload, "flightData.item.country");
            var date = (0, _momentTimezone.default)(dateString).format("YYYY-MM-DD");
            setShowCalendarModal(false);
            dispatch(_flyRedux.FlyCreators.setFlightSearchDate(date));
            navigation.navigate("flightResultLandingScreen", {
              screen: direction === _flightProps.FlightDirection.departure ? _flightProps.FlightDirection.arrival : _flightProps.FlightDirection.departure,
              sourcePage: _adobe.AdobeTagName.CAppFlyFlightDetail,
              selectedDate: date,
              country: connectingFlightPayload.isConnecting ? "Singapore" : country
            });
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
            (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
          },
          testID: `${SCREEN_NAME}__AddReturnCalendar`,
          accessibilityLabel: `${SCREEN_NAME}__AddReturnCalendar`
        }), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
          ref: alertApp
        })]
      }), showFlightAddedFeedBackToastMessage(), (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForRemoveFlight,
        style: feedBackToastStyle,
        textButtonStyle: toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _translate.translate)("flyLanding.removeFlightNew")
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    containerEvent: {
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between"
    },
    containerFacilities: {
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between"
    },
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1,
      marginTop: 16
    },
    emptySectionListStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 24
    },
    flatListItemStyle: {
      marginBottom: 12
    },
    footerEventSectionStyles: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: 24
    },
    footerFacilityAndServiceSectionStyles: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: 24
    },
    footerSectionStyles: {
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 10,
      marginTop: 12
    },
    headerEventSectionStyle: {
      marginBottom: 7
    },
    headerSectionStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 0.96,
      lineHeight: 22,
      marginBottom: 16,
      marginTop: 40
    }),
    itemResultStyle: {
      marginBottom: 12
    },
    moreTextStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple
    }),
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    },
    scanBtnView: {
      left: 0,
      position: "absolute",
      right: 0,
      zIndex: 1
    },
    sectionContainerStyles: {
      paddingHorizontal: 24
    }
  });
