  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchParentScreen = exports.SearchIndex = exports.ParentNavigator = exports.MAX_LENGTH_DATA = exports.GROUP_TYPE = exports.DineShopTab = undefined;
  var SearchIndex = exports.SearchIndex = /*#__PURE__*/function (SearchIndex) {
    SearchIndex["dine"] = "DINE";
    SearchIndex["shop"] = "SHOP";
    SearchIndex["all"] = "ALL";
    SearchIndex["flights"] = "FLIGHT";
    SearchIndex["airport"] = "AIRPORT";
    SearchIndex["attractions"] = "ATTRACTIONS";
    SearchIndex["events"] = "EVENTS";
    return SearchIndex;
  }({});
  var SearchParentScreen = exports.SearchParentScreen = /*#__PURE__*/function (SearchParentScreen) {
    SearchParentScreen["DINE"] = "bottomNavigation.dineShop";
    SearchParentScreen["SHOP"] = "bottomNavigation.dineShop";
    return SearchParentScreen;
  }({});
  var ParentNavigator = exports.ParentNavigator = /*#__PURE__*/function (ParentNavigator) {
    ParentNavigator["bottomNavigator"] = "bottomNavigation";
    return ParentNavigator;
  }({});
  var DineShopTab = exports.DineShopTab = /*#__PURE__*/function (DineShopTab) {
    DineShopTab["DINE"] = "Dine";
    DineShopTab["SHOP"] = "Shop";
    return DineShopTab;
  }({});
  var GROUP_TYPE = exports.GROUP_TYPE = {
    airlines: "airlines",
    dines: "dines",
    shops: "shops",
    flights: "flights",
    cities: "cities",
    facilities: "facilities",
    attractions: "attractions",
    events: "events"
  };
  var MAX_LENGTH_DATA = exports.MAX_LENGTH_DATA = {
    events: 3,
    attractions: 4
  };
