  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  var FacilitiesAndServiceItem = function FacilitiesAndServiceItem(props) {
    var onPressed = props.onPressed,
      logoUrl = props.logoUrl,
      location = props.location,
      title = props.title,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "FacilitiesAndServiceItem" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "FacilitiesAndServiceItem" : _props$accessibilityL;
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      onPress: onPressed,
      testID: `${testID}__TouchableItemFacilitiesAndService`,
      accessibilityLabel: `${accessibilityLabel}__TouchableItemFacilitiesAndService`,
      accessible: false,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewContainer,
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.logoImageContainer,
          source: {
            uri: logoUrl
          }
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.textContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.tenantTextStyle,
            numberOfLines: 2,
            preset: "bodyTextBold",
            textBreakStrategy: "highQuality",
            children: title
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            numberOfLines: 2,
            style: styles.locationTextStyle,
            preset: "caption1Regular",
            children: location
          })]
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    locationTextStyle: {
      color: _theme.color.palette.darkestGrey,
      marginBottom: 8
    },
    logoImageContainer: {
      alignSelf: "center",
      borderRadius: 12,
      flexDirection: "column",
      height: 80,
      width: 80
    },
    tenantTextStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 4
    },
    textContainer: {
      alignSelf: "center",
      flexBasis: "72%",
      flexDirection: "column",
      justifyContent: "center",
      marginLeft: 16
    },
    viewContainer: {
      flexDirection: "row"
    }
  });
  var _default = exports.default = FacilitiesAndServiceItem;
