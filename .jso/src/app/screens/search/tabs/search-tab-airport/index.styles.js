  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = _reactNative.StyleSheet.create({
    containerRecentSearch: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      paddingHorizontal: 24
    },
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1
    },
    emptyStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestGrey,
      height: "100%",
      justifyContent: "center",
      paddingHorizontal: 24
    },
    feedBackToastStyle: {
      bottom: 8,
      paddingHorizontal: 16
    },
    flatListItemsStyle: {
      paddingVertical: 16
    },
    flatListStyle: {
      flex: 1,
      paddingTop: 24
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      paddingBottom: 80
    },
    paddingContentList: {
      paddingBottom: 24,
      paddingHorizontal: 24
    },
    titleContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingTop: 14
    },
    titleStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey
    }),
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    ymalContainer: {
      marginTop: 40,
      paddingHorizontal: 0
    },
    ymalContainerInNoResult: {
      marginTop: 40
    },
    ymalEmptyStyles: {
      height: 0
    },
    ymalFlatListStyles: {
      paddingBottom: 0
    }
  });
  var _default = exports.default = styles;
