  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[10]);
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[12]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[13]);
  var _searchRecentSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _searchRecentSection2 = _$$_REQUIRE(_dependencyMap[15]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _text = _$$_REQUIRE(_dependencyMap[17]);
  var _constants = _$$_REQUIRE(_dependencyMap[18]);
  var _feedbackToast = _$$_REQUIRE(_dependencyMap[19]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[20]);
  var _index = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _facilitiesAndServiceItem = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[24]);
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _noResults = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _searchYouMayAlsoLike = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _translate = _$$_REQUIRE(_dependencyMap[28]);
  var _adobe = _$$_REQUIRE(_dependencyMap[29]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[30]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[31]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "SEARCH_FACILITIES_AND_SERVICES_SCREEN";
  var SearchFacilitiesAndServicesScreen = function SearchFacilitiesAndServicesScreen(_ref) {
    var _keyword$trim;
    var setModule = _ref.setModule;
    var toast = (0, _react.useRef)(null);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      sourcePage = _useContext.sourcePage,
      isPressSearchKey = _useContext.isPressSearchKey,
      trackingSearchResultClick = _useContext.trackingSearchResultClick;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var airportSearchPayload = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.airportSearchPayload);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingLocal = _useState4[0],
      setLoadingLocal = _useState4[1];
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var ehr12 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR12";
    });
    var loadingSearch = (0, _lodash.get)(airportSearchPayload, "loading", false);
    var isPagingRequest = (0, _lodash.get)(airportSearchPayload, "query.pagingRequest", false);
    var error = (0, _lodash.get)(airportSearchPayload, "error", null);
    var data = (0, _lodash.get)(airportSearchPayload, "data", null);
    var totalItems = (0, _lodash.get)(airportSearchPayload, "query.total", 0);
    var loading = loadingSearch || loadingLocal;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("AIRPORT_SEARCH_FACILITIES_SERVICES"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var searchAirport = function searchAirport(text) {
      var pagingRequest = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
      var textSearch = text == null ? undefined : text.trim();
      if (!textSearch || (textSearch == null ? undefined : textSearch.length) < 2) {
        return;
      }
      dispatch(_searchRedux.default.airportSearchRequest(textSearch, pagingRequest));
    };
    var handleSearchAirport = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (text) {
        var pagingRequest = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (!isConnected) {
          setLoadingLocal(true);
          setTimeout(function () {
            setNoConnection(true);
            setLoadingLocal(false);
          }, 1000);
          return;
        }
        searchAirport(text, pagingRequest);
      });
      return function handleSearchAirport(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var loadMoreData = function loadMoreData() {
      if (Array.isArray(data) && (data == null ? undefined : data.length) < totalItems && isFocused) {
        handleSearchAirport(keyword, true);
      }
    };
    (0, _react.useEffect)(function () {
      if (isFocused) {
        setModule(_searchIndex.SearchIndex.airport);
      } else {
        dispatch(_searchRedux.default.sendSearchKeywordCollection(_searchIndex.SearchIndex.airport, sourcePage));
        dispatch(_searchRedux.default.resetListYouMayAlsoLikeData());
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
      return function () {
        dispatch(_searchRedux.default.airportSearchReset());
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (!keyword) {
        dispatch(_searchRedux.default.airportSearchReset());
      }
    }, [keyword]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        return;
      }
      if (keyword) {
        handleSearchAirport(keyword);
      }
    }, [keyword, isFocused]);
    (0, _react.useEffect)(function () {
      if (isPressSearchKey) {
        handleSearchAirport(keyword);
      }
    }, [isPressSearchKey]);
    (0, _react.useEffect)(function () {
      var _toast$current2;
      if (!loadingSearch && error && isPagingRequest) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.show(_constants.TOAST_MESSAGE_DURATION);
        return;
      }
      toast == null || (_toast$current2 = toast.current) == null || _toast$current2.closeNow();
    }, [loadingSearch, error, isPagingRequest]);
    var onReloadData = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch3.isConnected;
        if (isConnected) {
          setNoConnection(false);
          handleSearchAirport(keyword);
        }
      });
      return function onReloadData() {
        return _ref4.apply(this, arguments);
      };
    }();
    var ListHeaderComponent = (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _index.default.titleContainer,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _index.default.titleStyle,
          tx: "search.resultCaption.airport"
        })
      })
    });
    var onPressItemFacilitiesService = function onPressItemFacilitiesService(item) {
      var type = (0, _lodash.get)(item, "navigation.type");
      var value = (0, _lodash.get)(item, "navigation.value");
      var redirect = (0, _lodash.get)(item, "redirect", {});
      if (!type || !value) return;
      handleNavigation(type, value, redirect);
    };
    var handleYmalItemOnPress = function handleYmalItemOnPress(item) {
      var contextData = {
        tabName: (0, _translate.translate)("search.tabTitles.airport"),
        keyword: keyword == null ? undefined : keyword.trim(),
        ymalTitle: item.title
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppSearchYAML, contextData);
      var navigation = item.navigation;
      if ((0, _lodash.isEmpty)(navigation)) {
        return;
      }
      handleNavigation(navigation == null ? undefined : navigation.type, navigation == null ? undefined : navigation.value, (navigation == null ? undefined : navigation.redirect) || {});
    };
    var renderFlatListData = function renderFlatListData(_ref5) {
      var item = _ref5.item,
        index = _ref5.index;
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _index.default.flatListItemsStyle,
        children: (0, _jsxRuntime.jsx)(_facilitiesAndServiceItem.default, {
          testID: `${SCREEN_NAME}__FlatListSearchResult`,
          accessibilityLabel: `${SCREEN_NAME}__FlatListSearchResult`,
          onPressed: function onPressed() {
            trackingSearchResultClick(item == null ? undefined : item.title, _searchIndex.SearchIndex.airport, index);
            onPressItemFacilitiesService(item);
          },
          logoUrl: (0, _screenHelper.getUriImage)(item == null ? undefined : item.image),
          location: item == null ? undefined : item.locationDisplayText,
          title: item == null ? undefined : item.title
        })
      });
    };
    var footerComponent = function footerComponent() {
      return (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
        containerStyles: _index.default.ymalContainer,
        flatListStyles: _index.default.ymalFlatListStyles,
        emptyScreenStyles: _index.default.ymalEmptyStyles,
        itemOnPress: handleYmalItemOnPress
      });
    };
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
          onReload: onReloadData,
          noInternetOverlayStyle: _index.default.overlayStyle
        })
      });
    }
    if (error && !isPagingRequest) {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        onReload: onReloadData,
        testID: `${SCREEN_NAME}__ErrorOverlay`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
        overlayStyle: _index.default.overlayStyle
      });
    }
    if ((0, _lodash.isEmpty)(data) && Array.isArray(data) && !error && !loading) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          children: [(0, _jsxRuntime.jsx)(_noResults.default, {
            title: ehr12 == null ? undefined : ehr12.header,
            message: ehr12 == null ? undefined : ehr12.subHeader
          }), (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
            containerStyles: _index.default.ymalContainerInNoResult,
            flatListStyles: _index.default.ymalFlatListStyles,
            emptyScreenStyles: _index.default.ymalEmptyStyles,
            itemOnPress: handleYmalItemOnPress
          })]
        })
      });
    }
    if (!keyword || (keyword == null || (_keyword$trim = keyword.trim()) == null ? undefined : _keyword$trim.length) < 2) {
      var _popularSearchKeyword;
      return (0, _jsxRuntime.jsx)(_reactNative.Pressable, {
        onPress: _reactNative2.Keyboard.dismiss,
        style: _index.default.containerStyle,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "always",
          children: [(0, _jsxRuntime.jsx)(_searchRecentSection.default, {
            type: _searchRecentSection2.SearchRecentSectionType.default
          }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
            data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
              return (item == null ? undefined : item.searchCategoryTab) === "airport";
            })) == null ? undefined : _popularSearchKeyword.popularKeywords,
            searchIndex: _searchIndex.SearchIndex.airport
          })]
        })
      });
    }
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _reactNative2.Keyboard.dismiss();
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _index.default.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: loading,
          onPressed: function onPressed() {
            return _reactNative2.Keyboard.dismiss();
          }
        }), (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: onReloadData
          }),
          onScroll: _reactNative2.Keyboard.dismiss,
          data: data,
          renderItem: renderFlatListData,
          ItemSeparatorComponent: function ItemSeparatorComponent() {
            return (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _index.default.dividerStyle
            });
          },
          showsVerticalScrollIndicator: false,
          onEndReachedThreshold: 0.3,
          onEndReached: function onEndReached(_ref6) {
            var distanceFromEnd = _ref6.distanceFromEnd;
            if (distanceFromEnd >= 0) {
              loadMoreData();
            }
          },
          keyExtractor: function keyExtractor(_, index) {
            return index.toString();
          },
          style: _index.default.flatListStyle,
          contentContainerStyle: _index.default.paddingContentList,
          ListHeaderComponent: (0, _lodash.isEmpty)(data) ? null : ListHeaderComponent,
          ListFooterComponent: footerComponent
        }), (0, _jsxRuntime.jsx)(_feedbackToast.FeedBackToast, {
          ref: toast,
          style: _index.default.feedBackToastStyle,
          textButtonStyle: _index.default.toastButtonStyle,
          position: "custom",
          textStyle: _index.default.toastTextStyle,
          type: _feedbackToast.FeedBackToastType.fullWidthFeedBack,
          text: "Unable to refresh airport searches",
          testID: `${SCREEN_NAME}__FeedBackToastErrorMessage`,
          accessibilityLabel: `${SCREEN_NAME}__FeedBackToastErrorMessage`
        })]
      })
    });
  };
  var _default = exports.default = SearchFacilitiesAndServicesScreen;
