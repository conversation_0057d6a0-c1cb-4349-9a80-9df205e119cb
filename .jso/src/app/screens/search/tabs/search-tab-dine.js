  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _searchScreen = _$$_REQUIRE(_dependencyMap[9]);
  var _searchResult = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _searchIndex = _$$_REQUIRE(_dependencyMap[11]);
  var _error = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[15]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[16]);
  var _searchRecentSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _searchRecentSection2 = _$$_REQUIRE(_dependencyMap[18]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _native = _$$_REQUIRE(_dependencyMap[20]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[22]);
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _noResults = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _searchYouMayAlsoLike = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _screenHelper = _$$_REQUIRE(_dependencyMap[29]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[30]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[31]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "SearchTabDine__";
  var SearchTabDine = function SearchTabDine(_ref) {
    var _searchKeyword$trim;
    var setModule = _ref.setModule;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var toast = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      sourcePage = _useContext.sourcePage,
      isPressSearchKey = _useContext.isPressSearchKey,
      setIsPressSearchKey = _useContext.setIsPressSearchKey;
    var searchResultPayload = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.dineSearchResults);
    var isResultFetching = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.dineSearchResultsFetching);
    var isPaginating = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.dineSearchResultsPaginating);
    var searchKeyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var totalResultItems = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.dineSearchTotalItems);
    var currentPage = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.dineSearchCurrentPage);
    var isAPIError = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.dineSearchError);
    var isFocused = (0, _native.useIsFocused)();
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingLocal = _useState4[0],
      setLoadingLocal = _useState4[1];
    var ehr12 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR12";
    });
    var checkInternetAndSearch = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          setNoConnection(false);
          if (isFocused) {
            setModule(_searchIndex.SearchIndex.dine);
          }
          if ((searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2 && isFocused) {
            dispatch(_searchRedux.default.dineSearchRequest({
              keyword: searchKeyword,
              pageNumber: 1,
              pageSize: 15
            }));
          }
        } else {
          setLoadingLocal(true);
          setTimeout(function () {
            setLoadingLocal(false);
            setNoConnection(true);
          }, 1000);
        }
      });
      return function checkInternetAndSearch() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Search_Dine");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Search_Dine", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        dispatch(_searchRedux.default.sendSearchKeywordCollection(_searchIndex.SearchIndex.dine, sourcePage));
        dispatch(_searchRedux.default.resetListYouMayAlsoLikeData());
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      checkInternetAndSearch();
      return function () {
        isFocused && dispatch(_searchRedux.default.dineSearchReset());
      };
    }, [searchKeyword, isFocused]);
    (0, _react.useEffect)(function () {
      if (isPressSearchKey) {
        checkInternetAndSearch();
        setIsPressSearchKey(false);
      }
    }, [isPressSearchKey]);
    (0, _react.useEffect)(function () {
      if (isAPIError && (searchResultPayload == null ? undefined : searchResultPayload.length) < totalResultItems) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.show(_constants.TOAST_MESSAGE_DURATION);
      }
    }, [isAPIError]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [isFocused]);
    var loadMoreData = function loadMoreData() {
      if ((searchResultPayload == null ? undefined : searchResultPayload.length) < totalResultItems) {
        toast.current.close(1);
        isFocused && dispatch(_searchRedux.default.dineSearchPaginate({
          keyword: searchKeyword,
          pageNumber: currentPage + 1,
          pageSize: 15
        }));
      }
    };
    var reloadData = function reloadData() {
      if (isFocused) {
        dispatch(_searchRedux.default.dineSearchReset());
        dispatch(_searchRedux.default.dineSearchRequest({
          keyword: searchKeyword,
          pageNumber: 1,
          pageSize: 15
        }));
        dispatch(_searchRedux.default.resetListYouMayAlsoLikeData());
      }
    };
    var handleYmalItemOnPress = function handleYmalItemOnPress(item) {
      var contextData = {
        tabName: (0, _i18n.translate)("search.tabTitles.dine"),
        keyword: searchKeyword == null ? undefined : searchKeyword.trim(),
        ymalTitle: item.title
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppSearchYAML, contextData);
      navigation.navigate(_constants.NavigationConstants.restaurantDetailScreen, {
        tenantId: item == null ? undefined : item.id,
        name: item == null ? undefined : item.title
      });
    };
    var showErrorToastMessage = function showErrorToastMessage() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _searchScreen.styles.toastContainer,
        children: (0, _jsxRuntime.jsx)(_feedbackToast.default, {
          ref: toast,
          style: _searchScreen.styles.feedBackToastStyle,
          textButtonStyle: _searchScreen.styles.toastButtonStyle,
          textStyle: _searchScreen.styles.toastTextStyle,
          type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA,
          text: (0, _i18n.translate)("search.dinePaginationError"),
          buttonText: (0, _i18n.translate)("search.retry"),
          onPress: loadMoreData,
          testID: `${COMPONENT_NAME}FeedBackToastError`,
          accessibilityLabel: `${COMPONENT_NAME}FeedBackToastError`
        })
      });
    };
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          testID: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
          accessibilityLabel: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
          onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch3.isConnected;
            if (isConnected) {
              setNoConnection(false);
              reloadData();
            }
          }),
          noInternetOverlayStyle: _searchScreen.styles.overlayStyle
        })
      });
    }
    if (!searchKeyword || (searchKeyword == null || (_searchKeyword$trim = searchKeyword.trim()) == null ? undefined : _searchKeyword$trim.length) < 2) {
      var _popularSearchKeyword;
      return (0, _jsxRuntime.jsx)(_reactNative.Pressable, {
        style: _searchScreen.styles.container,
        onPress: _reactNative2.Keyboard.dismiss,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "always",
          children: [(0, _jsxRuntime.jsx)(_searchRecentSection.default, {
            type: _searchRecentSection2.SearchRecentSectionType.default
          }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
            data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
              return (item == null ? undefined : item.searchCategoryTab) === "dine";
            })) == null ? undefined : _popularSearchKeyword.popularKeywords,
            searchIndex: _searchIndex.SearchIndex.dine
          })]
        })
      });
    }
    if (!isResultFetching && Array.isArray(searchResultPayload) && (searchResultPayload == null ? undefined : searchResultPayload.length) === 0 && (searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          children: [(0, _jsxRuntime.jsx)(_noResults.default, {
            title: ehr12 == null ? undefined : ehr12.header,
            message: ehr12 == null ? undefined : ehr12.subHeader
          }), (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
            containerStyles: _searchScreen.styles.ymalContainer,
            flatListStyles: _searchScreen.styles.ymalFlatListStyles,
            emptyScreenStyles: _searchScreen.styles.ymalEmptyStyles,
            itemOnPress: handleYmalItemOnPress
          })]
        })
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
      style: _searchScreen.styles.container,
      onPress: _reactNative2.Keyboard.dismiss,
      children: [isAPIError && totalResultItems === null ? (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
        onReload: function onReload() {
          return reloadData();
        },
        testID: `${COMPONENT_NAME}__ErrorReload`,
        accessibilityLabel: `${COMPONENT_NAME}__ErrorReload`
      }) : !isResultFetching && (searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2 && (0, _jsxRuntime.jsx)(_searchResult.default, {
        onRefresh: reloadData,
        data: searchResultPayload,
        searchIndex: _searchIndex.SearchIndex.dine,
        onEndReached: loadMoreData,
        testID: `${COMPONENT_NAME}__SearchResult`,
        accessibilityLabel: `${COMPONENT_NAME}__SearchResult`,
        useTitle: true,
        textTitle: (0, _i18n.translate)("search.resultCaption.dine"),
        itemYmalOnPress: handleYmalItemOnPress
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: isPaginating || isResultFetching || loadingLocal,
        onPressed: function onPressed() {
          return _reactNative2.Keyboard.dismiss();
        },
        customStyle: Object.assign({}, _reactNative2.Platform.select({
          android: {
            elevation: 0
          }
        }))
      }), showErrorToastMessage()]
    });
  };
  var _default = exports.default = SearchTabDine;
