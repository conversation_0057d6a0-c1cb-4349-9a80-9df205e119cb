  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.NavigationType = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[7]));
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _searchScreen = _$$_REQUIRE(_dependencyMap[9]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[10]);
  var _error = _$$_REQUIRE(_dependencyMap[11]);
  var _constants = _$$_REQUIRE(_dependencyMap[12]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[14]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[15]);
  var _searchRecentSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _searchRecentSection2 = _$$_REQUIRE(_dependencyMap[17]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[18]);
  var _native = _$$_REQUIRE(_dependencyMap[19]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[21]);
  var _adobe = _$$_REQUIRE(_dependencyMap[22]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[25]);
  var _eventCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _text = _$$_REQUIRE(_dependencyMap[27]);
  var _theme = _$$_REQUIRE(_dependencyMap[28]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[29]);
  var _envParams = _$$_REQUIRE(_dependencyMap[30]);
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _noResults = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[33]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchTabStyle = _reactNative2.StyleSheet.create({
    contentContainerStyle: {
      paddingBottom: 20
    },
    eventCardContainerStyles: {
      marginBottom: 0,
      marginTop: 24
    },
    flatListStyle: {
      minHeight: "100%",
      paddingBottom: 20,
      paddingHorizontal: 16
      // width: "100%",
    },
    titleContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      marginBottom: 5,
      marginHorizontal: 8,
      paddingTop: 40
    },
    titleStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 1.08,
      lineHeight: 24
    })
  });
  var NavigationType = exports.NavigationType = /*#__PURE__*/function (NavigationType) {
    NavigationType["inapp"] = "in-app";
    NavigationType["external"] = "external";
    NavigationType["deepLink"] = "deep-link";
    return NavigationType;
  }({});
  var COMPONENT_NAME = "SearchTabEvents__";
  var SearchTabEvents = function SearchTabEvents(_ref) {
    var _searchKeyword$trim;
    var setModule = _ref.setModule;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var toast = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      sourcePage = _useContext.sourcePage,
      isPressSearchKey = _useContext.isPressSearchKey,
      setIsPressSearchKey = _useContext.setIsPressSearchKey,
      trackingSearchResultClick = _useContext.trackingSearchResultClick;
    var searchResultPayload = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.eventsSearchResults);
    var isResultFetching = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.eventsSearchResultsFetching);
    var isPaginating = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.eventsSearchResultsPaginating);
    var searchKeyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var totalResultItems = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.eventsSearchTotalItems);
    var currentPage = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.eventsSearchCurrentPage);
    var isAPIError = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.eventsSearchError);
    var isFocused = (0, _native.useIsFocused)();
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingLocal = _useState4[0],
      setLoadingLocal = _useState4[1];
    var ehr12 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR12";
    });
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewExplorePlayPass"),
      getPlayPassUrl = _useGeneratePlayPassU.getPlayPassUrl;
    var checkInternetAndSearch = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          setNoConnection(false);
          if (isFocused) {
            setModule(_searchIndex.SearchIndex.events);
          }
          if ((searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2 && isFocused) {
            dispatch(_searchRedux.default.eventsSearchRequest({
              keyword: searchKeyword,
              pageNumber: 1,
              pageSize: 15
            }));
          }
        } else {
          setLoadingLocal(true);
          setTimeout(function () {
            setLoadingLocal(false);
            setNoConnection(true);
          }, 1000);
        }
      });
      return function checkInternetAndSearch() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Search_Events");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Search_Events", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        dispatch(_searchRedux.default.sendSearchKeywordCollection(_searchIndex.SearchIndex.events, sourcePage));
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      checkInternetAndSearch();
      return function () {
        isFocused && dispatch(_searchRedux.default.eventsSearchReset());
      };
    }, [searchKeyword, isFocused]);
    (0, _react.useEffect)(function () {
      if (isPressSearchKey) {
        checkInternetAndSearch();
        setIsPressSearchKey(false);
      }
    }, [isPressSearchKey]);
    (0, _react.useEffect)(function () {
      if (isAPIError && (searchResultPayload == null ? undefined : searchResultPayload.length) < totalResultItems) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.show(_constants.TOAST_MESSAGE_DURATION);
      }
    }, [isAPIError]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [isFocused]);
    var loadMoreData = function loadMoreData() {
      if ((searchResultPayload == null ? undefined : searchResultPayload.length) < totalResultItems) {
        toast.current.close(1);
        isFocused && dispatch(_searchRedux.default.eventsSearchPaginate({
          keyword: searchKeyword,
          pageNumber: currentPage + 1,
          pageSize: 15
        }));
      }
    };
    var reloadData = function reloadData() {
      isFocused && dispatch(_searchRedux.default.eventsSearchReset());
      isFocused && dispatch(_searchRedux.default.eventsSearchRequest({
        keyword: searchKeyword,
        pageNumber: 1,
        pageSize: 15
      }));
    };
    var showErrorToastMessage = function showErrorToastMessage() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _searchScreen.styles.toastContainer,
        children: (0, _jsxRuntime.jsx)(_feedbackToast.default, {
          ref: toast,
          style: _searchScreen.styles.feedBackToastStyle,
          textButtonStyle: _searchScreen.styles.toastButtonStyle,
          textStyle: _searchScreen.styles.toastTextStyle,
          type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA,
          text: (0, _i18n.translate)("search.eventsPaginationError"),
          buttonText: (0, _i18n.translate)("search.retry"),
          onPress: loadMoreData,
          testID: `${COMPONENT_NAME}FeedBackToastError`,
          accessibilityLabel: `${COMPONENT_NAME}FeedBackToastError`
        })
      });
    };
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          testID: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
          accessibilityLabel: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
          onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch3.isConnected;
            if (isConnected) {
              setNoConnection(false);
              reloadData();
            }
          }),
          noInternetOverlayStyle: _searchScreen.styles.overlayStyle
        })
      });
    }
    if (!searchKeyword || (searchKeyword == null || (_searchKeyword$trim = searchKeyword.trim()) == null ? undefined : _searchKeyword$trim.length) < 2) {
      var _popularSearchKeyword;
      return (0, _jsxRuntime.jsx)(_reactNative.Pressable, {
        onPress: _reactNative2.Keyboard.dismiss,
        style: _searchScreen.styles.container,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "always",
          children: [(0, _jsxRuntime.jsx)(_searchRecentSection.default, {
            type: _searchRecentSection2.SearchRecentSectionType.default
          }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
            data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
              return (item == null ? undefined : item.searchCategoryTab) === "events";
            })) == null ? undefined : _popularSearchKeyword.popularKeywords,
            searchIndex: _searchIndex.SearchIndex.events
          })]
        })
      });
    }
    if (!isResultFetching && Array.isArray(searchResultPayload) && (searchResultPayload == null ? undefined : searchResultPayload.length) === 0 && (searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsx)(_noResults.default, {
          title: ehr12 == null ? undefined : ehr12.header,
          message: ehr12 == null ? undefined : ehr12.subHeader
        })
      });
    }
    var onPress = function onPress(eventDetailsItem, index) {
      var _env;
      trackingSearchResultClick(eventDetailsItem == null ? undefined : eventDetailsItem.package_name, _searchIndex.SearchIndex.events, index);
      if (isLoggedIn) {
        getPlayPassUrl(_constants.StateCode.PPEVENT, eventDetailsItem == null ? undefined : eventDetailsItem.code);
        return;
      }
      var params = `?app=3&package_code=${eventDetailsItem == null ? undefined : eventDetailsItem.code}`;
      navigation.navigate(_constants.NavigationConstants.playpassWebview, {
        uri: `${(_env = (0, _envParams.env)()) == null ? undefined : _env.PLAYPASS_URL_NONE_LOGIN}${params}`,
        title: "",
        needBackButton: true,
        needCloseButton: true
      });
    };
    var headerComponent = function headerComponent() {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: SearchTabStyle.titleContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: SearchTabStyle.titleStyle,
            text: (0, _i18n.translate)("search.tabTitles.events")
          })
        })
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _reactNative2.Keyboard.dismiss();
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _searchScreen.styles.container,
        children: [isAPIError && totalResultItems === null ? (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
          onReload: function onReload() {
            return reloadData();
          },
          testID: `${COMPONENT_NAME}__ErrorReload`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorReload`
        }) : !isResultFetching && (searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2 && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: reloadData
          }),
          data: searchResultPayload,
          renderItem: function renderItem(_ref5) {
            var item = _ref5.item,
              index = _ref5.index;
            return (0, _jsxRuntime.jsx)(_eventCard.default, {
              title: item == null ? undefined : item.package_name,
              packageCode: item == null ? undefined : item.code,
              imageUrl: item == null ? undefined : item.package_listing_img_url,
              categoryType: _exploreItemType.ExploreItemCategoryEnum.event,
              location: [item == null ? undefined : item.event_location],
              eventStart: item == null ? undefined : item.event_start,
              eventEnd: item == null ? undefined : item.event_end,
              ticketPrices: item == null ? undefined : item.price,
              tokenType: item == null ? undefined : item.token_type,
              onPressed: function onPressed() {
                return onPress(item, index);
              },
              testID: `${COMPONENT_NAME}__EventCard`,
              accessibilityLabel: `${COMPONENT_NAME}__EventCard`,
              eventCardContainerStyles: SearchTabStyle.eventCardContainerStyles,
              earnCrPointFlg: item == null ? undefined : item.earn_cr_points_flg
            });
          },
          keyExtractor: function keyExtractor(_, index) {
            return `search_event_${index}`;
          },
          numColumns: 1,
          style: SearchTabStyle.flatListStyle,
          contentContainerStyle: SearchTabStyle.contentContainerStyle,
          scrollEnabled: true,
          testID: `${COMPONENT_NAME}__FlatListSearchEvents`,
          accessibilityLabel: `${COMPONENT_NAME}__FlatListSearchEvents`,
          ListHeaderComponent: headerComponent,
          onEndReachedThreshold: 0.3,
          onEndReached: function onEndReached(_ref6) {
            var distanceFromEnd = _ref6.distanceFromEnd;
            if (distanceFromEnd >= 0) {
              loadMoreData();
            }
          }
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: isPaginating || isResultFetching || loadingLocal,
          onPressed: function onPressed() {
            return _reactNative2.Keyboard.dismiss();
          },
          customStyle: Object.assign({}, _reactNative2.Platform.select({
            android: {
              elevation: 0
            }
          }))
        }), showErrorToastMessage()]
      })
    });
  };
  var _default = exports.default = SearchTabEvents;
