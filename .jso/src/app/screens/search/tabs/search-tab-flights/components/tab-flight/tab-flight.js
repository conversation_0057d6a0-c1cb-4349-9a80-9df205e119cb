  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _flyRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[13]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _filterFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[16]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[17]);
  var _flightListingCard = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _translate = _$$_REQUIRE(_dependencyMap[20]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[21]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[22]);
  var _text = _$$_REQUIRE(_dependencyMap[23]);
  var _searchRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _adobe = _$$_REQUIRE(_dependencyMap[25]);
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _tabFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _store = _$$_REQUIRE(_dependencyMap[28]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[29]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[30]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[31]);
  var _flightInformationDisclaimer = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _errorCloud = _$$_REQUIRE(_dependencyMap[33]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[34]);
  var _ = _$$_REQUIRE(_dependencyMap[35]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[36]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var initialPageNumber = 1;
  var FlightsFooter = function FlightsFooter(_ref) {
    var _flightDetailsPayload;
    var loading = _ref.loading,
      filterFlightRef = _ref.filterFlightRef,
      flightDetailsPayload = _ref.flightDetailsPayload;
    var onPressSelectOtherDates = function onPressSelectOtherDates() {
      var _filterFlightRef$curr;
      (_filterFlightRef$curr = filterFlightRef.current) == null || _filterFlightRef$curr.setShowCalendar(true);
    };
    if (loading || !(flightDetailsPayload != null && (_flightDetailsPayload = flightDetailsPayload.data) != null && _flightDetailsPayload.length)) {
      return null;
    }
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
      style: _tabFlight.default.flightsFooter,
      onPress: onPressSelectOtherDates,
      children: (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "textLink",
        tx: "search.searchFlights.selectOtherDate"
      })
    });
  };
  var TabFlightScreen = function TabFlightScreen(props) {
    var _flightDetailsPayload2, _flightDetailsPayload5;
    var directionFlight = props.directionFlight,
      initialDate = props.initialDate,
      onSaveFlight = props.onSaveFlight,
      flightNavigationType = props.flightNavigationType,
      shouldIgnoreDirectionAndDate = props.shouldIgnoreDirectionAndDate,
      setShouldIgnoreDirectionAndDate = props.setShouldIgnoreDirectionAndDate;
    var COMPONENT_NAME = `Flight__Tab__${directionFlight}`;
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      trackingSearchResultClick = _useContext.trackingSearchResultClick;
    var dispatch = (0, _reactRedux.useDispatch)();
    var toast = (0, _react.useRef)(null);
    var filterFlightRef = (0, _react.useRef)(null);
    var onEndReachedCalledDuringMomentum = (0, _react.useRef)(true);
    var flyLastUpdatedTimeStamp = (0, _react.useRef)((0, _moment.default)());
    var navigation = (0, _native.useNavigation)();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var flyPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.searchDetailFlightsPayload);
    var viewAllFlight = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.viewAllFlight);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var flightDetailsPayload = (0, _lodash.get)(flyPayload, directionFlight);
    var loading = (0, _lodash.get)(flightDetailsPayload, "loading");
    var currentPageNumber = (0, _lodash.get)(flightDetailsPayload, "query.pageNumber", 0);
    var filterDate = (0, _lodash.get)(flightDetailsPayload, "query.filterDate", initialDate);
    var hasLoadMore = (0, _lodash.get)(flightDetailsPayload, "query.hasLoadMore");
    var filtersFlight = (0, _lodash.get)(flightDetailsPayload, "query.filters");
    var errorPaging = (0, _lodash.get)(flightDetailsPayload, "errorPaging");
    var isFocused = (0, _native.useIsFocused)();
    var filtersLocation = (0, _react.useRef)(null);
    var filterDateRef = (0, _react.useRef)(new Date());
    var FlightListingCardComponent = _flightListingCard.default;
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var _useRoute = (0, _native.useRoute)(),
      params = _useRoute.params;
    var _ref2 = params || {},
      isAutoToggleFromArrivial = _ref2.isAutoToggleFromArrivial;
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.GLOBAL_FLIGHT_SEARCH),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance,
      errorData = _useTickerbandMaintan.errorData,
      fetchTickerbandMaintanance = _useTickerbandMaintan.fetchTickerbandMaintanance;
    var firstFlightDetailItem = flightDetailsPayload == null || (_flightDetailsPayload2 = flightDetailsPayload.data) == null ? undefined : _flightDetailsPayload2[0];
    var firstFlightDetailItemScheduledDate = firstFlightDetailItem == null ? undefined : firstFlightDetailItem.scheduledDate;
    var isArrivalTab = directionFlight === _flightProps.FlightDirection.arrival;
    var initIgnoreDirectionAndDateValue = shouldIgnoreDirectionAndDate != null ? shouldIgnoreDirectionAndDate : isAutoToggleFromArrivial || isArrivalTab;
    var initIgnoreDirectionAndDateRefValue = viewAllFlight ? false : initIgnoreDirectionAndDateValue;
    var isRefreshingRef = (0, _react.useRef)(false);
    var shouldIgnoreDirectionAndDateRef = (0, _react.useRef)(initIgnoreDirectionAndDateRefValue);
    var searchedScheduledDateRef = (0, _react.useRef)(undefined);
    (0, _react.useEffect)(function () {
      dispatch(_flyRedux.FlyCreators.resetSearchDetailFlightsPayload());
      return function () {
        dispatch(_flyRedux.FlyCreators.setFilterDateSearchArrival(null));
        dispatch(_flyRedux.FlyCreators.setFilterDateSearchDeparture(null));
      };
    }, []);
    var searchFlight = function searchFlight(options) {
      if (!isFocused) return null;
      var text = keyword == null ? undefined : keyword.trim();
      if (!text || (text == null ? undefined : text.length) < 2) {
        return;
      }
      var fetchFlight = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          setNoConnection(!isConnected);
          props == null || props.setTabNoConnection(!isConnected);
          if (isConnected) {
            var isPagingRequest = options.isPagingRequest,
              date = options.date,
              pageNumber = options.pageNumber,
              _options$filters = options.filters,
              filters = _options$filters === undefined ? filtersFlight : _options$filters;
            var dateToRequest = date || handleDateSelectedByDirection() || searchedScheduledDateRef.current;
            dispatch(_flyRedux.FlyCreators.searchDetailFlightsRequest(directionFlight, isPagingRequest, text, pageNumber, dateToRequest, filters, shouldIgnoreDirectionAndDateRef.current));
          }
        });
        return function fetchFlight() {
          return _ref3.apply(this, arguments);
        };
      }();
      fetchFlight();
    };
    var searchTabFlight = function searchTabFlight(date, filters) {
      searchFlight({
        isPagingRequest: false,
        date: date,
        pageNumber: initialPageNumber,
        filters: filters
      });
    };
    var searchDetailFlightPaging = function searchDetailFlightPaging() {
      if (onEndReachedCalledDuringMomentum.current || shouldIgnoreDirectionAndDateRef.current) {
        return;
      }
      if (hasLoadMore) {
        searchFlight({
          isPagingRequest: true,
          date: filterDate,
          pageNumber: currentPageNumber + 1
        });
        onEndReachedCalledDuringMomentum.current = true;
        flyLastUpdatedTimeStamp.current = (0, _moment.default)();
      }
    };
    var refreshSearchDetailFlight = function refreshSearchDetailFlight() {
      isRefreshingRef.current = true;
      searchFlight({
        isPagingRequest: false,
        date: filterDate,
        pageNumber: initialPageNumber
      });
    };
    (0, _react.useEffect)(function () {
      var _toast$current2;
      if (!loading) {
        isRefreshingRef.current = false;
      }
      if (!loading && errorPaging) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.show(_feedbackToastProps.DURATION.FOREVER);
        return;
      }
      toast == null || (_toast$current2 = toast.current) == null || _toast$current2.closeNow();
    }, [loading, errorPaging]);
    (0, _react.useEffect)(function () {
      return function () {
        dispatch(_flyRedux.FlyCreators.resetSearchDetailFlights(directionFlight));
      };
    }, []);
    (0, _react.useEffect)(function () {
      if (isFocused) {
        var dateToDisplay = handleDateSelectedByDirection() || filterDate;
        searchTabFlight(dateToDisplay);
      }
    }, [isFocused, keyword]);
    var onFilterFlight = function onFilterFlight(_ref4) {
      var date = _ref4.date,
        filterLocation = _ref4.filterLocation;
      shouldIgnoreDirectionAndDateRef.current = false;
      if (date) {
        filterDateRef.current = date;
        if (directionFlight === _flightProps.FlightDirection.departure) {
          dispatch(_flyRedux.FlyCreators.setFilterDateSearchDeparture(date));
        } else {
          dispatch(_flyRedux.FlyCreators.setFilterDateSearchArrival(date));
        }
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyFilter, (0, _moment.default)(date).format("YYYY-MM-DD")));
      }
      if (filterLocation) {
        filtersLocation.current = filterLocation;
        (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyFilter, `${filterLocation.join("|")}`));
      }
      searchTabFlight(date, filterLocation);
    };
    /** */
    var onReloadData = function onReloadData() {
      searchTabFlight(filterDate);
    };
    var renderFlightList = function renderFlightList(_ref5) {
      var item = _ref5.item,
        index = _ref5.index;
      var isSaved = false;
      if (myTravelFlightsPayload != null && myTravelFlightsPayload.getMyTravelFlightDetails) {
        isSaved = myTravelFlightsPayload.getMyTravelFlightDetails.findIndex(function (savedFlight) {
          return savedFlight.flightNumber === item.flightNumber && savedFlight.scheduledDate === item.scheduledDate && (savedFlight.flightDirection || savedFlight.direction) === (item.flightDirection || item.direction);
        }) >= 0;
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _tabFlight.default.flatListItemStyle,
        children: (0, _jsxRuntime.jsx)(_flightListingCard.default, Object.assign({}, item, {
          isLoggedIn: isLoggedIn,
          onPressed: function onPressed() {
            var flightNumber = (0, _lodash.get)(item, "flightNumber", "");
            trackingSearchResultClick(flightNumber, _searchIndex.SearchIndex.flights, index);
            //@ts-ignore
            navigation.navigate("flightDetails", {
              payload: {
                item: item,
                flightNavigationType: flightNavigationType
              },
              direction: directionFlight
            });
            setShouldIgnoreDirectionAndDate(shouldIgnoreDirectionAndDateRef.current);
          },
          onSaved: function onSaved(isSaved) {
            return onSaveFlight({
              item: item,
              flightNavigationType: flightNavigationType
            }, isSaved);
          },
          isSaved: isSaved,
          itemIndex: index
        }))
      });
    };
    var FlightsEmpty = function FlightsEmpty() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _tabFlight.default.emptyView,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "caption1Regular",
          tx: "search.searchFlights.emptySearchFlightsDescription",
          style: _tabFlight.default.newEmptyTextStyle
        }), (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: function onPress() {
            var _filterFlightRef$curr2;
            return (_filterFlightRef$curr2 = filterFlightRef.current) == null ? undefined : _filterFlightRef$curr2.setShowCalendar(true);
          },
          style: _tabFlight.default.viewAllStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "textLink",
            tx: "search.searchFlights.selectOtherDate"
          })
        })]
      });
    };
    var renderContent = function renderContent() {
      var _flightDetailsPayload3;
      if (isNoConnection) {
        return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          testID: `${COMPONENT_NAME}__ErrorOverlayNoConnection`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorOverlayNoConnection`,
          onReload: onReloadData,
          noInternetOverlayStyle: _tabFlight.default.overlayStyle
        });
      }
      if (flightDetailsPayload != null && flightDetailsPayload.errorFlag && !errorPaging) {
        return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          onReload: onReloadData,
          testID: `${COMPONENT_NAME}__ErrorOverlay`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorOverlay`,
          overlayStyle: _tabFlight.default.overlayStyle,
          variant: _errorOverlay.ErrorOverlayVariant.VARIANTSECTION
        });
      }
      return (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        ListHeaderComponent: !!(flightDetailsPayload != null && (_flightDetailsPayload3 = flightDetailsPayload.data) != null && _flightDetailsPayload3.length) && (0, _jsxRuntime.jsx)(_flightInformationDisclaimer.default, {}),
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: refreshSearchDetailFlight
        }),
        data: (flightDetailsPayload == null ? undefined : flightDetailsPayload.data) || [],
        renderItem: renderFlightList,
        showsVerticalScrollIndicator: false,
        keyExtractor: function keyExtractor(_item, index) {
          return `${_item == null ? undefined : _item.flightNumber} ${_item == null ? undefined : _item.scheduledDate} ${index.toString()}`;
        },
        scrollEnabled: true,
        onEndReachedThreshold: 0.3,
        onEndReached: searchDetailFlightPaging,
        onMomentumScrollBegin: function onMomentumScrollBegin() {
          onEndReachedCalledDuringMomentum.current = false;
        },
        onTouchStart: function onTouchStart() {
          return _reactNative2.Keyboard.dismiss();
        },
        ListFooterComponent: (0, _jsxRuntime.jsx)(FlightsFooter, {
          loading: loading,
          flightDetailsPayload: flightDetailsPayload,
          filterFlightRef: filterFlightRef
        }),
        ListEmptyComponent: loading || !(flightDetailsPayload != null && flightDetailsPayload.data) ? null : (0, _jsxRuntime.jsx)(FlightsEmpty, {})
      });
    };
    var handleDateSelectedByDirection = function handleDateSelectedByDirection() {
      if (directionFlight === _flightProps.FlightDirection.departure) {
        return _store.store.getState().flyReducer.filterDateSearchDeparture;
      }
      return _store.store.getState().flyReducer.filterDateSearchArrival;
    };
    var handleDisplayDate = function handleDisplayDate() {
      var dateToDisplay = handleDateSelectedByDirection() || new Date();
      var scheduledDate = (0, _moment.default)(dateToDisplay).format(_dateTime.DateFormats.YearMonthDay);
      var currentDate = (0, _moment.default)().format(_dateTime.DateFormats.YearMonthDay);
      var isPast = new Date(scheduledDate) < new Date(currentDate);
      if (isPast) {
        scheduledDate = currentDate;
      }
      return (0, _dateTime.dateToFromNow)(scheduledDate);
    };
    var renderNoFlightsScheduledForToday = (0, _react.useMemo)(function () {
      var _flightDetailsPayload4;
      var isFirstFlightDetailItemScheduledForToday = (0, _moment.default)(firstFlightDetailItemScheduledDate).isSame((0, _dateTime.getCurrentDateSingapore)(), "day");
      var shouldRenderNoFlightsScheduledForToday = !!(flightDetailsPayload != null && (_flightDetailsPayload4 = flightDetailsPayload.data) != null && _flightDetailsPayload4.length) && !handleDateSelectedByDirection() && !isFirstFlightDetailItemScheduledForToday;
      if (shouldRenderNoFlightsScheduledForToday) {
        return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            preset: "caption1Regular",
            style: _tabFlight.default.noFlightsScheduledForToday,
            tx: "flightLanding.noFlightsScheduledForToday"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "h4",
            style: _tabFlight.default.dateTitleTextStyle,
            text: (0, _dateTime.dateToFromNow)(firstFlightDetailItemScheduledDate)
          })]
        });
      }
      return null;
    }, [filterDate, flightDetailsPayload == null || (_flightDetailsPayload5 = flightDetailsPayload.data) == null ? undefined : _flightDetailsPayload5.length, firstFlightDetailItemScheduledDate, shouldIgnoreDirectionAndDateRef.current]);
    (0, _react.useEffect)(function () {
      return function () {
        var shouldUpdateIgnoreDirectionAndDateRef = typeof (flightDetailsPayload == null ? undefined : flightDetailsPayload.shouldNavigateToDepartureTab) === "boolean" && !isRefreshingRef.current;
        if (shouldUpdateIgnoreDirectionAndDateRef) {
          shouldIgnoreDirectionAndDateRef.current = false;
        }
      };
    }, [isFocused, keyword, flightDetailsPayload, isRefreshingRef.current]);
    (0, _react.useEffect)(function () {
      var shouldNavigateToDeparture = (flightDetailsPayload == null ? undefined : flightDetailsPayload.shouldNavigateToDepartureTab) && isArrivalTab;
      if (shouldNavigateToDeparture) {
        shouldIgnoreDirectionAndDateRef.current = false;
        navigation.navigate(_.RedirectTab.DepartureTab, {
          isAutoToggleFromArrivial: true
        });
      }
      var shouldUpdateSearchedScheduledDate = !shouldNavigateToDeparture && (flightDetailsPayload == null ? undefined : flightDetailsPayload.length) && isArrivalTab || (flightDetailsPayload == null ? undefined : flightDetailsPayload.length) && !isArrivalTab;
      if (shouldUpdateSearchedScheduledDate) {
        var _flightDetailsPayload6;
        var _firstFlightDetailItem = flightDetailsPayload == null || (_flightDetailsPayload6 = flightDetailsPayload.data) == null ? undefined : _flightDetailsPayload6[0];
        var _firstFlightDetailItemScheduledDate = _firstFlightDetailItem == null ? undefined : _firstFlightDetailItem.scheduledDate;
        if (_firstFlightDetailItemScheduledDate) {
          searchedScheduledDateRef.current = _firstFlightDetailItemScheduledDate;
        }
      }
    }, [flightDetailsPayload]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
      style: _tabFlight.default.parentContainerStyle,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _tabFlight.default.containerFilter,
        children: (0, _jsxRuntime.jsx)(_filterFlight.default, {
          onFilterFlight: onFilterFlight,
          initialDate: filterDateRef.current,
          ref: filterFlightRef,
          initialFilterLocation: filtersLocation == null ? undefined : filtersLocation.current,
          isDateSelected: !!handleDateSelectedByDirection(),
          displayDate: handleDisplayDate(),
          isFromSearch: true
        })
      }), renderNoFlightsScheduledForToday, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _tabFlight.default.containerFlatList,
        children: [isShowMaintenance ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _tabFlight.default.maintenanceErrorContainer,
          children: (0, _jsxRuntime.jsx)(_errorCloud.ErrorCloudComponent, {
            skipStatusbar: true,
            style: {
              backgroundColor: "transparent"
            },
            titleStyle: {
              marginTop: 16
            },
            buttonStyle: {
              width: "auto"
            },
            errorData: errorData,
            onPress: fetchTickerbandMaintanance
          })
        }) : renderContent(), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: loading,
          customStyle: {
            elevation: 0
          }
        })]
      }), (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toast,
        style: _tabFlight.default.feedBackToastStyle,
        textButtonStyle: _tabFlight.default.toastButtonStyle,
        position: "custom",
        textStyle: _tabFlight.default.toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBack,
        text: (0, _translate.translate)("flightLanding.feedBackToastErrorMessage") + (0, _moment.default)(flyLastUpdatedTimeStamp.current).format(_dateTime.DateFormats.flyModuleUpdatedTime),
        testID: `${COMPONENT_NAME}__FeedBackToastErrorMessage`,
        accessibilityLabel: `${COMPONENT_NAME}__FeedBackToastErrorMessage`
      })]
    });
  };
  var _default = exports.default = TabFlightScreen;
