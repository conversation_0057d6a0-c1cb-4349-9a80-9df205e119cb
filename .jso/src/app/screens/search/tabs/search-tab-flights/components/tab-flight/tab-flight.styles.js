  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = _reactNative.StyleSheet.create({
    containerFilter: {
      paddingHorizontal: 24
    },
    containerFlatList: {
      flex: 1,
      marginTop: 24
    },
    emptyTextStyle: {
      marginBottom: 25,
      paddingRight: 20,
      textAlign: "left"
    },
    newEmptyTextStyle: {
      marginBottom: 25,
      textAlign: "left"
    },
    emptyView: {
      justifyContent: "center",
      paddingHorizontal: 21
    },
    errorComponentContainerStyle: {
      flex: 1,
      paddingHorizontal: 24
    },
    errorStyles: {
      marginHorizontal: 0
    },
    feedBackToastStyle: {
      bottom: 80,
      paddingHorizontal: 16,
      width: "100%"
    },
    flatListItemStyle: {
      alignItems: "center",
      paddingBottom: 12
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    },
    parentContainerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    viewAllDepartureFlightViewStyle: {
      alignSelf: "center"
    },
    viewAllStyle: {
      alignSelf: "center"
    },
    maintenanceErrorContainer: {
      marginBottom: 52
    },
    flightsFooter: {
      marginTop: 12,
      marginBottom: 120,
      alignSelf: "center"
    },
    noFlightsScheduledForToday: {
      padding: 24,
      color: _theme.color.palette.almostBlackGrey
    },
    dateTitleTextStyle: {
      marginTop: 8,
      paddingHorizontal: 24
    }
  });
  var _default = exports.default = styles;
