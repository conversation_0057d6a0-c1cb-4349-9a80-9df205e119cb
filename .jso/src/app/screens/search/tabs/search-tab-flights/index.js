  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.RedirectTab = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[7]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[8]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _momentTimezone = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _native = _$$_REQUIRE(_dependencyMap[12]);
  var _envParams = _$$_REQUIRE(_dependencyMap[13]);
  var _confirmPopupSaveFlight = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[15]);
  var _translate = _$$_REQUIRE(_dependencyMap[16]);
  var _scanButtonSearch = _$$_REQUIRE(_dependencyMap[17]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[18]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[22]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[23]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[24]);
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[25]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[27]));
  var _searchRecentSection = _$$_REQUIRE(_dependencyMap[28]);
  var _searchRecentSection2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[29]));
  var _adobe = _$$_REQUIRE(_dependencyMap[30]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[31]);
  var _storage = _$$_REQUIRE(_dependencyMap[32]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[33]);
  var _alertApp = _$$_REQUIRE(_dependencyMap[34]);
  var _alertApp2 = _$$_REQUIRE(_dependencyMap[35]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[36]);
  var _components = _$$_REQUIRE(_dependencyMap[37]);
  var _index = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[38]));
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[39]));
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[40]));
  var _utils = _$$_REQUIRE(_dependencyMap[41]);
  var _addReturnCalendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[42]));
  var _icons = _$$_REQUIRE(_dependencyMap[43]);
  var _useTickerbandMaintanence = _$$_REQUIRE(_dependencyMap[44]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[45]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[46]);
  var _firebase = _$$_REQUIRE(_dependencyMap[47]);
  var _flightDetail = _$$_REQUIRE(_dependencyMap[48]);
  var _saveFlightTraveOptionWrap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[49]));
  var _useModal3 = _$$_REQUIRE(_dependencyMap[50]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[51]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[52]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var RedirectTab = exports.RedirectTab = /*#__PURE__*/function (RedirectTab) {
    RedirectTab["DepartureTab"] = "DepartureSearchTab";
    RedirectTab["ArrivalTab"] = "ArrivalSearchTab";
    return RedirectTab;
  }({});
  var SCREEN_NAME = "SearchFlight__";
  var Tab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var SearchFlightsScreen = function SearchFlightsScreen(_ref) {
    var _keyword$trim, _saveFlightContent, _saveFlightContent2, _saveFlightContent3, _saveFlightContent4, _insertFlightPayload$6, _saveFlightContent5;
    var navigation = _ref.navigation,
      setModule = _ref.setModule;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      sourcePage = _useContext.sourcePage,
      trackingSearchResultClick = _useContext.trackingSearchResultClick;
    var date = new Date();
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isGoToListingFlight = _useState2[0],
      setIsGoToListingFlight = _useState2[1];
    var isFocused = (0, _native.useIsFocused)();
    var _useModal = (0, _useModal3.useModal)("saveConnectingFlightSearch"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useState3 = (0, _react.useState)(undefined),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      shouldIgnoreDirectionAndDate = _useState4[0],
      setShouldIgnoreDirectionAndDate = _useState4[1];
    var toastForRemoveFlight = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var searchFlightsLoading = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.searchFlightsLoading);
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var insertFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.insertFlightPayload);
    var removeFlightPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.removeFlightPayload);
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isNoConnection = _useState6[0],
      setNoConnection = _useState6[1];
    var toastForSavedFlight = (0, _react.useRef)(null);
    var messageCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var alertApp = (0, _react.useRef)(null);
    var msg47 = messageCommonAEM == null ? undefined : messageCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG47";
    });
    var msg58 = messageCommonAEM == null ? undefined : messageCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG58";
    });
    var msg48 = messageCommonAEM == null ? undefined : messageCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG48";
    });
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var _useState7 = (0, _react.useState)(_flightDetail.TravelOption.iAmTravelling),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      selectedTravelOption = _useState8[0],
      setSelectedTravelOption = _useState8[1];
    var _useModal2 = (0, _useModal3.useModal)("saveFlightTravelOptionSearchFlight"),
      isModalVisibleOption = _useModal2.isModalVisible,
      openModalOption = _useModal2.openModal,
      closeModalOption = _useModal2.closeModal;
    var _useState9 = (0, _react.useState)(false),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      loadingSaveFlight = _useState0[0],
      setLoadingSaveFlight = _useState0[1];
    var _useState1 = (0, _react.useState)(null),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      saveFlightPayload = _useState10[0],
      setSaveFlightPayload = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      showCalendarModal = _useState12[0],
      setShowCalendarModal = _useState12[1];
    var connectingFlightPayload = (0, _reactRedux.useSelector)(_flyRedux.FlySelectors.connectingFlightPayload);
    var _useTickerbandMaintan = (0, _useTickerbandMaintanence.useTickerbandMaintanance)(_useTickerbandMaintanence.MaintananceTickerBandType.GLOBAL_FLIGHT_SEARCH),
      isShowMaintenance = _useTickerbandMaintan.isShowMaintenance;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Search_Flights");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Search_Flights", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    var paddingScanBtn = (0, _react.useMemo)(function () {
      return {
        paddingBottom: _reactNative2.Platform.OS === "android" ? 24 : 20,
        bottom: 0,
        elevation: 4,
        backgroundColor: "transparent"
      };
    }, []);
    (0, _react.useEffect)(function () {
      isFocused && setModule(_searchIndex.SearchIndex.flights);
      if (!isFocused) {
        dispatch(_searchRedux.default.sendSearchKeywordCollection(_searchIndex.SearchIndex.flights, sourcePage));
        dispatch(_searchRedux.default.setIsDepartureData(true));
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      var _insertFlightPayload$, _env;
      var timeStamp = new Date().getTime();
      var insertFlightSuccessCondition = (0, _utils.ifOneTrue)([insertFlightPayload == null || (_insertFlightPayload$ = insertFlightPayload.insertFlightData) == null ? undefined : _insertFlightPayload$.success, insertFlightPayload == null ? undefined : insertFlightPayload.recordExist]);
      var addReturnPopupCondition = (0, _utils.ifOneTrue)([(0, _mmkvStorage.getLastSavedFlightTime)() + ((_env = (0, _envParams.env)()) == null ? undefined : _env.FLIGHT_SHOW_POPUP_ADD_RETURN) < timeStamp, (0, _lodash.size)(myTravelFlightsPayload == null ? undefined : myTravelFlightsPayload.getMyTravelFlightDetails) === 1]);
      if (insertFlightSuccessCondition) {
        if ((0, _utils.ifAllTrue)([insertFlightPayload == null ? undefined : insertFlightPayload.isInsertSuccessfully, isFocused])) {
          var _insertFlightPayload$2;
          (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
            data: insertFlightPayload,
            isSuccess: true,
            tag: _adobe.AdobeTagName.CAppSaveFlight,
            flyProfile: "flying",
            pageName: _adobe.AdobeTagName.CAppSearchResult,
            isSaveFlight: true
          });
          if (addReturnPopupCondition && insertFlightPayload != null && (_insertFlightPayload$2 = insertFlightPayload.flightData) != null && _insertFlightPayload$2.isPassenger) {
            openModal();
            (0, _mmkvStorage.setLastSavedFlightTime)(0);
          } else {
            var _toastForRemoveFlight, _toastForSavedFlight$;
            closeModalOption();
            dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
            toastForRemoveFlight == null || (_toastForRemoveFlight = toastForRemoveFlight.current) == null || _toastForRemoveFlight.closeNow();
            toastForSavedFlight == null || (_toastForSavedFlight$ = toastForSavedFlight.current) == null || _toastForSavedFlight$.show(_feedbackToastProps.DURATION.LENGTH_LONG);
            (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
          }
        }
      }
    }, [insertFlightPayload]);
    (0, _react.useEffect)(function () {
      if (removeFlightPayload != null && removeFlightPayload.isRemovedSuccessFully) {
        var _toastForSavedFlight$2, _toastForRemoveFlight2;
        (0, _screenHelper.putFlightInfoToAdobeAnalyticAfterSaveUnSave)({
          data: removeFlightPayload,
          isSuccess: true,
          tag: _adobe.AdobeTagName.CAppRemoveFlight,
          flyProfile: "flying",
          pageName: _adobe.AdobeTagName.CAppSearchResult,
          isSaveFlight: false
        });
        toastForSavedFlight == null || (_toastForSavedFlight$2 = toastForSavedFlight.current) == null || _toastForSavedFlight$2.closeNow();
        toastForRemoveFlight == null || (_toastForRemoveFlight2 = toastForRemoveFlight.current) == null || _toastForRemoveFlight2.show(_feedbackToastProps.DURATION.LENGTH_SHORT);
        dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      }
    }, [removeFlightPayload]);
    var handleMessage48 = function handleMessage48(message, number, place) {
      if (message) {
        return message.replace("<Flight No.>", number).replace("<country>", place);
      }
      return message;
    };
    var onRemoveFlight = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* (payload) {
        var item = payload == null ? undefined : payload.item;
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: item == null ? undefined : item.flightNumber,
          flightScheduledDate: item == null ? undefined : item.scheduledDate,
          flightDirection: item == null ? undefined : item.direction
        };
        _reactNative2.Alert.alert((msg48 == null ? undefined : msg48.title) || (0, _translate.translate)("flightLanding.areYouSure"), msg48 != null && msg48.message ? handleMessage48(msg48 == null ? undefined : msg48.message, item == null ? undefined : item.flightNumber, item == null ? undefined : item.destinationPlace) : `${(0, _translate.translate)("flightLanding.removeMessage1")} ${item == null ? undefined : item.flightNumber} ${(0, _translate.translate)("flightLanding.to")} ${item == null ? undefined : item.destinationPlace} ${(0, _translate.translate)("flightLanding.removeMessage2")}`, [{
          text: (msg48 == null ? undefined : msg48.firstButton) || (0, _translate.translate)("flightLanding.cancel")
        }, {
          text: (msg48 == null ? undefined : msg48.secondButton) || (0, _translate.translate)("flightLanding.remove"),
          style: "cancel",
          onPress: function onPress() {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyRemoveFlight, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyRemoveFlight, "1"));
            var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-search-unsave`);
            dtAction.reportStringValue("flight-search-unsave-press-flightNumber", `${item == null ? undefined : item.flightNumber}`);
            dtAction.reportStringValue("flight-search-unsave-press-scheduledDate", `${item == null ? undefined : item.scheduledDate}`);
            dtAction.reportStringValue("flight-search-unsave-press-direction", `${item == null ? undefined : item.direction}`);
            dispatch(_mytravelRedux.MytravelCreators.flyMyTravelRemoveFlightRequest(data, payload));
            dtAction.leaveAction();
          }
        }]);
      });
      return function onRemoveFlight(_x) {
        return _ref2.apply(this, arguments);
      };
    }();
    var handleMessage58 = function handleMessage58(message, flyItem) {
      if (message) {
        var _flyItem$flightStatus, _status;
        var status = flyItem == null || (_flyItem$flightStatus = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus.toLowerCase();
        if ((_status = status) != null && _status.includes("cancelled")) {
          status = `been ${status}`;
        }
        return message.replace("<Flight No.>", flyItem == null ? undefined : flyItem.flightNumber).replace("<departed/landed/been cancelled>", status);
      }
      return message;
    };
    var notAbleToSaveAlert = function notAbleToSaveAlert(flyItem) {
      var _flyItem$flightStatus2, _alertApp$current;
      var temp = flyItem == null || (_flyItem$flightStatus2 = flyItem.flightStatus) == null ? undefined : _flyItem$flightStatus2.split(" ");
      var status = (temp == null ? undefined : temp.length) > 0 ? temp[0] : "";
      var message = handleMessage58(msg58 == null ? undefined : msg58.message, flyItem) || `${(0, _translate.translate)("flightLanding.flight")} ${flyItem == null ? undefined : flyItem.flightNumber} ${(0, _translate.translate)("flightLanding.has")} ${status} ${(0, _translate.translate)("flightLanding.notSaveMessage")}`;
      alertApp == null || (_alertApp$current = alertApp.current) == null || _alertApp$current.show({
        title: (msg58 == null ? undefined : msg58.title) || (0, _translate.translate)("flightLanding.alert"),
        description: message,
        labelAccept: (msg58 == null ? undefined : msg58.firstButton) || (0, _translate.translate)("flightLanding.okay"),
        onAccept: function onAccept() {
          return null;
        },
        type: _alertApp2.AlertTypes.ALERT
      });
    };
    var checkFlightCanSave = function checkFlightCanSave(statusTag, item) {
      var status = statusTag == null ? undefined : statusTag.toLowerCase();
      var priorityTime = (item == null ? undefined : item.actualTimestamp) || (item == null ? undefined : item.estimatedTimestamp) || `${item == null ? undefined : item.flightDate} ${item == null ? undefined : item.timeOfFlight}`;
      var currentTimeToUTC = (0, _momentTimezone.default)().tz("Asia/Singapore");
      var flightTime = (0, _momentTimezone.default)(priorityTime, "YYYY-MM-DD HH:mm", "Asia/Singapore");
      switch (true) {
        case /departed/gim.test(status):
        case /cancelled/gim.test(status):
        case /landed/gim.test(status) && (0, _momentTimezone.default)(flightTime).add(1, "hours").format("YYYY-MM-DD HH:mm") < currentTimeToUTC.format("YYYY-MM-DD HH:mm"):
          return false;
        default:
          return true;
      }
    };
    var getFlightDirection = (0, _react.useMemo)(function () {
      var _saveFlightPayload$it;
      if ((0, _lodash.isEmpty)(saveFlightPayload)) return _flightProps.FlightDirection.departure;
      return saveFlightPayload == null || (_saveFlightPayload$it = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it.direction;
    }, [saveFlightPayload]);
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(saveFlightPayload)) {
        var _saveFlightPayload$it2;
        setSelectedTravelOption((saveFlightPayload == null || (_saveFlightPayload$it2 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it2.direction) === _flightProps.FlightDirection.departure ? _flightDetail.TravelOption.iAmTravelling : _flightDetail.TravelOption.iAmPicking);
      }
    }, [saveFlightPayload]);
    (0, _react.useEffect)(function () {
      if (insertFlightPayload != null && insertFlightPayload.errorFlag) {
        setLoadingSaveFlight(false);
        closeModalOption();
      }
    }, [insertFlightPayload]);
    (0, _react.useEffect)(function () {
      if (!keyword) {
        setShouldIgnoreDirectionAndDate(undefined);
      }
    }, [keyword]);
    var onSaveFlight = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (payload, isSaved) {
        var _payload$item;
        if (isSaved && isLoggedIn) {
          return onRemoveFlight(payload);
        }
        if (!checkFlightCanSave(payload == null || (_payload$item = payload.item) == null ? undefined : _payload$item.flightStatus, payload == null ? undefined : payload.item)) {
          return notAbleToSaveAlert(payload == null ? undefined : payload.item);
        }
        setSaveFlightPayload(payload);
        if (isLoggedIn) {
          openModalOption();
        } else {
          navigation.navigate(_constants.NavigationConstants.authScreen, {
            sourceSystem: _constants.SOURCE_SYSTEM.FLIGHTS,
            callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
              openModalOption();
            },
            callBackAfterLoginCancel: function callBackAfterLoginCancel() {
              return null;
            }
          });
        }
      });
      return function onSaveFlight(_x2, _x3) {
        return _ref3.apply(this, arguments);
      };
    }();
    var savedFlightOnPress = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _saveFlightPayload$it3, _saveFlightPayload$it4, _saveFlightPayload$it5;
        setLoadingSaveFlight(true);
        var data = {
          enterpriseUserId: profilePayload == null ? undefined : profilePayload.email,
          countryOfResidence: profilePayload == null ? undefined : profilePayload.residentialCountry,
          flightNumber: saveFlightPayload == null || (_saveFlightPayload$it3 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it3.flightNumber,
          flightScheduledDate: saveFlightPayload == null || (_saveFlightPayload$it4 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it4.scheduledDate,
          flightDirection: saveFlightPayload == null || (_saveFlightPayload$it5 = saveFlightPayload.item) == null ? undefined : _saveFlightPayload$it5.direction,
          // check param
          flightPax: selectedTravelOption === _flightDetail.TravelOption.iAmTravelling
        };
        if (isLoggedIn) {
          var dtAction = (0, _firebase.dtManualActionEvent)(`${_firebase.FE_LOG_PREFIX}App__flight-search-save`);
          dtAction.reportStringValue("flight-search-save-press-flightNumber", `${data.flightNumber}`);
          dtAction.reportStringValue("flight-search-save-press-flightScheduledDate", `${data.flightScheduledDate}`);
          dtAction.reportStringValue("flight-search-save-press-flightDirection", `${data.flightDirection}`);
          dtAction.reportStringValue("flight-search-save-press-isPassenger", String(data == null ? undefined : data.flightPax));
          dispatch(_mytravelRedux.MytravelCreators.flyMyTravelInsertFlightRequest(data, saveFlightPayload));
          dtAction.leaveAction();
        } else {
          navigation.navigate(_constants.NavigationConstants.authScreen);
        }
      });
      return function savedFlightOnPress() {
        return _ref4.apply(this, arguments);
      };
    }();
    var isRecentSearch = !keyword || (keyword == null || (_keyword$trim = keyword.trim()) == null ? undefined : _keyword$trim.length) < 2;
    var renderSearchDetailFlightStyle = Object.assign({}, isRecentSearch ? {
      opacity: 0
    } : null);
    var renderTabNavigation = function renderTabNavigation() {
      var arrivalTabScreen = (0, _jsxRuntime.jsx)(Tab.Screen, {
        options: {
          lazy: true,
          title: (0, _translate.translate)("flightLanding.arrivalTabTitle"),
          tabBarIcon: _icons.FlightArrivalIcon
        },
        name: RedirectTab.ArrivalTab,
        children: function children() {
          return (0, _jsxRuntime.jsx)(_components.TabFlightScreen, {
            directionFlight: _flightProps.FlightDirection.arrival,
            onSaveFlight: onSaveFlight,
            trackingSearchResultClick: trackingSearchResultClick,
            flightNavigationType: _flightProps.FlightNavigationType.ArrivalSearch,
            setTabNoConnection: function setTabNoConnection(status) {
              return setNoConnection(status);
            },
            shouldIgnoreDirectionAndDate: shouldIgnoreDirectionAndDate,
            setShouldIgnoreDirectionAndDate: setShouldIgnoreDirectionAndDate
          });
        }
      });
      var departureTabScreen = (0, _jsxRuntime.jsx)(Tab.Screen, {
        options: {
          lazy: true,
          title: (0, _translate.translate)("flightLanding.departureTabTitle"),
          tabBarIcon: _icons.FlightDepartureIcon
        },
        name: RedirectTab.DepartureTab,
        children: function children() {
          return (0, _jsxRuntime.jsx)(_components.TabFlightScreen, {
            directionFlight: _flightProps.FlightDirection.departure,
            onSaveFlight: onSaveFlight,
            trackingSearchResultClick: trackingSearchResultClick,
            flightNavigationType: _flightProps.FlightNavigationType.DepartureSearch,
            setTabNoConnection: function setTabNoConnection(status) {
              return setNoConnection(status);
            },
            shouldIgnoreDirectionAndDate: shouldIgnoreDirectionAndDate,
            setShouldIgnoreDirectionAndDate: setShouldIgnoreDirectionAndDate
          });
        }
      });
      return (0, _jsxRuntime.jsx)(Tab.Navigator, {
        tabBar: function tabBar(props) {
          return (0, _jsxRuntime.jsx)(_navigationUtilities.TopTabNavBarFlightListing, {
            props: Object.assign({}, props),
            topTabLabelsPresets: "tabsSmall",
            topTabParentStyle: _index.default.topTabParentStyle,
            topTabTouchableOpacityStyle: _index.default.topTabTouchableOpacityStyle,
            isCenter: false
          });
        },
        initialRouteName: RedirectTab.ArrivalTab,
        children: [arrivalTabScreen, departureTabScreen]
      });
    };
    var renderSearchDetailFlight = function renderSearchDetailFlight() {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: Object.assign({}, _index.default.sectionFlightDetailStyle, renderSearchDetailFlightStyle),
          children: renderTabNavigation()
        })
      });
    };
    var showFlightAddedFeedBackToastMessage = function showFlightAddedFeedBackToastMessage() {
      return (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForSavedFlight,
        style: _index.default.feedBackToastStyle,
        textButtonStyle: _index.default.toastButtonStyle,
        position: "bottom",
        textStyle: _index.default.toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.toastFlightSaved"),
        onCallback: function onCallback() {
          return dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
        }
      });
    };
    var recentSearchView = function recentSearchView() {
      var _popularSearchKeyword;
      return (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        showsHorizontalScrollIndicator: false,
        showsVerticalScrollIndicator: false,
        keyboardDismissMode: "on-drag",
        keyboardShouldPersistTaps: "always",
        children: [(0, _jsxRuntime.jsx)(_searchRecentSection2.default, {
          type: _searchRecentSection.SearchRecentSectionType.default
        }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
          data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
            return (item == null ? undefined : item.searchCategoryTab) === "flights";
          })) == null ? undefined : _popularSearchKeyword.popularKeywords,
          searchIndex: _searchIndex.SearchIndex.flights
        })]
      });
    };
    var onClosedSheet = function onClosedSheet() {
      if (!loadingSaveFlight) {
        closeModalOption();
      }
    };
    var travelOptionTapped = function travelOptionTapped(option) {
      setSelectedTravelOption(option);
    };
    var savedFlightTravelOptionsOnModalHide = function savedFlightTravelOptionsOnModalHide() {
      setSelectedTravelOption(_flightDetail.TravelOption.iAmTravelling);
    };
    var saveFlightContent = function saveFlightContent() {
      var _insertFlightPayload$3;
      var result = {
        title: msg47 == null ? undefined : msg47.title,
        messageText: (msg47 == null ? undefined : msg47.message) || (0, _translate.translate)("flightDetails.popupConfirmSaveFlight.message"),
        textButtonConfirm: msg47 == null ? undefined : msg47.firstButton,
        textButtonCancel: msg47 == null ? undefined : msg47.secondButton,
        textButtonConnection: ""
      };
      result.title = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.title");
      result.textButtonConnection = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.addConnectingFlightButton");
      result.textButtonConfirm = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.addReturnFlightButton");
      result.textButtonCancel = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.cancelButton");
      if ((insertFlightPayload == null || (_insertFlightPayload$3 = insertFlightPayload.flightData) == null || (_insertFlightPayload$3 = _insertFlightPayload$3.item) == null ? undefined : _insertFlightPayload$3.direction) === _flightProps.FlightDirection.arrival) {
        result.messageText = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.arrivalMessage");
      } else {
        result.messageText = (0, _translate.translate)("flightDetails.newPopupConfirmSaveFlight.departureMessage");
      }
      return result;
    };
    var handleModalConfirmSaveFlightHide = function handleModalConfirmSaveFlightHide() {
      var timeStamp = new Date().getTime();
      (0, _mmkvStorage.setLastSavedFlightTime)(timeStamp);
      if (isGoToListingFlight) {
        handleGoToListingFlight();
      } else {
        handleNotGoToListingFlight();
      }
    };
    var handleGoToListingFlight = function handleGoToListingFlight() {
      setShowCalendarModal(true);
      setIsGoToListingFlight(false);
    };
    var handleNotGoToListingFlight = function handleNotGoToListingFlight() {
      var _toastForSavedFlight$3;
      toastForSavedFlight == null || (_toastForSavedFlight$3 = toastForSavedFlight.current) == null || _toastForSavedFlight$3.show(_feedbackToastProps.DURATION.LENGTH_LONG);
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
    };
    var handleConnectingFlightOnPress = function handleConnectingFlightOnPress() {
      var _insertFlightPayload$4, _insertFlightPayload$5;
      var connectingFlight = {
        isConnecting: true,
        flightConnecting: Object.assign({}, insertFlightPayload == null || (_insertFlightPayload$4 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$4.item, {
          isPassenger: insertFlightPayload == null || (_insertFlightPayload$5 = insertFlightPayload.flightData) == null ? undefined : _insertFlightPayload$5.isPassenger
        })
      };
      setIsGoToListingFlight(true);
      closeModal();
      dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlight));
    };
    return (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
      onPress: _reactNative2.Keyboard.dismiss,
      style: _index.default.containerStyle,
      children: [isRecentSearch && recentSearchView(), keyword && isFocused && renderSearchDetailFlight(), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: searchFlightsLoading,
        onPressed: function onPressed() {
          return _reactNative2.Keyboard.dismiss();
        }
      }), (0, _jsxRuntime.jsx)(_saveFlightTraveOptionWrap.default, {
        onModalHide: savedFlightTravelOptionsOnModalHide,
        visible: isModalVisibleOption,
        onClosed: onClosedSheet,
        loadingSaveFlight: loadingSaveFlight,
        onBackPressed: onClosedSheet,
        selectedOption: selectedTravelOption,
        savedFlightOnPress: savedFlightOnPress,
        onPress: function onPress(option) {
          return travelOptionTapped(option);
        },
        flightDirection: getFlightDirection
      }), (0, _jsxRuntime.jsx)(_confirmPopupSaveFlight.default, {
        imageUrl: (0, _mediaHelper.handleImageUrl)(msg47 == null ? undefined : msg47.icon),
        visible: isModalVisible && isFocused,
        title: (_saveFlightContent = saveFlightContent()) == null ? undefined : _saveFlightContent.title,
        messageText: (_saveFlightContent2 = saveFlightContent()) == null ? undefined : _saveFlightContent2.messageText,
        onClose: function onClose() {
          closeModal();
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        },
        onButtonPressed: function onButtonPressed() {
          closeModal();
          setIsGoToListingFlight(true);
        },
        textButtonConfirm: (_saveFlightContent3 = saveFlightContent()) == null ? undefined : _saveFlightContent3.textButtonConfirm,
        textButtonCancel: (_saveFlightContent4 = saveFlightContent()) == null ? undefined : _saveFlightContent4.textButtonCancel,
        onModalHide: handleModalConfirmSaveFlightHide,
        isShowButtonConnection: (insertFlightPayload == null || (_insertFlightPayload$6 = insertFlightPayload.flightData) == null || (_insertFlightPayload$6 = _insertFlightPayload$6.item) == null ? undefined : _insertFlightPayload$6.direction) === _flightProps.FlightDirection.arrival,
        onButtonConnectionPressed: handleConnectingFlightOnPress,
        textButtonConnection: (_saveFlightContent5 = saveFlightContent()) == null ? undefined : _saveFlightContent5.textButtonConnection,
        disableCloseButton: true,
        openPendingModal: true
      }), (0, _jsxRuntime.jsx)(_addReturnCalendar.default, {
        isVisible: showCalendarModal,
        filterDate: (0, _momentTimezone.default)((0, _lodash.get)(insertFlightPayload, "flightData.item.displayTimestamp")).format("YYYY-MM-DD") || (0, _momentTimezone.default)().format("YYYY-MM-DD"),
        initialMinDate: (0, _momentTimezone.default)((0, _lodash.get)(insertFlightPayload, "flightData.item.displayTimestamp")).format("YYYY-MM-DD") || (0, _momentTimezone.default)().format("YYYY-MM-DD"),
        onClosedCalendarModal: function onClosedCalendarModal() {
          var _toastForSavedFlight$4;
          toastForSavedFlight == null || (_toastForSavedFlight$4 = toastForSavedFlight.current) == null || _toastForSavedFlight$4.show(_feedbackToastProps.DURATION.LENGTH_LONG);
          setShowCalendarModal(false);
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          var connectingFlight = {
            isConnecting: false,
            flightConnecting: null
          };
          dispatch(_flyRedux.FlyCreators.setConnectingFlightPayload(connectingFlight));
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        },
        onDateSelected: function onDateSelected(dateString) {
          var direction = (0, _lodash.get)(insertFlightPayload, "flightData.item.direction", "DEP");
          var country = (0, _lodash.get)(insertFlightPayload, "flightData.item.country", "");
          var dateFormat = (0, _momentTimezone.default)(dateString).format("YYYY-MM-DD");
          setShowCalendarModal(false);
          dispatch(_flyRedux.FlyCreators.setFlightSearchDate(dateFormat));
          navigation.navigate("flightResultLandingScreen", {
            screen: direction === _flightProps.FlightDirection.departure ? _flightProps.FlightDirection.arrival : _flightProps.FlightDirection.departure,
            sourcePage: _adobe.AdobeTagName.CAppFlyFlightDetail,
            selectedDate: dateFormat,
            country: connectingFlightPayload.isConnecting ? "Singapore" : country
          });
          dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
          (0, _storage.save)(_storageKey.StorageKey.isSaveFlightTriggered, true);
        },
        testID: `${SCREEN_NAME}__AddReturnCalendar`,
        accessibilityLabel: `${SCREEN_NAME}__AddReturnCalendar`
      }), (0, _jsxRuntime.jsx)(_feedbackToast.default, {
        ref: toastForRemoveFlight,
        style: _index.default.feedBackToastStyle,
        textButtonStyle: _index.default.toastButtonStyle,
        position: "custom",
        positionValue: {
          bottom: 8
        },
        textStyle: _index.default.toastTextStyle,
        type: _feedbackToastProps.FeedBackToastType.smallFeedBack,
        text: (0, _translate.translate)("flyLanding.removeFlightNew")
      }), !isNoConnection && ((0, _utils.ifAllTrue)([isShowMaintenance, !isRecentSearch]) ? null : (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: [_index.default.scanView, paddingScanBtn],
        children: (0, _jsxRuntime.jsx)(_scanButtonSearch.ScanButtonSearch, {
          onButtonPressed: function onButtonPressed() {
            (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, "1"));
          }
        })
      })), showFlightAddedFeedBackToastMessage(), (0, _jsxRuntime.jsx)(_alertApp.AlertApp, {
        ref: alertApp
      })]
    });
  };
  var _default = exports.default = SearchFlightsScreen;
