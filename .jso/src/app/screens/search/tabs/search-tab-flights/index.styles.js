  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = _reactNative.StyleSheet.create({
    containerRecentSearch: {
      paddingHorizontal: 24
    },
    containerStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    dateTextStyle: {
      height: 24,
      paddingHorizontal: 24,
      textTransform: "uppercase"
    },
    emptyOrderStyle: {
      alignItems: "center",
      height: "100%",
      justifyContent: "center",
      paddingHorizontal: 24
    },
    feedBackToastStyle: {
      bottom: 80,
      paddingHorizontal: 16,
      width: "100%"
    },
    flatListContentContainerStyle: {},
    flatListItemStyle: {
      alignItems: "center",
      paddingBottom: 12
    },
    flatListStyle: {
      marginTop: 16
    },
    itemContainerStyle: {
      paddingBottom: 5
    },
    optionContainer: {
      marginTop: 12,
      paddingHorizontal: 24
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    },
    recentSearchContainer: {
      paddingHorizontal: 24
    },
    scanView: {
      left: 0,
      position: "absolute",
      right: 0
    },
    searchFlightContainerStyle: {
      marginBottom: 94
    },
    sectionFlightDetailStyle: {
      flex: 1,
      paddingTop: 24
    },
    sectionHeader: {
      marginTop: 40,
      paddingHorizontal: 24,
      textTransform: "uppercase"
    },
    sectionStyle: {
      flex: 1
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    topTabParentStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      height: 30,
      paddingHorizontal: 24
    },
    topTabTouchableOpacityStyle: {
      alignItems: "center",
      marginEnd: 24
    },
    viewAllStyle: {
      alignSelf: "center",
      marginBottom: 12,
      marginTop: 8
    }
  });
  var _default = exports.default = styles;
