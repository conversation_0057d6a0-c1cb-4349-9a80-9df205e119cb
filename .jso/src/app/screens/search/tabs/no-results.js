  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  var NoResultsDisplay = function NoResultsDisplay(props) {
    var _props$title = props.title,
      title = _props$title === undefined ? "" : _props$title,
      _props$message = props.message,
      message = _props$message === undefined ? "" : _props$message;
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.mainNoResultsContainer,
      children: [(0, _jsxRuntime.jsx)(_icons.SearchNoResultsIconSmall, {}), (0, _jsxRuntime.jsx)(_text.Text, {
        numberOfLines: 1,
        text: title,
        preset: "bodyTextBold",
        style: styles.titleTextStyle
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: message,
        preset: "caption1Regular",
        style: styles.bodyTextStyle
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    bodyTextStyle: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    },
    mainNoResultsContainer: {
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: 24,
      paddingTop: 40
    },
    titleTextStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginVertical: 8
    }
  });
  var _default = exports.default = NoResultsDisplay;
