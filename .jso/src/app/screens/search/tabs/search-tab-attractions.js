  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.NavigationType = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _i18n = _$$_REQUIRE(_dependencyMap[9]);
  var _searchScreen = _$$_REQUIRE(_dependencyMap[10]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[11]);
  var _error = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _feedbackToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _feedbackToastProps = _$$_REQUIRE(_dependencyMap[15]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[16]);
  var _searchRecentSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _searchRecentSection2 = _$$_REQUIRE(_dependencyMap[18]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _native = _$$_REQUIRE(_dependencyMap[20]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[22]);
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _searchScreenContext = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _attractionsFacilitiesServices = _$$_REQUIRE(_dependencyMap[26]);
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[27]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[28]);
  var _text = _$$_REQUIRE(_dependencyMap[29]);
  var _theme = _$$_REQUIRE(_dependencyMap[30]);
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _noResults = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _searchYouMayAlsoLike = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[34]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[35]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[36]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchTabStyle = _reactNative2.StyleSheet.create({
    contentContainerStyle: {
      paddingBottom: 20
    },
    flatListStyle: {
      paddingBottom: 20,
      paddingHorizontal: 16,
      width: "100%"
    },
    titleContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      marginBottom: 4,
      marginHorizontal: 8,
      paddingTop: 40
    },
    titleStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 1.08,
      lineHeight: 24
    }),
    ymalContainer: {
      marginTop: 40
    },
    ymalContainerFooter: {
      marginTop: 40,
      paddingHorizontal: 8
    },
    ymalEmptyStyles: {
      height: 0
    },
    ymalFlatListStyles: {
      paddingBottom: 0
    }
  });
  var NavigationType = exports.NavigationType = /*#__PURE__*/function (NavigationType) {
    NavigationType["inapp"] = "in-app";
    NavigationType["external"] = "external";
    NavigationType["deepLink"] = "deep-link";
    return NavigationType;
  }({});
  var COMPONENT_NAME = "SearchTabAttractions__";
  var SearchTabAttractions = function SearchTabAttractions(_ref) {
    var _searchKeyword$trim;
    var setModule = _ref.setModule;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var toast = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      sourcePage = _useContext.sourcePage,
      isPressSearchKey = _useContext.isPressSearchKey,
      setIsPressSearchKey = _useContext.setIsPressSearchKey,
      trackingSearchResultClick = _useContext.trackingSearchResultClick;
    var searchResultPayload = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.attractionsSearchResults);
    var isResultFetching = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.attractionsSearchResultsFetching);
    var isPaginating = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.attractionsSearchResultsPaginating);
    var searchKeyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var totalResultItems = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.attractionsSearchTotalItems);
    var currentPage = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.attractionsSearchCurrentPage);
    var isAPIError = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.attractionsSearchError);
    var isFocused = (0, _native.useIsFocused)();
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getErrorsCommon);
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingLocal = _useState4[0],
      setLoadingLocal = _useState4[1];
    var ehr12 = dataCommonAEM == null ? undefined : dataCommonAEM.find(function (e) {
      return (e == null ? undefined : e.code) === "EHR12";
    });
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ATTRACTION_SEARCH_TAB"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var checkInternetAndSearch = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          setNoConnection(false);
          if (isFocused) {
            setModule(_searchIndex.SearchIndex.attractions);
          }
          if ((searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2 && isFocused) {
            dispatch(_searchRedux.default.attractionsSearchRequest({
              keyword: searchKeyword,
              pageNumber: 1,
              pageSize: 16
            }));
          }
        } else {
          setLoadingLocal(true);
          setTimeout(function () {
            setLoadingLocal(false);
            setNoConnection(true);
          }, 1000);
        }
      });
      return function checkInternetAndSearch() {
        return _ref2.apply(this, arguments);
      };
    }();
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Search_Attractions");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Search_Attractions", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!isFocused) {
        dispatch(_searchRedux.default.sendSearchKeywordCollection(_searchIndex.SearchIndex.attractions, sourcePage));
        dispatch(_searchRedux.default.resetListYouMayAlsoLikeData());
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      checkInternetAndSearch();
      return function () {
        isFocused && dispatch(_searchRedux.default.attractionsSearchReset());
      };
    }, [searchKeyword, isFocused]);
    (0, _react.useEffect)(function () {
      if (isPressSearchKey) {
        checkInternetAndSearch();
        setIsPressSearchKey(false);
      }
    }, [isPressSearchKey]);
    (0, _react.useEffect)(function () {
      if (isAPIError && (searchResultPayload == null ? undefined : searchResultPayload.length) < totalResultItems) {
        var _toast$current;
        toast == null || (_toast$current = toast.current) == null || _toast$current.show(_constants.TOAST_MESSAGE_DURATION);
      }
    }, [isAPIError]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, [isFocused]);
    var loadMoreData = function loadMoreData() {
      if ((searchResultPayload == null ? undefined : searchResultPayload.length) < totalResultItems) {
        toast.current.close(1);
        isFocused && dispatch(_searchRedux.default.attractionsSearchPaginate({
          keyword: searchKeyword,
          pageNumber: currentPage + 1,
          pageSize: 16
        }));
      }
    };
    var reloadData = function reloadData() {
      isFocused && dispatch(_searchRedux.default.attractionsSearchReset());
      isFocused && dispatch(_searchRedux.default.attractionsSearchRequest({
        keyword: searchKeyword,
        pageNumber: 1,
        pageSize: 16
      }));
    };
    var handleYmalItemOnPress = function handleYmalItemOnPress(item) {
      var contextData = {
        tabName: (0, _i18n.translate)("search.tabTitles.attractions"),
        keyword: searchKeyword == null ? undefined : searchKeyword.trim(),
        ymalTitle: item.title
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppSearchYAML, contextData);
      var navigation = item.navigation;
      if ((0, _lodash.isEmpty)(navigation)) {
        return;
      }
      handleNavigation(navigation == null ? undefined : navigation.type, navigation == null ? undefined : navigation.value, (navigation == null ? undefined : navigation.redirect) || {});
    };
    var showErrorToastMessage = function showErrorToastMessage() {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _searchScreen.styles.toastContainer,
        children: (0, _jsxRuntime.jsx)(_feedbackToast.default, {
          ref: toast,
          style: _searchScreen.styles.feedBackToastStyle,
          textButtonStyle: _searchScreen.styles.toastButtonStyle,
          textStyle: _searchScreen.styles.toastTextStyle,
          type: _feedbackToastProps.FeedBackToastType.fullWidthFeedBackWithCTA,
          text: (0, _i18n.translate)("search.attractionsPaginationError"),
          buttonText: (0, _i18n.translate)("search.retry"),
          onPress: loadMoreData,
          testID: `${COMPONENT_NAME}FeedBackToastError`,
          accessibilityLabel: `${COMPONENT_NAME}FeedBackToastError`
        })
      });
    };
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: false,
          headerBackgroundColor: "transparent",
          hideScreenHeader: false,
          visible: true,
          testID: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
          accessibilityLabel: `${COMPONENT_NAME}ErrorOverlayNoConnection`,
          onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
              isConnected = _yield$NetInfo$fetch3.isConnected;
            if (isConnected) {
              setNoConnection(false);
              reloadData();
            }
          }),
          noInternetOverlayStyle: _searchScreen.styles.overlayStyle
        })
      });
    }
    if (!searchKeyword || (searchKeyword == null || (_searchKeyword$trim = searchKeyword.trim()) == null ? undefined : _searchKeyword$trim.length) < 2) {
      var _popularSearchKeyword;
      return (0, _jsxRuntime.jsx)(_reactNative.Pressable, {
        onPress: _reactNative2.Keyboard.dismiss,
        style: _searchScreen.styles.container,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "always",
          children: [(0, _jsxRuntime.jsx)(_searchRecentSection.default, {
            type: _searchRecentSection2.SearchRecentSectionType.default
          }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
            data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
              return (item == null ? undefined : item.searchCategoryTab) === "attractions";
            })) == null ? undefined : _popularSearchKeyword.popularKeywords,
            searchIndex: _searchIndex.SearchIndex.attractions
          })]
        })
      });
    }
    if (!isResultFetching && Array.isArray(searchResultPayload) && (searchResultPayload == null ? undefined : searchResultPayload.length) === 0 && (searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2) {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPress: function onPress() {
          return _reactNative2.Keyboard.dismiss();
        },
        children: (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsVerticalScrollIndicator: false,
          children: [(0, _jsxRuntime.jsx)(_noResults.default, {
            title: ehr12 == null ? undefined : ehr12.header,
            message: ehr12 == null ? undefined : ehr12.subHeader
          }), (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
            containerStyles: SearchTabStyle.ymalContainer,
            flatListStyles: SearchTabStyle.ymalFlatListStyles,
            emptyScreenStyles: SearchTabStyle.ymalEmptyStyles,
            itemOnPress: handleYmalItemOnPress
          })]
        })
      });
    }
    var onPress = function onPress(item, index) {
      if (item != null && item.navigation) {
        var _item$navigation2;
        trackingSearchResultClick(item == null ? undefined : item.title, _searchIndex.SearchIndex.attractions, index);
        var _item$navigation = item == null ? undefined : item.navigation,
          type = _item$navigation.type,
          value = _item$navigation.value;
        handleNavigation(type, value, (item == null || (_item$navigation2 = item.navigation) == null ? undefined : _item$navigation2.redirect) || {});
      }
    };
    var headerComponent = function headerComponent() {
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: SearchTabStyle.titleContainer,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: SearchTabStyle.titleStyle,
            text: (0, _i18n.translate)("search.tabTitles.attractions")
          })
        })
      });
    };
    var footerComponent = function footerComponent() {
      return (0, _jsxRuntime.jsx)(_searchYouMayAlsoLike.default, {
        containerStyles: SearchTabStyle.ymalContainerFooter,
        flatListStyles: SearchTabStyle.ymalFlatListStyles,
        emptyScreenStyles: SearchTabStyle.ymalEmptyStyles,
        itemOnPress: handleYmalItemOnPress
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return _reactNative2.Keyboard.dismiss();
      },
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _searchScreen.styles.container,
        children: [isAPIError && totalResultItems === null ? (0, _jsxRuntime.jsx)(_error.ErrorScreen, {
          onReload: function onReload() {
            return reloadData();
          },
          testID: `${COMPONENT_NAME}__ErrorReload`,
          accessibilityLabel: `${COMPONENT_NAME}__ErrorReload`
        }) : !isResultFetching && (searchKeyword == null ? undefined : searchKeyword.trim().length) >= 2 && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
          refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
            refreshing: false,
            onRefresh: reloadData
          }),
          showsVerticalScrollIndicator: false,
          data: searchResultPayload,
          renderItem: function renderItem(_ref5) {
            var item = _ref5.item,
              index = _ref5.index;
            return (0, _jsxRuntime.jsx)(_attractionsFacilitiesServices.AttractionsFacilitiesServices, {
              type: _exploreItemType.ExploreItemTypeEnum.default,
              title: item == null ? undefined : item.title,
              imageUrl: (0, _screenHelper.getUriImage)(item == null ? undefined : item.image),
              attractionId: undefined,
              locationDisplayText: item == null ? undefined : item.locationDisplayText,
              onPressed: function onPressed() {
                onPress(item, index);
              },
              testID: `${COMPONENT_NAME}__SearchResult__${index}`,
              accessibilityLabel: `${COMPONENT_NAME}__SearchResult__${index}`
            });
          },
          keyExtractor: function keyExtractor(_, index) {
            return `search_attractions_${index}`;
          },
          numColumns: 2,
          style: SearchTabStyle.flatListStyle,
          contentContainerStyle: SearchTabStyle.contentContainerStyle,
          scrollEnabled: true,
          testID: `${COMPONENT_NAME}__FlatListSearchAttractions`,
          accessibilityLabel: `${COMPONENT_NAME}__FlatListSearchAttractions`,
          ListHeaderComponent: headerComponent,
          ListFooterComponent: footerComponent,
          onEndReachedThreshold: 0.3,
          onEndReached: function onEndReached(_ref6) {
            var distanceFromEnd = _ref6.distanceFromEnd;
            if (distanceFromEnd >= 0) {
              loadMoreData();
            }
          }
        }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
          visible: isPaginating || isResultFetching || loadingLocal,
          onPressed: function onPressed() {
            return _reactNative2.Keyboard.dismiss();
          },
          customStyle: Object.assign({}, _reactNative2.Platform.select({
            android: {
              elevation: 0
            }
          }))
        }), showErrorToastMessage()]
      })
    });
  };
  var _default = exports.default = SearchTabAttractions;
