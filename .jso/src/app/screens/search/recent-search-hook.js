  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.useRecentSearch = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _$$_REQUIRE(_dependencyMap[3]);
  var _constants = _$$_REQUIRE(_dependencyMap[4]);
  var _storage = _$$_REQUIRE(_dependencyMap[5]);
  var useRecentSearch = exports.useRecentSearch = function useRecentSearch() {
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      recentSearch = _useState2[0],
      setRecentSearch = _useState2[1];
    (0, _react.useEffect)(function () {
      fetchData();
    }, []);
    var fetchData = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var localData = yield (0, _storage.load)(_constants.RECENT_SEARCH_APP);
        setRecentSearch(localData || []);
      });
      return function fetchData() {
        return _ref.apply(this, arguments);
      };
    }();
    var getLocalData = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        return yield (0, _storage.load)(_constants.RECENT_SEARCH_APP);
      });
      return function getLocalData() {
        return _ref2.apply(this, arguments);
      };
    }();
    var saveLocal = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (value) {
        return yield (0, _storage.save)(_constants.RECENT_SEARCH_APP, value);
      });
      return function saveLocal(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var addNewItem = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* (keyword) {
        var _temp;
        var value = yield getLocalData();
        var temp = value || [];
        temp = (_temp = temp) == null ? undefined : _temp.filter(function (element) {
          return element !== (keyword == null ? undefined : keyword.trim());
        });
        temp.unshift(keyword == null ? undefined : keyword.trim());
        yield saveLocal(temp.slice(0, 5));
      });
      return function addNewItem(_x2) {
        return _ref4.apply(this, arguments);
      };
    }();
    var removeAll = /*#__PURE__*/function () {
      var _ref5 = (0, _asyncToGenerator2.default)(function* () {
        yield saveLocal([]);
      });
      return function removeAll() {
        return _ref5.apply(this, arguments);
      };
    }();
    var addNewRecentSearchKeyword = /*#__PURE__*/function () {
      var _ref6 = (0, _asyncToGenerator2.default)(function* (keyword) {
        yield addNewItem(keyword);
        yield fetchData();
      });
      return function addNewRecentSearchKeyword(_x3) {
        return _ref6.apply(this, arguments);
      };
    }();
    var removeItemKeySearch = /*#__PURE__*/function () {
      var _ref7 = (0, _asyncToGenerator2.default)(function* (keyword) {
        var _temp2;
        var recentSearchList = yield (0, _storage.load)(_constants.RECENT_SEARCH_APP);
        var temp = recentSearchList;
        temp = (_temp2 = temp) == null ? undefined : _temp2.filter(function (element) {
          return element !== (keyword == null ? undefined : keyword.trim());
        });
        (0, _storage.save)(_constants.RECENT_SEARCH_APP, temp);
        yield fetchData();
      });
      return function removeItemKeySearch(_x4) {
        return _ref7.apply(this, arguments);
      };
    }();
    var refreshRecentSearch = /*#__PURE__*/function () {
      var _ref8 = (0, _asyncToGenerator2.default)(function* () {
        yield fetchData();
      });
      return function refreshRecentSearch() {
        return _ref8.apply(this, arguments);
      };
    }();
    return {
      recentSearch: recentSearch,
      addNewRecentSearchKeyword: addNewRecentSearchKeyword,
      removeAll: removeAll,
      removeItemKeySearch: removeItemKeySearch,
      refreshRecentSearch: refreshRecentSearch
    };
  };
