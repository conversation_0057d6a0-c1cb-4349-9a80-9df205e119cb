  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _searchTabDine = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _searchTabShop = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _searchBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _searchScreen = _$$_REQUIRE(_dependencyMap[12]);
  var _i18n = _$$_REQUIRE(_dependencyMap[13]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[14]);
  var _searchTabAll = _$$_REQUIRE(_dependencyMap[15]);
  var _recentSearchHook = _$$_REQUIRE(_dependencyMap[16]);
  var _searchTabFlights = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _searchScreenContext = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _searchTabAirport = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _flyRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[22]);
  var _searchTabAttractions = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _searchTabEvents = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _lodash = _$$_REQUIRE(_dependencyMap[25]);
  var _storage = _$$_REQUIRE(_dependencyMap[26]);
  var _searchSaga = _$$_REQUIRE(_dependencyMap[27]);
  var _autocompleteSearch = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[28]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[29]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[30]);
  var _native = _$$_REQUIRE(_dependencyMap[31]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[32]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[33]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[34]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchTab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var SCREEN_NAME = "SearchScreen__";
  var SearchScreen = function SearchScreen(_ref) {
    var _route$params, _route$params2, _keySearch$trim;
    var navigation = _ref.navigation,
      route = _ref.route;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useRecentSearch = (0, _recentSearchHook.useRecentSearch)(),
      addNewRecentSearchKeyword = _useRecentSearch.addNewRecentSearchKeyword;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SEARCH_SCREEN"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var routeModule = (_route$params = route.params) != null && _route$params.module ? (_route$params2 = route.params) == null ? undefined : _route$params2.module : _searchIndex.SearchIndex.all;
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var _useState = (0, _react.useState)(routeModule),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      module = _useState2[0],
      setModule = _useState2[1];
    var appState = (0, _react.useRef)(_reactNative.AppState.currentState);
    var sourcePage = (0, _lodash.get)(route, "params.sourcePage", _adobe.AdobeTagName.CAppSearchResult);
    var _useState3 = (0, _react.useState)(""),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      keySearch = _useState4[0],
      setKeySearch = _useState4[1];
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      searchCollectClickStreamFlag = _useContext.searchCollectClickStreamFlag;
    var keySearchLength = !(0, _lodash.isEmpty)(keySearch) && (keySearch == null || (_keySearch$trim = keySearch.trim()) == null ? undefined : _keySearch$trim.length);
    (0, _react.useEffect)(function () {
      var getMissingKeywordSearch = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var keywordCollection = yield (0, _storage.load)(_storage.StorageKey.keywordSearchMissingByAppState);
          if (!(0, _lodash.isEmpty)(keywordCollection)) {
            (0, _searchSaga.standardizeKeywordCollection)(keywordCollection, module, sourcePage);
            (0, _storage.remove)(_storage.StorageKey.keywordSearchMissingByAppState);
          }
        });
        return function getMissingKeywordSearch() {
          return _ref2.apply(this, arguments);
        };
      }();
      getMissingKeywordSearch();
    }, []);
    (0, _react.useEffect)(function () {
      var subscription = _reactNative.AppState.addEventListener("change", function (nextAppState) {
        if (appState.current.match(/inactive/) && nextAppState === "background") {
          dispatch(_searchRedux.default.handleSearchKeywordForAppState(module, sourcePage));
          dispatch(_searchRedux.default.sendSearchKeywordCollection(module, sourcePage));
        }
        appState.current = nextAppState;
      });
      return function () {
        subscription.remove();
      };
    }, [module]);
    (0, _react.useEffect)(function () {
      dispatch(_searchRedux.default.resetSearchKeywordCollection());
      dispatch(_flyRedux.FlyCreators.setFilterDateSearchArrival(null));
      dispatch(_flyRedux.FlyCreators.setFilterDateSearchDeparture(null));
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      dispatch(_searchRedux.default.popularSearchKeywordRequest());
      return function () {
        dispatch(_searchRedux.default.setSearchKeyword(""));
      };
    }, []);
    var getTabName = (0, _react.useMemo)(function () {
      switch (module) {
        case _searchIndex.SearchIndex.dine:
          return "dine";
        case _searchIndex.SearchIndex.shop:
          return "shop";
        case _searchIndex.SearchIndex.flights:
          return "flights";
        case _searchIndex.SearchIndex.airport:
          return "facilities";
        case _searchIndex.SearchIndex.attractions:
          return "attractions";
        case _searchIndex.SearchIndex.events:
          return "events";
        default:
          return "";
      }
    }, [module]);
    var onGetAutoCompleteKeyword = function onGetAutoCompleteKeyword(newKeyword) {
      var param = {
        text: newKeyword == null ? undefined : newKeyword.trim(),
        dataType: getTabName
      };
      dispatch(_searchRedux.default.setViewAllFlight(false));
      dispatch(_searchRedux.default.getAutoCompleteKeywordRequest(param));
    };
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(onGetAutoCompleteKeyword, 200), [module]);
    var handleSearchKeywordChange = function handleSearchKeywordChange(newKeyword) {
      var _newKeyword$trim;
      if (!(0, _lodash.isEmpty)(keyword) && keyword !== newKeyword) {
        dispatch(_searchRedux.default.setSearchKeyword(""));
      }
      setKeySearch(newKeyword);
      if ((newKeyword == null || (_newKeyword$trim = newKeyword.trim()) == null ? undefined : _newKeyword$trim.length) > 1) {
        onDebounceKeySearch(newKeyword);
      } else {
        dispatch(_searchRedux.default.resetListAutoCompleteKeyword());
      }
    };
    var handlePressBack = function handlePressBack() {
      dispatch(_searchRedux.default.setSearchKeyword(""));
      navigation.goBack();
    };
    var handleAutoCompleteOnPress = function handleAutoCompleteOnPress(item, index) {
      var contextData = {
        tab: (0, _searchScreenContext.getSearchTabName)(module),
        searchKeyword: keySearch,
        suggestionSelected: item.name,
        position: `${index + 1}`
      };
      (0, _screenHelper.trackActionNewFormat)(_adobe.AdobeTagName.CAppSearchAutoComplete, contextData);
      if ((0, _lodash.isEmpty)(item.navigation)) {
        setKeySearch("");
        addNewRecentSearchKeyword(item.name);
        dispatch(_searchRedux.default.setSearchKeyword(item.name));
      } else {
        var _navigation = item.navigation;
        handleNavigation(_navigation == null ? undefined : _navigation.type, _navigation == null ? undefined : _navigation.value, (_navigation == null ? undefined : _navigation.redirect) || {});
      }
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var onBackPress = function onBackPress() {
        handlePressBack();
        return true;
      };
      var subscription = _reactNative.BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return function () {
        return subscription.remove();
      };
    }, []));
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
        style: _searchScreen.styles.headerStyle,
        testID: "SearchScreen",
        accessibilityLabel: "SearchScreen"
      }), (0, _jsxRuntime.jsxs)(_reactNative.SafeAreaView, {
        style: _searchScreen.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
          translucent: true,
          backgroundColor: "transparent",
          barStyle: "dark-content"
        }), (0, _jsxRuntime.jsxs)(_searchScreenContext.SearchScreenProvider, {
          children: [(0, _jsxRuntime.jsx)(_searchBar.default, {
            tab: module,
            keyword: keyword,
            onPressBack: function onPressBack() {
              return handlePressBack();
            },
            onChangeKeyword: function onChangeKeyword(s) {
              handleSearchKeywordChange(s);
            },
            onSearchClear: function onSearchClear() {
              dispatch(_searchRedux.default.setSearchKeyword(""));
              handleSearchKeywordChange("");
            },
            onSubmitEditing: function onSubmitEditing() {
              setKeySearch("");
            },
            searchAutoCompleteFlag: _remoteConfig.REMOTE_FLAG_VALUE.ON,
            testID: `${SCREEN_NAME}SearchBar`,
            accessibilityLabel: `${SCREEN_NAME}SearchBar`
          }), keySearchLength > 1 ? (0, _jsxRuntime.jsx)(_autocompleteSearch.default, {
            keySearch: keySearch == null ? undefined : keySearch.trim(),
            handleItemOnPess: handleAutoCompleteOnPress
          }) : (0, _jsxRuntime.jsxs)(SearchTab.Navigator, {
            initialRouteName: module,
            keyboardDismissMode: "none",
            tabBar: function tabBar(props) {
              return (0, _jsxRuntime.jsx)(_navigationUtilities.TopTabNavBar, {
                props: Object.assign({}, props),
                shouldKeyboardBeDismissed: true,
                topTabParentStyle: _searchScreen.styles.topTabParentStyle,
                topTabTouchableOpacityStyle: _searchScreen.styles.topTabTouchableOpacityStyle,
                topTabActiveIndicatorStyle: _searchScreen.styles.topTabActiveIndicatorStyle,
                topTabActiveLabelStyle: _searchScreen.styles.topTabActiveStyle,
                topTabInActiveLabelStyle: _searchScreen.styles.topTabInActiveStyle
              });
            },
            children: [(0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: "vituralTab",
              component: function component() {
                return (0, _jsxRuntime.jsx)(_reactNative.View, {
                  style: _searchScreen.styles.flex1
                });
              },
              options: {
                title: ""
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.all,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.all"),
                tabBarTestID: `${SCREEN_NAME}TabAll`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabAll`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.all
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.all")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabAll.SearchTabAll, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.dine,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.dine"),
                tabBarTestID: `${SCREEN_NAME}TabDine`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabDine`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.dine
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.dine")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabDine.default, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.shop,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.shop"),
                tabBarTestID: `${SCREEN_NAME}TabShop`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabShop`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.shop
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.shop")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabShop.default, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.flights,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.flights"),
                tabBarTestID: `${SCREEN_NAME}TabFlights`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabFlights`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.flights
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.flights")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabFlights.default, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.airport,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.airport"),
                tabBarTestID: `${SCREEN_NAME}TabAirport`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabAirport`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.airport
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.airport")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabAirport.default, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.attractions,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.attractions"),
                tabBarTestID: `${SCREEN_NAME}TabAttractions`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabAttractions`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.attractions
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.attractions")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabAttractions.default, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.events,
              options: {
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.events"),
                tabBarTestID: `${SCREEN_NAME}TabEvents`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabEvents`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.events
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.events")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabEvents.default, Object.assign({}, props, {
                  setModule: setModule
                }));
              }
            })]
          })]
        })]
      })]
    });
  };
  var _default = exports.default = SearchScreen;
