  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchScreenContextProvider = exports.SearchScreenContextConsumer = undefined;
  exports.SearchScreenProvider = SearchScreenProvider;
  exports.trackingSearchResultClickEvent = exports.getSearchTabName = exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _searchRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _lodash = _$$_REQUIRE(_dependencyMap[7]);
  var _adobe = _$$_REQUIRE(_dependencyMap[8]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[9]);
  var _i18n = _$$_REQUIRE(_dependencyMap[10]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchScreenContext = (0, _react.createContext)({
    searchCollectClickStreamFlag: null,
    trackingSearchResultClick: null,
    isPressSearchKey: null,
    setIsPressSearchKey: null,
    sourcePage: null
  });
  var SearchScreenContextProvider = exports.SearchScreenContextProvider = SearchScreenContext.Provider,
    SearchScreenContextConsumer = exports.SearchScreenContextConsumer = SearchScreenContext.Consumer;
  var getSearchTabName = exports.getSearchTabName = function getSearchTabName(searchIndex) {
    switch (searchIndex) {
      case _searchIndex.SearchIndex.all:
        return (0, _i18n.translate)("search.tabTitles.all");
      case _searchIndex.SearchIndex.dine:
        return (0, _i18n.translate)("search.tabTitles.dine");
      case _searchIndex.SearchIndex.shop:
        return (0, _i18n.translate)("search.tabTitles.shop");
      case _searchIndex.SearchIndex.flights:
        return (0, _i18n.translate)("search.tabTitles.flights");
      case _searchIndex.SearchIndex.airport:
        return (0, _i18n.translate)("search.tabTitles.airport");
      case _searchIndex.SearchIndex.attractions:
        return (0, _i18n.translate)("search.tabTitles.attractions");
      case _searchIndex.SearchIndex.events:
        return (0, _i18n.translate)("search.tabTitles.events");
      default:
        return "";
    }
  };
  var trackingSearchResultClickEvent = exports.trackingSearchResultClickEvent = function trackingSearchResultClickEvent(_ref) {
    var keyword = _ref.keyword,
      sourcePage = _ref.sourcePage,
      name = _ref.name,
      searchIndex = _ref.searchIndex,
      rankIndex = _ref.rankIndex;
    (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultClicked, (0, _defineProperty2.default)((0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultClicked, `${sourcePage} | ${getSearchTabName(searchIndex)} | ${keyword} | ${name}`), _adobe.AdobeTagName.CAppSearchResultsClickedRank, `${sourcePage} | ${keyword} | ${name} | ${rankIndex + 1}`));
  };
  function SearchScreenProvider(props) {
    var children = props.children;
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isPressSearchKey = _useState2[0],
      setIsPressSearchKey = _useState2[1];
    var route = (0, _native.useRoute)();
    var searchCollectClickStreamFlag = '';
    var sourcePage = (0, _lodash.get)(route, "params.sourcePage", _adobe.AdobeTagName.CAppSearchResult);
    var trackingSearchResultClick = (0, _react.useCallback)(function (name, searchIndex, rankIndex) {
      trackingSearchResultClickEvent({
        sourcePage: sourcePage,
        keyword: keyword,
        name: name,
        searchIndex: searchIndex,
        rankIndex: rankIndex
      });
    }, [sourcePage, keyword]);
    return (0, _jsxRuntime.jsx)(SearchScreenContextProvider, {
      value: {
        searchCollectClickStreamFlag: searchCollectClickStreamFlag,
        trackingSearchResultClick: trackingSearchResultClick,
        isPressSearchKey: isPressSearchKey,
        setIsPressSearchKey: setIsPressSearchKey,
        sourcePage: sourcePage
      },
      children: children
    });
  }
  var _default = exports.default = SearchScreenContext;
