  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.AutocompleteSearch = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _searchRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[10]);
  var AutocompleteSearch = exports.AutocompleteSearch = function AutocompleteSearch(props) {
    var keySearch = props.keySearch,
      handleItemOnPess = props.handleItemOnPess,
      containerStyle = props.containerStyle;
    var autoCompleteKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.autoCompleteKeywordList);
    var escapeRegExp = function escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };
    var renderTextAutoComplete = function renderTextAutoComplete(value) {
      var parts = value.split(new RegExp(`(${escapeRegExp(keySearch)})`, "gi"));
      var keywordCount = 0;
      var highlightedText = parts.map(function (part) {
        if (part.toLowerCase() === keySearch.toLowerCase() && keywordCount === 0) {
          keywordCount++;
          return (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.textItemHighlight,
            preset: "bodyTextBold",
            text: part
          });
        } else {
          return part;
        }
      });
      return (0, _jsxRuntime.jsx)(_text.Text, {
        preset: "bodyTextRegular",
        style: styles.textItemStyles,
        numberOfLines: 1,
        children: highlightedText
      });
    };
    var _renderItem = function renderItem(item, index) {
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.itemViewContainer,
        onPress: function onPress() {
          handleItemOnPess(item, index);
        },
        children: [!(0, _lodash.isEmpty)(item == null ? undefined : item.image) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.iconContainer,
          children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
            source: {
              uri: (0, _screenHelper.getUriImage)(item.image)
            },
            style: styles.iconStyles,
            resizeMode: "cover"
          })
        }), renderTextAutoComplete(item.name)]
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [styles.container, containerStyle],
      children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        data: autoCompleteKeywordList,
        renderItem: function renderItem(_ref) {
          var item = _ref.item,
            index = _ref.index;
          return _renderItem(item, index);
        },
        keyExtractor: function keyExtractor(_, index) {
          return index.toString();
        }
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginTop: 12
    },
    iconContainer: {
      borderRadius: 5,
      height: 24,
      marginRight: 10,
      overflow: "hidden",
      width: 24
    },
    iconStyles: {
      height: 24,
      width: 24
    },
    itemViewContainer: {
      alignContent: "center",
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingVertical: 16
    },
    textItemHighlight: {
      color: _theme.color.palette.almostBlackGrey
    },
    textItemStyles: {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16
    }
  });
  var _default = exports.default = AutocompleteSearch;
