  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeSizeMatters = _$$_REQUIRE(_dependencyMap[3]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    bodyTextStyle: {
      color: _theme.color.palette.darkestGrey,
      marginHorizontal: 53,
      marginTop: 16,
      textAlign: "center"
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1
    },
    containerRecentSearch: {
      flex: 1,
      paddingHorizontal: 24
    },
    containerStyle: {
      backgroundColor: _theme.color.palette.whiteGrey,
      flex: 1
    },
    emptyOrderStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestGrey,
      height: "100%",
      justifyContent: "center",
      paddingHorizontal: 24
    },
    emptyStateButtonStyle: {
      width: (0, _reactNativeSizeMatters.scale)(156)
    },
    feedBackToastStyle: {
      alignItems: "flex-start",
      backgroundColor: _theme.color.palette.black,
      borderRadius: 8,
      height: 60,
      marginBottom: 20,
      marginHorizontal: 16,
      width: "95%"
    },
    flex1: {
      flex: 1
    },
    headerStyle: {
      backgroundColor: _theme.color.palette.whiteGrey
    },
    lazyLoaderContainer: {
      backgroundColor: _theme.color.palette.whiteColorOpacity,
      height: "100%",
      position: "absolute",
      width: "100%"
    },
    mainNoResultsContainer: {
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 25
    },
    msgContainer: {
      alignItems: "center",
      justifyContent: "center"
    },
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    },
    positionStyle: {
      bottom: 30
    },
    tabParentStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      height: 50
    }, _theme.shadow.secondaryShadow, {
      paddingStart: 24
    }),
    tabTouchableOpacityStyle: {
      alignItems: "center",
      marginEnd: 24
    },
    titleContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingHorizontal: 24,
      paddingTop: 40
    },
    titleStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    titleTextStyle: {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 50
    },
    toastButtonStyle: Object.assign({}, _text.presets.textLink, {
      alignItems: "flex-end",
      color: _theme.color.palette.lightBlue,
      fontWeight: "normal"
    }),
    toastContainer: {
      bottom: 20,
      paddingHorizontal: 6,
      position: "absolute",
      width: "100%"
    },
    toastStyle: {
      alignItems: "flex-start",
      backgroundColor: _theme.color.palette.black,
      borderRadius: 8,
      flex: 1,
      marginBottom: 20,
      width: "95%"
    },
    toastTextStyle: Object.assign({}, _text.presets.bodyTextRegular, {
      color: _theme.color.palette.whiteGrey,
      width: "80%"
    }),
    topTabActiveIndicatorStyle: {
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 56,
      height: 4
    },
    topTabActiveStyle: {
      color: _theme.color.palette.lightPurple,
      flex: 1
    },
    topTabInActiveStyle: {
      color: _theme.color.palette.darkestGrey,
      flex: 1
    },
    topTabParentStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      flexDirection: "row",
      height: 54,
      justifyContent: "center"
    }, _theme.shadow.secondaryShadow, {
      alignItems: "flex-end",
      elevation: 10
    }),
    topTabTouchableOpacityStyle: {
      alignItems: "center",
      height: (0, _reactNativeSizeMatters.scale)(30),
      marginEnd: 24
    },
    whiteContainer: {
      backgroundColor: _theme.color.transparent
    },
    ymalContainer: {
      marginTop: 40
    },
    ymalEmptyStyles: {
      height: 0
    },
    ymalFlatListStyles: {
      paddingBottom: 0
    }
  });
