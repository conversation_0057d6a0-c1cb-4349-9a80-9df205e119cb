  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _theme = _$$_REQUIRE(_dependencyMap[0]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var CONTAINER = Object.assign({}, _reactNative.Platform.select({
    ios: {
      shadowRadius: 2,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      },
      borderRadius: 20,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    android: {
      elevation: 2,
      borderRadius: 20,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  }));
  var styles = exports.styles = _reactNative.StyleSheet.create({
    cardTerminal: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      marginBottom: 12,
      padding: 16
    }, CONTAINER),
    contentContainerStyle: {
      color: _theme.color.palette.lightestGrey
    },
    defaultColor: {
      color: _theme.color.palette.almostBlack<PERSON>rey
    },
    delete: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 9,
      height: 18,
      justifyContent: "center",
      marginLeft: 10,
      width: 18
    },
    divider: {
      backgroundColor: _theme.color.palette.lightestGrey,
      height: 1,
      marginVertical: 16
    },
    errorUnplannedMaintenance: {
      marginTop: 30
    },
    fullColor: {
      color: _theme.color.palette.baseRed
    },
    informationFirstRow: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
    },
    leftInformation: {
      height: 40,
      width: "49%"
    },
    messageCardTerminal: {},
    rightInformation: {
      height: 40,
      width: "49%"
    },
    scrollViewFilterStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      color: _theme.color.palette.lightestGrey,
      maxHeight: 50,
      paddingHorizontal: 24,
      paddingTop: 18
    },
    textItemFilterActive: {
      color: _theme.color.palette.whiteGrey,
      textAlign: "center"
    },
    textItemFilterInActive: {
      color: _theme.color.palette.darkestGrey,
      textAlign: "center"
    },
    titleCardTerminal: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8,
      width: "90%"
    },
    titleInformation: {
      color: _theme.color.palette.darkestGrey
    },
    titleTerminalSection: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 16
    },
    touchableItemFilterActive: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightPurple,
      borderRadius: 15,
      height: 30,
      justifyContent: "center",
      marginRight: 10,
      paddingHorizontal: 12,
      paddingVertical: 4
    },
    touchableItemFilterInActive: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 15,
      borderWidth: 1,
      height: 30,
      justifyContent: "center",
      marginRight: 10,
      paddingHorizontal: 12,
      paddingVertical: 4
    },
    touchableLocation: {
      position: "absolute",
      right: 20.85,
      top: 18
    },
    valueInformation: {
      color: _theme.color.palette.almostBlackGrey
    },
    valueInformationNA: {
      color: _theme.color.palette.baseRed
    },
    valueInformationRed: {
      color: _theme.color.palette.baseRed
    },
    valueInformationWithHandiCap: {
      color: _theme.color.palette.almostBlackGrey,
      marginLeft: 4
    },
    valueInformationWithHandiCapNA: {
      color: _theme.color.palette.baseRed,
      marginLeft: 4
    },
    valueInformationWithHandiCapRed: {
      color: _theme.color.palette.baseRed,
      marginLeft: 4
    },
    valueWithIconHandicap: {
      alignItems: "center",
      flexDirection: "row",
      marginTop: 2
    },
    wrapTerminalSection: {
      backgroundColor: _theme.color.palette.lightestGrey,
      marginBottom: 16,
      marginTop: 30,
      paddingHorizontal: 24
    },
    wrapViewTerminalSection: {
      backgroundColor: _theme.color.palette.lightestGrey
    }
  });
