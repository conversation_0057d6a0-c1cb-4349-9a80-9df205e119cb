  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.handleHeight = exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _availability = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _carPark = _$$_REQUIRE(_dependencyMap[11]);
  var _carPark2 = _$$_REQUIRE(_dependencyMap[12]);
  var _cardPrivileges = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _lodash = _$$_REQUIRE(_dependencyMap[14]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[17]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _storage = _$$_REQUIRE(_dependencyMap[19]);
  var _storageKey = _$$_REQUIRE(_dependencyMap[20]);
  var _caculator = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[21]));
  var _findMyCar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _constants = _$$_REQUIRE(_dependencyMap[23]);
  var _adobe = _$$_REQUIRE(_dependencyMap[24]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[25]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[28]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[29]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[30]);
  var _queries = _$$_REQUIRE(_dependencyMap[31]);
  var _tabView = _$$_REQUIRE(_dependencyMap[32]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[33]));
  var _native = _$$_REQUIRE(_dependencyMap[34]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[35]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[36]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[37]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  /* eslint-disable react/display-name */

  var testID = "CarPark";
  var storageCurrentTab = 0;
  var TERMINAL_ITEM = 200;
  var FIND_MY_CAR_ITEM = 250;
  var SPACE_OF_EACH_TERMINAL = 60;
  var DEFAUTL_SPACE = 600;
  var handleMappingCarParkV2 = function handleMappingCarParkV2(data, isV2) {
    if (isV2 || (0, _isEmpty.default)(data)) return data;
    return data == null ? undefined : data.map(function (element) {
      return Object.assign({}, element, {
        name: element == null ? undefined : element.name_old,
        message: element == null ? undefined : element.message_old,
        name_zh: element == null ? undefined : element.name_zh_old,
        message_zh: element == null ? undefined : element.message_zh_old
      });
    });
  };
  var handleHeight = exports.handleHeight = function handleHeight(totalTerminal, totalSectionTerminal, currentTab) {
    if (!currentTab) {
      return Number(totalTerminal) * TERMINAL_ITEM + Number(totalSectionTerminal) * SPACE_OF_EACH_TERMINAL;
    } else if (currentTab === 2) {
      return Number(totalTerminal) * FIND_MY_CAR_ITEM + 100;
    }
    return DEFAUTL_SPACE;
  };
  var handleCurrentTab = function handleCurrentTab(currentTab) {
    if (currentTab === undefined || currentTab === null) {
      return storageCurrentTab;
    }
    storageCurrentTab = currentTab;
    return storageCurrentTab;
  };
  var validateNumber = function validateNumber(data) {
    if (!data) return 1;
    return Number(data);
  };
  var CarParkScreen = function CarParkScreen(_ref) {
    var _currentState$routes;
    var navigation = _ref.navigation,
      route = _ref.route;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)(""),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var currentState = navigation == null ? undefined : navigation.getState();
    var _ref2 = (route == null ? undefined : route.params) || {},
      activeTab = _ref2.activeTab;
    var currentTab = handleCurrentTab(currentState == null || (_currentState$routes = currentState.routes) == null || (_currentState$routes = _currentState$routes[currentState == null ? undefined : currentState.index]) == null || (_currentState$routes = _currentState$routes.state) == null ? undefined : _currentState$routes.index);
    var dispatch = (0, _reactRedux.useDispatch)();
    var scrollViewRef = (0, _react.useRef)(null);
    var animatedContainer = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      dismissed = _useState2[0],
      setDismissed = _useState2[1];
    var parkingPrivilegesData = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.PARKING_PRIVILEGES_DATA));
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var loadingAEM = (0, _lodash.get)(parkingPrivilegesData, "loading");
    var contentParkingPrivilegesData = (0, _lodash.get)(parkingPrivilegesData, "data.list.0", []);
    var getCarParkAvailabilityLoading = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkAvailabilityLoading);
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("viewCarPassAirPort"),
      getCommonLoginModule = _useGeneratePlayPassU.getCommonLoginModule;
    var getCarParkUrlFetching = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkUrlFetching);
    var getCarParkFareDetailFetching = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkFareDetailFetching);
    var getCarParkAvailabilityError = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkAvailabilityError);
    var getCarParkFareDetailError = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkFareDetailError);
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isShowError = _useState4[0],
      setIsShowError = _useState4[1];
    var totalTerminalCard = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.totalTerminalCard);
    var getCarParkAvailability = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getCarParkAvailability);
    var getMyCarPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMyCarPayload);
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isInternetConnected = _useState6[0],
      setIsInternetConnected = _useState6[1];
    var _useState7 = (0, _react.useState)(0),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      _heightTab = _useState8[0],
      _setHeightTab = _useState8[1];
    var getCurrenPageName = function getCurrenPageName(tab) {
      switch (tab) {
        case 0:
          return "Carpark_Availability";
        case 1:
          return "Carpark_Calculator";
        case 2:
          return "Carpark_FindMyCar";
        default:
          return "Carpark_Availability";
      }
    };
    (0, _react.useEffect)(function () {
      if (currentTab !== undefined) {
        (0, _screenHook.setCurrentScreenActive)(getCurrenPageName(currentTab));
        (0, _adobe.commonTrackingScreen)(getCurrenPageName(currentTab), (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      }
    }, [currentTab]);
    (0, _react.useEffect)(function () {
      var unsubscribeBlur = navigation.addListener("blur", function () {
        (0, _screenHook.setPreviousScreen)(getCurrenPageName(currentTab));
      });
      return unsubscribeBlur;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref3 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          setIsInternetConnected(isConnected);
          if (isConnected) {
            fetchData();
          }
        });
        return function checkInternet() {
          return _ref3.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    (0, _react.useEffect)(function () {
      if (getCarParkFareDetailError || getCarParkAvailabilityError) {
        setIsShowError(true);
      }
    }, [getCarParkFareDetailError, getCarParkAvailabilityError]);
    (0, _react.useEffect)(function () {
      var showSubscription = _reactNative2.Keyboard.addListener("keyboardDidShow", function () {
        _reactNative2.Animated.timing(animatedContainer, {
          toValue: -120,
          duration: 200,
          useNativeDriver: true
        }).start();
      });
      var hideSubscription = _reactNative2.Keyboard.addListener("keyboardDidHide", function () {
        _reactNative2.Animated.timing(animatedContainer, {
          toValue: 0,
          duration: 100,
          useNativeDriver: true
        }).start();
      });
      return function () {
        showSubscription.remove();
        hideSubscription.remove();
      };
    }, []);
    (0, _react.useLayoutEffect)(function () {
      navigation == null || navigation.setOptions({
        header: function header() {
          return false;
        }
      });
    }, [navigation]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      dispatch(_airportLandingRedux.default.getMyCarReset());
      dispatch(_airportLandingRedux.default.getMyCarLocationReset());
    }, []));
    (0, _react.useEffect)(function () {
      var subscription = _awsAmplify.API.graphql({
        query: _queries.subScriptionCarParkAvailability
      }).subscribe({
        next: function next(_ref4) {
          var _value$data;
          var value = _ref4.value;
          dispatch(_airportLandingRedux.default.getCarParkAvailabilitySuccess({
            getCarparkAvailability: handleMappingCarParkV2(value == null || (_value$data = value.data) == null ? undefined : _value$data.carparkAvailabilityUpdates, true)
          }));
        },
        error: function error(_error) {
          return undefined;
        }
      });
      return function () {
        return subscription.unsubscribe();
      };
    }, []);
    var fetchData = function fetchData() {
      dispatch(_airportLandingRedux.default.getCarParkAvailabilityRequest());
      dispatch(_aemRedux.default.getAemConfigData({
        name: _aemRedux.AEM_PAGE_NAME.PARKING_PRIVILEGES_DATA,
        pathName: "getParkingPrivileges"
      }));
      dispatch(_airportLandingRedux.default.getCarParkFareDetailRequest());
    };
    (0, _react.useEffect)(function () {
      var checkCard = /*#__PURE__*/function () {
        var _ref5 = (0, _asyncToGenerator2.default)(function* () {
          var dismissedData = yield (0, _storage.load)(_storageKey.StorageKey.userDismissedCardPrivileges);
          if (!dismissedData) {
            setDismissed(false);
          }
        });
        return function checkCard() {
          return _ref5.apply(this, arguments);
        };
      }();
      checkCard();
      return function () {
        (0, _storage.save)(_storageKey.StorageKey.userDismissedCardPrivileges, false);
      };
    }, []);
    var _onDismissCard = function onDismissCard() {
      (0, _storage.save)(_storageKey.StorageKey.userDismissedCardPrivileges, true);
      setDismissed(true);
    };
    var onCtaPress = function onCtaPress(cta) {
      var _cta$navigation, _cta$navigation2;
      return handleNavigation(cta == null || (_cta$navigation = cta.navigation) == null ? undefined : _cta$navigation.type, cta == null || (_cta$navigation2 = cta.navigation) == null ? undefined : _cta$navigation2.value);
    };
    var onFindMore = function onFindMore() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppCarparkAvailabilityParkingPriviledges, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppCarparkAvailabilityParkingPriviledges, "1"));
      var cta = contentParkingPrivilegesData == null ? undefined : contentParkingPrivilegesData.cta;
      var enableDRIVE_PARKING = (0, _remoteConfig.getFeatureFlagInit)(_remoteConfig.REMOTE_CONFIG_FLAGS.DRIVE_PARKING);
      if (cta && enableDRIVE_PARKING) return onCtaPress(cta);
      if (!isLoggedIn) {
        navigation.navigate(_constants.NavigationConstants.authScreen, {
          sourceSystem: _constants.SOURCE_SYSTEM.OTHERS,
          callBackAfterLoginSuccess: function callBackAfterLoginSuccess() {
            return getCommonLoginModule(_constants.StateCode.CARPASS);
          },
          callBackAfterLoginCancel: undefined
        });
      } else {
        getCommonLoginModule(_constants.StateCode.CARPASS);
      }
    };
    var handleGoBack = function handleGoBack() {
      navigation.goBack();
    };
    var headerCarPark = function headerCarPark() {
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _carPark2.styles.wrapHeaderCarPark,
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _carPark2.styles.wrapLeftAction,
          children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
            onPress: function onPress() {
              return handleGoBack();
            },
            children: (0, _jsxRuntime.jsx)(_icons.BackButton, {})
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "carParkScreen.header",
          preset: "subTitleBold",
          style: _carPark2.styles.carParkHeader
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _carPark2.styles.wrapRightAction
        })]
      });
    };
    (0, _react.useEffect)(function () {
      var _getMyCarPayload$item, _Object$keys;
      var tHeight = handleHeight(currentTab === 2 ? validateNumber(getMyCarPayload == null || (_getMyCarPayload$item = getMyCarPayload.items) == null ? undefined : _getMyCarPayload$item.length) : validateNumber(totalTerminalCard), currentTab === 2 || !getCarParkAvailability ? 0 : (_Object$keys = Object.keys(getCarParkAvailability)) == null ? undefined : _Object$keys.length, currentTab);
      _setHeightTab(tHeight);
    }, [currentTab, totalTerminalCard, getCarParkAvailability, getMyCarPayload]);
    var carParkTab = function carParkTab() {
      return (0, _jsxRuntime.jsx)(_tabView.TabView, {
        routes: [{
          key: "Availability",
          name: _carPark.CarParkTabType.Availability
        }, {
          key: "Calculator",
          name: _carPark.CarParkTabType.Calculator
        }, {
          key: "FindMyCar",
          name: _carPark.CarParkTabType.FindMyCar
        }],
        tabs: {
          tabList: [{
            component: _availability.default,
            props: {
              setHeightTab: function setHeightTab(tHeight) {
                return _setHeightTab(tHeight);
              },
              enableV2: true
            }
          }, {
            component: _caculator.default,
            props: {
              caculateHeight: function caculateHeight(tHeight) {
                return _setHeightTab(tHeight);
              },
              enableV2: true
            }
          }, {
            component: _findMyCar.default,
            props: {}
          }]
        },
        topTabParentStyle: {
          height: _reactNative2.Platform.select({
            ios: 55,
            android: 50
          })
        },
        isAnimate: true,
        tabIndexInit: activeTab
      });
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.Animated.View, {
      style: [_carPark2.styles.container, {
        transform: [{
          translateY: animatedContainer
        }]
      }],
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        barStyle: "light-content"
      }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          flex: 1
        },
        children: !isInternetConnected ? (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: false,
          header: true,
          headerBackgroundColor: "transparent",
          visible: !isInternetConnected,
          testID: `${testID}__ErrorOverlayNoConnection`,
          onBack: function onBack() {
            return navigation.goBack();
          }
        }) : (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          style: _carPark2.styles.scrollView,
          showsVerticalScrollIndicator: false,
          contentContainerStyle: _carPark2.styles.contentContainerStyle,
          ref: scrollViewRef,
          children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
            source: _$$_REQUIRE(_dependencyMap[38]),
            resizeMode: "cover",
            style: _carPark2.styles.heroImage
          }), headerCarPark(), !(0, _isEmpty.default)(contentParkingPrivilegesData) && !dismissed && (0, _jsxRuntime.jsx)(_cardPrivileges.default, {
            contentParkingPrivilegesData: contentParkingPrivilegesData,
            onDismissCard: function onDismissCard() {
              return _onDismissCard();
            },
            onFindMore: onFindMore
          }), carParkTab()]
        })
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: true,
        headerTx: "carParkScreen.header",
        visible: isShowError,
        onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch2.isConnected;
          if (isConnected) {
            fetchData();
            setIsShowError(false);
          }
        }),
        onBack: function onBack() {
          navigation.goBack();
        },
        testID: `${testID}__ErrorOverlay`,
        accessibilityLabel: `${testID}__ErrorOverlay`,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT1
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: getCarParkUrlFetching || loadingAEM || getCarParkAvailabilityLoading || getCarParkFareDetailFetching
      })]
    });
  };
  var _default = exports.default = CarParkScreen;
