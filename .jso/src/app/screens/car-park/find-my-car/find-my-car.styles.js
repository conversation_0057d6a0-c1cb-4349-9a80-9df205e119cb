  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _button = _$$_REQUIRE(_dependencyMap[0]);
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _theme = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    viewRoot: {
      width: '100%',
      height: '100%',
      paddingHorizontal: 24,
      paddingTop: 30
    },
    buttonContainer: {
      borderColor: _theme.color.palette.transparent,
      width: "100%"
    },
    buttonContainerDisabled: {
      borderColor: _theme.color.palette.transparent,
      width: "100%"
    },
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      width: "100%",
      height: '100%'
    },
    description: {
      marginTop: 8
    },
    errorUnplannedMaintenance: {
      marginTop: 30
    },
    inputNumber: Object.assign({}, _text.presets.h1, {
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 16,
      color: _theme.color.palette.lightPurple,
      height: 86,
      textAlign: "center",
      width: 75
    }, _reactNative.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        },
        borderRadius: 20,
        backgroundColor: _theme.color.palette.whiteGrey
      },
      android: {
        elevation: 3,
        borderRadius: 20,
        backgroundColor: _theme.color.palette.whiteGrey
      }
    })),
    linearButtonContainer: {
      alignSelf: "center",
      borderRadius: 60,
      marginTop: 30,
      width: "100%"
    },
    listInputNumber: {
      marginTop: 16
    },
    textButtonActive: {
      color: _theme.color.palette.almostWhiteGrey
    },
    textButtonInActive: {
      color: _theme.color.palette.darkGrey
    },
    textSubmitActive: Object.assign({}, _button.textPresets.buttonLarge, {
      color: _theme.color.palette.almostWhiteGrey
    }),
    textSubmitInActive: Object.assign({}, _button.textPresets.buttonLarge, {
      color: _theme.color.palette.darkGrey
    }),
    touchableSubmit: {
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 10
    },
    wrapInputNumber: Object.assign({}, _text.presets.h1, {
      backgroundColor: _theme.color.palette.lightestGrey,
      borderRadius: 16,
      color: _theme.color.palette.lightPurple,
      height: 86,
      textAlign: "center"
    })
  });
