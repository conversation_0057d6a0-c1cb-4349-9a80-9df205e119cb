  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNativeImagePanZoom = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _bottomModalSwipe = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _baseImageBackground = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    height = _Dimensions$get.height,
    width = _Dimensions$get.width;
  var MARK_WIDTH = 60;
  var MARK_HEIGHT = 80;
  var MyCarInMap = function MyCarInMap(_ref) {
    var visible = _ref.visible,
      onCloseModal = _ref.onCloseModal,
      url = _ref.url,
      xPosition = _ref.xPosition,
      yPosition = _ref.yPosition,
      terminal = _ref.terminal,
      level = _ref.level,
      spaceNumber = _ref.spaceNumber;
    var _useState = (0, _react.useState)({
        width: 0,
        height: 0
      }),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      resolutionImage = _useState2[0],
      setResolutionImage = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      animateModal = _useState4[0],
      setanimateModal = _useState4[1];
    (0, _react.useEffect)(function () {
      url && _reactNative2.Image.getSize(url, function (w, h) {
        setResolutionImage({
          width: w,
          height: h
        });
      }, function (err) {});
    }, [visible]);
    var imageBackground = {
      width: resolutionImage == null ? undefined : resolutionImage.width,
      height: resolutionImage == null ? undefined : resolutionImage.height
    };
    var imageMark = {
      width: MARK_WIDTH,
      height: MARK_HEIGHT,
      position: "absolute",
      top: yPosition ? yPosition - MARK_HEIGHT : 0,
      left: xPosition ? xPosition - 30 : 0
    };
    return (0, _jsxRuntime.jsx)(_bottomModalSwipe.default, {
      modalVisible: visible,
      PressToanimate: animateModal,
      ContentModal: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: {
          paddingHorizontal: 24,
          paddingTop: 44
        },
        children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.imageZoom,
          children: (0, _jsxRuntime.jsx)(_reactNativeImagePanZoom.default, {
            cropWidth: width - 48,
            cropHeight: 275,
            imageWidth: resolutionImage == null ? undefined : resolutionImage.width,
            imageHeight: resolutionImage == null ? undefined : resolutionImage.height,
            minScale: 0.4,
            maxScale: 2
            // enableSwipeDown={true}
            ,
            enableCenterFocus: false,
            style: styles.imageZoom,
            children: (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
              imageStyle: imageBackground,
              source: {
                uri: url
              },
              children: (0, _jsxRuntime.jsx)(_baseImage.default, {
                source: _$$_REQUIRE(_dependencyMap[12]),
                style: imageMark
              })
            })
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          text: terminal,
          preset: "h2",
          style: styles.titleTerminal
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.levelAndSpaceNumber,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.contentLevelAndSpace,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: "Level",
              preset: "XSmallRegular",
              style: styles.titleH2ContentLevel
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: level,
              preset: "h2",
              style: styles.h2ContentLevel
            })]
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.divider
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.contentLevelAndSpace,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: "Space number",
              preset: "XSmallRegular",
              style: styles.titleH2ContentLevel
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: spaceNumber,
              preset: "h2",
              style: styles.h2ContentLevel
            })]
          })]
        })]
      }),
      HeaderStyle: {
        marginTop: 0,
        position: 'absolute',
        bottom: height * 0.7
      },
      HeaderContent: (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: {
          alignContent: "center",
          alignItems: "center",
          backgroundColor: _theme.color.palette.transparent,
          flex: 1,
          justifyContent: "center",
          height: 40
        },
        children: (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onCloseModal,
          children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.wrapImageZoom
          })
        })
      }),
      MainContainerModal: {
        flex: 1
      },
      onClose: function onClose() {
        onCloseModal();
        setanimateModal(false);
      },
      onBackGroundPress: function onBackGroundPress() {
        onCloseModal();
        setanimateModal(false);
      }
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    contentLevelAndSpace: {
      alignItems: "center",
      borderColor: _theme.color.palette.lightGrey,
      borderRadius: 16,
      borderWidth: 1,
      height: 86,
      justifyContent: "center",
      width: 120
    },
    contentModal: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: height * 0.7,
      padding: 24
    },
    divider: {
      width: 8
    },
    h2ContentLevel: {
      color: _theme.color.palette.almostBlackGrey
    },
    imageZoom: Object.assign({}, _reactNative2.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    }), {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderRadius: 16
    }),
    levelAndSpaceNumber: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "center",
      marginTop: 16
    },
    modal: {
      justifyContent: "flex-end",
      marginBottom: 0,
      marginHorizontal: 0
    },
    titleH2ContentLevel: {
      color: _theme.color.palette.almostBlackGrey
    },
    titleTerminal: {
      marginTop: 50,
      textAlign: "center"
    },
    wrapImageZoom: {
      alignSelf: "center",
      backgroundColor: _theme.color.palette.silver,
      borderRadius: 36,
      height: 4,
      marginBottom: 15,
      width: 50
    }
  });
  var _default = exports.default = MyCarInMap;
