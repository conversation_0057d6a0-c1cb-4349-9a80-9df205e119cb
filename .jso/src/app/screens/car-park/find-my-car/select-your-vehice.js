  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _loadingModal = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _button = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _icons = _$$_REQUIRE(_dependencyMap[8]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[10]);
  var _airportLandingRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _myCarInMap = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _lodash = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var COMPONENT_NAME = "SelectYourVehice__";
  var SelectYourVehice = function SelectYourVehice(_ref) {
    var _listVehice$items, _getMyCarLocationPayl, _getMyCarLocationPayl2, _getMyCarLocationPayl3;
    var listVehice = _ref.listVehice;
    var dispatch = (0, _reactRedux.useDispatch)();
    var getMyCarLocationFetching = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMyCarLocationFetching);
    var getMyCarLocationPayload = (0, _reactRedux.useSelector)(_airportLandingRedux.AirportLandingSelectors.getMyCarLocationPayload);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      visibleMyCarInMap = _useState2[0],
      setVisibleMyCarInMap = _useState2[1];
    var _useState3 = (0, _react.useState)(null),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      selectedCar = _useState4[0],
      setSelectedCar = _useState4[1];
    (0, _react.useEffect)(function () {
      if (getMyCarLocationPayload && !(0, _lodash.isEmpty)(getMyCarLocationPayload == null ? undefined : getMyCarLocationPayload.location)) {
        setVisibleMyCarInMap(true);
      }
    }, [getMyCarLocationPayload]);
    var resetDataMyCarInMap = function resetDataMyCarInMap() {
      dispatch(_airportLandingRedux.default.getMyCarLocationReset());
      setVisibleMyCarInMap(false);
    };
    var requestLocation = function requestLocation(item) {
      setSelectedCar(item);
      dispatch(_airportLandingRedux.default.getMyCarLocationRequest(item == null ? undefined : item.slotID, item == null ? undefined : item.level));
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppCarparkFindMyCarSelectCar, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppCarparkFindMyCarSelectCar, "1"));
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.root,
      children: [(0, _jsxRuntime.jsx)(_text.Text, {
        tx: "carParkScreen.findMyCarTab.selectYourVehicle",
        preset: "h4"
      }), listVehice == null || (_listVehice$items = listVehice.items) == null ? undefined : _listVehice$items.map(function (element) {
        return (0, _jsxRuntime.jsx)(ItemVehice, {
          element: element,
          onSelect: function onSelect(item) {
            return requestLocation(item);
          }
        }, element == null ? undefined : element.slotID);
      }), (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: getMyCarLocationFetching
      }), (0, _jsxRuntime.jsx)(_myCarInMap.default, {
        visible: visibleMyCarInMap,
        onCloseModal: function onCloseModal() {
          return resetDataMyCarInMap();
        },
        url: getMyCarLocationPayload == null || (_getMyCarLocationPayl = getMyCarLocationPayload.location) == null ? undefined : _getMyCarLocationPayl.mapImgURL,
        xPosition: getMyCarLocationPayload == null || (_getMyCarLocationPayl2 = getMyCarLocationPayload.location) == null ? undefined : _getMyCarLocationPayl2.x2,
        yPosition: getMyCarLocationPayload == null || (_getMyCarLocationPayl3 = getMyCarLocationPayload.location) == null ? undefined : _getMyCarLocationPayl3.y2,
        terminal: selectedCar == null ? undefined : selectedCar.buildingName,
        level: selectedCar == null ? undefined : selectedCar.level,
        spaceNumber: selectedCar == null ? undefined : selectedCar.slotName
      })]
    });
  };
  var _default = exports.default = SelectYourVehice;
  var ItemVehice = function ItemVehice(_ref2) {
    var element = _ref2.element,
      onSelect = _ref2.onSelect;
    var renderVehiceImage = !!(element != null && element.img_Url) ? (0, _jsxRuntime.jsx)(_baseImage.default, {
      source: {
        uri: element == null ? undefined : element.img_Url
      },
      style: styles.imageItemVehice
    }) : (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.noImageItemVehice,
      children: [(0, _jsxRuntime.jsx)(_icons.SadFace, {
        width: 12,
        height: 12
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        tx: "carParkScreen.findMyCarTab.unableToLoadImage",
        style: styles.noImageItemVehiceText
      })]
    });
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [renderVehiceImage, (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.wrapInformation,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          text: element == null ? undefined : element.licenseNumber,
          preset: "bodyTextBold",
          style: styles.licenseNumber
        }), (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.touchableSelect,
          onPress: function onPress() {
            return onSelect(element);
          },
          testID: `${COMPONENT_NAME}SelectVehice`,
          accessibilityLabel: `${COMPONENT_NAME}SelectVehice`,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "carParkScreen.findMyCarTab.select",
            style: styles.textSelect
          }), (0, _jsxRuntime.jsx)(_icons.ArrowRight, {})]
        })]
      })]
    }, element == null ? undefined : element.slotID);
  };
  var styles = _reactNative2.StyleSheet.create({
    container: Object.assign({
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderRadius: 16,
      height: 158,
      marginTop: 70,
      width: "100%"
    }, _reactNative2.Platform.select({
      ios: {
        shadowRadius: 2,
        shadowOpacity: 0.08,
        shadowOffset: {
          width: 0,
          height: 3
        }
      },
      android: {
        elevation: 3
      }
    })),
    imageItemVehice: {
      borderRadius: 16,
      height: 154,
      marginHorizontal: 24,
      marginTop: -48
    },
    noImageItemVehice: {
      borderRadius: 16,
      height: 154,
      marginHorizontal: 24,
      marginTop: -48,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: _theme.color.palette.lighterGrey
    },
    noImageItemVehiceText: Object.assign({
      marginTop: 6
    }, _text.newPresets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    licenseNumber: {
      color: _theme.color.palette.almostBlackGrey
    },
    root: {
      paddingBottom: 50,
      paddingHorizontal: 24,
      paddingTop: 30
    },
    textSelect: Object.assign({}, _button.textPresets.buttonSmall, {
      marginRight: 8
    }),
    touchableSelect: {
      alignItems: "center",
      flexDirection: "row"
    },
    wrapInformation: {
      alignItems: "center",
      bottom: 16,
      flexDirection: "row",
      justifyContent: "space-between",
      paddingHorizontal: 24,
      position: "absolute",
      width: "100%"
    }
  });
