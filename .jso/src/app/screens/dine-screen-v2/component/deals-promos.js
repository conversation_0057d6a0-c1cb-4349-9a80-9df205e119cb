  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.DealsPromos = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _native = _$$_REQUIRE(_dependencyMap[6]);
  var _miffyGamificationBanner = _$$_REQUIRE(_dependencyMap[7]);
  var _color = _$$_REQUIRE(_dependencyMap[8]);
  var _typography = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[12]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[13]);
  var _reactNativeSnapCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _staffPerkPromotionDetailController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _errorSectionV = _$$_REQUIRE(_dependencyMap[16]);
  var _i18n = _$$_REQUIRE(_dependencyMap[17]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[18]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SnapCarousel = _reactNativeSnapCarousel.default;
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    screenWidth = _Dimensions$get.width;
  var ITEM_WIDTH = 194.4;
  var ITEM_HEIGHT = 129.6;
  var Loading = function Loading() {
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.viewContent,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
        shimmerStyle: [styles.loadingDescriptionStyle, {
          marginLeft: 20
        }]
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
        shimmerStyle: styles.loadingDescriptionStyle
      }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: _miffyGamificationBanner.LOADING_COLORS,
        shimmerStyle: styles.loadingDescriptionStyle
      })]
    });
  };
  var DealsPromos = exports.DealsPromos = _react.default.memo(function (props) {
    var data = props.data,
      isLoading = props.isLoading,
      isError = props.isError,
      navigation = props.navigation,
      onReload = props.onReload;
    var carouselRef = (0, _react.useRef)(null);
    var calculateDataValue = function calculateDataValue(promos) {
      if (!(promos != null && promos.length)) return [];
      var length = promos.length;
      if (length === 1) {
        return promos; // Keep 1 item
      } else if (length === 2) {
        return [].concat((0, _toConsumableArray2.default)(promos), (0, _toConsumableArray2.default)(promos), (0, _toConsumableArray2.default)(promos)); // Multiply 3 times = 6 items
      } else if (length >= 3 && length <= 5) {
        return [].concat((0, _toConsumableArray2.default)(promos), (0, _toConsumableArray2.default)(promos));
      } else {
        return promos.slice(0, 8); // Keep the rest (maximum 8)
      }
    };

    // Fix: Crash https://github.com/meliorence/react-native-snap-carousel/issues/586 
    var dataValue = (0, _react.useMemo)(function () {
      return calculateDataValue(data == null ? undefined : data.promos);
    }, [data == null ? undefined : data.promos]);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      var _carouselRef$current;
      (_carouselRef$current = carouselRef.current) == null || _carouselRef$current.startAutoplay();
      return function () {
        var _carouselRef$current2;
        return (_carouselRef$current2 = carouselRef.current) == null ? undefined : _carouselRef$current2.stopAutoplay();
      };
    }, []));

    // Pause auto scroll when user interacts
    var handleScrollBeginDrag = function handleScrollBeginDrag() {
      var _carouselRef$current3;
      (_carouselRef$current3 = carouselRef.current) == null || _carouselRef$current3.stopAutoplay();
    };

    // Resume auto scroll after user stops interacting
    var handleScrollEndDrag = function handleScrollEndDrag() {
      var _carouselRef$current4;
      (_carouselRef$current4 = carouselRef.current) == null || _carouselRef$current4.startAutoplay();
    };
    var handleClickItem = function handleClickItem(item) {
      _staffPerkPromotionDetailController.default.showModal(navigation, {
        item: item
      });
    };
    var renderCarouselItem = function renderCarouselItem(_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        style: styles.carouselItemContainer,
        onPress: function onPress() {
          handleClickItem(item);
        },
        children: (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: styles.image,
          source: {
            uri: item == null ? undefined : item.imageUrl
          },
          resizeMode: "cover"
        })
      });
    };
    var renderConetent = function renderConetent() {
      if (isLoading) {
        return (0, _jsxRuntime.jsx)(Loading, {});
      } else if (isError) {
        return (0, _jsxRuntime.jsx)(_errorSectionV.ErrorSectionV2, {
          reload: true,
          onReload: onReload,
          extendCode: "EHR1.1",
          containerStyle: styles.errorContainer,
          reloadButtonStyleOverride: styles.reloadButtonStyle,
          reloadButtonTextStyleOverride: styles.reloadButtonTextStyle,
          textContainerStyle: styles.textContainerStyle,
          reloadButtonInnerStyleOverride: styles.reloadButtonInnerStyle,
          title: (0, _i18n.translate)("errorOverlay.variant1.title"),
          message: (0, _i18n.translate)("errorOverlay.variant1.message"),
          reloadText: (0, _i18n.translate)("errorOverlay.variant1.reload"),
          iconSize: 120
        });
      } else {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.carouselContainer,
          children: (0, _jsxRuntime.jsx)(SnapCarousel, {
            ref: carouselRef,
            data: dataValue,
            renderItem: renderCarouselItem,
            sliderWidth: screenWidth,
            itemWidth: ITEM_WIDTH,
            autoplay: (dataValue == null ? undefined : dataValue.length) > 1,
            autoplayInterval: 3000,
            autoplayDelay: 1000,
            loop: (dataValue == null ? undefined : dataValue.length) > 1,
            inactiveSlideScale: 0.833,
            loopClonesPerSide: 10,
            enableMomentum: false,
            lockScrollWhileSnapping: true,
            decelerationRate: 0.9,
            removeClippedSubviews: false,
            scrollEventThrottle: 16,
            onScrollBeginDrag: handleScrollBeginDrag,
            onScrollEndDrag: handleScrollEndDrag,
            keyExtractor: function keyExtractor(item, index) {
              return `${item.id}-${index}`;
            }
          })
        });
      }
    };
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.viewTitle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          tx: "dineScreenV2.dealsPromo.title",
          preset: isError ? "bodyTextBlack" : "caption1BoldSmall",
          style: {
            color: isError ? _color.color.palette.almostBlackGrey : _color.color.palette.darkestGrey
          }
        }), !isError && (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
          style: styles.viewRow,
          onPress: function onPress() {
            return navigation.navigate(_constants.NavigationConstants.dealsPromosListing);
          },
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            tx: "dineScreenV2.dealsPromo.seeAll",
            style: styles.txtButton
          }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
            color: _color.color.palette.lightPurple
          })]
        })]
      }), renderConetent()]
    });
  });
  var styles = _reactNative2.StyleSheet.create({
    container: {
      width: "100%",
      marginTop: 50,
      gap: 12
    },
    viewTitle: {
      paddingHorizontal: 20,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center"
    },
    viewRow: {
      flexDirection: "row",
      alignItems: "center"
    },
    txtButton: {
      fontFamily: _typography.typography.bold,
      color: _color.color.palette.lightPurple,
      fontSize: 12,
      fontWeight: _reactNative2.Platform.select({
        ios: "700",
        android: "normal"
      }),
      lineHeight: 16,
      marginRight: 8
    },
    viewContent: {
      width: "100%",
      flexDirection: "row",
      alignItems: "center",
      gap: 8
    },
    carouselContainer: {
      height: ITEM_HEIGHT
    },
    carouselItemContainer: {
      width: ITEM_WIDTH,
      height: ITEM_HEIGHT,
      borderRadius: 8,
      overflow: "hidden"
    },
    image: {
      width: ITEM_WIDTH,
      height: ITEM_HEIGHT
    },
    loadingDescriptionStyle: {
      width: 162,
      height: 108,
      borderRadius: 4
    },
    errorContainer: {
      marginTop: 24
    },
    reloadButtonStyle: {
      paddingLeft: 0,
      paddingRight: 0
    },
    reloadButtonTextStyle: {
      fontSize: 14,
      lineHeight: 18,
      color: _color.color.palette.whiteGrey
    },
    textContainerStyle: {
      paddingHorizontal: 20
    },
    reloadButtonInnerStyle: {
      height: undefined,
      paddingVertical: 5,
      paddingHorizontal: 12
    }
  });
