  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _toConsumableArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _filterBottomSheet = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[6]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[7]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _native = _$$_REQUIRE(_dependencyMap[11]);
  var _adobe = _$$_REQUIRE(_dependencyMap[12]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _dealsPromosFilterBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _dealsPromosTile = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _isEqual2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _dealsPromosCategoryListing = _$$_REQUIRE(_dependencyMap[17]);
  var _dealsPromosError = _$$_REQUIRE(_dependencyMap[18]);
  var _collapsibleHeader = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _dealsPromoListing = _$$_REQUIRE(_dependencyMap[20]);
  var _i18n = _$$_REQUIRE(_dependencyMap[21]);
  var _theme = _$$_REQUIRE(_dependencyMap[22]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[23]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var PERK_ITEM_FIXED_MARGIN = 12;
  var ItemComponent = (0, _react.memo)(function (_ref) {
    var dataLength = _ref.dataLength,
      index = _ref.index,
      item = _ref.item,
      navigation = _ref.navigation,
      offsetRecalculationCount = _ref.offsetRecalculationCount,
      perkItemOffsetListRef = _ref.perkItemOffsetListRef,
      setOffsetRecalculationCount = _ref.setOffsetRecalculationCount;
    return (0, _jsxRuntime.jsx)(_dealsPromosTile.default, {
      dataLength: dataLength,
      index: index,
      item: item,
      navigation: navigation,
      onLayout: function onLayout(event) {
        var _Object$keys;
        var layoutHeight = event.nativeEvent.layout.height + PERK_ITEM_FIXED_MARGIN;
        if (!perkItemOffsetListRef) return;
        if (!(perkItemOffsetListRef != null && perkItemOffsetListRef.current)) {
          perkItemOffsetListRef.current = (0, _defineProperty2.default)({}, index, layoutHeight);
        } else {
          perkItemOffsetListRef.current[index] = layoutHeight;
        }
        if (((_Object$keys = Object.keys(perkItemOffsetListRef.current)) == null ? undefined : _Object$keys.length) === dataLength) {
          setOffsetRecalculationCount(offsetRecalculationCount + 1);
        }
      }
    });
  }, function (prevProps, nextProps) {
    return !(0, _isEqual2.default)(prevProps, nextProps);
  });
  var DealsPromosListing = function DealsPromosListing(_ref2) {
    var navigation = _ref2.navigation;
    var rootItemOffsetRef = (0, _react.useRef)([]);
    var rootListRef = (0, _react.useRef)(null);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)([]),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      areaFilters = _useState2[0],
      setAreaFilters = _useState2[1];
    var _useState3 = (0, _react.useState)([]),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      terminalFilters = _useState4[0],
      setTerminalFilters = _useState4[1];
    var _useState5 = (0, _react.useState)([]),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      categoryFilters = _useState6[0],
      setCategoryFilters = _useState6[1];
    var _useState7 = (0, _react.useState)(_filterBottomSheet.SortBy.LatestAddedDate),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      sortBy = _useState8[0],
      setSortBy = _useState8[1];
    var _useState9 = (0, _react.useState)(0),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      offsetRecalculationCount = _useState0[0],
      setOffsetRecalculationCount = _useState0[1];
    var perkItemOffsetListRef = (0, _react.useRef)([]);
    var _useDealsPromosListin = (0, _dealsPromosCategoryListing.useDealsPromosListingRequests)(),
      listData = _useDealsPromosListin.listData,
      loadingDealsPromosList = _useDealsPromosListin.loadingDealsPromosList,
      hasError = _useDealsPromosListin.hasError,
      handlePressReloadError = _useDealsPromosListin.handlePressReloadError,
      setListData = _useDealsPromosListin.setListData,
      originalListData = _useDealsPromosListin.originalListData,
      onRefresh = _useDealsPromosListin.onRefresh;
    (0, _react.useEffect)(function () {
      var filteredData = originalListData;

      // Apply filters if any are active
      if (areaFilters != null && areaFilters.length || terminalFilters != null && terminalFilters.length || categoryFilters != null && categoryFilters.length) {
        // Pre-compute filter sets for O(1) average time complexity lookups.
        var areaNamesSet = (areaFilters == null ? undefined : areaFilters.length) > 0 ? new Set(areaFilters.map(function (a) {
          return a.tagName;
        })) : null;
        var terminalCodesSet = (terminalFilters == null ? undefined : terminalFilters.length) > 0 ? new Set(terminalFilters.map(function (t) {
          return t.tagCode;
        })) : null;
        var categoryFiltersSet = (categoryFilters == null ? undefined : categoryFilters.length) > 0 ? new Set(categoryFilters) : null;

        // Pre-compute special terminal conditions to avoid repeated lookups inside the loop.
        var hasJewelTerminal = (terminalCodesSet == null ? undefined : terminalCodesSet.has("jewel")) || (terminalCodesSet == null ? undefined : terminalCodesSet.has("tj"));
        var hasJ1Terminal = terminalCodesSet == null ? undefined : terminalCodesSet.has("j1");
        var hasAllTerminal = terminalCodesSet == null ? undefined : terminalCodesSet.has("all");

        // A single pass over the data combines all filtering logic.
        filteredData = originalListData.filter(function (item) {
          // Category filter check (AND logic) - must be satisfied if categoryFilters exist
          if (categoryFiltersSet) {
            var _itemCategories;
            // Parse categories string to array if it's a string
            var itemCategories = item.categories;
            if (typeof itemCategories === "string") {
              // Parse string like "[SHOPPING_PERKS, NEW_STAFF_PERKS]" to array
              itemCategories = itemCategories.replace(/[\[\]]/g, "") // Remove square brackets
              .split(",").map(function (cat) {
                return cat.trim();
              }).filter(function (cat) {
                return cat.length > 0;
              });
            }
            if (!((_itemCategories = itemCategories) != null && _itemCategories.some(function (category) {
              return categoryFiltersSet.has(category);
            }))) {
              return false;
            }
          }

          // Location filter check (OR logic between area and terminal)
          var hasAreaFilters = areaNamesSet && areaNamesSet.size > 0;
          var hasTerminalFilters = terminalCodesSet && terminalCodesSet.size > 0 && !hasAllTerminal;
          if (hasAreaFilters || hasTerminalFilters) {
            var _item$area;
            var hasMatchingLocation = false;

            // Check area filters
            if (hasAreaFilters && (_item$area = item.area) != null && _item$area.some(function (area) {
              return areaNamesSet.has(area);
            })) {
              hasMatchingLocation = true;
            }

            // Check terminal filters (if area check didn't pass or no area filters)
            if (hasTerminalFilters && !hasMatchingLocation) {
              var _item$terminal;
              var hasMatchingTerminal = (_item$terminal = item.terminal) == null ? undefined : _item$terminal.some(function (terminal) {
                if (terminalCodesSet.has(terminal)) return true;
                if (hasJewelTerminal && terminal === "j1") return true;
                if (hasJ1Terminal && (terminal === "jewel" || terminal === "tj")) return true;
                return false;
              });
              if (hasMatchingTerminal) {
                hasMatchingLocation = true;
              }
            }

            // If we have location filters but no match found, exclude the item
            if (!hasMatchingLocation) {
              return false;
            }
          }
          return true;
        });
      }

      // Apply sorting based on sortBy
      var sortedData = (0, _toConsumableArray2.default)(filteredData);
      if (sortBy === _filterBottomSheet.SortBy.AZ) {
        // Sort by tenantName in ascending order
        sortedData.sort(function (a, b) {
          var tenantNameA = (a.tenantName || "").toLowerCase();
          var tenantNameB = (b.tenantName || "").toLowerCase();
          return tenantNameA.localeCompare(tenantNameB);
        });
      }
      // For SortBy.LatestAddedDate, keep original order (no sorting needed)

      setListData(sortedData);
    }, [originalListData, areaFilters, terminalFilters, categoryFilters, sortBy]);

    // Reset perkItemOffsetListRef when sortBy changes to force recalculation
    (0, _react.useEffect)(function () {
      perkItemOffsetListRef.current = [];
      setOffsetRecalculationCount(0);
    }, [sortBy]);
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.DealsPromosListing);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.DealsPromosListing, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
    }, [navigation, isLoggedIn]));
    var renderFilterBar = function renderFilterBar() {
      return (0, _jsxRuntime.jsx)(_dealsPromosFilterBar.default, {
        isShow: !loadingDealsPromosList && !hasError,
        areaFilters: areaFilters,
        setAreaFilters: setAreaFilters,
        terminalFilters: terminalFilters,
        setTerminalFilters: setTerminalFilters,
        categoryFilters: categoryFilters,
        setCategoryFilters: setCategoryFilters,
        sortBy: sortBy,
        setSortBy: setSortBy
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: styles.container,
      children: (0, _jsxRuntime.jsx)(_collapsibleHeader.default, {
        navigation: navigation,
        headerImageSource: _dealsPromoListing.DealsPromoListingBackground,
        headerTitle: (0, _i18n.translate)("dealsPromosScreen.title"),
        renderFilter: renderFilterBar,
        hasError: !!hasError,
        renderError: function renderError() {
          return (0, _jsxRuntime.jsx)(_dealsPromosError.DealsPromosError, {
            typeError: hasError,
            handlePressReload: handlePressReloadError,
            rootItemOffsetRef: rootItemOffsetRef
          });
        },
        isLoading: loadingDealsPromosList,
        sortBy: sortBy,
        listData: listData,
        perkItemOffsetListRef: perkItemOffsetListRef,
        rootItemOffsetRef: rootItemOffsetRef,
        rootListRef: rootListRef,
        data: listData,
        renderItem: function renderItem(_ref3) {
          var index = _ref3.index,
            item = _ref3.item;
          return (0, _jsxRuntime.jsx)(ItemComponent, {
            dataLength: listData == null ? undefined : listData.length,
            index: index,
            item: item,
            navigation: navigation,
            offsetRecalculationCount: offsetRecalculationCount,
            perkItemOffsetListRef: perkItemOffsetListRef,
            setOffsetRecalculationCount: setOffsetRecalculationCount
          });
        },
        keyExtractor: function keyExtractor(item, index) {
          return `${item == null ? undefined : item.id}-${sortBy}-${index}`;
        },
        showsVerticalScrollIndicator: false,
        contentContainerStyle: styles.contentContainerStyle,
        ItemSeparatorComponent: !loadingDealsPromosList && hasError === null ? function () {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: styles.itemSeparator
          });
        } : null,
        initialNumToRender: 1000,
        maxToRenderPerBatch: 50,
        windowSize: 50,
        refreshControl: (0, _jsxRuntime.jsx)(_reactNative.RefreshControl, {
          refreshing: false,
          onRefresh: onRefresh,
          progressViewOffset: -300
        }),
        scrollEnabled: listData.length > 0
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      flex: 1
    },
    contentContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      paddingBottom: 24
    },
    itemSeparator: {
      height: 12
    }
  });
  var _default = exports.default = DealsPromosListing;
