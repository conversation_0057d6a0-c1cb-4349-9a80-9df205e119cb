  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _walletRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[5]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[11]);
  var _icons = _$$_REQUIRE(_dependencyMap[12]);
  var _loadingModal = _$$_REQUIRE(_dependencyMap[13]);
  var _native = _$$_REQUIRE(_dependencyMap[14]);
  var _adobe = _$$_REQUIRE(_dependencyMap[15]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[18]);
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[19]);
  var _lodash = _$$_REQUIRE(_dependencyMap[20]);
  var _constants = _$$_REQUIRE(_dependencyMap[21]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[22]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _exploreItemType = _$$_REQUIRE(_dependencyMap[25]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[26]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var CONTAINER = Object.assign({}, _reactNative2.Platform.select({
    ios: {
      shadowRadius: 2,
      shadowOpacity: 0.08,
      shadowOffset: {
        width: 0,
        height: 3
      },
      borderRadius: 20,
      backgroundColor: _theme.color.palette.whiteGrey
    },
    android: {
      elevation: 3,
      borderRadius: 20,
      backgroundColor: _theme.color.palette.whiteGrey
    }
  }));
  var SCREEN_NAME = "DETAIL_WALLET_PERK";
  var DetailPerkScreen = function DetailPerkScreen(_ref) {
    var route = _ref.route;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var onBack = function onBack() {
      if (onBackScreen) {
        onBackScreen();
      }
      navigation.goBack();
    };
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _ref2 = (route == null ? undefined : route.params) || {},
      item = _ref2.item,
      onBackScreen = _ref2.onBackScreen;
    var walletMyDetailPerksFetching = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyDetailPerksFetching);
    var walletMyDetailPerks = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyDetailPerks);
    var walletMyDetailPerksError = (0, _reactRedux.useSelector)(_walletRedux.WalletSelectors.walletMyDetailPerksError);
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setIsConnected = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isShowError = _useState4[0],
      setIsShowError = _useState4[1];
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      loadingScreen = _useState6[0],
      setLoadingScreen = _useState6[1];
    var _useGeneratePlayPassU = (0, _screenHook.useGeneratePlayPassUrl)("detailEventPerk", true, undefined, onBack, onBack, true),
      getEventDetailPerk = _useGeneratePlayPassU.getPlayPassUrl,
      loadingEventDetailPerk = _useGeneratePlayPassU.loading;
    var _useGeneratePlayPassU2 = (0, _screenHook.useGeneratePlayPassUrl)("detailEventPerkMoreEvent", false, undefined, onBack, onBack, true),
      getEventDetailPerkMoreEvent = _useGeneratePlayPassU2.getPlayPassUrl,
      loadingEventDetailPerkMoreEvent = _useGeneratePlayPassU2.loading;
    (0, _react.useEffect)(function () {
      if ((0, _lodash.size)(walletMyDetailPerks) === 1) {
        var _walletMyDetailPerks$, _walletMyDetailPerks$2;
        if (((_walletMyDetailPerks$ = walletMyDetailPerks[0]) == null ? undefined : _walletMyDetailPerks$.visible_flag) === "false") {
          setLoadingScreen(true);
          if (onBackScreen) {
            onBackScreen();
          }
          navigation == null || navigation.pop();
          dispatch(_walletRedux.default.resetWalletMyDetailPerks());
          _changiEcardControler.default.showModal(navigation);
        } else if (((_walletMyDetailPerks$2 = walletMyDetailPerks[0]) == null ? undefined : _walletMyDetailPerks$2.visible_flag) === "true") {
          var _walletMyDetailPerks$3;
          setLoadingScreen(true);
          handleOpenEventDetail((_walletMyDetailPerks$3 = walletMyDetailPerks[0]) == null ? undefined : _walletMyDetailPerks$3.package_cd, walletMyDetailPerks[0].package_name);
        }
      } else if ((0, _lodash.size)(walletMyDetailPerks) > 1) {
        var listPackageCd = [];
        var listPackgename = [];
        for (var i = 0; i < (0, _lodash.size)(walletMyDetailPerks); i++) {
          var _walletMyDetailPerks$4;
          if (((_walletMyDetailPerks$4 = walletMyDetailPerks[i]) == null ? undefined : _walletMyDetailPerks$4.visible_flag) === "true") {
            var _walletMyDetailPerks$5, _walletMyDetailPerks$6;
            listPackageCd.push((_walletMyDetailPerks$5 = walletMyDetailPerks[i]) == null ? undefined : _walletMyDetailPerks$5.package_cd);
            listPackgename.push((_walletMyDetailPerks$6 = walletMyDetailPerks[i]) == null ? undefined : _walletMyDetailPerks$6.package_name);
          }
        }
        if ((0, _lodash.isEmpty)(listPackageCd)) {
          setLoadingScreen(true);
          if (onBackScreen) {
            onBackScreen();
          }
          navigation == null || navigation.pop();
          dispatch(_walletRedux.default.resetWalletMyDetailPerks());
          _changiEcardControler.default.showModal(navigation);
        } else if ((0, _lodash.size)(listPackageCd) === 1) {
          setLoadingScreen(true);
          handleOpenEventDetail(listPackageCd[0], listPackgename[0]);
        }
      }
    }, [walletMyDetailPerks, onBackScreen]);
    var handleOpenEventDetail = function handleOpenEventDetail(packageCode, packageName) {
      var _walletMyDetailPerks$7;
      var cookiesData = {
        entryPoint: _exploreItemType.PlayPassEntryPoint.PERK_PAGE,
        perkTitle: packageName
      };
      getEventDetailPerk(_constants.StateCode.PPEVENT, packageCode || ((_walletMyDetailPerks$7 = walletMyDetailPerks[0]) == null ? undefined : _walletMyDetailPerks$7.package_cd), cookiesData);
      dispatch(_walletRedux.default.resetWalletMyDetailPerks());
    };
    var handleOpenEventDetailMoreEvent = function handleOpenEventDetailMoreEvent(packageCode, packageName) {
      var _walletMyDetailPerks$8;
      var cookiesData = {
        entryPoint: _exploreItemType.PlayPassEntryPoint.PERK_DETAILS,
        perkTitle: packageName
      };
      getEventDetailPerkMoreEvent(_constants.StateCode.PPEVENT, packageCode || ((_walletMyDetailPerks$8 = walletMyDetailPerks[0]) == null ? undefined : _walletMyDetailPerks$8.package_cd), cookiesData);
      dispatch(_walletRedux.default.resetWalletMyDetailPerks());
    };
    var fetchData = function fetchData() {
      var params = {
        token_type: "",
        package_cd: "",
        campaign_cd: "",
        perk_type: ""
      };
      if ((item == null ? undefined : item.perk_type) === "ChangiRewardPerk") {
        params.package_cd = item == null ? undefined : item.perk_code;
      } else if ((item == null ? undefined : item.perk_type) === "PlaypassPerk") {
        params.token_type = item == null ? undefined : item.token_type;
        params.perk_type = _constants.PerkType.PlaypassPerk;
      }
      dispatch(_walletRedux.default.walletMyDetailPerksRequest(params));
    };
    (0, _react.useEffect)(function () {
      if (!(0, _lodash.isEmpty)(walletMyDetailPerksError)) {
        setIsShowError(true);
      }
    }, [walletMyDetailPerksError]);
    var perkTitle = Number(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(`Perk_Detail_${perkTitle}`);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(`Perk_Detail_${perkTitle}`, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
        var checkInternet = /*#__PURE__*/function () {
          var _ref3 = (0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
              isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
            if (!isConnectedNetInfo) {
              setIsConnected(false);
            } else {
              fetchData();
            }
          });
          return function checkInternet() {
            return _ref3.apply(this, arguments);
          };
        }();
        checkInternet();
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useLayoutEffect)(function () {
      navigation.setOptions({
        header: function header() {
          return false;
        }
      });
    }, [navigation]);
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var onBackPress = function onBackPress() {
        onBack();
        return true;
      };
      if (onBackScreen) {
        navigation.setOptions({
          gestureEnabled: false
        });
      }
      var subscription = _reactNative2.BackHandler.addEventListener("hardwareBackPress", onBackPress);
      return function () {
        return subscription.remove();
      };
    }, []));
    var dataPerk = (0, _react.useMemo)(function () {
      if ((0, _lodash.isEmpty)(walletMyDetailPerks) || !(0, _lodash.isArray)(walletMyDetailPerks)) return [];
      return walletMyDetailPerks == null ? undefined : walletMyDetailPerks.filter(function (e) {
        return (e == null ? undefined : e.visible_flag) === "true";
      });
    }, [walletMyDetailPerks]);
    var itemPerk = function itemPerk(element, index) {
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        style: styles.touchableItemPerk,
        onPress: function onPress() {
          return handleOpenEventDetailMoreEvent(element == null ? undefined : element.package_cd, element == null ? undefined : element.package_name);
        },
        children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
          source: {
            uri: element == null ? undefined : element.package_listing_img_url
          },
          style: styles.imageItemPerk
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: styles.contentItemPerk,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: element == null ? undefined : element.package_name,
            preset: "bodyTextBold",
            style: styles.bodyTextBold
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              text: `${(0, _moment.default)(element == null ? undefined : element.package_start).format(_dateTime.DateFormats.DateMonth)} - ${(0, _moment.default)(element == null ? undefined : element.package_end).format(_dateTime.DateFormats.DayMonthYear)} · From ${element == null ? undefined : element.price}`,
              preset: "bodyTextRegular",
              style: styles.bodyTextRegularContent
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: `${element == null ? undefined : element.event_location}`,
              preset: "caption1Regular",
              style: styles.caption1Regular
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: styles.markPerk,
            children: [(0, _jsxRuntime.jsx)(_icons.StarPerk, {
              color: _theme.color.palette.whiteGrey
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              text: "PERKS",
              style: styles.textMarkPerk,
              preset: "XSmallBold"
            })]
          })]
        })]
      }, index);
    };
    if (walletMyDetailPerksFetching || loadingEventDetailPerk || loadingEventDetailPerkMoreEvent || loadingScreen) {
      return (0, _jsxRuntime.jsx)(_loadingModal.LoadingOverlay, {
        visible: true
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        translucent: true,
        backgroundColor: "transparent"
      }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlayHeader, {
        title: Number(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular,
        testID: `${SCREEN_NAME}__HeaderDetailComponent`,
        accessibilityLabel: `${SCREEN_NAME}__HeaderDetailComponent`,
        containerStyle: styles.containerHeaderStyle,
        onBack: onBack
      }), (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
        style: styles.safeAreaView,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          contentContainerStyle: styles.contentContainerStyle,
          showsVerticalScrollIndicator: false,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            text: `You have unlocked ${Number(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular}!`,
            preset: "h1",
            style: styles.h1
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            preset: "bodyTextRegular",
            text: `You have unlocked ${item == null ? undefined : item.token_qty} ${Number(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular}! Choose which events you want to redeem.`,
            style: styles.bodyTextRegular
          }), dataPerk == null ? undefined : dataPerk.map(function (element, index) {
            return itemPerk(element, index);
          })]
        }), (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: true,
          header: true,
          headerText: Number(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular,
          headerBackgroundColor: "transparent",
          visible: !isConnected,
          testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
          onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
              isConnectedNetInfo = _yield$NetInfo$fetch2.isConnected;
            if (isConnectedNetInfo) {
              fetchData();
              setIsConnected(false);
            }
          })
        }), (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
          reload: true,
          header: false,
          headerText: Number(item == null ? undefined : item.token_qty) > 1 ? item == null ? undefined : item.token_name_plural : item == null ? undefined : item.token_name_singular,
          hideScreenHeader: false,
          visible: isShowError,
          onReload: /*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
            var _yield$NetInfo$fetch3 = yield _netinfo.default.fetch(),
              isConnectedNetInfo = _yield$NetInfo$fetch3.isConnected;
            if (isConnectedNetInfo) {
              fetchData();
              setIsShowError(false);
            }
          }),
          onBack: function onBack() {
            navigation.goBack();
          },
          testID: `${SCREEN_NAME}__ErrorOverlay`,
          accessibilityLabel: `${SCREEN_NAME}__ErrorOverlay`,
          variant: _errorOverlay.ErrorOverlayVariant.VARIANT1,
          ignoreShowNoInternet: true
        })]
      })]
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    bodyTextBold: {
      color: _theme.color.palette.almostBlackGrey
    },
    bodyTextRegular: {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 24,
      marginTop: 16
    },
    bodyTextRegularContent: {
      color: _theme.color.palette.almostBlackGrey,
      paddingVertical: 8
    },
    caption1Regular: {
      color: _theme.color.palette.darkestGrey,
      width: "80%"
    },
    container: {
      flex: 1
    },
    containerHeaderStyle: {
      flex: 0,
      position: "relative"
    },
    contentContainerStyle: {
      padding: 24
    },
    contentItemPerk: {
      padding: 16
    },
    h1: {
      color: _theme.color.palette.almostBlackGrey
    },
    imageItemPerk: {
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      height: 140,
      width: "100%"
    },
    markPerk: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.baseBlue,
      borderRadius: 4,
      bottom: 13,
      flexDirection: "row",
      paddingHorizontal: 8.5,
      paddingVertical: 6.5,
      position: "absolute",
      right: 16
    },
    safeAreaView: {
      backgroundColor: _theme.color.palette.whiteGrey,
      flex: 1
    },
    textMarkPerk: {
      color: _theme.color.palette.whiteGrey,
      marginLeft: 4.5
    },
    touchableItemPerk: Object.assign({}, CONTAINER, {
      marginBottom: 12
    })
  });
  var _default = exports.default = DetailPerkScreen;
