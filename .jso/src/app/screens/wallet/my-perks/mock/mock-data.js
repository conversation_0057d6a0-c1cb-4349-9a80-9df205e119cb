  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.mockData = undefined;
  var mockData = exports.mockData = [{
    catalog_code: "",
    expiry_dt: "2023-09-30",
    pending_description: null,
    perk_code: "COLLINCB",
    perk_type: "ChangiRewardPerk",
    privileges_type: "Instant Promo Code Issuance",
    token_img_url: "https://edmsource.ascentismedia.com/uatresource/VoucherTypeImage/CAG/COLLINCB.png",
    token_name_plural: null,
    token_name_singular: "Complimentary Premium Iced Tea at COLLIN'S®",
    token_qty: 1,
    token_type: null,
    validated_flg: null,
    wallet_description: null
  }, {
    catalog_code: "",
    expiry_dt: "2023-09-30",
    pending_description: null,
    perk_code: "COLLINCB",
    perk_type: "ChangiRewardPerk",
    privileges_type: "Instant Promo Code Issuance",
    token_img_url: "https://edmsource.ascentismedia.com/uatresource/VoucherTypeImage/CAG/COLLINCB.png",
    token_name_plural: null,
    token_name_singular: "Complimentary Premium Iced Tea at COLLIN'S®",
    token_qty: 1,
    token_type: null,
    validated_flg: null,
    wallet_description: null
  }, {
    catalog_code: "",
    expiry_dt: "2023-09-30",
    pending_description: null,
    perk_code: "COLLINCB",
    perk_type: "ChangiRewardPerk",
    privileges_type: "Instant Promo Code Issuance",
    token_img_url: "https://edmsource.ascentismedia.com/uatresource/VoucherTypeImage/CAG/COLLINCB.png",
    token_name_plural: null,
    token_name_singular: "Complimentary Premium Iced Tea at COLLIN'S®",
    token_qty: 1,
    token_type: null,
    validated_flg: null,
    wallet_description: null
  }, {
    catalog_code: "",
    expiry_dt: "2023-09-30",
    pending_description: null,
    perk_code: "COLLINCB",
    perk_type: "ChangiRewardPerk",
    privileges_type: "Instant Promo Code Issuance",
    token_img_url: "https://edmsource.ascentismedia.com/uatresource/VoucherTypeImage/CAG/COLLINCB.png",
    token_name_plural: null,
    token_name_singular: "Complimentary Premium Iced Tea at COLLIN'S®",
    token_qty: 1,
    token_type: null,
    validated_flg: null,
    wallet_description: null
  }];
