  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.PerkTypes = exports.PerkCodes = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _myPerks = _$$_REQUIRE(_dependencyMap[6]);
  var _text = _$$_REQUIRE(_dependencyMap[7]);
  var _mockData = _$$_REQUIRE(_dependencyMap[8]);
  var _forEach = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _perks = _$$_REQUIRE(_dependencyMap[10]);
  var _adobe = _$$_REQUIRE(_dependencyMap[11]);
  var _utils = _$$_REQUIRE(_dependencyMap[12]);
  var _changiEcardControler = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _constants = _$$_REQUIRE(_dependencyMap[14]);
  var _globalLoadingController = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[16]);
  var COMPONENT_NAME = "List_My_Pass";
  var testID = "List_My_Pass";
  var accessibilityLabel = "List_My_Pass";
  var _Dimensions$get = _reactNative2.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var PerkTypes = exports.PerkTypes = /*#__PURE__*/function (PerkTypes) {
    PerkTypes["CpmsPerk"] = "CpmsPerk";
    PerkTypes["PlaypassPerk"] = "PlaypassPerk";
    PerkTypes["ChangiRewardPerk"] = "ChangiRewardPerk";
    PerkTypes["CRBDayGift"] = "CRBDayGift";
    PerkTypes["InstantWinPrize"] = "InstantWinPrize";
    return PerkTypes;
  }({});
  var PerkCodes = exports.PerkCodes = /*#__PURE__*/function (PerkCodes) {
    PerkCodes["CRBDayGift"] = "CRBDayGift";
    PerkCodes["CRXmas2023active"] = "CRXmas2023active";
    PerkCodes["CRXmas2023inactive"] = "CRXmas2023inactive";
    PerkCodes["eVoucher52023"] = "eVoucherCNY5-2023";
    PerkCodes["ignoreFeatureFlag"] = "cr_vouchers_campaign";
    return PerkCodes;
  }({});
  var MyPerksList = function MyPerksList(props) {
    var data = props.data,
      showSkeleton = props.showSkeleton,
      isPast = props.isPast,
      loadMore = props.loadMore,
      totalData = props.totalData,
      navigation = props.navigation;
    var handleTag = function handleTag(item) {
      if (item.status === "redeemed") {
        return "Redeemed";
      } else {
        return "Expired";
      }
    };
    var handleActionForPerk = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* (item) {
        if (item.perk_type === PerkTypes.ChangiRewardPerk) {
          if (item != null && item.cr_vouchers_campaign) {
            navigation.navigate(_constants.NavigationConstants.yourReward, {
              item: item
            });
          } else {
            _globalLoadingController.default.showLoading(true);
            _globalLoadingController.default.hideLoading();
            if ((item == null ? undefined : item.redemption_mode) === null) {
              _changiEcardControler.default.showModal(navigation);
              return;
            }
            navigation.navigate(_constants.NavigationConstants.yourReward, {
              item: item
            });
          }
        } else if (item.perk_type === PerkTypes.CpmsPerk) {
          // do nothing
        } else if (item.perk_type === PerkTypes.InstantWinPrize) {
          navigation.navigate(_constants.NavigationConstants.webview, {
            uri: item == null ? undefined : item.redirectUrl
          });
        } else {
          navigation.navigate("detailPerkScreen", {
            item: item
          });
        }
      });
      return function handleActionForPerk(_x) {
        return _ref.apply(this, arguments);
      };
    }();
    var handlePressPerk = function handlePressPerk(item) {
      var perkTitle = (0, _utils.handleCondition)(Number(item == null ? undefined : item.token_qty) > 1, item == null ? undefined : item.token_name_plural, item == null ? undefined : item.token_name_singular);
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountFolioPerks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountFolioPerks, `Active Perks List | ${perkTitle || ""}`));
      handleActionForPerk(item);
    };
    var Item = function Item(_ref2) {
      var item = _ref2.item,
        index = _ref2.index;
      switch (item == null ? undefined : item.perk_type) {
        case PerkTypes.PlaypassPerk:
          return (0, _jsxRuntime.jsx)(_perks.Perks, {
            width: width - 48,
            isLoading: showSkeleton,
            category: "PERK",
            imageUrl: item == null ? undefined : item.token_img_url,
            onPressed: function onPressed() {
              return handlePressPerk(item);
            },
            testID: `${testID}__Perks__${index}`,
            accessibilityLabel: `${accessibilityLabel}__Perks__${index}`,
            tenantName: (0, _utils.handleCondition)(Number(item == null ? undefined : item.token_qty) > 1, item == null ? undefined : item.token_name_plural, item == null ? undefined : item.token_name_singular),
            validTillDate: item == null ? undefined : item.expiry_dt,
            isValidTillDate: true,
            addButton: false,
            ribbonText: item == null ? undefined : item.ribbonText,
            isDisabled: isPast,
            quantity: (0, _utils.handleCondition)(Number(item == null ? undefined : item.token_qty) && Number(item == null ? undefined : item.token_qty) > 0, `Qty: ${item == null ? undefined : item.token_qty}`, ""),
            tag: handleTag(item)
          });
        case PerkTypes.InstantWinPrize:
          return (0, _jsxRuntime.jsx)(_perks.Perks, {
            width: width - 48,
            isLoading: showSkeleton,
            category: item == null ? undefined : item.categoryLabel,
            imageUrl: item == null ? undefined : item.image,
            onPressed: function onPressed() {
              (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppAccountFolioPerks, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppAccountFolioPerks, `Active Perks List | ${(item == null ? undefined : item.title) || ""}`));
              handleActionForPerk(item);
            },
            testID: `${testID}__Perks__${index}`,
            accessibilityLabel: `${accessibilityLabel}__Perks__${index}`,
            tenantName: item == null ? undefined : item.title,
            validTillDate: item == null ? undefined : item.expiry_dt,
            isValidTillDate: true,
            isInstantWinPrize: true,
            addButton: false,
            ribbonText: item == null ? undefined : item.ribbonText,
            isDisabled: isPast,
            quantity: (0, _utils.handleCondition)(Number(item == null ? undefined : item.labelText) && Number(item == null ? undefined : item.labelText) > 0, `Qty: ${item == null ? undefined : item.labelText}`, ""),
            tag: handleTag(item)
          });
        default:
          return (0, _jsxRuntime.jsx)(_perks.Perks, {
            width: width - 48,
            isLoading: showSkeleton,
            category: item.category_name,
            imageUrl: item == null ? undefined : item.token_img_url,
            onPressed: function onPressed() {
              return handlePressPerk(item);
            },
            testID: `${testID}__Perks__${index}`,
            accessibilityLabel: `${accessibilityLabel}__Perks__${index}`,
            tenantName: (0, _utils.handleCondition)(Number(item == null ? undefined : item.token_qty) > 1, item == null ? undefined : item.token_name_plural, item == null ? undefined : item.token_name_singular),
            validTillDate: (item == null ? undefined : item.expiry) || (item == null ? undefined : item.expiry_dt),
            isValidTillDate: true,
            addButton: false,
            ribbonText: item == null ? undefined : item.ribbonText,
            isDisabled: isPast,
            quantity: (0, _utils.handleCondition)(Number(item == null ? undefined : item.token_qty) && Number(item == null ? undefined : item.token_qty) > 0, `Qty: ${item == null ? undefined : item.token_qty}`, ""),
            tag: handleTag(item),
            balance: item == null ? undefined : item.balance
          });
      }
    };
    var Footer = function Footer() {
      if (showSkeleton || (data == null ? undefined : data.length) >= (totalData == null ? undefined : totalData.length)) return null;
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
        onPress: loadMore,
        testID: `${COMPONENT_NAME}TouchableLoadMore`,
        accessibilityLabel: `${COMPONENT_NAME}TouchableLoadMore`,
        children: (0, _jsxRuntime.jsx)(_text.Text, {
          style: _myPerks.styles.loadMoreStyle,
          tx: "transactionsScreen.loadMore"
        })
      });
    };
    var headerItem = function headerItem() {
      if (!isPast) return null;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _myPerks.styles.headerItem,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _myPerks.styles.headerSectionStyle,
          testID: `${COMPONENT_NAME}HeaderText`,
          text: "PAST PERKS"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _myPerks.styles.headerSectionDescriptionStyle,
          testID: `${COMPONENT_NAME}HeaderText`,
          text: "These perks will be automatically deleted after 1 month"
        })]
      });
    };
    if (showSkeleton) {
      return (0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _myPerks.styles.spaceHorizontal,
        children: _mockData.mockData.map(function (e, i) {
          return (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _myPerks.styles.sepratorItem,
            children: (0, _jsxRuntime.jsx)(Item, {
              item: e,
              index: i
            })
          }, `mockData_${i}`);
        })
      });
    }
    var listArr = [];
    (0, _forEach.default)(data, function (e, i) {
      listArr.push((0, _jsxRuntime.jsx)(_reactNative2.View, {
        style: _myPerks.styles.sepratorItem,
        children: (0, _jsxRuntime.jsx)(Item, {
          item: e,
          index: i
        })
      }, `data_${i}`));
    });
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: _myPerks.styles.spaceHorizontal,
      children: [headerItem(), listArr, (0, _jsxRuntime.jsx)(Footer, {})]
    });
  };
  var _default = exports.default = MyPerksList;
