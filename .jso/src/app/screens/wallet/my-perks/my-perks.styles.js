  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    containerStyle: {
      flex: 1
    },
    emptyTransactionStyle: {
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 50,
      marginTop: 30,
      paddingHorizontal: 35
    },
    headerItem: {},
    headerSectionDescriptionStyle: Object.assign({}, _text.presets.caption1Regular, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 24
    }),
    headerSectionStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      marginBottom: 8
    }),
    headerText: {},
    loadMoreStyle: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple,
      marginTop: 25,
      textAlign: "center"
    }),
    marginContentBottom: {
      height: 80
    },
    scrollViewContainer: {
      backgroundColor: _theme.color.palette.lightestGrey,
      paddingTop: 24
    },
    sepratorItem: {
      marginBottom: 12
    },
    spaceHorizontal: {
      padding: 24
    },
    wrapFlatList: {
      marginBottom: 17
    }
  });
