  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.Perks = Perks;
  exports.textTagPast = exports.tagsLabelStyle = exports.tagsContainerStyle = exports.tagPast = exports.pastOpacity = exports.labelCardStyle = undefined;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _voucherCard = _$$_REQUIRE(_dependencyMap[4]);
  var _theme = _$$_REQUIRE(_dependencyMap[5]);
  var _constants = _$$_REQUIRE(_dependencyMap[6]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _text2 = _$$_REQUIRE(_dependencyMap[8]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[10]);
  var _lodash = _$$_REQUIRE(_dependencyMap[11]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _utils = _$$_REQUIRE(_dependencyMap[13]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[14]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var containerStyle = {
    height: 136,
    width: "100%",
    alignSelf: "center",
    flexDirection: "row",
    alignItems: "center"
  };
  var tenantNameTextStyle = Object.assign({}, _text.presets.caption1Regular, {
    marginTop: 5,
    width: 165
  });
  var offerDescriptionTextStyle = Object.assign({}, _text.presets.bodyTextBold, {
    color: _theme.color.palette.almostBlackGrey,
    paddingRight: 5
  });
  var validTillDateTextStyle = Object.assign({}, _text.presets.caption2Regular, {
    marginTop: 5
  });
  var imageStyle = {
    borderBottomLeftRadius: 20,
    borderTopLeftRadius: 20,
    height: "100%",
    width: width * 0.3
  };
  var textViewStyle = {
    height: "100%",
    width: "100%",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    marginTop: 20
  };
  var cornerShapesViewStyle = {
    overflow: "hidden",
    width: 10,
    height: 15,
    position: "absolute",
    alignSelf: "center",
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var cornerShapeLeft = Object.assign({}, cornerShapesViewStyle, {
    borderTopRightRadius: 20,
    borderBottomRightRadius: 20,
    left: -3
  });
  var cornerShapeRight = Object.assign({}, cornerShapesViewStyle, {
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    right: -3
  });
  var voucherImageStyle = {
    height: "100%"
  };
  var skeletonLayoutImage = [{
    width: 98,
    height: 136,
    borderBottomLeftRadius: 20,
    borderTopLeftRadius: 20
  }];
  var voucherTextStyle = {
    marginLeft: 12,
    flex: 1
  };
  var voucherTextStyleCaseRibbon = {
    marginLeft: 12,
    marginTop: 25
  };
  var lightGreyLoadingColors = [_theme.color.palette.lightGrey, _theme.color.background, _theme.color.palette.lightGrey];
  var lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
  var skeletonLayoutText = [{
    width: 107,
    height: 13,
    borderRadius: 4,
    marginLeft: 12
  }, {
    width: 56,
    height: 13,
    borderRadius: 4,
    marginTop: 10,
    marginLeft: 12
  }, {
    width: 112,
    height: 13,
    borderRadius: 4,
    marginTop: 10,
    marginLeft: 12
  }];
  var wrapRibbonTextStyle = {
    position: "absolute",
    left: 50,
    top: 9,
    backgroundColor: "#A80055",
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderRadius: 4
  };
  var ribbonTextStyle = {
    color: _theme.color.palette.whiteGrey
  };
  var labelCardStyle = exports.labelCardStyle = Object.assign({}, _text.presets.caption2Bold, {
    color: _theme.color.palette.lightPurple,
    textTransform: "uppercase",
    flex: 1
  });
  var pastOpacity = exports.pastOpacity = Object.assign({}, _theme.shadow.noShadow, {
    opacity: 0.5
  });
  var tagsContainerStyle = exports.tagsContainerStyle = {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    overflow: "hidden",
    backgroundColor: _theme.color.palette.lightestPurple
  };
  var tagsLabelStyle = exports.tagsLabelStyle = Object.assign({}, _text.presets.caption2Bold, {
    color: _theme.color.palette.lightPurple
  });
  var tagPast = exports.tagPast = {
    backgroundColor: _theme.color.palette.lighterGrey,
    position: "absolute",
    right: 12,
    top: 12,
    paddingHorizontal: 8,
    paddingVertical: 5,
    borderRadius: 8
  };
  var textTagPast = exports.textTagPast = Object.assign({}, _text.presets.caption2Bold, {
    color: _theme.color.palette.almostBlackGrey
  });
  var wrapCategoryAndQty = {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginRight: 12
  };
  var loadingView = function loadingView(newWidth) {
    return (0, _jsxRuntime.jsxs)(_voucherCard.VoucherCard, {
      style: Object.assign({}, containerStyle, {
        width: newWidth
      }),
      isDisabled: true,
      children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
        duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
        shimmerColors: lightGreyLoadingColors,
        shimmerStyle: skeletonLayoutImage[0]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutText[0]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutText[1]
        }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
          duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
          shimmerColors: lighterGreyLoadingColors,
          shimmerStyle: skeletonLayoutText[2]
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: cornerShapeRight
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: cornerShapeLeft
      })]
    });
  };
  var handleSubcopy = function handleSubcopy(isInstantWinPrize, isValidTillDate, validTillDate) {
    if (isInstantWinPrize) {
      return (0, _jsxRuntime.jsx)(_text2.Text, {
        style: validTillDateTextStyle,
        numberOfLines: 1,
        tx: "walletAccountScreen.perk.instantWinPrizeText"
      });
    }
    return (0, _utils.handleCondition)(isValidTillDate && validTillDate, (0, _jsxRuntime.jsx)(_text2.Text, {
      style: validTillDateTextStyle,
      numberOfLines: 1,
      children: `Valid till ${(0, _moment.default)(validTillDate).format(_dateTime.DateFormats.DayMonthYear)}`
    }), null);
  };
  function Perks(props) {
    var tenantName = props.tenantName,
      offerTitle = props.offerTitle,
      validTillDate = props.validTillDate,
      _props$isValidTillDat = props.isValidTillDate,
      isValidTillDate = _props$isValidTillDat === undefined ? false : _props$isValidTillDat,
      _props$isLoading = props.isLoading,
      isLoading = _props$isLoading === undefined ? false : _props$isLoading,
      imageUrl = props.imageUrl,
      onPressed = props.onPressed,
      _props$isDisabled = props.isDisabled,
      isDisabled = _props$isDisabled === undefined ? false : _props$isDisabled,
      _props$width = props.width,
      newWidth = _props$width === undefined ? 288 : _props$width,
      ribbonText = props.ribbonText,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "RedeemNewRewards" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "RedeemNewRewards" : _props$accessibilityL,
      isPast = props.isPast,
      category = props.category,
      quantity = props.quantity,
      tag = props.tag,
      balance = props.balance,
      isInstantWinPrize = props.isInstantWinPrize;
    var pastOpacityStyle = React.useMemo(function () {
      return isPast ? pastOpacity : {};
    }, [isPast]);
    return isLoading ? loadingView(newWidth) : (0, _jsxRuntime.jsxs)(_voucherCard.VoucherCard, {
      style: Object.assign({}, containerStyle, {
        width: newWidth
      }),
      onPressed: onPressed,
      isDisabled: isDisabled,
      testID: `${testID}__VoucherCard`,
      accessibilityLabel: `${accessibilityLabel}__VoucherCard`,
      accessible: false,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: Object.assign({}, containerStyle, {
          opacity: (0, _utils.handleCondition)(isDisabled, 0.5, 1)
        }),
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: voucherImageStyle,
          children: (0, _jsxRuntime.jsx)(_baseImage.default, {
            source: {
              uri: imageUrl
            },
            style: imageStyle
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: (0, _utils.handleCondition)(ribbonText, voucherTextStyleCaseRibbon, voucherTextStyle),
          children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: textViewStyle,
            children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: wrapCategoryAndQty,
              children: [!(0, _lodash.isEmpty)(category) && (0, _jsxRuntime.jsx)(_text2.Text, {
                style: [labelCardStyle, pastOpacityStyle],
                text: category,
                numberOfLines: 1
              }), (0, _utils.handleCondition)(balance, (0, _jsxRuntime.jsx)(_reactNative.View, {
                style: tagsContainerStyle,
                children: (0, _jsxRuntime.jsx)(_text2.Text, {
                  style: tagsLabelStyle,
                  numberOfLines: 1,
                  children: balance
                })
              }), null), !!quantity && (0, _jsxRuntime.jsx)(_reactNative.View, {
                style: Object.assign({}, tagsContainerStyle),
                children: (0, _jsxRuntime.jsx)(_text2.Text, {
                  text: quantity,
                  style: Object.assign({}, tagsLabelStyle)
                })
              })]
            }), !!tenantName && (0, _jsxRuntime.jsx)(_text2.Text, {
              style: offerDescriptionTextStyle,
              numberOfLines: 1,
              children: tenantName
            }), !!offerTitle && (0, _jsxRuntime.jsx)(_text2.Text, {
              style: tenantNameTextStyle,
              numberOfLines: 2,
              children: offerTitle == null ? undefined : offerTitle.slice(0, 45)
            }), handleSubcopy(isInstantWinPrize, isValidTillDate, validTillDate)]
          })
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: cornerShapeRight
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: cornerShapeLeft
      }), ribbonText && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: wrapRibbonTextStyle,
        children: (0, _jsxRuntime.jsx)(_text2.Text, {
          text: ribbonText.toUpperCase(),
          numberOfLines: 1,
          preset: "XSmallBold",
          style: ribbonTextStyle
        })
      }), !!tag && isDisabled && (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: Object.assign({}, tagPast),
        children: (0, _jsxRuntime.jsx)(_text2.Text, {
          text: tag,
          style: Object.assign({}, textTagPast)
        })
      })]
    });
  }
