  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.styles = exports.shimmerPlaceholderStype = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var styles = exports.styles = _reactNative.StyleSheet.create({
    buttonStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 60,
      marginTop: 16
    },
    container: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1
    },
    containerNoInternet: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1
    },
    content: {
      backgroundColor: _theme.color.palette.transparent,
      flex: 1,
      marginTop: 24,
      paddingHorizontal: 24
    },
    copy: {
      color: _theme.color.palette.whiteGrey,
      fontSize: 14,
      lineHeight: 18,
      marginTop: 16,
      textAlign: "center"
    },
    imageInformative: {
      height: 60,
      width: 60
    },
    imageWelcome: {
      height: 240,
      marginTop: 18,
      width: "100%"
    },
    richTextHtmlStyle: {
      marginTop: 24,
      width: "100%"
    },
    rowInformative: {
      alignItems: "flex-start",
      flexDirection: "row",
      marginTop: 32,
      opacity: 0,
      paddingRight: 24,
      transform: [{
        translateX: _reactNative.Dimensions.get("window").width - 24
      }]
    },
    textInformative: Object.assign({}, _text.presets.caption1Bold, {
      color: "rgba(252, 252, 252,0.8)",
      fontSize: 16,
      lineHeight: 20,
      marginLeft: 18,
      paddingRight: 24,
      textAlign: "left"
    }),
    textSkip: {
      color: _theme.color.palette.whiteGrey,
      fontSize: 16
    },
    textTitle: Object.assign({}, _text.presets.h2, {
      color: _theme.color.palette.whiteGrey,
      fontSize: 32
    }),
    touchableSkip: {
      alignSelf: "flex-end"
    },
    wrapTextTitle: {
      marginTop: 76
    }
  });
  var shimmerPlaceholderStype = exports.shimmerPlaceholderStype = [{
    width: 180,
    height: 180,
    borderRadius: 90,
    marginTop: 52,
    alignSelf: "center"
  }, {
    width: "100%",
    height: 28,
    borderRadius: 4,
    marginTop: 54
  }, {
    width: "100%",
    height: 16,
    borderRadius: 4,
    marginTop: 24
  }, {
    width: "75%",
    height: 16,
    borderRadius: 4,
    marginTop: 12,
    alignSelf: "center"
  }];
