  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _button = _$$_REQUIRE(_dependencyMap[7]);
  var _text = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[11]);
  var _welcomeScreen = _$$_REQUIRE(_dependencyMap[12]);
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _navigators = _$$_REQUIRE(_dependencyMap[14]);
  var _profileRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[16]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _native = _$$_REQUIRE(_dependencyMap[18]);
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _backgrounds = _$$_REQUIRE(_dependencyMap[21]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[22]);
  var _itemFeature = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _navigationType = _$$_REQUIRE(_dependencyMap[24]);
  var _baseImageBackground = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[25]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[26]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[27]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[28]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var UserType = /*#__PURE__*/function (UserType) {
    UserType["New"] = "New";
    UserType["Existing"] = "Existing";
    UserType["New_Native_CIAM"] = "New_Native_CIAM";
    UserType["Existing_Native_CIAM"] = "Existing_Native_CIAM";
    return UserType;
  }(UserType || {});
  var backgroundImageStyle = {
    flex: 1
  };
  var specialNavigationValue = {
    explore: "explore",
    fly: "fly",
    dine: "dine",
    shop: "shop",
    dineShop: "dineShop",
    account: "account"
  };
  var bottomSectionStyle = {
    position: "absolute",
    bottom: 100,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    height: 120
  };
  var wrapScrollViewStyle = {
    width: _reactNative2.Dimensions.get("window").width,
    height: _reactNative2.Dimensions.get("window").height
  };
  var testID = "Welcome";
  var WelcomeScreen = function WelcomeScreen(_ref) {
    var _commonAEM$data;
    var navigation = _ref.navigation,
      route = _ref.route;
    var _route$params = route == null ? undefined : route.params,
      status = _route$params.status;
    var insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var isFocused = (0, _native.useIsFocused)();
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isInternetConnected = _useState2[0],
      setIsInternetConnected = _useState2[1];
    var commonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var dataWelcomePage = commonAEM == null || (_commonAEM$data = commonAEM.data) == null || (_commonAEM$data = _commonAEM$data.welcomeUsers) == null ? undefined : _commonAEM$data.find(function (e) {
      return (e == null ? undefined : e.type) === (status && status === _navigators.InstallAppStatus.INSTALL_FROM_UPGRADE ? UserType.Existing_Native_CIAM : UserType.New_Native_CIAM);
    });
    var profilePayload = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var animatedOpacityValue = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("WELCOME_SCREEN"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.NavigationConstants.appWideWelcome);
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(_constants.NavigationConstants.appWideWelcome, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!(0, _isEmpty.default)(dataWelcomePage == null ? undefined : dataWelcomePage.buttonLabel) || !(0, _isEmpty.default)(dataWelcomePage == null ? undefined : dataWelcomePage.copy)) {
        _reactNative2.Animated.timing(animatedOpacityValue, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true
        }).start();
      }
    }, [dataWelcomePage == null ? undefined : dataWelcomePage.buttonLabel, dataWelcomePage == null ? undefined : dataWelcomePage.copy]);
    (0, _react.useEffect)(function () {
      (0, _baseImageBackground.flushAllImageCached)();
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          setIsInternetConnected(isConnected);
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
    }, []);
    (0, _react.useLayoutEffect)(function () {
      navigation.setOptions({
        header: function header() {
          return false;
        }
      });
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!(0, _isEmpty.default)(profilePayload) && isFocused) {
        onPressSkip();
      }
    }, [profilePayload, isFocused]);
    var onPressSkip = function onPressSkip() {
      navigation.replace(_constants.NavigationConstants.bottomNavigation);
    };
    var onPress = function onPress() {
      var _ref3 = (dataWelcomePage == null ? undefined : dataWelcomePage.navigation) || "",
        navigationType = _ref3.type,
        navigationValue = _ref3.value;
      var _ref4 = dataWelcomePage || {},
        redirect = _ref4.redirect;
      if (navigationType === _navigationType.NavigationTypeEnum.inApp && specialNavigationValue != null && specialNavigationValue[navigationValue]) {
        navigation.replace(_constants.NavigationConstants.bottomNavigation, {
          screen: navigationValue
        });
        return null;
      }
      handleNavigation(navigationType, navigationValue, redirect);
    };
    var handleTitleWelcome = function handleTitleWelcome(title) {
      if ((0, _isEmpty.default)(title)) return null;
      if (title != null && title.includes("\\n")) {
        var _dataWelcomePage$titl;
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _welcomeScreen.styles.wrapTextTitle,
          children: dataWelcomePage == null || (_dataWelcomePage$titl = dataWelcomePage.title) == null || (_dataWelcomePage$titl = _dataWelcomePage$titl.split("\\n")) == null ? undefined : _dataWelcomePage$titl.map(function (element, index) {
            if (index <= 1) {
              return (0, _jsxRuntime.jsx)(_text.Text, {
                text: element,
                style: _welcomeScreen.styles.textTitle,
                preset: "h2",
                numberOfLines: 1
              }, index);
            }
          })
        });
      } else {
        return (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _welcomeScreen.styles.wrapTextTitle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: title,
            style: _welcomeScreen.styles.textTitle,
            preset: "h2",
            numberOfLines: 2
          })
        });
      }
    };
    var renderContent = function renderContent() {
      var _dataWelcomePage$info;
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: _welcomeScreen.styles.content,
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          style: _welcomeScreen.styles.touchableSkip,
          onPress: onPressSkip,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            text: "Skip",
            preset: "bodyTextBold",
            style: _welcomeScreen.styles.textSkip
          })
        }), handleTitleWelcome(dataWelcomePage == null ? undefined : dataWelcomePage.title), !(0, _isEmpty.default)(dataWelcomePage == null ? undefined : dataWelcomePage.informative) && (dataWelcomePage == null || (_dataWelcomePage$info = dataWelcomePage.informative) == null ? undefined : _dataWelcomePage$info.map(function (element, index) {
          return (0, _jsxRuntime.jsx)(_itemFeature.default, {
            element: element,
            index: index
          });
        }))]
      });
    };
    var renderView = function renderView() {
      if (!isInternetConnected) {
        return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
          reload: false,
          header: false,
          headerBackgroundColor: "transparent",
          visible: !isInternetConnected,
          testID: `${testID}__ErrorOverlayNoConnection`
        });
      } else {
        return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: wrapScrollViewStyle,
          children: [(0, _jsxRuntime.jsx)(_reactNative2.ScrollView, {
            showsVerticalScrollIndicator: false,
            children: renderContent()
          }), (0, _jsxRuntime.jsxs)(_reactNative2.Animated.View, {
            style: [bottomSectionStyle, {
              opacity: animatedOpacityValue
            }],
            children: [!(0, _isEmpty.default)(dataWelcomePage == null ? undefined : dataWelcomePage.buttonLabel) && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              style: _welcomeScreen.styles.buttonStyle,
              onPress: onPress,
              activeOpacity: 0.5,
              children: (0, _jsxRuntime.jsx)(_button.Button, {
                sizePreset: "large",
                textPreset: "buttonLarge",
                typePreset: "secondary",
                statePreset: "default",
                backgroundPreset: "light",
                text: dataWelcomePage == null ? undefined : dataWelcomePage.buttonLabel,
                testID: `${testID}__ButtonLogin`,
                accessibilityLabel: `${testID}__ButtonLogin`,
                style: {
                  borderColor: "transparent"
                },
                textStyle: {
                  color: _theme.color.palette.lightPurple
                },
                disabled: true
              })
            }), !(0, _isEmpty.default)(dataWelcomePage == null ? undefined : dataWelcomePage.copy) && (0, _jsxRuntime.jsx)(_text.Text, {
              text: dataWelcomePage == null ? undefined : dataWelcomePage.copy,
              preset: "caption1Regular",
              style: _welcomeScreen.styles.copy,
              numberOfLines: 2
            })]
          })]
        });
      }
    };
    return (0, _jsxRuntime.jsx)(_baseImageBackground.default, {
      source: _backgrounds.WelcomeBackgroundImage,
      style: [backgroundImageStyle, {
        paddingTop: insets.top
      }],
      children: (0, _jsxRuntime.jsx)(_reactNative2.SafeAreaView, {
        style: !isInternetConnected ? _welcomeScreen.styles.containerNoInternet : _welcomeScreen.styles.container,
        children: renderView()
      })
    });
  };
  var _default = exports.default = WelcomeScreen;
