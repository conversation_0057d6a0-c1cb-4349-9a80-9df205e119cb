  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _mediaHelper = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _text = _$$_REQUIRE(_dependencyMap[5]);
  var _welcomeScreen = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var WIDTH = _reactNative.Dimensions.get("window").width;
  var ItemFeature = function ItemFeature(_ref) {
    var element = _ref.element,
      index = _ref.index;
    var animatedPositionValue = (0, _react.useRef)(new _reactNative.Animated.Value(WIDTH - 24)).current;
    var animatedOpacityValue = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
    (0, _react.useEffect)(function () {
      _reactNative.InteractionManager.runAfterInteractions(function () {
        _reactNative.Animated.timing(animatedPositionValue, {
          toValue: 0,
          duration: 700,
          delay: 700 * index + 10,
          useNativeDriver: true
        }).start();
        _reactNative.Animated.timing(animatedOpacityValue, {
          toValue: 1,
          duration: 800,
          delay: 700 * index + 10,
          useNativeDriver: true
        }).start();
      });
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
      style: [_welcomeScreen.styles.rowInformative, {
        transform: [{
          translateX: animatedPositionValue
        }],
        opacity: animatedOpacityValue
      }],
      children: [(0, _jsxRuntime.jsx)(_baseImage.default, {
        source: {
          uri: (0, _mediaHelper.handleImageUrl)(element == null ? undefined : element.icon)
        },
        style: _welcomeScreen.styles.imageInformative
      }), (0, _jsxRuntime.jsx)(_text.Text, {
        text: element == null ? undefined : element.text,
        style: _welcomeScreen.styles.textInformative,
        preset: "caption1Regular",
        numberOfLines: 4
      })]
    }, index == null ? undefined : index.toString());
  };
  var _default = exports.default = ItemFeature;
