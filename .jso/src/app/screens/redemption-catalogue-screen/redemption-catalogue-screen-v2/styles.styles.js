  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.tabBarStyles = exports.pointsTabStyles = exports.perksTabStyles = exports.loadingPageStyles = exports.lighterGreyLoadingColors = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var CONTENT_SCREEN_WIDTH = _reactNative.Dimensions.get("screen").width;
  var COMMON_CARD_WIDTH = (CONTENT_SCREEN_WIDTH - 32 - 12) / 2;
  var pointsTabStyles = exports.pointsTabStyles = _reactNative.StyleSheet.create({
    scrollViewContainerStyle: {
      flex: 1,
      backgroundColor: _theme.color.palette.lightestGrey
    },
    pointContainer: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      marginBottom: 8,
      padding: 16
    }, _theme.shadow.primaryShadow),
    flexRow: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center"
    },
    childFlex: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      width: "50%"
    },
    yourTotalBalanceLabel: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    expiringDate: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    redeemablePointsLabel: Object.assign({}, _text.presets.caption2Regular, {
      color: _theme.color.palette.darkestGrey
    }),
    redeemablePointsValue: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.darkestGrey
    }),
    totalPoint: Object.assign({}, _text.presets.h2),
    expriringPoint: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      lineHeight: _responsive.default.getFontSize(20),
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontFamily: _theme.typography.bold
    }),
    seeAllExpiryBtn: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.lightPurple
    }),
    butterflyBackground: {
      width: 16,
      height: 16,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 50,
      marginRight: 8
    },
    rewardsTilesContainer: {
      paddingTop: 16
    },
    rewardsCategory: Object.assign({}, _text.presets.XSmallBold, {
      color: _theme.color.palette.darkestGrey,
      marginBottom: 4
    }),
    rewardName: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      fontFamily: _theme.typography.bold,
      lineHeight: 20,
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: 'normal'
      }),
      marginBottom: 13,
      height: 60
    }),
    rewardImage: {
      height: 100,
      width: "100%",
      marginBottom: 13
    },
    redemptionPoints: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "left"
    }),
    butterflyIcon: {
      transform: [{
        rotate: "345deg"
      }],
      marginRight: 4,
      marginTop: 1
    },
    rewardsTilesCard: Object.assign({}, _theme.shadow.primaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      width: COMMON_CARD_WIDTH,
      height: 254,
      padding: 16,
      borderRadius: 12
    }),
    flatList: {
      paddingHorizontal: 16
    },
    emptyRewardListMessage: Object.assign({}, _text.presets.caption1Italic, {
      fontFamily: _theme.typography.regular,
      marginTop: 20,
      textAlign: "center"
    })
  });
  var perksTabStyles = exports.perksTabStyles = _reactNative.StyleSheet.create({
    scrollViewContainerStyle: {
      flex: 1,
      backgroundColor: _theme.color.palette.lightestGrey
    },
    captionContainer: Object.assign({
      padding: 16,
      backgroundColor: _theme.color.palette.whiteGrey
    }, _theme.shadow.primaryShadow),
    titleCaptionText: Object.assign({}, _text.presets.caption1Bold, {
      textAlign: "left",
      color: _theme.color.palette.almostBlackGrey
    }),
    captionText: Object.assign({}, _text.presets.caption1Italic, {
      fontFamily: _theme.typography.regular,
      color: _theme.color.palette.darkestGrey,
      marginTop: 8
    }),
    perksListContainer: {
      paddingTop: 24
    },
    flatList: {
      paddingHorizontal: 16,
      paddingBottom: 50
    },
    perkCard: Object.assign({}, _theme.shadow.primaryShadow, {
      backgroundColor: _theme.color.palette.whiteGrey,
      width: COMMON_CARD_WIDTH,
      padding: 16,
      borderRadius: 12,
      marginBottom: 12
    }),
    perkImage: {
      width: "100%",
      height: 100,
      marginBottom: 12
    },
    perkName: Object.assign({}, _text.presets.bodyTextBlackRegular, {
      lineHeight: _responsive.default.getFontSize(20),
      fontWeight: _reactNative.Platform.select({
        ios: "700",
        android: "normal"
      }),
      fontFamily: _reactNative.Platform.select({
        ios: _theme.typography.regular,
        android: _theme.typography.bold
      }),
      marginBottom: 4,
      height: 60
    }),
    perkExpiryDate: Object.assign({}, _text.presets.caption2Italic, {
      fontFamily: _theme.typography.regular,
      marginBottom: 12
    }),
    perkQuantityContainer: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      width: 32,
      height: 32,
      backgroundColor: _theme.color.palette.lightestPurple,
      borderRadius: 4
    },
    perkQuantity: Object.assign({}, _text.presets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    emptyRewardListMessage: Object.assign({}, _text.presets.caption1Italic, {
      fontFamily: _theme.typography.regular,
      marginTop: 20,
      textAlign: "center"
    }),
    errorOverlay: {
      marginTop: 40
    }
  });
  var tabBarStyles = exports.tabBarStyles = _reactNative.StyleSheet.create({
    headerContainer: {
      height: 56,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey
    },
    backButtonHeaderStyles: {
      position: "absolute",
      left: 16,
      top: 0,
      bottom: 0,
      justifyContent: "center",
      alignItems: "center"
    },
    titleHeaderStyles: {
      color: _theme.color.palette.almostBlackGrey,
      textAlign: "center"
    },
    topTabParentStyle: Object.assign({
      backgroundColor: _theme.color.palette.whiteGrey,
      flexDirection: "row",
      height: 72,
      justifyContent: "center"
    }, _theme.shadow.secondaryShadow, {
      alignItems: "flex-end",
      elevation: 10
    }),
    topTabTouchableOpacityStyle: {
      alignItems: "center",
      height: "auto",
      flex: 1
    },
    topTabActiveIndicatorStyle: {
      backgroundColor: _theme.color.palette.lightPurple,
      height: 2
    },
    topTabInActiveIndicatorStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1
    },
    topTabActiveStyle: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.almostBlackGrey,
      marginTop: 8
    }),
    topTabInActiveStyle: Object.assign({}, _text.presets.caption2Bold, {
      color: _theme.color.palette.darkGrey999,
      marginTop: 8
    }),
    topIconInActiveStyle: {
      color: _theme.color.palette.darkGrey999
    },
    topIconActiveStyle: {
      color: _theme.color.palette.lighterPurple
    }
  });
  var loadingPageStyles = exports.loadingPageStyles = _reactNative.StyleSheet.create({
    loadingStyle: {
      backgroundColor: _theme.color.palette.lightGrey,
      borderRadius: 4,
      height: 16,
      marginBottom: 8
    },
    rewardsCategory: {
      marginBottom: 12,
      height: 12,
      width: "100%"
    },
    rewardName: {
      height: 12,
      width: 74,
      marginBottom: 56
    },
    redemptionPoints: {
      height: 12,
      width: 40
    }
  });
  var lighterGreyLoadingColors = exports.lighterGreyLoadingColors = [_theme.color.palette.lighterGrey, _theme.color.background, _theme.color.palette.lighterGrey];
