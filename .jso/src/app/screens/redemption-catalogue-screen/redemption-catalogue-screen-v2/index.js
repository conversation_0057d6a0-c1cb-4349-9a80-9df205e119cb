  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _native = _$$_REQUIRE(_dependencyMap[4]);
  var _validate = _$$_REQUIRE(_dependencyMap[5]);
  var _styles = _$$_REQUIRE(_dependencyMap[6]);
  var _theme = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _pointsTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[10]);
  var _navigators = _$$_REQUIRE(_dependencyMap[11]);
  var _icons = _$$_REQUIRE(_dependencyMap[12]);
  var _constants = _$$_REQUIRE(_dependencyMap[13]);
  var _perksTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[14]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[15]);
  var _text = _$$_REQUIRE(_dependencyMap[16]);
  var _reactNativeSafeAreaContext = _$$_REQUIRE(_dependencyMap[17]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[18]);
  var _adobe = _$$_REQUIRE(_dependencyMap[19]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[20]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[21]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[22]);
  var _forYouRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[23]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[24]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[25]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var RedemptionCatalogueScreenV2 = function RedemptionCatalogueScreenV2() {
    var dispatch = (0, _reactRedux.useDispatch)();
    var RedeemTab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
    var navigation = (0, _native.useNavigation)();
    var _useRoute = (0, _native.useRoute)(),
      params = _useRoute.params;
    var tabActive = (params == null ? undefined : params.tabActive) || _constants.NavigationConstants.pointsTab;
    var positionScrollRef = (0, _react.useRef)(new _reactNative2.Animated.Value(0)).current;
    var tabLabelAnimationValue = positionScrollRef.interpolate({
      inputRange: [0, 104],
      outputRange: [0, -15],
      extrapolate: "clamp"
    });
    var scaleIconAnimationValue = positionScrollRef.interpolate({
      inputRange: [0, 104],
      outputRange: [1, 0],
      extrapolate: "clamp"
    });
    var heightAnimationValue = positionScrollRef.interpolate({
      inputRange: [0, 104],
      outputRange: [72, 40],
      extrapolate: "clamp"
    });
    var inset = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
    var insetTopRef = (0, _react.useRef)(inset.top);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var shadowHeaderStyle = _theme.shadow.noShadow;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)(_constants.TrackingScreenName.RedemptionCatalogueV2);
    var onPressBackButton = function onPressBackButton() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRedeem, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRedeem, _adobe.AdobeValueByTagName.CAppRedeemBack));
      navigation.goBack();
    };
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)(_constants.TrackingScreenName.RedemptionCatalogueV2, (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      if (!(0, _validate.isEmpty)(navigation)) {
        navigation.setOptions({
          headerShown: false
        });
      }
    }, [navigation]);
    var callBackPress = (0, _react.useCallback)(function (name, isFocused) {
      if (name === _constants.NavigationConstants.perksTab && !isFocused) {
        dispatch(_forYouRedux.default.totalPlaypassPerksSuccess(null));
      }
    }, []);
    (0, _react.useEffect)(function () {
      if (tabActive === _navigationHelper.NavigationPageSource.perksTab) {
        dispatch(_forYouRedux.default.totalPlaypassPerksSuccess(null));
      }
    }, [tabActive]);
    return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
      style: {
        flex: 1,
        backgroundColor: _theme.color.palette.whiteGrey
      },
      children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: [_styles.tabBarStyles.headerContainer, shadowHeaderStyle, {
          marginTop: insetTopRef.current,
          marginBottom: 0
        }],
        children: [(0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
          onPress: onPressBackButton,
          style: _styles.tabBarStyles.backButtonHeaderStyles,
          hitSlop: {
            top: 5,
            left: 5,
            right: 5,
            bottom: 5
          },
          children: (0, _jsxRuntime.jsx)(_icons.ArrowLeftGray, {
            width: 24,
            height: 24
          })
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          tx: "redemptionCatalogueScreenV2.titlePage",
          preset: "bodyTextBold",
          style: _styles.tabBarStyles.titleHeaderStyles
        })]
      }), (0, _jsxRuntime.jsxs)(RedeemTab.Navigator, {
        initialRouteName: tabActive,
        backBehavior: "none",
        tabBar: function tabBar(props) {
          return (0, _jsxRuntime.jsx)(_navigators.TabNavBarWithTopIconAnimation, {
            props: Object.assign({}, props),
            isCenter: true,
            topTabParentStyle: Object.assign({}, _styles.tabBarStyles.topTabParentStyle, {
              height: heightAnimationValue
            }),
            topIconActiveStyle: _styles.tabBarStyles.topIconActiveStyle,
            topIconInActiveStyle: _styles.tabBarStyles.topIconInActiveStyle,
            topIconAnimationStyle: {
              transform: [{
                scale: scaleIconAnimationValue
              }]
            },
            topTabTouchableOpacityStyle: _styles.tabBarStyles.topTabTouchableOpacityStyle,
            topTabActiveIndicatorStyle: _styles.tabBarStyles.topTabActiveIndicatorStyle,
            topTabLabelStyleAnimation: {
              transform: [{
                translateY: tabLabelAnimationValue
              }]
            },
            topTabActiveLabelStyle: _styles.tabBarStyles.topTabActiveStyle,
            topTabInActiveLabelStyle: _styles.tabBarStyles.topTabInActiveStyle,
            topTabInActiveIndicatorStyle: _styles.tabBarStyles.topTabInActiveIndicatorStyle,
            callBackPress: callBackPress
          });
        },
        children: [(0, _jsxRuntime.jsx)(RedeemTab.Screen, {
          name: _constants.NavigationConstants.pointsTab,
          options: {
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: (0, _i18n.translate)("redemptionCatalogueScreenV2.pointsTabTitle"),
            tabBarIcon: _icons.PointsTabIcon,
            tabBarTestID: `${_constants2.SCREEN_NAME}__PointsTab`,
            tabBarAccessibilityLabel: `${_constants2.SCREEN_NAME}__PointsTab`,
            animationEnabled: false
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_pointsTab.default, Object.assign({}, props, {
              positionScrollRef: positionScrollRef,
              testID: `${_constants2.SCREEN_NAME}__PointsTab`,
              accessibilityLabel: `${_constants2.SCREEN_NAME}__PointsTab`
            }));
          }
        }), (0, _jsxRuntime.jsx)(RedeemTab.Screen, {
          name: _constants.NavigationConstants.perksTab,
          options: {
            lazy: true,
            swipeEnabled: false,
            tabBarLabel: (0, _i18n.translate)("redemptionCatalogueScreenV2.perksTabTitle"),
            tabBarIcon: _icons.PerksTabIcon,
            tabBarTestID: `${_constants2.SCREEN_NAME}__PerksTab`,
            tabBarAccessibilityLabel: `${_constants2.SCREEN_NAME}__PerksTab`,
            animationEnabled: false
          },
          children: function children(props) {
            return (0, _jsxRuntime.jsx)(_perksTab.default, Object.assign({}, props, {
              positionScrollRef: positionScrollRef,
              testID: `${_constants2.SCREEN_NAME}__PerksTab`,
              accessibilityLabel: `${_constants2.SCREEN_NAME}__PerksTab`
            }));
          }
        })]
      })]
    });
  };
  var _default = exports.default = _react.default.memo(RedemptionCatalogueScreenV2);
