  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _theme = _$$_REQUIRE(_dependencyMap[9]);
  var _icons = _$$_REQUIRE(_dependencyMap[10]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[11]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[12]);
  var _changiRewardsRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[14]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _redemptionCatalogueRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[17]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[19]);
  var _loadingPointsTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[20]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[21]);
  var _focusStatusBar = _$$_REQUIRE(_dependencyMap[22]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[23]);
  var _adobe = _$$_REQUIRE(_dependencyMap[24]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[25]);
  var _pointsFilterBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _sortBy2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[27]));
  var _reactNativeReanimated = _$$_REQUIRE(_dependencyMap[28]);
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[29]));
  var _get2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _iscEntryPointBanner = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _isEmpty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[32]));
  var _utils = _$$_REQUIRE(_dependencyMap[33]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[34]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var RewardsTilesCard = function RewardsTilesCard(_ref) {
    var _item$categoryName;
    var item = _ref.item,
      index = _ref.index,
      memberIconInfo = _ref.memberIconInfo,
      testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel,
      onItemPress = _ref.onItemPress,
      moreRewardsItemLists = _ref.moreRewardsItemLists;
    var _useState = (0, _react.useState)(1),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      opacity = _useState2[0],
      setOpacity = _useState2[1];
    return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
      onPress: function onPress() {
        return onItemPress(item);
      },
      onPressIn: function onPressIn() {
        return setOpacity(0.2);
      },
      onPressOut: function onPressOut() {
        return setOpacity(1);
      },
      testID: `${testID}__RewardsTilesCard`,
      accessibilityLabel: `${accessibilityLabel}__RewardsTilesCard`,
      children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: Object.assign({}, _styles.pointsTabStyles.rewardsTilesCard, {
          marginLeft: index % 2 ? 12 : 0,
          marginBottom: index + 1 === moreRewardsItemLists.length ? 50 : 12,
          opacity: opacity
        }),
        children: [(item == null ? undefined : item.imageLink) && (0, _jsxRuntime.jsx)(_baseImage.default, {
          style: _styles.pointsTabStyles.rewardImage,
          source: {
            uri: item == null ? undefined : item.imageLink
          },
          resizeMode: "cover"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.pointsTabStyles.rewardsCategory,
          numberOfLines: 1,
          children: (item == null || (_item$categoryName = item.categoryName) == null ? undefined : _item$categoryName.toUpperCase()) || ""
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.pointsTabStyles.rewardName,
          numberOfLines: 3,
          children: (item == null ? undefined : item.name) || ""
        }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.pointsTabStyles.flexRow,
          children: [memberIconInfo && (0, _jsxRuntime.jsx)(_icons.MemberIconFlexColor, {
            width: 15,
            height: 11,
            style: Object.assign({}, _styles.pointsTabStyles.butterflyIcon),
            color: memberIconInfo.color
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.pointsTabStyles.redemptionPoints,
            numberOfLines: 1,
            children: (item == null ? undefined : item.pointsShort) || ""
          })]
        })]
      })
    });
  };
  var PointsTab = function PointsTab(_ref2) {
    var testID = _ref2.testID,
      accessibilityLabel = _ref2.accessibilityLabel,
      positionScrollRef = _ref2.positionScrollRef;
    var navigation = (0, _native.useNavigation)();
    var dispatch = (0, _reactRedux.useDispatch)();
    var rewardsDetailVersionTwoError = (0, _reactRedux.useSelector)(_changiRewardsRedux.ChangiRewardsSelectors.rewardsDetailVersionTwoError);
    var rewardsDetailVersionTwoPayload = (0, _reactRedux.useSelector)(_changiRewardsRedux.ChangiRewardsSelectors.rewardsDetailVersionTwoPayload);
    var moreRewardsError = (0, _reactRedux.useSelector)(_redemptionCatalogueRedux.RedemptionCatalogueSelectors.moreRewardsError);
    var moreRewardsItemLists = (0, _reactRedux.useSelector)(_redemptionCatalogueRedux.RedemptionCatalogueSelectors.moreRewardsItemLists);
    var moreRewardsFetching = (0, _reactRedux.useSelector)(_redemptionCatalogueRedux.RedemptionCatalogueSelectors.moreRewardsFetching);
    var aemiSCEntryPoints = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.ISC_ENTRY_POINTS));
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      currentTier = _useRewardTier.currentTier,
      memberIconInfo = _useRewardTier.memberIconInfo;
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      loadingPage = _useState4[0],
      setLoadingPage = _useState4[1];
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isConnected = _useState6[0],
      setConnected = _useState6[1];
    var _useState7 = (0, _react.useState)(true),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      isRefreshData = _useState8[0],
      setIsRefreshData = _useState8[1];
    var _useState9 = (0, _react.useState)([]),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      filterCategories = _useState0[0],
      setFilterCategories = _useState0[1];
    var _useState1 = (0, _react.useState)(""),
      _useState10 = (0, _slicedToArray2.default)(_useState1, 2),
      sortBy = _useState10[0],
      setSortBy = _useState10[1];
    var _useState11 = (0, _react.useState)(false),
      _useState12 = (0, _slicedToArray2.default)(_useState11, 2),
      isPointsFilterBSVisible = _useState12[0],
      setIsPointsFilterBSVisible = _useState12[1];
    var _useState13 = (0, _react.useState)([]),
      _useState14 = (0, _slicedToArray2.default)(_useState13, 2),
      filteredRewardList = _useState14[0],
      setFilteredRewardList = _useState14[1];
    var scrollRef = (0, _react.useRef)(null);
    var scrollPosition = (0, _reactNativeReanimated.useSharedValue)(0);
    var iSCEntryPointData = (0, _get2.default)(aemiSCEntryPoints, "data.list.0", null);
    var isEmptyList = (0, _isEmpty2.default)(filteredRewardList == null || filteredRewardList.filter == null ? undefined : filteredRewardList.filter(function (item) {
      return !(0, _isEmpty2.default)(item) && (item == null ? undefined : item.type) !== _constants2.BannerType.iSCEntryPoint;
    }));
    var scrollIndices = (0, _react.useMemo)(function () {
      if (loadingPage) {
        return [];
      }
      return [1];
    }, [loadingPage]);
    var onGoBack = function onGoBack() {
      var _scrollRef$current;
      setIsRefreshData(false);
      (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollTo({
        y: positionScrollRef._value,
        animated: true
      });
    };
    var onItemPress = function onItemPress(item) {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRedeem, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRedeem, `${_adobe.AdobeValueByTagName.CAppRedeemPoints} | ${item == null ? undefined : item.categoryName} | ${item == null ? undefined : item.name}`));
      navigation.navigate(_constants.NavigationConstants.redeemRewardDetailScreen, {
        SKU: item.SKU,
        onGoBack: onGoBack
      });
    };
    var onItemPressSeeAllExpiryBtn = function onItemPressSeeAllExpiryBtn() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRedeem, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRedeem, `${_adobe.AdobeValueByTagName.CAppRedeemPoints} | ${_adobe.AdobeValueByTagName.CAppRedeemSeeAllExpiry}`));
      navigation == null || navigation.navigate(_constants.NavigationConstants.transactions, {
        screen: _constants.NavigationConstants.pointsExpiry,
        onGoBack: onGoBack
      });
    };
    var handleScroll = function handleScroll(event) {
      scrollPosition.value = event.nativeEvent.contentOffset.y;
      positionScrollRef.setValue(event.nativeEvent.contentOffset.y);
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        setConnected(isConnectedNetInfo);
      });
      return function checkConnection() {
        return _ref3.apply(this, arguments);
      };
    }();
    var retry = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch2.isConnected;
        setConnected(isConnectedNetInfo);
        if (isConnectedNetInfo) {
          dispatch(_changiRewardsRedux.default.rewardsDetailVersionTwoRequest());
          setLoadingPage(true);
        }
      });
      return function retry() {
        return _ref4.apply(this, arguments);
      };
    }();
    var setRewardListWithFilterNSorting = function setRewardListWithFilterNSorting() {
      setFilteredRewardList(function () {
        var _newList2;
        var newList = moreRewardsItemLists;
        if (filterCategories != null && filterCategories.length) {
          var _newList;
          newList = (_newList = newList) == null || _newList.filter == null ? undefined : _newList.filter(function (item) {
            return filterCategories == null || filterCategories.some == null ? undefined : filterCategories.some(function (code) {
              return code === (item == null ? undefined : item.categoryName);
            });
          });
        }
        if (sortBy === _constants2.PointsSortBy.LowToHigh) {
          newList = (0, _sortBy2.default)(newList, [function (item) {
            return Number(item == null ? undefined : item.redemptionpoints);
          }]);
        } else if (sortBy === _constants2.PointsSortBy.HighToLow) {
          newList = (0, _sortBy2.default)(newList, [function (item) {
            return -Number(item == null ? undefined : item.redemptionpoints);
          }]);
        }
        var isShowISCEntryPoint = (0, _utils.ifAllTrue)([(_newList2 = newList) == null ? undefined : _newList2.length, (0, _utils.ifOneTrue)([(0, _utils.ifAllTrue)([!(filterCategories != null && filterCategories.length), !sortBy]), filterCategories == null || filterCategories.some == null ? undefined : filterCategories.some(function (code) {
          return code === _constants2.FilterCategory.Shop;
        })])]);
        if (iSCEntryPointData && isShowISCEntryPoint) {
          var _newList3, _newList4, _newList5;
          var entryPointListItem = Object.assign({}, iSCEntryPointData, {
            type: _constants2.BannerType.iSCEntryPoint
          });
          newList = (_newList3 = newList) == null || _newList3.filter == null ? undefined : _newList3.filter(function (item) {
            return (item == null ? undefined : item.type) !== _constants2.BannerType.iSCEntryPoint;
          });
          (_newList4 = newList) == null || _newList4.splice == null || _newList4.splice(2, 0, null);
          (_newList5 = newList) == null || _newList5.splice == null || _newList5.splice(2, 0, entryPointListItem);
        }
        return newList;
      });
    };
    (0, _react.useEffect)(function () {
      checkConnection();
    }, []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      if (isRefreshData) {
        var _scrollRef$current2;
        (_scrollRef$current2 = scrollRef.current) == null || _scrollRef$current2.scrollTo({
          y: 0,
          animated: true
        });
        dispatch(_changiRewardsRedux.default.rewardsDetailVersionTwoRequest());
        dispatch(_aemRedux.default.getAemConfigData({
          name: _aemRedux.AEM_PAGE_NAME.ISC_ENTRY_POINTS,
          pathName: "getiSCEntryPoints"
        }));
        setLoadingPage(true);
      }
      return function () {
        return setIsRefreshData(true);
      };
    }, [isRefreshData]));
    (0, _react.useEffect)(function () {
      if (rewardsDetailVersionTwoPayload != null && rewardsDetailVersionTwoPayload.tier) {
        dispatch(_redemptionCatalogueRedux.default.moreRewardsRequest({
          isRewardV2: true
        }, false, "", rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.tier));
      }
    }, [rewardsDetailVersionTwoPayload]);
    (0, _react.useEffect)(function () {
      if (loadingPage && (rewardsDetailVersionTwoError || moreRewardsError || !moreRewardsFetching)) {
        setLoadingPage(false);
      }
    }, [rewardsDetailVersionTwoError, moreRewardsError, moreRewardsFetching]);

    // Apply filter & sorting for reward list
    (0, _react.useEffect)(function () {
      if (!isPointsFilterBSVisible) {
        setRewardListWithFilterNSorting();
      }
    }, [isPointsFilterBSVisible, JSON.stringify(filterCategories)]);
    (0, _react.useEffect)(function () {
      setRewardListWithFilterNSorting();
    }, [JSON.stringify(moreRewardsItemLists), sortBy, JSON.stringify(iSCEntryPointData)]);
    var errorInternetConnection = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        onReload: retry,
        header: false,
        noInternetOverlayStyle: Object.assign({}, _styles.pointsTabStyles.scrollViewContainerStyle, {
          marginTop: -220
        }),
        headerBackgroundColor: "transparent",
        testID: `${testID}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`,
        visible: true
      });
    }, []);
    var errorOverlay = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        visible: true,
        onReload: retry,
        overlayStyle: Object.assign({}, _styles.pointsTabStyles.scrollViewContainerStyle, {
          marginTop: -162
        }),
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        testID: `${testID}__ErrorOverlay`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlay`,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL
      });
    }, []);
    if (!isConnected) {
      return (0, _jsxRuntime.jsx)(_react.Fragment, {
        children: errorInternetConnection
      });
    }
    if ((rewardsDetailVersionTwoError || moreRewardsError) && !loadingPage) {
      return (0, _jsxRuntime.jsx)(_react.Fragment, {
        children: errorOverlay
      });
    }
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_focusStatusBar.FocusAwareStatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
        ref: scrollRef,
        style: _styles.pointsTabStyles.scrollViewContainerStyle,
        onScroll: handleScroll,
        showsVerticalScrollIndicator: false,
        stickyHeaderIndices: scrollIndices,
        children: [loadingPage && (0, _jsxRuntime.jsx)(_loadingPointsTab.default, {}), !loadingPage && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.pointsTabStyles.pointContainer,
          children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.pointsTabStyles.flexRow,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.pointsTabStyles.childFlex,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.pointsTabStyles.yourTotalBalanceLabel,
                tx: "redemptionCatalogueScreenV2.yourTotalBalanceLabel"
              })
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.pointsTabStyles.childFlex,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.pointsTabStyles.expiringDate,
                children: rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.points_expiry_date_text
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.pointsTabStyles.flexRow,
            children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
              style: _styles.pointsTabStyles.childFlex,
              children: [memberIconInfo && (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: Object.assign({}, _styles.pointsTabStyles.butterflyBackground, {
                  borderColor: memberIconInfo.backgroundColor,
                  borderWidth: 2
                }),
                children: [_changiRewardsMemberCard.Tier.Monarch, _changiRewardsMemberCard.Tier.StaffMonarch].includes(currentTier) ? (0, _jsxRuntime.jsx)(_icons.MonarchMemberTierIcon, {
                  width: 13,
                  height: 11
                }) : (0, _jsxRuntime.jsx)(_icons.MemberIconFlexColor, {
                  width: 10,
                  height: 8,
                  color: memberIconInfo.color
                })
              }), (0, _jsxRuntime.jsx)(_text.Text, {
                style: _styles.pointsTabStyles.totalPoint,
                children: rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.total_points_bal_text
              })]
            }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.pointsTabStyles.childFlex,
              children: (0, _jsxRuntime.jsx)(_text.Text, {
                style: Object.assign({}, _styles.pointsTabStyles.expriringPoint, !(rewardsDetailVersionTwoPayload != null && rewardsDetailVersionTwoPayload.points_expiring) && (rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.points_expiring) !== 0 ? {
                  fontSize: 11,
                  marginTop: -13,
                  color: _theme.color.palette.darkGrey999
                } : {
                  fontSize: 16,
                  marginTop: -6,
                  color: _theme.color.palette.almostBlackGrey
                }),
                children: rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.points_expiring_text
              })
            })]
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _styles.pointsTabStyles.flexRow,
            children: [(0, _jsxRuntime.jsx)(_reactNative2.View, {
              style: _styles.pointsTabStyles.childFlex,
              children: (0, _jsxRuntime.jsx)(_reactNative2.View, {
                style: _styles.pointsTabStyles.flexRow,
                children: (0, _jsxRuntime.jsxs)(_text.Text, {
                  style: _styles.pointsTabStyles.redeemablePointsValue,
                  children: [rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.redeemable_points_text, " ", (0, _jsxRuntime.jsx)(_text.Text, {
                    style: _styles.pointsTabStyles.redeemablePointsLabel,
                    tx: "redemptionCatalogueScreenV2.redeemablePointsLabel"
                  })]
                })
              })
            }), (rewardsDetailVersionTwoPayload == null ? undefined : rewardsDetailVersionTwoPayload.points_expiring) !== 0 && (0, _jsxRuntime.jsx)(_reactNative.TouchableOpacity, {
              onPress: onItemPressSeeAllExpiryBtn,
              style: _styles.pointsTabStyles.childFlex,
              children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
                style: Object.assign({}, _styles.pointsTabStyles.flexRow, {
                  justifyContent: "center"
                }),
                children: [(0, _jsxRuntime.jsx)(_text.Text, {
                  style: Object.assign({}, _styles.pointsTabStyles.seeAllExpiryBtn, {
                    flex: 1
                  }),
                  tx: "redemptionCatalogueScreenV2.seeAllExpiryBtn"
                }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
                  width: 10,
                  height: 10,
                  color: _theme.color.palette.lightPurple
                })]
              })
            })]
          })]
        }), !loadingPage && !isEmptyList && (0, _jsxRuntime.jsx)(_pointsFilterBar.default, {
          filterCategories: filterCategories,
          isBSVisible: isPointsFilterBSVisible,
          scrollPosition: scrollPosition,
          setFilterCategories: setFilterCategories,
          setIsBSVisible: setIsPointsFilterBSVisible,
          setSortBy: setSortBy,
          sortBy: sortBy
        }), !loadingPage && (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.pointsTabStyles.rewardsTilesContainer,
          children: [!(0, _isEmpty2.default)(filteredRewardList) && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            scrollEnabled: false,
            style: _styles.pointsTabStyles.flatList,
            data: filteredRewardList,
            numColumns: 2,
            renderItem: function renderItem(_ref5) {
              var item = _ref5.item,
                index = _ref5.index;
              if (!item) return null;
              if ((item == null ? undefined : item.type) === _constants2.BannerType.iSCEntryPoint) {
                return (0, _jsxRuntime.jsx)(_iscEntryPointBanner.default, {
                  data: item
                });
              }
              return (0, _jsxRuntime.jsx)(RewardsTilesCard, {
                item: item,
                index: index,
                testID: testID,
                onItemPress: onItemPress,
                memberIconInfo: memberIconInfo,
                accessibilityLabel: accessibilityLabel,
                moreRewardsItemLists: filteredRewardList
              });
            },
            showsVerticalScrollIndicator: false,
            keyExtractor: function keyExtractor(item) {
              return item == null ? undefined : item.SKU;
            }
          }), isEmptyList && (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.pointsTabStyles.emptyRewardListMessage,
            tx: "redemptionCatalogueScreenV2.thereAreCurrentlyNoRewardsAvailable"
          })]
        })]
      })]
    });
  };
  var _default = exports.default = PointsTab;
