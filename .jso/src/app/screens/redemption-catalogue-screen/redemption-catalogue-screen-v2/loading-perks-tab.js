  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[7]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LoadingPerksTab = function LoadingPerksTab() {
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _styles.perksTabStyles.captionContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.perksTabStyles.titleCaptionText,
          tx: "redemptionCatalogueScreenV2.redeemEventsAndPremiums"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: _styles.perksTabStyles.captionText,
          tx: "redemptionCatalogueScreenV2.perksTabCaption"
        })]
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: _styles.pointsTabStyles.rewardsTilesContainer,
        children: (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
          style: _styles.pointsTabStyles.flatList,
          data: [1, 2, 3, 4],
          numColumns: 2,
          renderItem: function renderItem(_ref) {
            var index = _ref.index;
            return (0, _jsxRuntime.jsxs)(_reactNative.View, {
              style: Object.assign({}, _styles.pointsTabStyles.rewardsTilesCard, {
                marginLeft: index % 2 ? 12 : 0,
                marginBottom: 16
              }),
              children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _styles.lighterGreyLoadingColors,
                shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.pointsTabStyles.rewardImage, {
                  borderRadius: 0
                })
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _styles.lighterGreyLoadingColors,
                shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.loadingPageStyles.rewardsCategory)
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _styles.lighterGreyLoadingColors,
                shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.loadingPageStyles.rewardName)
              }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
                duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
                shimmerColors: _styles.lighterGreyLoadingColors,
                shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.loadingPageStyles.redemptionPoints)
              })]
            });
          },
          showsVerticalScrollIndicator: false,
          keyExtractor: function keyExtractor(item) {
            return item.toString();
          }
        })
      })]
    });
  };
  var _default = exports.default = LoadingPerksTab;
