  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _constants = _$$_REQUIRE(_dependencyMap[2]);
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _styles = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _theme = _$$_REQUIRE(_dependencyMap[8]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[9]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var LoadingPointsTab = function LoadingPointsTab() {
    return (0, _jsxRuntime.jsxs)(_react.Fragment, {
      children: [loadingPoint(), loadingRewardsTiles()]
    });
  };
  var _default = exports.default = LoadingPointsTab;
  var loadingPoint = function loadingPoint() {
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: _styles.pointsTabStyles.pointContainer,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: Object.assign({}, _styles.pointsTabStyles.flexRow, {
          marginBottom: 4
        }),
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _styles.pointsTabStyles.childFlex,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.pointsTabStyles.yourTotalBalanceLabel,
            tx: "redemptionCatalogueScreenV2.yourTotalBalanceLabel"
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _styles.pointsTabStyles.childFlex,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.pointsTabStyles.expiringDate,
            tx: "redemptionCatalogueScreenV2.expiringLabel"
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _styles.pointsTabStyles.flexRow,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _styles.pointsTabStyles.childFlex,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _styles.lighterGreyLoadingColors,
            shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, {
              width: 144,
              height: 24
            })
          })
        }), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: Object.assign({}, _styles.pointsTabStyles.childFlex, {
            marginTop: -13
          }),
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _styles.lighterGreyLoadingColors,
            shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, {
              width: 74,
              height: 12
            })
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _styles.pointsTabStyles.flexRow,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _styles.pointsTabStyles.childFlex,
          children: (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
            duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
            shimmerColors: _styles.lighterGreyLoadingColors,
            shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, {
              width: 74,
              height: 12
            })
          })
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: Object.assign({
            justifyContent: "space-between"
          }, _styles.pointsTabStyles.childFlex, {
            marginTop: -8
          }),
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.pointsTabStyles.seeAllExpiryBtn,
            tx: "redemptionCatalogueScreenV2.seeAllExpiryBtn"
          }), (0, _jsxRuntime.jsx)(_icons.ArrowRightV2, {
            width: 10,
            height: 10,
            color: _theme.color.palette.lightPurple
          })]
        })]
      })]
    });
  };
  var loadingRewardsTiles = function loadingRewardsTiles() {
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: _styles.pointsTabStyles.rewardsTilesContainer,
      children: (0, _jsxRuntime.jsx)(_reactNative.FlatList, {
        style: _styles.pointsTabStyles.flatList,
        data: [1, 2, 3, 4],
        numColumns: 2,
        renderItem: function renderItem(_ref) {
          var index = _ref.index;
          return (0, _jsxRuntime.jsxs)(_reactNative.View, {
            style: Object.assign({}, _styles.pointsTabStyles.rewardsTilesCard, {
              marginLeft: index % 2 ? 12 : 0,
              marginBottom: 16
            }),
            children: [(0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _styles.lighterGreyLoadingColors,
              shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.pointsTabStyles.rewardImage, {
                borderRadius: 0
              })
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _styles.lighterGreyLoadingColors,
              shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.loadingPageStyles.rewardsCategory)
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _styles.lighterGreyLoadingColors,
              shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.loadingPageStyles.rewardName)
            }), (0, _jsxRuntime.jsx)(_shimmerPlaceholder.default, {
              duration: _constants.PLACEHOLDER_ANIMATION_SPEED_IN_MS,
              shimmerColors: _styles.lighterGreyLoadingColors,
              shimmerStyle: Object.assign({}, _styles.loadingPageStyles.loadingStyle, _styles.loadingPageStyles.redemptionPoints)
            })]
          });
        },
        showsVerticalScrollIndicator: false,
        keyExtractor: function keyExtractor(item) {
          return item.toString();
        }
      })
    });
  };
