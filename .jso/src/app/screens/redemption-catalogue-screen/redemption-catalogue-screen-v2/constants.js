  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SCREEN_NAME = exports.RedeemPerkTokenType = exports.PointsSortBy = exports.POINTS_FILTER_CATEGORIES = exports.FilterCategory = exports.DeepLinkTaskCode = exports.DURATION = exports.BannerType = exports.ANIMATION_VALUE = exports.ANDROID_TAB_LABEL_DURATION = exports.ANDROID_DURATION = undefined;
  var DURATION = exports.DURATION = 185;
  var ANDROID_DURATION = exports.ANDROID_DURATION = 50;
  var ANDROID_TAB_LABEL_DURATION = exports.ANDROID_TAB_LABEL_DURATION = 0;
  var SCREEN_NAME = exports.SCREEN_NAME = "RedemptionCatalogueScreenV2";
  var ANIMATION_VALUE = exports.ANIMATION_VALUE = {
    SCALE_ICON: {
      START_VALUE: 1,
      END_VALUE: 0
    },
    HEIGHT: {
      START_VALUE: 72,
      END_VALUE: 40
    },
    TAB_LABEL: {
      START_VALUE: 0,
      END_VALUE: -15
    }
  };
  var PointsSortBy = exports.PointsSortBy = /*#__PURE__*/function (PointsSortBy) {
    PointsSortBy["LowToHigh"] = "lowToHigh";
    PointsSortBy["HighToLow"] = "highToLow";
    return PointsSortBy;
  }({});
  var FilterCategory = exports.FilterCategory = /*#__PURE__*/function (FilterCategory) {
    FilterCategory["ChangiEVouchers"] = "Changi e-Vouchers";
    FilterCategory["ChangiExclusives"] = "Changi Exclusives";
    FilterCategory["Dine"] = "Dine";
    FilterCategory["Experiences"] = "Experiences";
    FilterCategory["Parking"] = "Parking";
    FilterCategory["Shop"] = "Shop";
    FilterCategory["Travel"] = "Travel";
    return FilterCategory;
  }({});
  var POINTS_FILTER_CATEGORIES = exports.POINTS_FILTER_CATEGORIES = [{
    label: "redemptionCatalogueScreen.filterBar.category.shop",
    value: FilterCategory.Shop
  }, {
    label: "redemptionCatalogueScreen.filterBar.category.dine",
    value: FilterCategory.Dine
  }, {
    label: "redemptionCatalogueScreen.filterBar.category.experiences",
    value: FilterCategory.Experiences
  }, {
    label: "redemptionCatalogueScreen.filterBar.category.travel",
    value: FilterCategory.Travel
  }, {
    label: "redemptionCatalogueScreen.filterBar.category.changiEVouchers",
    value: FilterCategory.ChangiEVouchers
  }, {
    label: "redemptionCatalogueScreen.filterBar.category.parking",
    value: FilterCategory.Parking
  }, {
    label: "redemptionCatalogueScreen.filterBar.category.changiExclusives",
    value: FilterCategory.ChangiExclusives
  }];
  var BannerType = exports.BannerType = /*#__PURE__*/function (BannerType) {
    BannerType["iSCEntryPoint"] = "iSCEntryPoint";
    return BannerType;
  }({});
  var RedeemPerkTokenType = exports.RedeemPerkTokenType = /*#__PURE__*/function (RedeemPerkTokenType) {
    RedeemPerkTokenType["SpendGame24"] = "spendgame24-tkn";
    RedeemPerkTokenType["MiffySpend10"] = "miffyspend10-tkn";
    RedeemPerkTokenType["MiffyISC60"] = "miffyisc60-tkn";
    return RedeemPerkTokenType;
  }({});
  var DeepLinkTaskCode = exports.DeepLinkTaskCode = /*#__PURE__*/function (DeepLinkTaskCode) {
    DeepLinkTaskCode["Spend10"] = "SPEND10";
    DeepLinkTaskCode["ISC60"] = "ISC60";
    return DeepLinkTaskCode;
  }({});
