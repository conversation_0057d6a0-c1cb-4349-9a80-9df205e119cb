  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _text = _$$_REQUIRE(_dependencyMap[6]);
  var _native = _$$_REQUIRE(_dependencyMap[7]);
  var _styles = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[9]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[10]);
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[11]));
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[13]);
  var _validate = _$$_REQUIRE(_dependencyMap[14]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _loadingPerksTab = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[17]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[18]));
  var _errorOverlay = _$$_REQUIRE(_dependencyMap[19]);
  var _constants = _$$_REQUIRE(_dependencyMap[20]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[21]);
  var _navigationType = _$$_REQUIRE(_dependencyMap[22]);
  var _adobe = _$$_REQUIRE(_dependencyMap[23]);
  var _authentication = _$$_REQUIRE(_dependencyMap[24]);
  var _constants2 = _$$_REQUIRE(_dependencyMap[25]);
  var _utils = _$$_REQUIRE(_dependencyMap[26]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var PerksTab = function PerksTab(_ref) {
    var _rewardsData$reward;
    var testID = _ref.testID,
      accessibilityLabel = _ref.accessibilityLabel,
      positionScrollRef = _ref.positionScrollRef;
    var navigation = (0, _native.useNavigation)();
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("PERKS_TAB"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var dispatch = (0, _reactRedux.useDispatch)();
    var _useState = (0, _react.useState)(true),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isConnected = _useState2[0],
      setConnected = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isDisplayErrorOverlay = _useState4[0],
      setIsDisplayErrorOverlay = _useState4[1];
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var totalPlaypassPerksData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.totalPlaypassPerksData);
    var totalPlaypassPerksLoading = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.totalPlaypassPerksFetching);
    var totalPlaypassPerksError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.totalPlaypassPerksError);
    var cardNo = rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null ? undefined : _rewardsData$reward.cardNo;
    var activePerks = ((totalPlaypassPerksData == null ? undefined : totalPlaypassPerksData.active_perks) || []).map(function (ele) {
      return Object.assign({}, ele, {
        keyItem: Math.random().toString(16).slice(2)
      });
    });
    var _useState5 = (0, _react.useState)(true),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      isRefresh = _useState6[0],
      setIsRefresh = _useState6[1];
    var scrollRef = (0, _react.useRef)(null);
    var onBackScreen = function onBackScreen() {
      setIsRefresh(false);
    };
    var onItemPress = function onItemPress(item) {
      var tokenNameValue = (item == null ? undefined : item.token_qty) === 1 ? item == null ? undefined : item.token_name_singular : item == null ? undefined : item.token_name_plural;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppRedeem, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppRedeem, `${_adobe.AdobeValueByTagName.CAppRedeemPerks} | ${tokenNameValue}`));
      var commonInput = {
        aaTag: (0, _utils.joinTexts)([_navigationHelper.NavigationAATag.redeemPerks, tokenNameValue]),
        isLoggedInAtTriggerTime: (0, _authentication.checkLoginState)(),
        pageSource: _navigationHelper.NavigationPageSource.perksTab
      };
      switch (item == null ? undefined : item.token_type) {
        case _constants2.RedeemPerkTokenType.SpendGame24:
          handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, Object.assign({}, commonInput, {
            taskCode: _constants2.DeepLinkTaskCode.Spend10
          }));
          break;
        case _constants2.RedeemPerkTokenType.MiffySpend10:
          handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, Object.assign({}, commonInput, {
            taskCode: _constants2.DeepLinkTaskCode.Spend10,
            utmCampaign: "miffybirthdaybash25"
          }));
          break;
        case _constants2.RedeemPerkTokenType.MiffyISC60:
          handleNavigation(_navigationType.NavigationTypeEnum.deepLink, _navigationHelper.NavigationValueDeepLink.missionpass, Object.assign({}, commonInput, {
            taskCode: _constants2.DeepLinkTaskCode.ISC60,
            utmCampaign: "miffybirthdaybash25"
          }));
          break;
        default:
          navigation.navigate(_constants.NavigationConstants.detailPerkScreen, {
            item: item,
            onBackScreen: onBackScreen
          });
      }
    };
    var handleScroll = function handleScroll(event) {
      positionScrollRef.setValue(event.nativeEvent.contentOffset.y);
    };
    var checkConnection = /*#__PURE__*/function () {
      var _ref2 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch.isConnected;
        setConnected(isConnectedNetInfo);
      });
      return function checkConnection() {
        return _ref2.apply(this, arguments);
      };
    }();
    var retry = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnectedNetInfo = _yield$NetInfo$fetch2.isConnected;
        setConnected(isConnectedNetInfo);
        if (isConnectedNetInfo) {
          dispatch(_forYouRedux.default.totalPlaypassPerksRequest({
            cardNo: cardNo
          }));
        }
      });
      return function retry() {
        return _ref3.apply(this, arguments);
      };
    }();
    (0, _react.useEffect)(function () {
      checkConnection();
    }, []);
    (0, _native.useFocusEffect)(_react.default.useCallback(function () {
      if (isRefresh) {
        var _scrollRef$current;
        (_scrollRef$current = scrollRef.current) == null || _scrollRef$current.scrollTo({
          y: 0,
          animated: true
        });
        setTimeout(function () {
          return dispatch(_forYouRedux.default.totalPlaypassPerksRequest({
            cardNo: cardNo
          }));
        }, 50);
      }
      return function () {
        setIsDisplayErrorOverlay(false);
        if (!isRefresh) {
          setIsRefresh(true);
        }
      };
    }, [isRefresh]));
    (0, _react.useEffect)(function () {
      if (!totalPlaypassPerksLoading) {
        setIsRefresh(false);
        setTimeout(function () {
          return setIsDisplayErrorOverlay(true);
        }, 100);
      }
    }, [totalPlaypassPerksLoading]);
    var PerkCard = function PerkCard(_ref4) {
      var item = _ref4.item,
        index = _ref4.index;
      var _useState7 = (0, _react.useState)(1),
        _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
        opacity = _useState8[0],
        setOpacity = _useState8[1];
      return (0, _jsxRuntime.jsx)(_reactNative.TouchableWithoutFeedback, {
        onPressIn: function onPressIn() {
          return setOpacity(0.2);
        },
        onPressOut: function onPressOut() {
          return setOpacity(1);
        },
        onPress: function onPress() {
          return onItemPress(item);
        },
        testID: `${testID}__PerkCard`,
        accessibilityLabel: `${accessibilityLabel}__PerkCard`,
        children: (0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: Object.assign({}, _styles.perksTabStyles.perkCard, {
            marginLeft: index % 2 ? 12 : 0,
            opacity: opacity
          }),
          children: [(item == null ? undefined : item.token_img_url) && (0, _jsxRuntime.jsx)(_baseImage.default, {
            style: _styles.perksTabStyles.perkImage,
            source: {
              uri: item == null ? undefined : item.token_img_url
            },
            resizeMode: "cover"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.perksTabStyles.perkName,
            numberOfLines: 3,
            children: (item == null ? undefined : item.token_qty) === 1 ? item == null ? undefined : item.token_name_singular : item == null ? undefined : item.token_name_plural
          }), (item == null ? undefined : item.expiry_dt) && (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.perksTabStyles.perkExpiryDate,
            numberOfLines: 1,
            tx: "redemptionCatalogueScreenV2.validTillTxt",
            txOptions: {
              value: (0, _moment.default)(item == null ? undefined : item.expiry_dt).format(_dateTime.DateFormats.DayMonthYear)
            }
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _styles.perksTabStyles.perkQuantityContainer,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: _styles.perksTabStyles.perkQuantity,
              children: item == null ? undefined : item.token_qty
            })
          })]
        })
      });
    };
    var errorInternetConnection = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        onReload: retry,
        header: false,
        noInternetOverlayStyle: Object.assign({}, _styles.perksTabStyles.scrollViewContainerStyle, {
          marginTop: -220
        }),
        headerBackgroundColor: "transparent",
        visible: !isConnected,
        testID: `${testID}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlayNoConnection`
      });
    }, [isConnected, testID, accessibilityLabel]);
    var errorOverlay = (0, _react.useMemo)(function () {
      return (0, _jsxRuntime.jsx)(_errorOverlay.ErrorOverlay, {
        reload: true,
        header: false,
        visible: true,
        onReload: retry,
        overlayStyle: _styles.perksTabStyles.errorOverlay,
        variant: _errorOverlay.ErrorOverlayVariant.VARIANT2,
        testID: `${testID}__ErrorOverlay`,
        accessibilityLabel: `${accessibilityLabel}__ErrorOverlay`,
        extendCode: _errorOverlay.ERROR_HANDLING_CODE.ERROR_PAGE_LEVEL
      });
    }, []);
    if (!isConnected) {
      return (0, _jsxRuntime.jsx)(_react.Fragment, {
        children: errorInternetConnection
      });
    }
    return (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
      ref: scrollRef,
      style: _styles.perksTabStyles.scrollViewContainerStyle,
      onScroll: handleScroll,
      showsVerticalScrollIndicator: false,
      children: [(0, _jsxRuntime.jsx)(_reactNative2.StatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "dark-content"
      }), totalPlaypassPerksLoading ? (0, _jsxRuntime.jsx)(_loadingPerksTab.default, {}) : (0, _jsxRuntime.jsxs)(_react.Fragment, {
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _styles.perksTabStyles.captionContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.perksTabStyles.titleCaptionText,
            tx: "redemptionCatalogueScreenV2.redeemEventsAndPremiums"
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: _styles.perksTabStyles.captionText,
            tx: "redemptionCatalogueScreenV2.perksTabCaption"
          })]
        }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: _styles.perksTabStyles.perksListContainer,
          children: !(0, _validate.isEmpty)(activePerks) && (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
            style: _styles.perksTabStyles.flatList,
            data: activePerks,
            numColumns: 2,
            renderItem: function renderItem(_ref5) {
              var item = _ref5.item,
                index = _ref5.index;
              return (0, _jsxRuntime.jsx)(PerkCard, {
                item: item,
                index: index
              });
            },
            showsVerticalScrollIndicator: false,
            keyExtractor: function keyExtractor(item) {
              return item == null ? undefined : item.keyItem;
            }
          })
        }), isDisplayErrorOverlay && totalPlaypassPerksError && errorOverlay]
      })]
    });
  };
  var _default = exports.default = PerksTab;
