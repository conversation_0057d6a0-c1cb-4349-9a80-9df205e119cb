  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _filterPill = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _i18n = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _utils = _$$_REQUIRE(_dependencyMap[4]);
  var _icons = _$$_REQUIRE(_dependencyMap[5]);
  var _react = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _pointsFilterBottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _constants = _$$_REQUIRE(_dependencyMap[9]);
  var _reactNativeReanimated = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[10]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[11]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_WIDTH = _reactNative.Dimensions.get("window").width;
  var _worklet_8233625122167_init_data = {
    code: "function pointsFilterBarTsx1(){const{scrollPosition,color}=this.__closure;const backgroundColor=scrollPosition.value>=104?color.palette.lightestGrey:\"transparent\";return{backgroundColor:backgroundColor};}"
  };
  var PointsFilterBar = function PointsFilterBar(props) {
    var filterCategories = props.filterCategories,
      isBSVisible = props.isBSVisible,
      scrollPosition = props.scrollPosition,
      setFilterCategories = props.setFilterCategories,
      setIsBSVisible = props.setIsBSVisible,
      setSortBy = props.setSortBy,
      sortBy = props.sortBy;
    var isCategoryFilterActive = !!(filterCategories != null && filterCategories.length);
    var categoryDisplayLabel = (0, _react.useMemo)(function () {
      if (isCategoryFilterActive) {
        return (0, _i18n.translate)("redemptionCatalogueScreen.filterBar.categoryLabelWithNumber", {
          amount: filterCategories == null ? undefined : filterCategories.length
        });
      }
      return (0, _i18n.translate)("redemptionCatalogueScreen.filterBar.categoryDefaultLabel");
    }, [JSON.stringify(filterCategories)]);
    var sortingDisplayLabel = (0, _react.useMemo)(function () {
      if (!sortBy || sortBy === _constants.PointsSortBy.LowToHigh) {
        return (0, _i18n.translate)("redemptionCatalogueScreen.filterBar.sortingLabel.lowToHigh");
      }
      return (0, _i18n.translate)("redemptionCatalogueScreen.filterBar.sortingLabel.highToLow");
    }, [sortBy]);
    var containerStyle = (0, _reactNativeReanimated.useAnimatedStyle)(function () {
      var pointsFilterBarTsx1 = function pointsFilterBarTsx1() {
        var backgroundColor = scrollPosition.value >= 104 ? _theme.color.palette.lightestGrey : "transparent";
        return {
          backgroundColor: backgroundColor
        };
      };
      pointsFilterBarTsx1.__closure = {
        scrollPosition: scrollPosition,
        color: _theme.color
      };
      pointsFilterBarTsx1.__workletHash = 8233625122167;
      pointsFilterBarTsx1.__initData = _worklet_8233625122167_init_data;
      return pointsFilterBarTsx1;
    }(), [scrollPosition.value]);
    var handlePressCategoryFilter = function handlePressCategoryFilter() {
      setIsBSVisible(true);
    };
    var handleToggleSortBy = function handleToggleSortBy() {
      setSortBy(function (oldValue) {
        if (!oldValue || oldValue === _constants.PointsSortBy.HighToLow) {
          return _constants.PointsSortBy.LowToHigh;
        }
        return _constants.PointsSortBy.HighToLow;
      });
    };
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsxs)(_reactNativeReanimated.default.View, {
        style: [styles.containerStyle, containerStyle],
        children: [(0, _jsxRuntime.jsx)(_filterPill.default, {
          active: isCategoryFilterActive,
          containerStyle: styles.categoryPillContainerStyle,
          IconComponent: _icons.Filter,
          iconSize: 12,
          label: categoryDisplayLabel,
          labelStyle: styles.categoryPillLabelStyle,
          onPress: handlePressCategoryFilter,
          outlineColor: _theme.color.palette.lighterGrey,
          rightIconComponent: (0, _jsxRuntime.jsx)(_icons.ArrowDown, {
            color: (0, _utils.handleCondition)(isCategoryFilterActive, _theme.color.palette.lighterPurple, _theme.color.palette.darkestGrey),
            height: 16,
            width: 16
          }),
          variant: "outline"
        }), (0, _jsxRuntime.jsx)(_filterPill.default, {
          active: !!sortBy,
          containerStyle: styles.sortingPillContainerStyle,
          IconComponent: _icons.SortWithArrowIcon,
          iconSize: 16,
          label: sortingDisplayLabel,
          onClearSelection: function onClearSelection() {
            return setSortBy("");
          },
          onPress: handleToggleSortBy,
          outlineColor: _theme.color.palette.lighterGrey,
          variant: "outline"
        })]
      }), (0, _jsxRuntime.jsx)(_pointsFilterBottomSheet.default, {
        filterCategories: filterCategories,
        setFilterCategories: setFilterCategories,
        setVisible: setIsBSVisible,
        visible: isBSVisible
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      alignItems: "center",
      backgroundColor: "transparent",
      flexDirection: "row",
      gap: 4,
      paddingBottom: 8,
      paddingHorizontal: 16,
      paddingTop: 8,
      width: SCREEN_WIDTH
    },
    categoryPillContainerStyle: {
      flex: 1,
      gap: 4
    },
    categoryPillLabelStyle: {
      flex: 1
    },
    sortingPillContainerStyle: {
      minWidth: (SCREEN_WIDTH - 32) / 2 - 4
    }
  });
  var _default = exports.default = PointsFilterBar;
