  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[1]);
  var _baseImage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _theme = _$$_REQUIRE(_dependencyMap[4]);
  var _utils = _$$_REQUIRE(_dependencyMap[5]);
  var _icons = _$$_REQUIRE(_dependencyMap[6]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[7]);
  var _get2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _cloneDeep2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[10]);
  var _useRewardTier2 = _$$_REQUIRE(_dependencyMap[11]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[12]);
  var ISCEntryPointBanner = function ISCEntryPointBanner(props) {
    var data = props.data;
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("ISC_BANNER"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    var _useRewardTier = (0, _useRewardTier2.useRewardTier)(),
      memberIconInfo = _useRewardTier.memberIconInfo;
    var handlePressiSCEntryPoint = function handlePressiSCEntryPoint() {
      var navigationData = (0, _get2.default)(data, "callToAction.navigation");
      var redirect = (0, _cloneDeep2.default)((0, _get2.default)(data, "callToAction.redirect"));
      if (!(navigationData != null && navigationData.type) || !(navigationData != null && navigationData.value)) return;
      if (redirect != null && redirect.redirectTarget) {
        var urlObj = new URL(redirect == null ? undefined : redirect.redirectTarget);
        redirect.redirectTarget = urlObj == null ? undefined : urlObj.pathname;
      }
      handleNavigation(navigationData == null ? undefined : navigationData.type, navigationData == null ? undefined : navigationData.value, redirect);
    };
    return (0, _jsxRuntime.jsxs)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
      onPress: handlePressiSCEntryPoint,
      style: styles.containerStyle,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.contentContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.categoryTitleTextStyle,
          text: data == null ? undefined : data.categoryTitle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 2,
          style: styles.bannerTitleTextStyle,
          text: data == null ? undefined : data.bannerTitle
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          numberOfLines: 3,
          style: styles.descriptionTextStyle,
          text: data == null ? undefined : data.description
        }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
          style: styles.redemptionPointContainerStyle,
          children: [(0, _jsxRuntime.jsx)(_icons.ButterFlyTiltIcon, {
            color: memberIconInfo == null ? undefined : memberIconInfo.iconColor,
            height: 16,
            style: styles.butterflyIconStyle,
            width: 16
          }), (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.redemptionPointTextStyle,
            text: data == null ? undefined : data.redemptionPointsText
          })]
        })]
      }), (0, _jsxRuntime.jsx)(_baseImage.default, {
        resizeMode: "contain",
        source: {
          uri: (0, _utils.mappingUrlAem)(data == null ? undefined : data.image)
        },
        style: styles.imageStyle
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: Object.assign({
      alignItems: "center",
      backgroundColor: _theme.color.palette.whiteGrey,
      borderRadius: 12,
      flexDirection: "row",
      gap: _reactNative.Platform.select({
        android: 11,
        ios: 12
      }),
      justifyContent: "space-between",
      marginBottom: 12,
      padding: 16,
      width: "100%"
    }, _theme.shadow.primaryShadow),
    contentContainerStyle: {
      flex: 1,
      gap: 4
    },
    categoryTitleTextStyle: Object.assign({}, _text.newPresets.XSmallBold, {
      color: _theme.color.palette.darkestGrey
    }),
    bannerTitleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    descriptionTextStyle: Object.assign({}, _text.newPresets.caption2Regular),
    butterflyIconStyle: {
      marginRight: 4
    },
    redemptionPointContainerStyle: {
      alignItems: "flex-end",
      flexDirection: "row",
      marginTop: 12
    },
    redemptionPointTextStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    imageStyle: {
      height: 100,
      width: 134
    }
  });
  var _default = exports.default = ISCEntryPointBanner;
