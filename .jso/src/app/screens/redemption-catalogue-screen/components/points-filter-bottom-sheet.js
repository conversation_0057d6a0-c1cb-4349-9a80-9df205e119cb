  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _multimediaTouchableOpacity = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _checkboxOption = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _theme = _$$_REQUIRE(_dependencyMap[6]);
  var _icons = _$$_REQUIRE(_dependencyMap[7]);
  var _react = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[9]);
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _reactNativeLinearGradient = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _redemptionCatalogueRedux = _$$_REQUIRE(_dependencyMap[14]);
  var _sortBy2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _adobe = _$$_REQUIRE(_dependencyMap[16]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[17]);
  var PointsFilterBottomSheet = function PointsFilterBottomSheet(props) {
    var filterCategories = props.filterCategories,
      setFilterCategories = props.setFilterCategories,
      setVisible = props.setVisible,
      visible = props.visible;
    var moreRewardsItemLists = (0, _reactRedux.useSelector)(_redemptionCatalogueRedux.RedemptionCatalogueSelectors.moreRewardsItemLists);
    var originalFiltersRef = (0, _react.useRef)(null);
    var isPressedClearAll = (0, _react.useRef)(false);
    var categoryFilterList = (0, _react.useMemo)(function () {
      var availableCategories = moreRewardsItemLists == null || moreRewardsItemLists.map == null ? undefined : moreRewardsItemLists.map(function (item) {
        return item == null ? undefined : item.categoryName;
      });
      return (0, _sortBy2.default)(_constants.POINTS_FILTER_CATEGORIES.filter(function (item) {
        return availableCategories == null || availableCategories.some == null ? undefined : availableCategories.some(function (cat) {
          return cat === (item == null ? undefined : item.value);
        });
      }).map(function (item) {
        return Object.assign({}, item, {
          checked: filterCategories == null || filterCategories.some == null ? undefined : filterCategories.some(function (filterVal) {
            return (item == null ? undefined : item.value) === filterVal;
          })
        });
      }), [function (item) {
        var _item$value;
        return item == null || (_item$value = item.value) == null || _item$value.toUpperCase == null ? undefined : _item$value.toUpperCase();
      }]);
    }, [JSON.stringify(moreRewardsItemLists), JSON.stringify(filterCategories)]);
    var handleCloseBS = function handleCloseBS() {
      var _originalFiltersRef$c;
      setFilterCategories(((_originalFiltersRef$c = originalFiltersRef.current) == null ? undefined : _originalFiltersRef$c.filterCategories) || []);
      setVisible(false);
    };
    var handleToggleCategoryOption = function handleToggleCategoryOption(item, newValue) {
      setFilterCategories(function (list) {
        if (newValue) {
          return list == null || list.concat == null ? undefined : list.concat(item == null ? undefined : item.value);
        }
        return list == null || list.filter == null ? undefined : list.filter(function (val) {
          return val !== (item == null ? undefined : item.value);
        });
      });
    };
    var handleClearAllFilters = function handleClearAllFilters() {
      setFilterCategories([]);
      isPressedClearAll.current = true;
    };
    var handleApplyFilters = function handleApplyFilters() {
      setVisible(false);
      var dataToBeSent = "All";
      if (filterCategories != null && filterCategories.length && (filterCategories == null ? undefined : filterCategories.length) < (categoryFilterList == null ? undefined : categoryFilterList.length)) {
        dataToBeSent = filterCategories.join(", ");
      }
      (0, _adobe.trackAction)(_adobe.AdobeTagName.RewardCatalogueFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.RewardCatalogueFilter, `${_adobe.AdobeValueByTagName.RewardCatalogueApplyFilters}${dataToBeSent}`));
      if (isPressedClearAll.current && (filterCategories == null ? undefined : filterCategories.length) > 0) {
        (0, _adobe.trackAction)(_adobe.AdobeTagName.RewardCatalogueFilter, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.RewardCatalogueFilter, _adobe.AdobeValueByTagName.RewardCatalogueClearAll));
      }
      isPressedClearAll.current = false;
    };
    (0, _react.useEffect)(function () {
      if (visible) {
        originalFiltersRef.current = {
          filterCategories: filterCategories
        };
      }
    }, [visible]);
    return (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
      animationInTiming: 400,
      animationOutTiming: 400,
      containerStyle: styles.containerStyle,
      isModalVisible: visible,
      onClosedSheet: handleCloseBS,
      stopDragCollapse: true,
      children: [(0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.headerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.titleTextStyle,
          tx: "staffPerkListing.filterBs.title"
        }), (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handleCloseBS,
          style: styles.closeBtnStyle,
          children: (0, _jsxRuntime.jsx)(_icons.Cross, {
            color: _theme.color.palette.darkestGrey,
            height: 24,
            width: 24
          })
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.sectionContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: styles.sectionHeaderContainerStyle,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.sectionTitleTextStyle,
            tx: "staffPerkListing.filterBs.category.title"
          })
        }), categoryFilterList.map(function (item, index) {
          var checked = filterCategories == null || filterCategories.some == null ? undefined : filterCategories.some(function (val) {
            return val === item.value;
          });
          return (0, _jsxRuntime.jsx)(_checkboxOption.default, {
            checked: checked,
            containerStyle: index === 0 ? {
              marginTop: 14
            } : undefined,
            label: (0, _i18n.translate)(item == null ? undefined : item.label),
            onCheck: function onCheck(newValue) {
              return handleToggleCategoryOption(item, newValue);
            }
          }, `${item.value}_${checked}`);
        })]
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.footerContainerStyle,
        children: [(0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
          onPress: handleClearAllFilters,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.clearAllBtnStyle,
            tx: "staffPerkListing.filterBs.clearAllBtn"
          })
        }), (0, _jsxRuntime.jsx)(_reactNativeLinearGradient.default, {
          style: styles.applyFiltersBtnContainerStyle,
          start: {
            x: 0,
            y: 1
          },
          end: {
            x: 1,
            y: 0
          },
          colors: [_theme.color.palette.gradientColor1Start, _theme.color.palette.gradientColor1End],
          children: (0, _jsxRuntime.jsx)(_multimediaTouchableOpacity.MultimediaTouchableOpacity, {
            onPress: handleApplyFilters,
            style: styles.applyFiltersBtnStyle,
            children: (0, _jsxRuntime.jsx)(_text.Text, {
              style: styles.applyFiltersBtnLabelTextStyle,
              tx: "staffPerkListing.filterBs.applyFiltersBtn"
            })
          })
        })]
      })]
    });
  };
  var styles = _reactNative.StyleSheet.create({
    containerStyle: {
      height: _reactNative.Dimensions.get("window").height - 79,
      backgroundColor: _theme.color.palette.whiteGrey,
      borderTopLeftRadius: 16,
      borderTopEndRadius: 16
    },
    headerContainerStyle: {
      alignItems: "center",
      height: 64,
      justifyContent: "center"
    },
    titleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey
    }),
    closeBtnStyle: {
      position: "absolute",
      right: 16,
      top: 20
    },
    sectionContainerStyle: {
      flex: 1,
      paddingHorizontal: 20
    },
    sectionHeaderContainerStyle: {
      alignItems: "center",
      flexDirection: "row",
      justifyContent: "space-between"
      // marginBottom: 12,
    },
    sectionTitleTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.almostBlackGrey,
      fontWeight: _reactNative.Platform.select({
        ios: "900",
        android: "normal"
      })
    }),
    footerContainerStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      borderTopColor: _theme.color.palette.lighterGrey,
      borderTopWidth: 1,
      flexDirection: "row",
      height: 96,
      justifyContent: "space-between",
      paddingBottom: 40,
      paddingHorizontal: 20,
      paddingTop: 12
    },
    clearAllBtnStyle: Object.assign({}, _text.newPresets.caption1Bold, {
      color: _theme.color.palette.lightPurple
    }),
    applyFiltersBtnContainerStyle: {
      borderRadius: 60,
      height: 44
    },
    applyFiltersBtnStyle: {
      height: 44,
      paddingHorizontal: 24,
      paddingVertical: 10,
      width: "100%"
    },
    applyFiltersBtnLabelTextStyle: Object.assign({}, _text.newPresets.bodyTextBold, {
      color: _theme.color.palette.whiteGrey,
      lineHeight: 24
    })
  });
  var _default = exports.default = PointsFilterBottomSheet;
