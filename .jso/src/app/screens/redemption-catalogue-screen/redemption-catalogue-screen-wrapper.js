  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.RedemptionCatalogueScreen = RedemptionCatalogueScreen;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[1]));
  var _redemptionCatalogueScreenV = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[3]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  function RedemptionCatalogueScreen(props) {
    return (0, _jsxRuntime.jsx)(_react.Fragment, {
      children: (0, _jsxRuntime.jsx)(_redemptionCatalogueScreenV.default, Object.assign({}, props))
    });
  }
