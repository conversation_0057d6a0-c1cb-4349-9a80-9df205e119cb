  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _materialTopTabs = _$$_REQUIRE(_dependencyMap[4]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[5]);
  var _searchRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _searchScreen = _$$_REQUIRE(_dependencyMap[7]);
  var _i18n = _$$_REQUIRE(_dependencyMap[8]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[9]);
  var _searchTabFlights = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _adobe = _$$_REQUIRE(_dependencyMap[11]);
  var _searchScreenContext = _$$_REQUIRE(_dependencyMap[12]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[13]);
  var _navigationUtilities = _$$_REQUIRE(_dependencyMap[14]);
  var _native = _$$_REQUIRE(_dependencyMap[15]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _searchTabAll = _$$_REQUIRE(_dependencyMap[17]);
  var _icons = _$$_REQUIRE(_dependencyMap[18]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[19]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SearchTab = (0, _materialTopTabs.createMaterialTopTabNavigator)();
  var SCREEN_NAME = "SearchScreenV2__";
  var SearchScreen = function SearchScreen(_ref) {
    var _route$params, _route$params2;
    var navigation = _ref.navigation,
      route = _ref.route;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isModuleFlights = _searchIndex.SearchIndex.flights === ((_route$params = route.params) == null ? undefined : _route$params.screen);
    var routeModule = isModuleFlights ? _searchIndex.SearchIndex.flights : _searchIndex.SearchIndex.all;
    var autoFocusTextInput = ((_route$params2 = route.params) == null ? undefined : _route$params2.focusTextInput) || false;
    (0, _react.useEffect)(function () {
      dispatch(_searchRedux.default.resetSearchKeywordCollection());
      dispatch(_searchRedux.default.popularSearchKeywordRequest());
      return function () {
        dispatch(_searchRedux.default.setSearchKeyword(""));
      };
    }, []);
    (0, _react.useEffect)(function () {
      dispatch(_searchRedux.default.resetSearchKeywordCollection());
      dispatch(_flyRedux.FlyCreators.setFilterDateSearchArrival(null));
      dispatch(_flyRedux.FlyCreators.setFilterDateSearchDeparture(null));
      dispatch(_mytravelRedux.MytravelCreators.flyClearInsertFlightPayload());
      dispatch(_searchRedux.default.popularSearchKeywordRequest());
      return function () {
        dispatch(_searchRedux.default.setSearchKeyword(""));
      };
    }, []);
    var handlePressBack = function handlePressBack() {
      dispatch(_searchRedux.default.setSearchKeyword(""));
      navigation.goBack();
    };
    (0, _native.useFocusEffect)((0, _react.useCallback)(function () {
      var onBackPress = function onBackPress() {
        handlePressBack();
        return true;
      };
      var subscription = _reactNative.BackHandler.addEventListener("hardwareBackPress", onBackPress);
      return function () {
        return subscription.remove();
      };
    }, []));
    return (0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [(0, _jsxRuntime.jsx)(_reactNative.SafeAreaView, {
        style: _searchScreen.styles.headerStyle,
        testID: SCREEN_NAME,
        accessibilityLabel: SCREEN_NAME
      }), (0, _jsxRuntime.jsxs)(_reactNative.SafeAreaView, {
        style: _searchScreen.styles.containerStyle,
        children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
          translucent: true,
          backgroundColor: "transparent",
          barStyle: "dark-content"
        }), (0, _jsxRuntime.jsx)(_searchScreenContext.SearchScreenProvider, {
          children: (0, _jsxRuntime.jsxs)(SearchTab.Navigator, {
            initialRouteName: routeModule,
            keyboardDismissMode: "none",
            tabBar: function tabBar(props) {
              return (0, _jsxRuntime.jsx)(_navigationUtilities.TabNavBarWithTopIcon, {
                props: Object.assign({}, props),
                isCenter: true,
                topTabParentStyle: _searchScreen.styles.topTabParentStyle,
                topIconActiveStyle: _searchScreen.styles.topIconActiveStyle,
                topIconInActiveStyle: _searchScreen.styles.topIconInActiveStyle,
                topTabTouchableOpacityStyle: _searchScreen.styles.topTabTouchableOpacityStyle,
                topTabActiveIndicatorStyle: _searchScreen.styles.topTabActiveIndicatorStyle,
                topTabActiveLabelStyle: _searchScreen.styles.topTabActiveStyle,
                topTabInActiveLabelStyle: _searchScreen.styles.topTabInActiveStyle,
                topTabInActiveIndicatorStyle: _searchScreen.styles.topTabInActiveIndicatorStyle
              });
            },
            children: [(0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.all,
              options: {
                tabBarIcon: _icons.SearchIconV2,
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.all"),
                tabBarTestID: `${SCREEN_NAME}TabAll`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabAll`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.all
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.all")));
                }
              },
              children: function children(props) {
                return (0, _jsxRuntime.jsx)(_searchTabAll.SearchTabAll, Object.assign({
                  autoFocusTextInput: autoFocusTextInput
                }, props));
              }
            }), (0, _jsxRuntime.jsx)(SearchTab.Screen, {
              name: _searchIndex.SearchIndex.flights,
              options: {
                tabBarIcon: _icons.PlaneIcon,
                lazy: true,
                swipeEnabled: false,
                tabBarLabel: (0, _i18n.translate)("search.tabTitles.flights"),
                tabBarTestID: `${SCREEN_NAME}TabFlights`,
                tabBarAccessibilityLabel: `${SCREEN_NAME}TabFlights`
              },
              listeners: {
                tabPress: function tabPress() {
                  navigation.setParams({
                    screen: _searchIndex.SearchIndex.flights
                  });
                  (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultCategoryToggle, (0, _i18n.translate)("search.tabTitles.flights")));
                }
              },
              children: function children() {
                var _route$params3, _route$params4, _route$params5;
                return (0, _jsxRuntime.jsx)(_searchTabFlights.default, {
                  keyword: route == null || (_route$params3 = route.params) == null ? undefined : _route$params3.keyword,
                  date: route == null || (_route$params4 = route.params) == null ? undefined : _route$params4.date,
                  direction: route == null || (_route$params5 = route.params) == null ? undefined : _route$params5.direction,
                  isPushNavigation: !!(route != null && route.params)
                });
              }
            })]
          })
        })]
      })]
    });
  };
  var _default = exports.default = SearchScreen;
