  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = exports.AutocompleteSearch = undefined;
  var _text = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _theme = _$$_REQUIRE(_dependencyMap[3]);
  var _react = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[5]);
  var _lodash = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _searchRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _screenHelper = _$$_REQUIRE(_dependencyMap[9]);
  var _responsive = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _i18n = _$$_REQUIRE(_dependencyMap[11]);
  var _consts = _$$_REQUIRE(_dependencyMap[12]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[13]);
  var AutocompleteSearch = exports.AutocompleteSearch = function AutocompleteSearch(props) {
    var keySearch = props.keySearch,
      handleItemOnPess = props.handleItemOnPess,
      containerStyle = props.containerStyle;
    var autoCompleteKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.autoCompleteKeywordList);
    var filteredList = autoCompleteKeywordList.filter(function (item) {
      var _Object$values$join, _item$dataType;
      return !((_Object$values$join = Object.values(_consts.SeachType).join(",")) != null && _Object$values$join.includes((_item$dataType = item.dataType) == null ? undefined : _item$dataType.toLowerCase()));
    });
    var escapeRegExp = function escapeRegExp(string) {
      return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    };
    var renderTextAutoComplete = function renderTextAutoComplete(item) {
      var _autoCompleteName;
      var autoCompleteName = item == null ? undefined : item.name;
      if (item != null && item.locationFilter) {
        autoCompleteName = (0, _i18n.translate)("searchV2.autocomplete.text", {
          keyword: item == null ? undefined : item.originalKeyword
        });
      }
      var parts = (_autoCompleteName = autoCompleteName) == null ? undefined : _autoCompleteName.split(new RegExp(`(${escapeRegExp(keySearch)})`, "gi"));
      var keywordCount = 0;
      var highlightedText = parts.map(function (part, index) {
        if (part.toLowerCase() === keySearch.toLowerCase() && keywordCount === 0) {
          keywordCount++;
          return (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.textItemHighlight,
            preset: "bodyTextBold",
            text: part
          }, `part${index}`);
        } else {
          return part;
        }
      });
      return (0, _jsxRuntime.jsxs)(_reactNative2.View, {
        style: styles.wrapText,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          preset: "bodyTextRegular",
          style: styles.textItemStyles,
          numberOfLines: 1,
          children: highlightedText
        }), item != null && item.locationFilter ? (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.wrapTagText,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            style: styles.tagTextStyle,
            preset: "XSmallBold",
            accessibilityLabel: item == null ? undefined : item.locationFilter,
            testID: "locationFilterText",
            text: item == null ? undefined : item.locationFilter
          })
        }) : null]
      });
    };
    var renderItem = function renderItem(_ref) {
      var item = _ref.item,
        index = _ref.index;
      return (0, _jsxRuntime.jsxs)(_reactNative.TouchableOpacity, {
        testID: `AutocompleteItemBtn${index}`,
        accessibilityLabel: `AutocompleteItemBtn${index}`,
        style: styles.itemViewContainer,
        onPress: function onPress() {
          handleItemOnPess(item, index);
        },
        children: [!(0, _lodash.isEmpty)(item == null ? undefined : item.image) && (0, _jsxRuntime.jsx)(_reactNative2.View, {
          style: styles.iconContainer,
          children: (0, _jsxRuntime.jsx)(_reactNative2.Image, {
            source: {
              uri: (0, _screenHelper.getUriImage)(item.image)
            },
            style: styles.iconStyles,
            resizeMode: "cover"
          })
        }), renderTextAutoComplete(item)]
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative2.View, {
      style: [styles.container, containerStyle],
      children: (0, _jsxRuntime.jsx)(_reactNative2.FlatList, {
        contentContainerStyle: styles.contentContainerStyle,
        data: filteredList,
        renderItem: renderItem,
        keyExtractor: function keyExtractor(item, index) {
          return `key_autocomplete_item_${item == null ? undefined : item.name}_${index}`;
        },
        onScroll: _reactNative2.Keyboard.dismiss
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginTop: 12
    },
    contentContainerStyle: {
      paddingBottom: 80
    },
    iconContainer: {
      borderRadius: 5,
      height: 24,
      marginRight: 10,
      overflow: "hidden",
      width: 24
    },
    iconStyles: {
      height: 24,
      width: 24
    },
    itemViewContainer: {
      alignContent: "center",
      borderBottomColor: _theme.color.palette.lighterGrey,
      borderBottomWidth: 1,
      flexDirection: "row",
      paddingHorizontal: 16,
      paddingVertical: 16
    },
    textItemHighlight: {
      color: _theme.color.palette.almostBlackGrey
    },
    textItemStyles: {
      color: _theme.color.palette.almostBlackGrey,
      fontSize: 16
    },
    wrapText: {
      flexDirection: "row",
      alignItems: "center",
      gap: 4
    },
    wrapTagText: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: _theme.color.palette.greyCCCCCC
    },
    tagTextStyle: {
      fontSize: _responsive.default.getFontSize(11),
      textAlign: "center",
      textTransform: "none",
      color: _theme.color.palette.darkestGrey
    }
  });
  var _default = exports.default = AutocompleteSearch;
