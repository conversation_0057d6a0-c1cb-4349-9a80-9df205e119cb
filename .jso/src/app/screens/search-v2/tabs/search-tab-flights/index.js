  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[7]));
  var _reactRedux = _$$_REQUIRE(_dependencyMap[8]);
  var _lodash = _$$_REQUIRE(_dependencyMap[9]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[11]);
  var _tabContent = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _bottomSheet = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _text = _$$_REQUIRE(_dependencyMap[14]);
  var _icons = _$$_REQUIRE(_dependencyMap[15]);
  var _searchFlights = _$$_REQUIRE(_dependencyMap[16]);
  var _autocompleteSearch = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[17]));
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[18]));
  var _searchBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[19]));
  var _searchIndex = _$$_REQUIRE(_dependencyMap[20]);
  var _i18n = _$$_REQUIRE(_dependencyMap[21]);
  var _calendar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[22]));
  var _consts = _$$_REQUIRE(_dependencyMap[23]);
  var _native = _$$_REQUIRE(_dependencyMap[24]);
  var _constants = _$$_REQUIRE(_dependencyMap[25]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[26]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[27]);
  var _adobe = _$$_REQUIRE(_dependencyMap[28]);
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[29]);
  var _switchToast = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[30]));
  var _flightTabs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[31]));
  var _useModal2 = _$$_REQUIRE(_dependencyMap[32]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[33]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "SearchFlightsV2__";
  var SearchFlightsTab = function SearchFlightsTab(props) {
    var _props$keyword, _props$direction, _props$date;
    var dispatch = (0, _reactRedux.useDispatch)();
    var switchToastRef = (0, _react.useRef)(null);
    var navigation = (0, _native.useNavigation)();
    var autoCompleteKeywork = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var isFirstTimeEnterFlightSearch = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.isFirstTimeEnterFlightSearch);
    var _useState = (0, _react.useState)((_props$keyword = props.keyword) != null ? _props$keyword : autoCompleteKeywork),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      keySearch = _useState2[0],
      setKeySearch = _useState2[1];
    var _useState3 = (0, _react.useState)(false),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      isNoConnection = _useState4[0],
      setNoConnection = _useState4[1];
    var _useState5 = (0, _react.useState)((_props$direction = props.direction) != null ? _props$direction : _constants.FlightDirection.Arrival),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      selectedTab = _useState6[0],
      setSelectedTab = _useState6[1];
    var _useModal = (0, _useModal2.useModal)("searchTabFlightFilter"),
      isModalVisible = _useModal.isModalVisible,
      openModal = _useModal.openModal,
      closeModal = _useModal.closeModal;
    var _useState7 = (0, _react.useState)(_consts.BottomSheetType.AutoComplete),
      _useState8 = (0, _slicedToArray2.default)(_useState7, 2),
      bottomSheetType = _useState8[0],
      setBottomSheetType = _useState8[1];
    var _useState9 = (0, _react.useState)((_props$date = props.date) != null ? _props$date : (0, _moment.default)()),
      _useState0 = (0, _slicedToArray2.default)(_useState9, 2),
      selectedDate = _useState0[0],
      setSelectedDate = _useState0[1];
    var messageCommon = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getMessagesCommon);
    var msg61 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG61";
    });
    var msg62 = messageCommon == null ? undefined : messageCommon.find(function (e) {
      return (e == null ? undefined : e.code) === "MSG62";
    });
    var Rationale = {
      title: (msg61 == null ? undefined : msg61.title) || (0, _i18n.translate)("requestPermission.camera.title"),
      message: (msg61 == null ? undefined : msg61.message) || (0, _i18n.translate)("requestPermission.camera.message"),
      buttonPositive: (msg61 == null ? undefined : msg61.secondButton) || (0, _i18n.translate)("requestPermission.camera.buttonPositive"),
      buttonNegative: (msg61 == null ? undefined : msg61.firstButton) || (0, _i18n.translate)("requestPermission.camera.buttonNegative")
    };
    var handleScan = function handleScan() {
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchResultFlyScanBoardingPass, "1"));
      (0, _reactNativePermissions.request)(_reactNative2.Platform.OS === "ios" ? _reactNativePermissions.PERMISSIONS.IOS.CAMERA : _reactNativePermissions.PERMISSIONS.ANDROID.CAMERA, Rationale).then(function (result) {
        if (result === _reactNativePermissions.RESULTS.BLOCKED) {
          _reactNative2.Alert.alert((msg62 == null ? undefined : msg62.title) || (0, _i18n.translate)("scanCode.needAccessPermission.title"), (msg62 == null ? undefined : msg62.message) || (0, _i18n.translate)("scanCode.needAccessPermission.description"), [{
            text: (msg62 == null ? undefined : msg62.firstButton) || (0, _i18n.translate)("scanCode.needAccessPermission.firstButton"),
            style: "cancel",
            onPress: function onPress() {
              (0, _reactNativePermissions.openSettings)();
            }
          }, {
            text: (msg62 == null ? undefined : msg62.secondButton) || (0, _i18n.translate)("scanCode.needAccessPermission.secondButton"),
            onPress: function onPress() {
              return null;
            }
          }]);
        } else if (result === _reactNativePermissions.RESULTS.GRANTED) {
          navigation.navigate("scanCode", {
            shouldTrackDetectedFlightNumber: true
          });
        }
      });
    };
    var checkInternet = /*#__PURE__*/function () {
      var _ref = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch.isConnected;
        if (isConnected) {
          setNoConnection(false);
        } else {
          setNoConnection(true);
        }
      });
      return function checkInternet() {
        return _ref.apply(this, arguments);
      };
    }();
    var checkEnterSearchFlightFirstTime = function checkEnterSearchFlightFirstTime() {
      if (!isFirstTimeEnterFlightSearch) {
        var _switchToastRef$curre;
        switchToastRef == null || (_switchToastRef$curre = switchToastRef.current) == null || _switchToastRef$curre.show == null || _switchToastRef$curre.show();
        dispatch(_searchRedux.default.setFirstTimeEnterFlightSearch());
      }
    };
    (0, _react.useEffect)(function () {
      checkInternet();
      checkEnterSearchFlightFirstTime();
    }, []);
    var onGetAutoCompleteKeyword = (0, _react.useCallback)(function (newKeyword) {
      var param = {
        text: newKeyword.trim(),
        dataType: Object.values(_consts.SeachType).join(",")
      };
      dispatch(_searchRedux.default.getAutoCompleteKeywordRequest(param));
      dispatch(_searchRedux.default.setSearchKeyword(newKeyword));
    }, [dispatch]);
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(onGetAutoCompleteKeyword, 200), [onGetAutoCompleteKeyword]);
    var handleSearchKeywordChange = (0, _react.useCallback)(function (newKeyword) {
      if (newKeyword.trim().length > 1) return onDebounceKeySearch(newKeyword);
      dispatch(_searchRedux.default.resetListAutoCompleteKeyword());
      dispatch(_searchRedux.default.setSearchKeyword(newKeyword));
    }, [onDebounceKeySearch, autoCompleteKeywork]);
    var updateKeySearch = (0, _react.useCallback)(function (value) {
      setKeySearch(value);
      closeModal();
      dispatch(_searchRedux.default.setSearchKeyword(value));
      handleSearchKeywordChange(value);
    }, [handleSearchKeywordChange, dispatch]);
    var onPressAutoCompleteItem = function onPressAutoCompleteItem(item, index) {
      var itemPosition = `${index + 1}`;
      var tabLabel = (0, _i18n.translate)("search.tabTitles.flights");
      var flightDirectionLabel = selectedTab === _constants.FlightDirection.Arrival ? (0, _i18n.translate)("flightLanding.arrivalTabTitle") : (0, _i18n.translate)("flightLanding.departureTabTitle");
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchAutoComplete, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchAutoComplete, `${tabLabel}(${flightDirectionLabel}) | ${autoCompleteKeywork} | ${item == null ? undefined : item.name} | ${itemPosition}`));
      updateKeySearch(item.name);
    };
    var renderTabContent = (0, _react.useCallback)(function () {
      return (0, _jsxRuntime.jsx)(_tabContent.default, {
        searchTerm: keySearch,
        date: selectedDate,
        onSearchTermPress: function onSearchTermPress() {
          openModal();
          handleSearchKeywordChange(keySearch);
          setBottomSheetType(_consts.BottomSheetType.AutoComplete);
        },
        onDatePress: function onDatePress() {
          openModal();
          setBottomSheetType(_consts.BottomSheetType.DatePicker);
        },
        onSearch: function onSearch() {
          if (props.onSearch) {
            return props.onSearch({
              keyword: keySearch,
              date: selectedDate,
              direction: selectedTab
            });
          }
          var params = {
            keyword: keySearch,
            date: selectedDate,
            direction: selectedTab,
            onGoBack: function onGoBack(params) {
              if (!params) return;
              params.direction && setSelectedTab(params.direction);
              params.date && setSelectedDate(params.date);
              params.keyword && setKeySearch(params.keyword);
            }
          };
          if (props.isPushNavigation) {
            navigation.push(_constants.NavigationConstants.searchFlightsV2Result, params);
          } else {
            navigation.navigate(_constants.NavigationConstants.searchFlightsV2Result, params);
          }
        },
        onScanBoardingPass: handleScan
      });
    }, [keySearch, selectedDate, selectedTab]);
    return isNoConnection ? (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
      reload: true,
      visible: true,
      header: false,
      hideScreenHeader: false,
      onReload: checkInternet,
      headerBackgroundColor: "transparent",
      testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
      accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
      noInternetOverlayStyle: _searchFlights.rootStyles.overlayStyle
    }) : (0, _jsxRuntime.jsxs)(_reactNative2.SafeAreaView, {
      style: _searchFlights.rootStyles.containerStyle,
      children: [(0, _jsxRuntime.jsx)(_flightTabs.default, {
        selectedTab: selectedTab,
        onSelectTab: setSelectedTab,
        content: renderTabContent()
      }), (0, _jsxRuntime.jsx)(_switchToast.default, {
        ref: switchToastRef
      }), (0, _jsxRuntime.jsxs)(_bottomSheet.default, {
        isModalVisible: isModalVisible,
        onClosedSheet: closeModal,
        stopDragCollapse: true,
        onBackPressHandle: closeModal,
        containerStyle: _searchFlights.bottomSheetStyles.container,
        animationInTiming: 300,
        animationOutTiming: 300,
        openPendingModal: true,
        children: [(0, _jsxRuntime.jsxs)(_reactNative2.View, {
          style: _searchFlights.bottomSheetStyles.headerContainer,
          children: [(0, _jsxRuntime.jsx)(_text.Text, {
            style: _searchFlights.bottomSheetStyles.headerTitle,
            tx: bottomSheetType === _consts.BottomSheetType.DatePicker ? "searchV2.flightsTab.datePickerTitle" : `searchV2.flightsTab.${selectedTab}.title`
          }), (0, _jsxRuntime.jsx)(_reactNative2.View, {
            style: _searchFlights.bottomSheetStyles.headerIcon,
            children: (0, _jsxRuntime.jsx)(_icons.Cross, {
              width: 24,
              height: 24,
              onPress: closeModal
            })
          })]
        }), bottomSheetType === _consts.BottomSheetType.DatePicker ? (0, _jsxRuntime.jsx)(_calendar.default, {
          selectedDate: selectedDate,
          onSelectDate: function onSelectDate(date) {
            setSelectedDate(date);
            closeModal();
          }
        }) : (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
          style: _searchFlights.bottomSheetStyles.contentContainer,
          onPress: _reactNative2.Keyboard.dismiss,
          children: [(0, _jsxRuntime.jsx)(_searchBar.default, {
            useInputFieldV2: true,
            searchAutoCompleteFlag: _remoteConfig.REMOTE_FLAG_VALUE.ON,
            containerStyle: _searchFlights.bottomSheetStyles.searchBar,
            inputContainerStyle: _searchFlights.bottomSheetStyles.searchInput,
            inputProps: {
              placeholder: (0, _i18n.translate)("searchV2.placeHolder.flights1"),
              placeholderList: [(0, _i18n.translate)("searchV2.placeHolder.flights1"), (0, _i18n.translate)("searchV2.placeHolder.flights2")]
            },
            isShowBack: false,
            tab: _searchIndex.SearchIndex.flights,
            keyword: keySearch,
            onChangeKeyword: handleSearchKeywordChange,
            onSearchClear: function onSearchClear() {
              dispatch(_searchRedux.default.setSearchKeyword(""));
              handleSearchKeywordChange("");
            },
            onSubmitLocal: function onSubmitLocal() {
              updateKeySearch(autoCompleteKeywork);
              _reactNative2.Keyboard.dismiss();
            },
            testID: `${SCREEN_NAME}SearchBar`,
            accessibilityLabel: `${SCREEN_NAME}SearchBar`,
            returnKeyType: "previous"
          }), (0, _jsxRuntime.jsxs)(_reactNative2.View, {
            style: _searchFlights.bottomSheetStyles.textContainer,
            children: [(0, _jsxRuntime.jsx)(_text.Text, {
              tx: `searchV2.flightsTab.${selectedTab}.information`,
              style: _searchFlights.bottomSheetStyles.informationText
            }), (0, _jsxRuntime.jsx)(_text.Text, {
              style: _searchFlights.bottomSheetStyles.askText,
              tx: `searchV2.flightsTab.${selectedTab}.ask`,
              onPress: function onPress() {
                return setSelectedTab(selectedTab === _constants.FlightDirection.Arrival ? _constants.FlightDirection.Departure : _constants.FlightDirection.Arrival);
              }
            })]
          }), (0, _jsxRuntime.jsx)(_autocompleteSearch.default, {
            containerStyle: _searchFlights.tabContentStyles.autocompleteContainerStyle,
            keySearch: autoCompleteKeywork.trim(),
            flightType: selectedTab,
            handleItemOnPress: onPressAutoCompleteItem
          })]
        })]
      })]
    });
  };
  var _default = exports.default = SearchFlightsTab;
