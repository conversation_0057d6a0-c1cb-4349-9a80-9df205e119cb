  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _searchFlights = _$$_REQUIRE(_dependencyMap[1]);
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _icons = _$$_REQUIRE(_dependencyMap[3]);
  var _text = _$$_REQUIRE(_dependencyMap[4]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[5]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SwitchToast = (0, _react.forwardRef)(function (props, ref) {
    var animatedValue = (0, _react.useRef)(new _reactNative.Animated.Value(0)).current;
    var startAnimation = function startAnimation() {
      var seq = _reactNative.Animated.sequence([_reactNative.Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1000,
        easing: _reactNative.Easing.linear,
        useNativeDriver: true
      }), _reactNative.Animated.delay(2000), _reactNative.Animated.timing(animatedValue, {
        toValue: 2,
        duration: 1000,
        easing: _reactNative.Easing.linear,
        useNativeDriver: true
      })]);
      seq.start(function (_ref) {
        var finished = _ref.finished;
        if (finished) {
          animatedValue.setValue(0);
        }
      });
    };
    (0, _react.useImperativeHandle)(ref, function () {
      return {
        show: show
      };
    }, []);
    var show = function show() {
      startAnimation();
    };
    var opacityValue = animatedValue.interpolate({
      inputRange: [0, 1, 2],
      outputRange: [0, 1, 0],
      extrapolate: "clamp"
    });
    var translateY = animatedValue.interpolate({
      inputRange: [0, 1, 2],
      outputRange: [10, 0, 0],
      extrapolate: "clamp"
    });
    var zIndexToastAnimated = animatedValue.interpolate({
      inputRange: [0, 1, 2],
      outputRange: [-1, 9, -1],
      extrapolate: "clamp"
    });
    return (0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      style: [_searchFlights.tabContentStyles.toastContainer, {
        top: 50,
        left: 187.5,
        opacity: opacityValue,
        zIndex: zIndexToastAnimated,
        transform: [{
          translateY: translateY
        }]
      }],
      children: (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: _searchFlights.tabContentStyles.wrapToast,
        children: [(0, _jsxRuntime.jsx)(_icons.FlightTopTooltip, {}), (0, _jsxRuntime.jsx)(_reactNative.View, {
          style: _searchFlights.tabContentStyles.wrapTxtToast,
          children: (0, _jsxRuntime.jsx)(_text.Text, {
            tx: "searchV2.flightsTab.toastSwitchToDeparture",
            preset: "caption1Italic",
            style: _searchFlights.tabContentStyles.txtToast
          })
        })]
      })
    });
  });
  var _default = exports.default = SwitchToast;
