  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SearchTabAll = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNative2 = _$$_REQUIRE(_dependencyMap[6]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _native = _$$_REQUIRE(_dependencyMap[8]);
  var _netinfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _searchRecentSection = _$$_REQUIRE(_dependencyMap[10]);
  var _searchRecentSection2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _searchPopularSection = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _searchRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[13]));
  var _text = _$$_REQUIRE(_dependencyMap[14]);
  var _theme = _$$_REQUIRE(_dependencyMap[15]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[16]);
  var _errorOverlayNoConnection = _$$_REQUIRE(_dependencyMap[17]);
  var _adobe = _$$_REQUIRE(_dependencyMap[18]);
  var _searchScreenContext = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[19]));
  var _screenHook = _$$_REQUIRE(_dependencyMap[20]);
  var _navigationHelper = _$$_REQUIRE(_dependencyMap[21]);
  var _recentSearchHook = _$$_REQUIRE(_dependencyMap[22]);
  var _searchIndex = _$$_REQUIRE(_dependencyMap[23]);
  var _searchBar = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[24]));
  var _lodash = _$$_REQUIRE(_dependencyMap[25]);
  var _autocompleteSearch = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _i18n = _$$_REQUIRE(_dependencyMap[27]);
  var _constants = _$$_REQUIRE(_dependencyMap[28]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[29]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[30]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var SCREEN_NAME = "SearchAll__";
  var SearchTabAll = exports.SearchTabAll = function SearchTabAll(_ref) {
    var _keySearch$trim, _popularSearchKeyword;
    var navigation = _ref.navigation,
      _ref$autoFocusTextInp = _ref.autoFocusTextInput,
      autoFocusTextInputProp = _ref$autoFocusTextInp === undefined ? false : _ref$autoFocusTextInp;
    var dispatch = (0, _reactRedux.useDispatch)();
    var isFocused = (0, _native.useIsFocused)();
    var _useContext = (0, _react.useContext)(_searchScreenContext.default),
      isPressSearchKey = _useContext.isPressSearchKey,
      setIsPressSearchKey = _useContext.setIsPressSearchKey;
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var _useRecentSearch = (0, _recentSearchHook.useRecentSearch)(),
      addNewRecentSearchKeyword = _useRecentSearch.addNewRecentSearchKeyword;
    var _useState = (0, _react.useState)(false),
      _useState2 = (0, _slicedToArray2.default)(_useState, 2),
      isNoConnection = _useState2[0],
      setNoConnection = _useState2[1];
    var keyword = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.searchKeyword);
    var popularSearchKeywordList = (0, _reactRedux.useSelector)(_searchRedux.SearchSelectors.popularSearchKeywordList);
    var _useState3 = (0, _react.useState)(""),
      _useState4 = (0, _slicedToArray2.default)(_useState3, 2),
      keySearch = _useState4[0],
      setKeySearch = _useState4[1];
    var keySearchLength = !(0, _lodash.isEmpty)(keySearch) && (keySearch == null || (_keySearch$trim = keySearch.trim()) == null ? undefined : _keySearch$trim.length);
    var onGetAutoCompleteKeyword = function onGetAutoCompleteKeyword(newKeyword) {
      var param = {
        text: newKeyword == null ? undefined : newKeyword.trim(),
        dataType: ""
      };
      dispatch(_searchRedux.default.setViewAllFlight(false));
      dispatch(_searchRedux.default.getAutoCompleteKeywordRequest(param));
    };
    var _useState5 = (0, _react.useState)(false),
      _useState6 = (0, _slicedToArray2.default)(_useState5, 2),
      autoFocusTextInput = _useState6[0],
      setAutoFocusTextInput = _useState6[1];
    var onDebounceKeySearch = (0, _react.useCallback)((0, _lodash.debounce)(onGetAutoCompleteKeyword, 200), []);
    var _useHandleNavigation = (0, _navigationHelper.useHandleNavigation)("SEARCH_TAB_ALL"),
      handleNavigation = _useHandleNavigation.handleNavigation;
    (0, _screenHook.useCurrentScreenActiveAndPreviousScreenHook)("Search_All");
    (0, _react.useEffect)(function () {
      var unsubscribeFocus = navigation.addListener("focus", function () {
        (0, _adobe.commonTrackingScreen)("Search_All", (0, _screenHook.getPreviousScreen)(), isLoggedIn);
      });
      return unsubscribeFocus;
    }, [navigation]);
    (0, _react.useEffect)(function () {
      var checkInternet = /*#__PURE__*/function () {
        var _ref2 = (0, _asyncToGenerator2.default)(function* () {
          var _yield$NetInfo$fetch = yield _netinfo.default.fetch(),
            isConnected = _yield$NetInfo$fetch.isConnected;
          if (!isConnected) {
            setNoConnection(true);
          }
        });
        return function checkInternet() {
          return _ref2.apply(this, arguments);
        };
      }();
      checkInternet();
      return function () {
        dispatch(_searchRedux.default.searchAllV2Reset());
      };
    }, []);
    (0, _react.useEffect)(function () {
      return function () {
        if (isFocused) {
          dispatch(_searchRedux.default.searchAllV2Reset());
        }
      };
    }, [keyword, isFocused]);
    (0, _react.useEffect)(function () {
      if (isFocused && autoFocusTextInputProp) {
        // Manually trigger TextInput focus when tab becomes active.
        // Delay avoids Android focus glitches during tab transition.
        setTimeout(function () {
          setAutoFocusTextInput(true);
        }, 100);
      }
      if (!isFocused) {
        dispatch(_searchRedux.default.resetListYouMayAlsoLikeData());
      }
    }, [isFocused]);
    (0, _react.useEffect)(function () {
      if (isPressSearchKey) {
        searchAll(keyword);
        setIsPressSearchKey(false);
      }
    }, [isPressSearchKey]);
    var searchAll = /*#__PURE__*/function () {
      var _ref3 = (0, _asyncToGenerator2.default)(function* (newKeyword) {
        var textSearch = newKeyword == null ? undefined : newKeyword.trim();
        if (!textSearch || (textSearch == null ? undefined : textSearch.length) < 2) {
          return;
        }
        dispatch(_searchRedux.default.setSearchKeyword(keyword));
        navigation.navigate(_constants.NavigationConstants.searchResult);
      });
      return function searchAll(_x) {
        return _ref3.apply(this, arguments);
      };
    }();
    var onReloadData = /*#__PURE__*/function () {
      var _ref4 = (0, _asyncToGenerator2.default)(function* () {
        var _yield$NetInfo$fetch2 = yield _netinfo.default.fetch(),
          isConnected = _yield$NetInfo$fetch2.isConnected;
        if (isConnected) {
          setNoConnection(false);
          searchAll(keyword);
        } else {
          setNoConnection(true);
        }
      });
      return function onReloadData() {
        return _ref4.apply(this, arguments);
      };
    }();
    if (isNoConnection) {
      return (0, _jsxRuntime.jsx)(_errorOverlayNoConnection.ErrorOverlayNoConnection, {
        reload: true,
        header: false,
        headerBackgroundColor: "transparent",
        hideScreenHeader: false,
        visible: true,
        testID: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        accessibilityLabel: `${SCREEN_NAME}__ErrorOverlayNoConnection`,
        onReload: onReloadData,
        noInternetOverlayStyle: styles.overlayStyle
      });
    }
    var handleSearchKeywordChange = function handleSearchKeywordChange(newKeyword) {
      var _newKeyword$trim;
      if (!(0, _lodash.isEmpty)(keyword) && keyword !== newKeyword) {
        dispatch(_searchRedux.default.setSearchKeyword(""));
      }
      setKeySearch(newKeyword);
      if ((newKeyword == null || (_newKeyword$trim = newKeyword.trim()) == null ? undefined : _newKeyword$trim.length) > 1) {
        onDebounceKeySearch(newKeyword);
      } else {
        dispatch(_searchRedux.default.resetListAutoCompleteKeyword());
      }
    };
    var handleAutoCompleteOnPress = function handleAutoCompleteOnPress(item, index) {
      var searchTabName = (0, _searchScreenContext.getSearchTabName)(_searchIndex.SearchIndex.all);
      var itemPosition = `${index + 1}`;
      (0, _adobe.trackAction)(_adobe.AdobeTagName.CAppSearchAutoComplete, (0, _defineProperty2.default)({}, _adobe.AdobeTagName.CAppSearchAutoComplete, `${searchTabName} | ${keySearch} | ${item == null ? undefined : item.name} | ${itemPosition}`));
      if ((0, _lodash.isEmpty)(item.navigation)) {
        setKeySearch("");
        addNewRecentSearchKeyword(item.name);
        if (item.locationFilter) {
          dispatch(_searchRedux.default.setSearchKeyword(item.originalKeyword));
          navigation.navigate(_constants.NavigationConstants.searchResult, {
            locationFilter: item.locationFilter
          });
        } else {
          dispatch(_searchRedux.default.setSearchKeyword(item.name));
          navigation.navigate(_constants.NavigationConstants.searchResult);
        }
      } else {
        var _navigation = item.navigation;
        handleNavigation(_navigation == null ? undefined : _navigation.type, _navigation == null ? undefined : _navigation.value, (_navigation == null ? undefined : _navigation.redirect) || {});
      }
    };
    return (0, _jsxRuntime.jsx)(_jsxRuntime.Fragment, {
      children: (0, _jsxRuntime.jsxs)(_reactNative.Pressable, {
        style: styles.container,
        onPress: _reactNative2.Keyboard.dismiss,
        children: [(0, _jsxRuntime.jsx)(_searchBar.default, {
          useInputFieldV2: true,
          containerStyle: styles.wrapSearchBar,
          inputContainerStyle: styles.inputContainerStyle,
          inputProps: {
            placeholder: (0, _i18n.translate)("searchV2.placeHolder.all1"),
            inputFieldStyle: {
              inputGroupStyle: styles.inputGroupStyle
            },
            placeholderList: [(0, _i18n.translate)("searchV2.placeHolder.all1"), (0, _i18n.translate)("searchV2.placeHolder.all2"), (0, _i18n.translate)("searchV2.placeHolder.all3")]
          },
          isShowBack: false,
          tab: _searchIndex.SearchIndex.all,
          keyword: keyword,
          onChangeKeyword: function onChangeKeyword(s) {
            handleSearchKeywordChange(s);
          },
          onSearchClear: function onSearchClear() {
            dispatch(_searchRedux.default.setSearchKeyword(""));
            handleSearchKeywordChange("");
          },
          onSubmitEditing: function onSubmitEditing() {
            setKeySearch("");
          },
          searchAutoCompleteFlag: _remoteConfig.REMOTE_FLAG_VALUE.ON,
          testID: `${SCREEN_NAME}SearchBar`,
          accessibilityLabel: `${SCREEN_NAME}SearchBar`,
          autoFocusTextInput: autoFocusTextInput
        }), keySearchLength > 1 ? (0, _jsxRuntime.jsx)(_autocompleteSearch.default, {
          containerStyle: styles.autocompleteContainerStyle,
          keySearch: keySearch == null ? undefined : keySearch.trim(),
          handleItemOnPess: handleAutoCompleteOnPress
        }) : (0, _jsxRuntime.jsxs)(_reactNative2.ScrollView, {
          showsHorizontalScrollIndicator: false,
          showsVerticalScrollIndicator: false,
          keyboardDismissMode: "on-drag",
          keyboardShouldPersistTaps: "always",
          children: [(0, _jsxRuntime.jsx)(_searchRecentSection2.default, {
            type: _searchRecentSection.SearchRecentSectionType.default
          }), (0, _jsxRuntime.jsx)(_searchPopularSection.default, {
            data: popularSearchKeywordList == null || (_popularSearchKeyword = popularSearchKeywordList.find(function (item) {
              return (item == null ? undefined : item.searchCategoryTab) === "all";
            })) == null ? undefined : _popularSearchKeyword.popularKeywords,
            searchIndex: _searchIndex.SearchIndex.all
          })]
        })]
      })
    });
  };
  var styles = _reactNative2.StyleSheet.create({
    container: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      flex: 1
    },
    autocompleteContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    containerEvent: {
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between"
    },
    containerFacilities: {
      display: "flex",
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between"
    },
    dividerStyle: {
      backgroundColor: _theme.color.palette.lighterGrey,
      height: 1,
      marginTop: 16
    },
    emptySectionListStyle: {
      alignItems: "center",
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      justifyContent: "center",
      paddingHorizontal: 20
    },
    flatListItemStyle: {
      marginBottom: 12
    },
    footerEventSectionStyles: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: 24
    },
    footerFacilityAndServiceSectionStyles: {
      alignItems: "center",
      justifyContent: "center",
      marginTop: 24
    },
    footerSectionStyles: {
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 10,
      marginTop: 12
    },
    headerEventSectionStyle: {
      marginBottom: 7
    },
    headerSectionStyle: Object.assign({}, _text.presets.h4, {
      color: _theme.color.palette.almostBlackGrey,
      letterSpacing: 0.96,
      lineHeight: 22,
      marginBottom: 16,
      marginTop: 40
    }),
    itemResultStyle: {
      marginBottom: 12
    },
    moreTextStyles: Object.assign({}, _text.presets.bodyTextBold, {
      color: _theme.color.palette.lightPurple
    }),
    overlayStyle: {
      backgroundColor: _theme.color.palette.lightestGrey,
      flex: 1,
      marginBottom: 80
    },
    sectionContainerStyles: {
      paddingHorizontal: 20
    },
    wrapSearchBar: {
      backgroundColor: _theme.color.palette.almostWhiteGrey,
      marginTop: 20,
      paddingLeft: 16,
      paddingRight: 16
    },
    inputContainerStyle: {
      backgroundColor: _theme.color.palette.almostWhiteGrey
    },
    iconLeft: {
      marginTop: 11,
      marginLeft: 5,
      marginRight: 10
    },
    inputGroupStyle: {
      borderColor: _theme.color.palette.lightGrey,
      height: 44
    }
  });
