  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.SEARCH_TAG = exports.SEARCH_SOURCES_TYPES = exports.SEARCH_SOURCES = exports.SEARCH_OPERATING_HOUR_TEXT = exports.SEARCH_LABELS = exports.SEARCH_FILTER_SPECIAL_VALUE = exports.SEARCH_FILTER_LOCATION = exports.SEARCH_FILTER_CATEGORY_KEY = exports.SEARCH_FILTER_AREAS = exports.OPEN_TIME = exports.MAX_ISHOPCHANGI_ITEMS = exports.MAX_AUTOCOMPLETE_ITEMS_V2 = exports.MAX_AUTOCOMPLETE_ITEMS_V1 = exports.ISHOP<PERSON>ANGI_SEARCHPAGE = exports.ISHOPCHANGI_ITEMS_POSITION = exports.ISHOPCHANGI_HOMEPAGE_BRAND_OFFER = exports.ISHOPCHANGI_HOMEPAGE = exports.FORMAT_DATE = exports.DEFAULT_ISHOPCHANGI_URL = exports.DEFAULT_CHANGIAIRPORT_URL = exports.CLOSE_TIME = undefined;
  var SEARCH_TAG = exports.SEARCH_TAG = /*#__PURE__*/function (SEARCH_TAG) {
    SEARCH_TAG["STAFF_PERKS"] = "Staff Perks";
    SEARCH_TAG["AVAILABLE_ON_ISHOPCHANGI"] = "Available on iShopChangi";
    SEARCH_TAG["BOOK_VIA_APP"] = "Book via App";
    SEARCH_TAG["ONLY_AVAILABLE_ON_APP"] = "Only available on App";
    SEARCH_TAG["SPEND_REDEEM"] = "Spend & Redeem";
    return SEARCH_TAG;
  }({});
  var SEARCH_OPERATING_HOUR_TEXT = exports.SEARCH_OPERATING_HOUR_TEXT = /*#__PURE__*/function (SEARCH_OPERATING_HOUR_TEXT) {
    SEARCH_OPERATING_HOUR_TEXT["CLOSED"] = "Closed";
    SEARCH_OPERATING_HOUR_TEXT["OPEN"] = "Open";
    return SEARCH_OPERATING_HOUR_TEXT;
  }({});
  var SEARCH_LABELS = exports.SEARCH_LABELS = /*#__PURE__*/function (SEARCH_LABELS) {
    SEARCH_LABELS["CATEGORY"] = "category";
    SEARCH_LABELS["DIETARY"] = "dietary";
    SEARCH_LABELS["AVAILABILITY"] = "availability";
    SEARCH_LABELS["CUISINE"] = "cuisine";
    return SEARCH_LABELS;
  }({});
  var SEARCH_SOURCES = exports.SEARCH_SOURCES = /*#__PURE__*/function (SEARCH_SOURCES) {
    SEARCH_SOURCES["TENANTS"] = "TENANTS";
    SEARCH_SOURCES["FACILITIES"] = "FACILITIES";
    SEARCH_SOURCES["ATTRACTIONS"] = "ATTRACTIONS";
    SEARCH_SOURCES["EVENTS"] = "EVENTS";
    SEARCH_SOURCES["ISC"] = "ISC";
    SEARCH_SOURCES["MAPPOI"] = "MAPPOI";
    SEARCH_SOURCES["CACONTENT"] = "CACONTENT";
    return SEARCH_SOURCES;
  }({});
  var SEARCH_SOURCES_TYPES = exports.SEARCH_SOURCES_TYPES = /*#__PURE__*/function (SEARCH_SOURCES_TYPES) {
    SEARCH_SOURCES_TYPES["shop"] = "shop";
    SEARCH_SOURCES_TYPES["dine"] = "dine";
    return SEARCH_SOURCES_TYPES;
  }({});
  var SEARCH_FILTER_AREAS = exports.SEARCH_FILTER_AREAS = /*#__PURE__*/function (SEARCH_FILTER_AREAS) {
    SEARCH_FILTER_AREAS["TRANSIT"] = "TRANSIT";
    SEARCH_FILTER_AREAS["PUBLIC"] = "PUBLIC";
    return SEARCH_FILTER_AREAS;
  }({});
  var SEARCH_FILTER_LOCATION = exports.SEARCH_FILTER_LOCATION = /*#__PURE__*/function (SEARCH_FILTER_LOCATION) {
    SEARCH_FILTER_LOCATION["JEWEL"] = "JEWEL";
    SEARCH_FILTER_LOCATION["T1"] = "T1";
    SEARCH_FILTER_LOCATION["T2"] = "T2";
    SEARCH_FILTER_LOCATION["T3"] = "T3";
    SEARCH_FILTER_LOCATION["T4"] = "T4";
    return SEARCH_FILTER_LOCATION;
  }({});
  var SEARCH_FILTER_SPECIAL_VALUE = exports.SEARCH_FILTER_SPECIAL_VALUE = {
    OPEN: "Open",
    OPEN_SOME_OUTLETS: "Open Some Outlets"
  };
  var SEARCH_FILTER_CATEGORY_KEY = exports.SEARCH_FILTER_CATEGORY_KEY = /*#__PURE__*/function (SEARCH_FILTER_CATEGORY_KEY) {
    SEARCH_FILTER_CATEGORY_KEY["category"] = "category";
    SEARCH_FILTER_CATEGORY_KEY["dines"] = "dines";
    SEARCH_FILTER_CATEGORY_KEY["shops"] = "shops";
    SEARCH_FILTER_CATEGORY_KEY["events"] = "events";
    SEARCH_FILTER_CATEGORY_KEY["facilities"] = "facilities";
    SEARCH_FILTER_CATEGORY_KEY["attractions"] = "attractions";
    SEARCH_FILTER_CATEGORY_KEY["promotions"] = "promotions";
    SEARCH_FILTER_CATEGORY_KEY["others"] = "others";
    // => “App Features”, “Map POI”, “CA Content”.
    SEARCH_FILTER_CATEGORY_KEY["cuisine"] = "cuisine";
    SEARCH_FILTER_CATEGORY_KEY["dietary"] = "dietary";
    SEARCH_FILTER_CATEGORY_KEY["availability"] = "availability";
    SEARCH_FILTER_CATEGORY_KEY["areaList"] = "areaList";
    SEARCH_FILTER_CATEGORY_KEY["locationList"] = "locationList";
    SEARCH_FILTER_CATEGORY_KEY["open"] = "open";
    SEARCH_FILTER_CATEGORY_KEY["free"] = "free";
    SEARCH_FILTER_CATEGORY_KEY["special"] = "special";
    return SEARCH_FILTER_CATEGORY_KEY;
  }({});
  var OPEN_TIME = exports.OPEN_TIME = "08:00:00";
  var CLOSE_TIME = exports.CLOSE_TIME = "22:00:00";
  var DEFAULT_CHANGIAIRPORT_URL = exports.DEFAULT_CHANGIAIRPORT_URL = "https://www.changiairport.com";
  var DEFAULT_ISHOPCHANGI_URL = exports.DEFAULT_ISHOPCHANGI_URL = "https://www.ishopchangi.com";
  var ISHOPCHANGI_SEARCHPAGE = exports.ISHOPCHANGI_SEARCHPAGE = "/en/search?utm_source=ichangi_app&utm_medium=at&utm_campaign=rec";
  var ISHOPCHANGI_HOMEPAGE = exports.ISHOPCHANGI_HOMEPAGE = "/en/home?utm_source=ichangi_app&utm_medium=at&utm_campaign=rec";
  var ISHOPCHANGI_HOMEPAGE_BRAND_OFFER = exports.ISHOPCHANGI_HOMEPAGE_BRAND_OFFER = "/en/home?utm_source=ichangi_app&utm_medium=app_carousel&utm_campaign=alwayson&utm_content=general&utm_term=shopall_textlink";
  var MAX_ISHOPCHANGI_ITEMS = exports.MAX_ISHOPCHANGI_ITEMS = 10;
  var ISHOPCHANGI_ITEMS_POSITION = exports.ISHOPCHANGI_ITEMS_POSITION = 4;
  var MAX_AUTOCOMPLETE_ITEMS_V1 = exports.MAX_AUTOCOMPLETE_ITEMS_V1 = 8;
  var MAX_AUTOCOMPLETE_ITEMS_V2 = exports.MAX_AUTOCOMPLETE_ITEMS_V2 = 20;
  var FORMAT_DATE = exports.FORMAT_DATE = "D MMM YYYY";
