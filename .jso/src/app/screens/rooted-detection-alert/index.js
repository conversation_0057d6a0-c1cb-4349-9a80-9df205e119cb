  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = RootedDetectionAlert;
  var React = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[0]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[1]);
  var _palette = _$$_REQUIRE(_dependencyMap[2]);
  var _text = _$$_REQUIRE(_dependencyMap[3]);
  var _icons = _$$_REQUIRE(_dependencyMap[4]);
  var _screenHook = _$$_REQUIRE(_dependencyMap[5]);
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[6]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var styles = _reactNative.StyleSheet.create({
    container: {
      backgroundColor: _palette.palette.lightestGrey,
      flex: 1,
      height: _reactNative.Dimensions.get("screen").height,
      justifyContent: "center",
      width: _reactNative.Dimensions.get("screen").width
    },
    contentContainer: {
      alignItems: "center",
      marginHorizontal: 40
    },
    descriptionText: {
      color: _palette.palette.black,
      textAlign: "center"
    },
    imageContainer: {
      alignItems: "center",
      marginBottom: 33
    },
    title: {
      color: _palette.palette.almostBlackGrey,
      lineHeight: 28,
      marginBottom: 16,
      textAlign: "center"
    }
  });
  function RootedDetectionAlert() {
    React.useEffect(function () {
      (0, _screenHook.hideSplashScreen)();
    }, []);
    return (0, _jsxRuntime.jsxs)(_reactNative.View, {
      style: styles.container,
      children: [(0, _jsxRuntime.jsx)(_reactNative.StatusBar, {
        translucent: true,
        backgroundColor: "transparent",
        barStyle: "dark-content"
      }), (0, _jsxRuntime.jsx)(_reactNative.View, {
        style: styles.imageContainer,
        children: (0, _jsxRuntime.jsx)(_icons.IconRootedDetection, {})
      }), (0, _jsxRuntime.jsxs)(_reactNative.View, {
        style: styles.contentContainer,
        children: [(0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.title,
          preset: "h2",
          tx: "rootedDetections.title"
        }), (0, _jsxRuntime.jsx)(_text.Text, {
          style: styles.descriptionText,
          preset: "caption1Regular",
          tx: "rootedDetections.description"
        })]
      })]
    });
  }
