  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.titleTextStyle = exports.overrideContinueButtonViewStyle = exports.gradientContinueButtonViewStyle = exports.disabledContinueButtonTextStyle = exports.disabledContinueButtonColors = exports.disabledClearAllButtonViewStyle = exports.disabledClearAllButtonTextStyle = exports.defaultContinueButtonTextStyle = exports.defaultContinueButtonColors = exports.defaultClearAllButtonViewStyle = exports.defaultClearAllButtonTextStyle = exports.contentTextStyle = exports.contentContainerStyle = exports.containerStyle = exports.buttonContainerStyle = undefined;
  var _reactNative = _$$_REQUIRE(_dependencyMap[0]);
  var _theme = _$$_REQUIRE(_dependencyMap[1]);
  var _text = _$$_REQUIRE(_dependencyMap[2]);
  var _Dimensions$get = _reactNative.Dimensions.get("window"),
    width = _Dimensions$get.width;
  var containerStyle = exports.containerStyle = {
    backgroundColor: _theme.color.palette.lightestGrey
  };
  var contentContainerStyle = exports.contentContainerStyle = {
    marginTop: 40,
    marginBottom: 89,
    marginStart: 24,
    marginEnd: 24
  };
  var titleTextStyle = exports.titleTextStyle = Object.assign({}, _text.presets.h2, {
    color: _theme.color.palette.almostBlackGrey,
    marginBottom: 16
  });
  var contentTextStyle = exports.contentTextStyle = Object.assign({}, _text.presets.bodyTextRegular, {
    color: _theme.color.palette.almostBlackGrey,
    marginBottom: 24
  });
  var buttonContainerStyle = exports.buttonContainerStyle = {
    backgroundColor: _theme.color.palette.almostWhiteGrey,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    height: 105,
    elevation: 5,
    shadowColor: _theme.color.palette.almostBlackGrey,
    shadowOpacity: 0.16,
    shadowRadius: 20,
    shadowOffset: {
      height: 6,
      width: 0
    },
    paddingStart: 24,
    paddingEnd: 24
  };
  var baseClearAllButtonViewStyle = {
    backgroundColor: _theme.color.palette.almostWhiteGrey,
    width: (width - 61) / 2,
    marginEnd: 6.5
  };
  var defaultClearAllButtonViewStyle = exports.defaultClearAllButtonViewStyle = Object.assign({}, baseClearAllButtonViewStyle, {
    borderColor: _theme.color.palette.lightPurple
  });
  var disabledClearAllButtonViewStyle = exports.disabledClearAllButtonViewStyle = Object.assign({}, baseClearAllButtonViewStyle, {
    borderColor: _theme.color.palette.darkGrey
  });
  var defaultClearAllButtonTextStyle = exports.defaultClearAllButtonTextStyle = {
    color: _theme.color.palette.lightPurple
  };
  var disabledClearAllButtonTextStyle = exports.disabledClearAllButtonTextStyle = {
    color: _theme.color.palette.darkGrey
  };
  var gradientContinueButtonViewStyle = exports.gradientContinueButtonViewStyle = {
    borderRadius: 60,
    width: (width - 61) / 2,
    marginStart: 6.5
  };
  var overrideContinueButtonViewStyle = exports.overrideContinueButtonViewStyle = {
    backgroundColor: _theme.color.transparent,
    borderColor: _theme.color.transparent
  };
  var defaultContinueButtonTextStyle = exports.defaultContinueButtonTextStyle = {
    color: _theme.color.palette.almostWhiteGrey
  };
  var disabledContinueButtonTextStyle = exports.disabledContinueButtonTextStyle = {
    color: _theme.color.palette.darkGrey
  };
  var defaultContinueButtonColors = exports.defaultContinueButtonColors = [_theme.color.palette.gradientColor1End, _theme.color.palette.gradientColor1Start];
  var disabledContinueButtonColors = exports.disabledContinueButtonColors = [_theme.color.palette.lightGrey, _theme.color.palette.lightGrey];
