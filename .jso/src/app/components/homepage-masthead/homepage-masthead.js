  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.HomepageMasthead = HomepageMasthead;
  exports.mappingTierCode = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _react = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactRedux = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _aemRedux = _$$_REQUIRE(_dependencyMap[6]);
  var _nativeAuthRedux = _$$_REQUIRE(_dependencyMap[7]);
  var _profileRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[8]));
  var _forYouRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _utils = _$$_REQUIRE(_dependencyMap[10]);
  var _icons = _$$_REQUIRE(_dependencyMap[11]);
  var _sectionsData = _$$_REQUIRE(_dependencyMap[12]);
  var _shimmerPlaceholder = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[13]));
  var _text = _$$_REQUIRE(_dependencyMap[14]);
  var _color = _$$_REQUIRE(_dependencyMap[15]);
  var _constants = _$$_REQUIRE(_dependencyMap[16]);
  var _utils2 = _$$_REQUIRE(_dependencyMap[17]);
  var _homepageMasthead = _$$_REQUIRE(_dependencyMap[18]);
  var _exploreRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _i18n = _$$_REQUIRE(_dependencyMap[20]);
  var _native = _$$_REQUIRE(_dependencyMap[21]);
  var _getConfigurationPermission = _$$_REQUIRE(_dependencyMap[22]);
  var _changiRewardsMemberCard = _$$_REQUIRE(_dependencyMap[23]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[24]);
  var _reactNativeReanimatedCarousel = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[25]));
  var _reactNativeVideo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[26]));
  var _jsxRuntime = _$$_REQUIRE(_dependencyMap[27]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var NON_LOGGED_IN = "Non-logged-in";
  var heightScreen = _reactNative.Dimensions.get("window").height;
  var transformRewardPoint = function transformRewardPoint(rewardsData) {
    var points = (0, _lodash.get)(rewardsData, "reward.point");
    if (points) {
      var pointSubstr = points.substring(0, 8);
      if (points.length > 10) {
        return `${pointSubstr}...`;
      }
      return points;
    }
    return "";
  };
  var mappingTierCode = exports.mappingTierCode = function mappingTierCode(tierCode, arrTier) {
    if (tierCode === _changiRewardsMemberCard.Tier.StaffGold) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Gold;
      });
    } else if (tierCode === _changiRewardsMemberCard.Tier.StaffMember) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Member;
      });
    } else if (tierCode === _changiRewardsMemberCard.Tier.StaffPlatinum) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Platinum;
      });
    } else if (tierCode === _changiRewardsMemberCard.Tier.StaffMonarch) {
      return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
        return tierIcon.tier === _changiRewardsMemberCard.Tier.Monarch;
      });
    }
    return arrTier == null ? undefined : arrTier.find(function (tierIcon) {
      return tierIcon.tier === tierCode;
    });
  };
  var transformTierIcon = function transformTierIcon(rewardsData, isLoggedIn, tierIconsAEM) {
    var _rewardsData$reward;
    var tier = isLoggedIn ? rewardsData == null || (_rewardsData$reward = rewardsData.reward) == null || (_rewardsData$reward = _rewardsData$reward.currentTierInfo) == null ? undefined : _rewardsData$reward.replace(" ", "") : NON_LOGGED_IN;
    var tierAEM = mappingTierCode(tier, tierIconsAEM);
    return (0, _utils.mappingUrlAem)(tierAEM == null ? undefined : tierAEM.icon);
  };
  var getThemeHomePage = function getThemeHomePage(isLoggedIn, rewardsData, mastHeadsAEM) {
    var _rewardsData$reward2;
    var tier = isLoggedIn ? rewardsData == null || (_rewardsData$reward2 = rewardsData.reward) == null || (_rewardsData$reward2 = _rewardsData$reward2.currentTierInfo) == null ? undefined : _rewardsData$reward2.replace(" ", "") : NON_LOGGED_IN;
    var imageHomepageAEM = mappingTierCode(tier, mastHeadsAEM);
    return {
      srcImageHomepage: (0, _utils.mappingUrlAem)(imageHomepageAEM == null ? undefined : imageHomepageAEM.image),
      isDarkTheme: (imageHomepageAEM == null ? undefined : imageHomepageAEM.background) === "Dark"
    };
  };
  var getThemeColor = function getThemeColor(isDarkTheme, errorAEM, profileCardNoMissing) {
    if (isDarkTheme || errorAEM || profileCardNoMissing) {
      return _color.color.palette.whiteGrey;
    }
    return _color.color.palette.almostBlackGrey;
  };
  var _Dimensions$get = _reactNative.Dimensions.get("screen"),
    width = _Dimensions$get.width,
    height = _Dimensions$get.height;
  var overLayLoading = {
    width: width,
    height: height,
    position: "absolute"
  };
  function HomepageMasthead(props) {
    var sourceSystem = props.sourceSystem,
      onChangiPayPressed = props.onChangiPayPressed,
      onSearchBarPressed = props.onSearchBarPressed,
      onChangiRewardsIconPressed = props.onChangiRewardsIconPressed,
      onChangiRewardsPointsPressed = props.onChangiRewardsPointsPressed,
      onReLoadUpComingEvent = props.onReLoadUpComingEvent,
      _props$testID = props.testID,
      testID = _props$testID === undefined ? "HomepageMasthead" : _props$testID,
      _props$accessibilityL = props.accessibilityLabel,
      accessibilityLabel = _props$accessibilityL === undefined ? "HomepageMasthead" : _props$accessibilityL;
    var dispatch = (0, _reactRedux.useDispatch)();
    var navigation = (0, _native.useNavigation)();
    var userProfile = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profilePayload);
    var profileLandingError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileLandingError);
    var profileCardNoMissing = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileCardNoMissing);
    var profileFetching = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileFetching);
    var profileError = (0, _reactRedux.useSelector)(_profileRedux.ProfileSelectors.profileError);
    var isLoggedIn = (0, _reactRedux.useSelector)(_nativeAuthRedux.NativeAuthSelectors.isLoggedIn);
    var rewardsData = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsData);
    var rewardsError = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsError);
    var rewardsFetching = (0, _reactRedux.useSelector)(_forYouRedux.ForYouSelectors.rewardsFetching);
    var dataCommonAEM = (0, _reactRedux.useSelector)(_aemRedux.AemSelectors.getAemConfig(_aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA));
    var tierIconsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.tierIcons");
    var mastHeadsAEM = (0, _lodash.get)(dataCommonAEM, "data.pageLanding.explore.mastHeads");
    var isLoadingAem = dataCommonAEM == null ? undefined : dataCommonAEM.loading;
    var errorAEM = dataCommonAEM == null ? undefined : dataCommonAEM.error;
    var firstName = (0, _lodash.get)(userProfile, "firstName");
    var logInText = "homepageMasthead.logInText";
    var welcomeText = "homepageMasthead.welcomeText";
    var getUpComingLoading = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpComingLoading);
    var getUpcomingEventsSuccess = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpcomingEventsSuccess);
    var upComingEventData = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.upComingEventsData);
    var getUpComingEventError = (0, _reactRedux.useSelector)(_exploreRedux.ExploreSelectors.getUpComingEventError);
    var myTravelFlightsPayload = (0, _reactRedux.useSelector)(_mytravelRedux.MytravelSelectors.myTravelFlightsPayload);
    var rewardPoints = (0, _react.useMemo)(function () {
      return transformRewardPoint(rewardsData);
    }, [rewardsData]);
    var tierIcon = (0, _react.useMemo)(function () {
      return transformTierIcon(rewardsData, isLoggedIn, tierIconsAEM);
    }, [rewardsData, tierIconsAEM, isLoggedIn]);
    var errorState = rewardsError || profileError;
    var _useGetConfigurationP = (0, _getConfigurationPermission.useGetConfigurationPermissionHelper)(),
      loadingGetConfig = _useGetConfigurationP.loadingGetConfig,
      getConfigApp = _useGetConfigurationP.getConfigApp,
      notifyDisableChangiRewards = _useGetConfigurationP.notifyDisableChangiRewards;
    var _useMemo = (0, _react.useMemo)(function () {
        return getThemeHomePage(isLoggedIn, rewardsData, mastHeadsAEM);
      }, [rewardsData, mastHeadsAEM, isLoggedIn]),
      srcImageHomepage = _useMemo.srcImageHomepage,
      isDarkTheme = _useMemo.isDarkTheme;
    var themeColor = getThemeColor(isDarkTheme, errorAEM, profileCardNoMissing);
    var loadingType = (0, _react.useMemo)(function () {
      return (0, _utils2.ifAllTrue)([!getUpComingLoading, !(myTravelFlightsPayload != null && myTravelFlightsPayload.loading)]);
    }, [getUpComingLoading, myTravelFlightsPayload]);
    var isShowUpcomingEventView = (0, _react.useMemo)(function () {
      if ((0, _utils2.ifAllTrue)([isLoggedIn, !(0, _lodash.isEmpty)(upComingEventData) || getUpComingEventError])) return true;
      return false;
    }, [isLoggedIn, upComingEventData, getUpComingEventError, getUpcomingEventsSuccess, profileError]);
    var _React$useState = _react.default.useState(0),
      _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
      currentIndex = _React$useState2[0],
      setCurrentIndex = _React$useState2[1];
    var videoSlides = [{
      uri: 'https://www.w3schools.com/html/mov_bbb.mp4'
    }, {
      uri: 'https://pouch.jumpshare.com/preview/x-8n36KLciv2be9NjCjXRp0LwTYqmJdsTxiGXwGuI6AR2Mqm5Uj6BEtdn9s0krYYMA1VMLMiPjeChqpv5xby3ryW0r2MLn8i2Jbv9mhZJpXmoryzUGqh0DDxbdQvO5ecv3IXAtFBoHlzZQ_lBPkIiW6yjbN-I2pg_cnoHs_AmgI.mp4'
    }, {
      uri: 'https://pouch.jumpshare.com/preview/YFFpqIp554PoKef9magY2nFDP5X3EuYh1egEL2AoGxTMplaM3Rw20uKq_BL48TIF-YdALrLePkWrVnoZ3RMqjcNJzllKptRnsVjDL1CKrSdeQdzaQNfBOA0kd62QukNZecZtKflYzA91S8IQPH9HrW6yjbN-I2pg_cnoHs_AmgI.mp4'
    }, {
      uri: 'https://pouch.jumpshare.com/preview/s0Qd92xI0ECNCOYvAskWLQgNVqBn5kbGugs7GSIQkmVFhQTNJ7o8PA7qCj3QOAvWLACP6HdfV7sSPqTnp4Xv8vz-LQuf1m5Ylmth44ADoeuD3oOY47TvapijstjX4K1gsQwNVuZxn8iOFyZqArHTu26yjbN-I2pg_cnoHs_AmgI.mp4'
    }, {
      uri: 'https://pouch.jumpshare.com/preview/MyYQwP4Ie-frpT2DQyHsz2_QmpZJOM6JWpu0xu7S9baH6F6_nvTh6gD7SI9P3iZXTSNMPKQBhMLPN1SZagIV_ltmm0pY3t8oXQuvBegVXnDIaECk0i_xS8n314kVXgtWovTOZmuZmCWegiGiqhcGRG6yjbN-I2pg_cnoHs_AmgI.mp4'
    }];
    var videoRefs = _react.default.useRef(videoSlides.map(function () {
      return _react.default.createRef();
    }));

    // const videoRefs = React.useRef<VideoRef[]>([]);

    // useEffect(() => {
    //   videoRefs.current.forEach((ref, index) => {
    //     if (index !== currentIndex && ref.current) {
    //       console.log("GIANG__seek", index)
    //       ref.current?.seek(0); // reset non-active videos
    //     }
    //   });
    // }, [currentIndex]);

    var renderCarouselItem = function renderCarouselItem(_ref2) {
      var item = _ref2.item,
        index = _ref2.index;
      var isActive = index === currentIndex;
      return (0, _jsxRuntime.jsx)(_reactNativeVideo.default, {
        ref: videoRefs.current[index],
        source: {
          uri: item == null ? undefined : item.uri
        },
        style: {
          width: '100%',
          aspectRatio: 1.7777777777777777
        },
        resizeMode: "cover",
        repeat: true // loop forever
        ,
        muted: true // no sound
        ,
        paused: !isActive // autoplay
        ,
        controls: false // hide UI
        ,
        ignoreSilentSwitch: "obey"
      });
    };
    return (0, _jsxRuntime.jsx)(_reactNative.View, {
      style: {
        flex: 1
      },
      children: (0, _jsxRuntime.jsx)(_reactNative.View, {
        children: (0, _jsxRuntime.jsx)(_reactNativeReanimatedCarousel.default, {
          loop: true,
          autoPlay: true,
          autoPlayInterval: 5000,
          data: videoSlides,
          style: {
            height: 300
          },
          renderItem: renderCarouselItem,
          width: _reactNative.Dimensions.get('window').width,
          testID: `${testID}__Carousel`,
          onProgressChange: function onProgressChange(_, absoluteProgress) {
            var _videoRefs$current;
            var index = Math.round(absoluteProgress) % videoSlides.length;
            (_videoRefs$current = videoRefs.current[index === (videoSlides == null ? undefined : videoSlides.length) - 1 ? 0 : index + 1]) == null || (_videoRefs$current = _videoRefs$current.current) == null || _videoRefs$current.seek(0);
            setCurrentIndex(index);
          }
          // Lower the swipe threshold to make switching items easier
          ,
          minScrollDistancePerSwipe: 1
          // onConfigurePanGesture={(panGesture) => {
          //   // Favor horizontal swipes; don't force failure on small vertical jitters
          //   panGesture.activeOffsetX([-8, 8])
          // }}
        })
      })
    });
  }
