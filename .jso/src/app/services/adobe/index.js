  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.trackState = exports.trackAction = exports.getExperienceCloudId = exports.commonTrackingScreen = exports.adobeUpdateConfiguration = exports.adobeSyncIdentifier = exports.adobeRetrieveLocationContent = exports.adobePrefetchContent = exports.adobeClickedLocation = exports.adobeCampaignSetLinkageFields = exports.adobeCampaignResetLinkageFields = exports.NotificationSettingToggleValue = exports.GAMIFICATION_TRACKING_VALUES = exports.DEFAULT_LOCATION_CONTENT = exports.AdobeValueByTagName = exports.AdobeTagName = exports.AdobeIdentifierType = exports.AdobeAuthenticationState = undefined;
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _reactNativeAcpcore = _$$_REQUIRE(_dependencyMap[4]);
  var _reactNativeAcptarget = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeAcpcampaign = _$$_REQUIRE(_dependencyMap[6]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[7]);
  var _lodash = _$$_REQUIRE(_dependencyMap[8]);
  var _envSettings = _$$_REQUIRE(_dependencyMap[9]);
  var EnvSettings = (0, _envSettings.getEnvSetting)();
  var AdobeAuthenticationState = exports.AdobeAuthenticationState = /*#__PURE__*/function (AdobeAuthenticationState) {
    AdobeAuthenticationState["authenticated"] = "authenticated";
    AdobeAuthenticationState["unknown"] = "unknown";
    AdobeAuthenticationState["loggedOut"] = "loggedOut";
    return AdobeAuthenticationState;
  }({});
  var AdobeIdentifierType = exports.AdobeIdentifierType = /*#__PURE__*/function (AdobeIdentifierType) {
    AdobeIdentifierType["uid"] = "UID";
    return AdobeIdentifierType;
  }({});
  var AdobeTagName = exports.AdobeTagName = /*#__PURE__*/function (AdobeTagName) {
    AdobeTagName["CAppNotificationPopUp"] = "CApp_Notification_Pop_Up";
    AdobeTagName["MobileDeviceID"] = "Mobile_Device_ID";
    AdobeTagName["CAppAppOpenState"] = "CApp_App_Open_State";
    // Widget
    AdobeTagName["CAppWidget"] = "CApp_Widget";
    // Home Page
    AdobeTagName["CAppHomePage"] = "CApp_Home_Page";
    AdobeTagName["CAppHomeECard"] = "CApp_Home_ECard";
    AdobeTagName["CAppHomeSearch"] = "CApp_Home_Search";
    AdobeTagName["CAppHomeCRPoints"] = "CApp_Home_CR_Points";
    AdobeTagName["CAppHomeLogin"] = "CApp_Home_Login";
    AdobeTagName["CAppHomeBookingTiles"] = "CApp_Home_BookingTiles";
    AdobeTagName["CAppHomeWallet"] = "CApp_Home_Wallet";
    AdobeTagName["CAppHomeQuicklinks"] = "CApp_Home_Quicklinks";
    AdobeTagName["CAppHomeQuicklinksPersonalization"] = "CApp_Home_Quicklinks_Personalization";
    AdobeTagName["CAppHomeSummaryTimelineTiles"] = "CApp_Home_SummaryTimelineTiles";
    AdobeTagName["CAppHomeLatestHappenings"] = "CApp_Home_Latest_Happenings";
    AdobeTagName["CAppLatestHappeningsDetails"] = "CApp_Latest_Happenings_Details";
    AdobeTagName["CAppHomeiSCProductSwimlane"] = "CApp_Home_iSCProductSwimlane";
    AdobeTagName["CAppHomePlaypassLandingCategory"] = "CApp_Home_PlaypassLanding_Category";
    AdobeTagName["CAppHomePlaypassLandingTiles"] = "CApp_Home_PlaypassLanding_Tiles";
    AdobeTagName["CAppHomePlaypassLandingFirst"] = "CApp_Home_PlaypassLanding_First";
    AdobeTagName["HomeExploreNotificationBanner"] = "Home_Explore_Notification_Banner";
    // Airport Landing
    AdobeTagName["CAppAirportLanding"] = "CApp_Airport_Landing";
    // flight landing
    AdobeTagName["CAppFlightLanding"] = "CApp_Flight_Landing";
    AdobeTagName["CAppFlyQuickLinks"] = "CApp_Fly_Quicklinks";
    AdobeTagName["CAppFlyTopMenuToggle"] = "CApp_Fly_TopMenuToggle";
    AdobeTagName["CAppFlyFlightSearchSearchBar"] = "CApp_Fly_FlightSearch_SearchBar";
    AdobeTagName["CAppFlyFlightSearchScanBoardingPass"] = "CApp_Fly_FlightSearch_ScanBoardingPass";
    AdobeTagName["CAppFlyFlightListDepArrToggle"] = "CApp_Fly_FlightList_DepArrToggle";
    AdobeTagName["CAppFlyFlightListEarlierFlights"] = "CApp_Fly_FlightList_EarlierFlights";
    AdobeTagName["CAppFlyFlightListFlightCardClicked"] = "CApp_Fly_FlightList_FlightCardClicked";
    AdobeTagName["CAppFlyFlightListSaveFlight"] = "CApp_Fly_FlightList_SaveFlight";
    AdobeTagName["CAppFlyFlightListRemoveFlight"] = "CApp_Fly_FlightList_RemoveFlight";
    AdobeTagName["CAppFlyFlightListViewAllDepFlights"] = "CApp_Fly_FlightList_ViewAllDepFlights";
    AdobeTagName["CAppFlyFlightListViewFlightDetails"] = "CApp_Fly_FlightList_ViewFlightDetails";
    AdobeTagName["CAppFlyFlightListFilters"] = "CApp_Fly_FlightList_Filters";
    AdobeTagName["CAppFlyContentSwimlaneTitle"] = "CApp_Fly_ContentSwimlaneTitle";
    AdobeTagName["CAppFlyFacilitiesServices"] = "CApp_Fly_Facilities&Services";
    AdobeTagName["CAppFlyFacilitiesServicesViewAll"] = "CApp_Fly_Facilities&Services_ViewAll";
    AdobeTagName["CAppFlightLandingFilter"] = "CApp_Flight_Landing_Filter";
    // flight listing
    AdobeTagName["CAppFlightListing"] = "CApp_FlightListing";
    AdobeTagName["CAppFlightListingDepArrToggle"] = "CApp_FlightListing_DepArrToggle";
    AdobeTagName["CAppFlightListingFlightSearch"] = "CApp_FlightListing_FlightSearch";
    AdobeTagName["CAppFlightListingScanBoardingPass"] = "CApp_FlightListing_ScanBoardingPass";
    AdobeTagName["CAppFlightListingEarlierFlights"] = "CApp_FlightListing_EarlierFlights";
    AdobeTagName["CAppFlightListingFlightListFilter"] = "CApp_FlightListing_FlightList_Filter";
    AdobeTagName["CAppFlightListingViewFlightDetails"] = "CApp_FlightListing_ViewFlightDetails";
    AdobeTagName["CAppFlightListingFlightCardClicked"] = "CApp_FlightListing_FlightCardClicked";
    AdobeTagName["CAppFlightListingSaveFlight"] = "CApp_FlightListing_SaveFlight";
    AdobeTagName["CAppFlightListingRemoveFlight"] = "CApp_FlightListing_RemoveFlight";
    AdobeTagName["CAppFlightListingFlightsearch"] = "capp_flightlisting_flightsearch";
    // flight details page
    AdobeTagName["CAppFlyFlightDetail"] = "CApp_Fly_FlightDetail";
    AdobeTagName["CAppFlyFlightDetailTopNavigationMenu"] = "CApp_Fly_FlightDetail_TopNavigationMenu";
    AdobeTagName["CAppFlyFlightDetailFlyProfile"] = "CApp_Fly_FlightDetail_FlyProfile";
    AdobeTagName["CAppFlyFlightDetailFlightCardLinks"] = "CApp_Fly_FlightDetail_FlightCardLinks";
    AdobeTagName["CAppNavigationMapsEnter"] = "CApp_navigationMapsEnter";
    AdobeTagName["CAppFlyFlightDetailPerks"] = "CApp_Fly_FlightDetail_Perks";
    AdobeTagName["CAppFlyFlightDetailTimelineTiles"] = "CApp_Fly_FlightDetail_TimelineTiles";
    AdobeTagName["CAppFlyFlightDetailSaveFlight"] = "CApp_Fly_FlightDetail_SaveFlight";
    AdobeTagName["CAppFlyFlightDetailRemoveFlight"] = "CApp_Fly_FlightDetail_RemoveFlight";
    AdobeTagName["CAppFlyFlightDetailFacilitiesServices"] = "CApp_Fly_FlightDetail_Facilities&Services";
    AdobeTagName["CAppFlyFlightDetailFacilitiesServicesViewAll"] = "CApp_Fly_FlightDetail_Facilities&Services_ViewAll";
    AdobeTagName["FlyFlightDetailsPageBottomDetails"] = "Flight_Details_Page_Bottom_Details";
    AdobeTagName["CAppFlightPersonalizationSaveFlight"] = "CApp_Flight_Personalization_Save_Flight";
    // CApp_Account_Folio_Menu_Toggle
    AdobeTagName["CAppFolioTopMenuToggle"] = "CApp_Folio_TopMenuToggle";
    // CApp_Account_Folio_Passes
    AdobeTagName["CAppAccountFolioPasses"] = "CApp_Account_Folio_Passes";
    AdobeTagName["CAppFolioPassesFilter"] = "CApp_Folio_Passes_Filter";
    AdobeTagName["CAppFolioPassesSort"] = "CApp_Folio_Passes_Sort";
    AdobeTagName["CAppFolioActivePassList"] = "CApp_Folio_ActivePassList";
    AdobeTagName["CAppFolioActivePassLoadMore"] = "CApp_Folio_ActivePass_LoadMore";
    AdobeTagName["CAppFolioPastPassesLoadMore"] = "CApp_Folio_PastPasses_LoadMore";
    // CApp_Account_Folio_Travel
    AdobeTagName["CAppAccountFolioTravel"] = "CApp_Account_Folio_Travel";
    AdobeTagName["CAppFolioTravel"] = "CApp_Folio_Travel";
    // CApp_Account_Folio_Credits
    AdobeTagName["CAppAccountFolioCredits"] = "CApp_Account_Folio_Credits";
    // CApp_Account_Folio_Perks
    AdobeTagName["CAppAccountFolioPerks"] = "CApp_Account_Folio_Perks";
    // CApp_Account_Folio_Orders
    AdobeTagName["CAppAccountFolioOrders"] = "CApp_Account_Folio_Orders";
    // CApp_Account_Landing
    AdobeTagName["CAppAccountProfileEdit"] = "CApp_Account_ProfileEdit";
    AdobeTagName["CAppAccountLogIn"] = "CApp_Account_LogIn";
    AdobeTagName["CAppAccountSettings"] = "CApp_Account_Settings";
    AdobeTagName["CAppAccountNotifications"] = "CApp_Account_Notifications";
    AdobeTagName["CAppAccountCRCard"] = "CApp_Account_CRCard";
    AdobeTagName["CAppAccountQuickLinks"] = "CApp_Account_QuickLinks";
    AdobeTagName["CAppLoginFlow"] = "CApp_Login_Flow";
    AdobeTagName["CAppSignupFlow"] = "CApp_Signup_Flow";
    // CApp_Jewel_Privilege
    AdobeTagName["CAppJewelPrivilege"] = "CApp_Jewel_Privilege";
    // Gamification
    AdobeTagName["CAppAccountGamification"] = "CApp_Account_Gamification";
    // CAppAccountAskMaxStartChat = "CApp_Account_AskMax_StartChat",
    AdobeTagName["CAppAccountMoreSection"] = "CApp_Account_MoreSection";
    AdobeTagName["CAppAccountLogOut"] = "CApp_Account_LogOut";
    // Dine Shop Toggle Menu
    AdobeTagName["CAppShopDineTopMenuToggle"] = "CApp_ShopDine_TopMenuToggle";
    AdobeTagName["CAppMarketplaceTiles"] = "CApp_Marketplace_Tiles";
    // Dine Landing Page
    AdobeTagName["CAppDineLanding"] = "CApp_Dine_Landing";
    AdobeTagName["CAppDineSearch"] = "CApp_Dine_Search";
    AdobeTagName["CAppDineSearchFilter"] = "CApp_Dine_Search_Filter";
    AdobeTagName["CAppDineSearchFilterDirectory"] = "CApp_Dine_Search_FilterDirectory";
    AdobeTagName["CAppDineFilterOptions"] = "CApp_Dine_Filter_Options";
    AdobeTagName["CAppDineHeroCarousel"] = "CApp_Dine_HeroCarousel";
    AdobeTagName["CAppDineNewlyOpened"] = "CApp_Dine_NewlyOpened";
    AdobeTagName["CAppDineRecommendedForYouVouchers"] = "CApp_Dine_RecommendedForYou_Vouchers";
    AdobeTagName["CAppDineRecommendedForYouOutlets"] = "CApp_Dine_RecommendedForYou_Outlets";
    AdobeTagName["CAppDineContentSwimlaneTiles"] = "CApp_Dine_ContentSwimlaneTiles";
    AdobeTagName["CAppDineSocial"] = "CApp_Dine_Social";
    AdobeTagName["CAppDineExploreMoreOutlets"] = "CApp_Dine_ExploreMore_Outlets";
    AdobeTagName["CAppDineExploreMoreViewAll"] = "CApp_Dine_ExploreMore_ViewAll";
    AdobeTagName["CAppDineExploreMoreFilters"] = "CApp_Dine_ExploreMore_Filters";
    AdobeTagName["EPICDineAndShop"] = "EPIC_Dine_and_Shop";
    // Shop Landing Page
    AdobeTagName["CAppShopLanding"] = "CApp_Shop_Landing";
    AdobeTagName["CAppShopSearch"] = "CApp_Shop_Search";
    AdobeTagName["CAppShopSearchFilter"] = "CApp_Shop_Search_Filter";
    AdobeTagName["CAppShopSearchFilterDirectory"] = "CApp_Shop_Search_FilterDirectory";
    AdobeTagName["CAppShopFilterOptions"] = "CApp_Shop_Filter_Options";
    AdobeTagName["CAppShopTenantPromoSwimlane"] = "CApp_Shop_TenantPromoSwimlane";
    AdobeTagName["CAppShopHeroCarousel"] = "CApp_Shop_HeroCarousel";
    AdobeTagName["CAppShopNewlyOpened"] = "CApp_Shop_NewlyOpened";
    AdobeTagName["CAppShopRecommendedForYouVouchers"] = "CApp_Shop_RecommendedForYou_Vouchers";
    AdobeTagName["CAppShopRecommendedForYouOutlets"] = "CApp_Shop_RecommendedForYou_Outlets";
    AdobeTagName["CAppShopContentSwimlaneTile"] = "CApp_Shop_ContentSwimlaneTile";
    AdobeTagName["CAppShopSocial"] = "CApp_Shop_Social";
    AdobeTagName["CAppShopExploreMoreOutlets"] = "CApp_Shop_ExploreMore_Outlets";
    AdobeTagName["CAppShopExploreMoreViewAll"] = "CApp_Shop_ExploreMore_ViewAll";
    AdobeTagName["CAppShopExploreMoreFilters"] = "CApp_Shop_ExploreMore_Filters";
    // CApp Dine Detail page
    AdobeTagName["CAppDineDetail"] = "CApp_Dine_Detail";
    AdobeTagName["CAppDineDetailNew"] = "CApp_DineDetail";
    AdobeTagName["CAppDineDetailShare"] = "CApp_DineDetail_Share";
    AdobeTagName["CAppDineDetailCarouselImage"] = "CApp_DineDetail_CarouselImage";
    AdobeTagName["CAppDineDetailDineInformation"] = "CApp_DineDetail_DineInformation";
    AdobeTagName["CAppDineDetailDineInformationGetDirection"] = "CApp_DineDetail_DineInformation_GetDirection";
    AdobeTagName["CAppDineDetailDineInformationPhoneNumber"] = "CApp_DineDetail_DineInformation_PhoneNumber";
    AdobeTagName["CAppDineDetailDineInformationViewWebsite"] = "CApp_DineDetail_DineInformation_ViewWebsite";
    AdobeTagName["CAppDineDetailDineInformationViewMenu"] = "CApp_DineDetail_DineInformation_ViewMenu";
    AdobeTagName["CAppDineDetailDineInformationBooking"] = "CApp_DineDetail_DineInformation_Booking";
    AdobeTagName["CAppDineDetailDealsPromo"] = "CApp_DineDetail_Deals&Promo";
    AdobeTagName["CAppDineDetailAbout"] = "CApp_DineDetail_About";
    AdobeTagName["CAppDineDetailSimilarTo"] = "CApp_DineDetail_SimilarTo";
    AdobeTagName["CAppDineDetailExploreMore"] = "CApp_Dine_Detail_ExploreMore";
    AdobeTagName["CAppDineDetailBlogsReviews"] = "CApp_DineDetail_Blogs&Reviews";
    AdobeTagName["CAppDineDetailRewardsTab"] = "CApp_DineDetail_RewardsTab";
    AdobeTagName["CAppDineDetailExploreMoreViewAll"] = "CApp_Dine_Detail_ExploreMore_ViewAll";
    AdobeTagName["CAppDineDetailExploreMoreCuisine"] = "CApp_Dine_Detail_ExploreMoreCuisine";
    AdobeTagName["CAppDineDetailDineInformationReserve"] = "CApp_DineDetail_DineInformation_Reserve";
    // CApp Shop Detail page
    AdobeTagName["CAppShopDetail"] = "CApp_Shop_Detail";
    AdobeTagName["CAppShopDetailShare"] = "CApp_ShopDetail_Share";
    AdobeTagName["CAppShopDetailCarouselImage"] = "CApp_ShopDetail_CarouselImage";
    AdobeTagName["CAppShopDetailShopInformation"] = "CApp_ShopDetail_ShopInformation";
    AdobeTagName["CAppShopDetailShopInformationGetDirection"] = "CApp_ShopDetail_ShopInformation_GetDirection";
    AdobeTagName["CAppShopDetailShopInformationPhoneNumber"] = "CApp_ShopDetail_ShopInformation_PhoneNumber";
    AdobeTagName["CAppShopDetailShopInformationViewWebsite"] = "CApp_ShopDetail_ShopInformation_ViewWebsite";
    AdobeTagName["CAppShopDetailShopInformationViewiSC"] = "CApp_ShopDetail_ShopInformation_ViewiSC";
    AdobeTagName["CAppShopDetailDealsPromo"] = "CApp_ShopDetail_Deals&Promo";
    AdobeTagName["CAppShopDetailAbout"] = "CApp_ShopDetail_About";
    AdobeTagName["CAppShopDetailSimilarTo"] = "CApp_ShopDetail_SimilarTo";
    AdobeTagName["CAppShopDetailExploreMore"] = "CApp_ShopDetail_ExploreMore";
    AdobeTagName["CAppShopDetailExploreMoreViewAll"] = "CApp_ShopDetail_ExploreMore_ViewAll";
    AdobeTagName["CAppShopDetailExploreMoreShops"] = "CApp_ShopDetailExploreMoreShops";
    AdobeTagName["CAppShopDetailBlogsReviews"] = "CApp_ShopDetail_Blogs&Reviews";
    AdobeTagName["CAppShopDetailRewardsTab"] = "CApp_ShopDetail_RewardsTab";
    // CApp Notifications Listing
    AdobeTagName["CAppNotificationsListing"] = "CApp_Notifications_Listing";
    AdobeTagName["CAppNotificationLanding"] = "CApp_Notification_Landing";
    AdobeTagName["CAppNotificationsListingDeleteAll"] = "CApp_Notifications_Listing_DeleteAll";
    AdobeTagName["CAppNotificationsListingDeleteAllConfirm"] = "CApp_Notifications_Listing_DeleteAll_Confirm";
    AdobeTagName["CAppNotificationsListingNotificationsClicked"] = "CApp_Notifications_Listing_NotificationsClicked";
    AdobeTagName["CAppNotificationsListingTopMenuToggle"] = "CApp_Notifications_Listing_TopMenuToggle";
    AdobeTagName["CAppNotificationsListingEnableNotification"] = "CApp_Notifications_Listing_EnableNotification";
    AdobeTagName["CAppNotificationCenter"] = "CApp_Notification_Center";
    // CApp Carpark
    AdobeTagName["CAppCarparkAvailabilityToggleMenu"] = "CApp_Carpark_Availability_ToggleMenu";
    AdobeTagName["CAppCarparkAvailabilityParkingPriviledges"] = "CApp_Carpark_Availability_ParkingPriviledges";
    AdobeTagName["CAppCarparkAvailabilityFiltersApplied"] = "CApp_Carpark_Availability_FiltersApplied";
    AdobeTagName["CAppCarparkCalculatorCarPickChosen"] = "CApp_Carpark_Calculator_CarPickChosen";
    AdobeTagName["CAppCarparkFindMyCarRequestCar"] = "CApp_Carpark_FindMyCar_RequestCar";
    AdobeTagName["CAppCarparkFindMyCarSelectCar"] = "CApp_Carpark_FindMyCar_SelectCar";
    // CApp Navigation
    AdobeTagName["CAppNavigationRouteSearchMode"] = "CApp_Navigation_RouteSearchMode";
    AdobeTagName["CAppNavigationLevels"] = "CApp_Navigation_Levels";
    AdobeTagName["CAppNavigationRouteSearch"] = "CApp_Navigation_RouteSearch";
    AdobeTagName["CAppNavigationOpenPOI"] = "CApp_Navigation_OpenPOI";
    // CApp Search Result page
    AdobeTagName["CAppSearchResult"] = "CApp_SearchResult";
    AdobeTagName["CAppSearchEvent"] = "CApp_SearchEvent";
    AdobeTagName["CAppSearchResultCategoryToggle"] = "CApp_SearchResult_CategoryToggle";
    AdobeTagName["CAppSearchResultClicked"] = "CApp_SearchResultClicked";
    AdobeTagName["CAppSearchResultsClickedRank"] = "CApp_Search_Results_Clicked_Rank";
    AdobeTagName["CAppSaveFlight"] = "CApp_saveFlight";
    AdobeTagName["CAppRemoveFlight"] = "CApp_removeFlight";
    AdobeTagName["CAppSearchResultFlySaveFlight"] = "CApp_SearchResult_Fly_SaveFlight";
    AdobeTagName["CAppSearchResultFlyRemoveFlight"] = "CApp_SearchResult_Fly_RemoveFlight";
    AdobeTagName["CAppSearchResultFlyScanBoardingPass"] = "CApp_SearchResult_Fly_ScanBoardingPass";
    AdobeTagName["CAppSearchResultFlyViewAll"] = "CApp_SearchResult_Fly_ViewAll";
    AdobeTagName["CAppSearchResultFlyFilter"] = "CApp_SearchResult_Fly_Filter";
    AdobeTagName["CAppSearchByCategory"] = "CApp_SearchByCategory";
    AdobeTagName["CAppPopularSearches"] = "CApp_Popular_Searches";
    AdobeTagName["CAppSearchAutoComplete"] = "CApp_Search_Auto_Complete";
    AdobeTagName["CAppSearchYAML"] = "CApp_Search_YAML";
    AdobeTagName["CAppSearchYMAL"] = "CApp_Search_YMAL";
    AdobeTagName["CAppSearchResultFlySaveFlightRank"] = "CApp_SearchResult_Fly_SaveFlight_Rank";
    AdobeTagName["SearchISCProductSwimlane"] = "Search_iSC_Product_Swimlane";
    AdobeTagName["CAppSearchResultFilter"] = "CApp_Search_Result_Filter";
    // CApp Redeem
    AdobeTagName["CAppRedeem"] = "CApp_Redeem";
    AdobeTagName["AccountYourPoints"] = "Account_Your_Points";
    // CApp_VPR
    AdobeTagName["CAppVPR"] = "CApp_VPR";
    // CApp Rewards
    AdobeTagName["CAppRewardsCatalogue"] = "CApp_Rewards_Catalogue";
    AdobeTagName["CAppRewardsCard"] = "CApp_Rewards_Card";
    AdobeTagName["CAppRewardsMembershipTier"] = "CApp_Rewards_Membership_Tier";
    AdobeTagName["RewardCatalogueFilter"] = "Reward_Catalogue_Filter";
    // tagName for bottom tab
    AdobeTagName["CAppNavigationBarHome"] = "CApp_NavigationBar_Home";
    AdobeTagName["CAppNavigationBarFly"] = "CApp_NavigationBar_Fly";
    AdobeTagName["CAppNavigationBarDineShop"] = "CApp_NavigationBar_DineShop";
    AdobeTagName["CAppNavigationBarCPay"] = "CApp_NavigationBar_CPay";
    AdobeTagName["CAppNavigationBarAccount"] = "CApp_NavigationBar_Account";
    // Tag name for unique visitors
    AdobeTagName["cappAppId"] = "capp.appId";
    AdobeTagName["cagLoginmethod"] = "cag.loginmethod";
    AdobeTagName["cappUserLoginSource"] = "capp.userLoginSource";
    AdobeTagName["cagUsername"] = "cag.username";
    AdobeTagName["cappChangiRewardsTier"] = "capp.changiRewardsTier";
    AdobeTagName["cagLoginstatus"] = "cag.loginstatus";
    AdobeTagName["cappUID"] = "capp.UID";
    AdobeTagName["cappNewLogin"] = "capp.newLogin";
    AdobeTagName["cappFirstTimeVisit"] = "capp.firstTimeVisit";
    AdobeTagName["cappVisitId"] = "capp.visitId";
    AdobeTagName["cappVisitSource"] = "capp.visitSource";
    // Tag name for common
    AdobeTagName["common"] = "common";
    AdobeTagName["cagPagename"] = "cag.pagename";
    AdobeTagName["cagSiteSection"] = "cag.sitesection";
    AdobeTagName["cagLanguage"] = "cag.language";
    AdobeTagName["cagPagePath"] = "cag.pagepath";
    AdobeTagName["cagSite"] = "cag.site";
    AdobeTagName["cagPreviousPage"] = "cag.previouspage";
    AdobeTagName["cagDayOfWeek"] = "cag.dayofweek";
    AdobeTagName["cagEcid"] = "cag.ecid";
    AdobeTagName["cagBusinessUnit"] = "cag.businessunit";
    AdobeTagName["cagPageview"] = "cag.pageview";
    AdobeTagName["appWide"] = "App-wide";
    AdobeTagName["cagRedeemYourPoint"] = "cag.redeemYourPoint";
    AdobeTagName["cagOtherPerksAndPrivileges"] = "cag.otherPerksAndPrivileges";
    // Restructure AA Tags
    AdobeTagName["CAppAccount"] = "CApp_Account";
    AdobeTagName["Campaign"] = "Campaign";
    // ATOMS
    AdobeTagName["CAppATOMSEntryClick"] = "capp_atom_entry_clicked";
    AdobeTagName["CAppATOMSSearchKeyword"] = "capp_atom_search_keyword";
    AdobeTagName["CAppATOMSSearchResults"] = "capp_atom_search_results";
    AdobeTagName["CAppATOMSMapClicks"] = "capp_atom_map_clicks";
    AdobeTagName["CAppATOMSLevelSelector"] = "capp_atom_levelSelector";
    AdobeTagName["CAppATOMsMap"] = "CApp_ATOMs_Map";
    // Playpass
    AdobeTagName["CAppPlaypassPassDetail"] = "CApp_Playpass_Pass_Detail";
    AdobeTagName["CAppHomePlaypassStickyCart"] = "CApp_Home_Playpass_Sticky_Cart";
    // StaffPerk
    AdobeTagName["CAppGalaxyEntry"] = "CApp_Galaxy_Entry";
    AdobeTagName["CAppGalaxyLanding"] = "CApp_Galaxy_Landing";
    AdobeTagName["CAppGalaxyDetail"] = "CApp_Galaxy_Detail";
    AdobeTagName["CAppShopDetailStaffPerks"] = "CApp_ShopDetail_StaffPerks";
    AdobeTagName["CAppGalaxyLandingTiles"] = "CApp_Galaxy_Landing_Tiles";
    AdobeTagName["CAppGalaxyFilters"] = "CApp_Galaxy_Filters";
    // Appacapade
    AdobeTagName["CAppAppscapadeBannerEntry"] = "CApp_Appscapade_Banner_Entry";
    // Notification Settings
    AdobeTagName["CAppNotificationSettings"] = "CApp_Notification_Settings";
    // Parking Landing
    AdobeTagName["CAppParkingLanding"] = "CApp_Parking_Landing";
    AdobeTagName["CAppL3FAQPage"] = "CApp_L3_FAQ_Page";
    AdobeTagName["CAppBenefitsSummary"] = "CApp_Benefits_Summary";
    // Parking onboarding
    AdobeTagName["CAppFeatureBuddy"] = "CApp_Feature_Buddy";
    // Promos code
    AdobeTagName["CAppPromoCodes"] = "CApp_Promo_Codes";
    return AdobeTagName;
  }({});
  var AdobeValueByTagName = exports.AdobeValueByTagName = /*#__PURE__*/function (AdobeValueByTagName) {
    //  For App Open State
    AdobeValueByTagName["AppOpenStateOn"] = "On";
    AdobeValueByTagName["AppOpenStateOff"] = "Off";
    AdobeValueByTagName["AppOpenStateLinked"] = "Linked";
    AdobeValueByTagName["AppOpenStateNotAvailable"] = "Not Available";
    AdobeValueByTagName["AppOpenStateEnableFaceID"] = "Enable [FaceID]";
    AdobeValueByTagName["AppOpenStateEnableFingerprint"] = "Enable [FingerprintID]";
    AdobeValueByTagName["AppOpenStateLinkedSocialAccounts"] = "Linked Social Accounts";
    AdobeValueByTagName["AppOpenStateBiometricAuthentication"] = "Biometric Authentication";
    // For notification center
    AdobeValueByTagName["CAppNotificationCenterFilter"] = "Filter | ";
    AdobeValueByTagName["CAppNotificationCenterSelected"] = " | Selected";
    AdobeValueByTagName["CAppNotificationCenterUnselected"] = " | Unselected";
    // For L2 Announcements
    AdobeValueByTagName["HomeExploreNotificationBannerDefault"] = "Default | ";
    AdobeValueByTagName["HomeExploreNotificationBannerInterstitial"] = "Interstitial | ";
    AdobeValueByTagName["HomeExploreNotificationBannerSingle"] = "Single | ";
    AdobeValueByTagName["HomeExploreNotificationBannerMultiple"] = "Multiple | ";
    AdobeValueByTagName["HomeExploreNotificationBannerViewMessage"] = "View Message | ";
    AdobeValueByTagName["HomeExploreNotificationBannerDelete"] = "Delete | ";
    AdobeValueByTagName["HomeExploreNotificationBannerDeleteAll"] = "Delete All";
    // For CAppAccount
    AdobeValueByTagName["CAppAccountAskMaxStartChat"] = "Ask Max Start Chat";
    AdobeValueByTagName["CAppAccountCRCard"] = "CRCard";
    AdobeValueByTagName["CAppAccountGamification"] = "Gamification";
    AdobeValueByTagName["CAppAccountLogIn"] = "LogIn";
    AdobeValueByTagName["CAppAccountLogOut"] = "LogOut";
    AdobeValueByTagName["CAppAccountMoreSection"] = "More Section | ";
    AdobeValueByTagName["CAppAccountNotifications"] = "Notifications";
    AdobeValueByTagName["CAppAccountProfileEdit"] = "Profile Edit";
    AdobeValueByTagName["CAppAccountQuickLinks"] = "QuickLinks | ";
    AdobeValueByTagName["CAppAccountSettings"] = "Settings";
    AdobeValueByTagName["CAppAccountTopNavigation"] = "Top Navigation";
    AdobeValueByTagName["CAppAccountCopyMemberCode"] = "Copy Member Code";
    AdobeValueByTagName["CAppAccountQuicklinks"] = "Quicklinks | ";
    AdobeValueByTagName["CAppDineDetailDineInformation"] = "Dine Information | ";
    AdobeValueByTagName["CAppDineDetailAbout"] = "About | ";
    AdobeValueByTagName["CAppDineDetailExploreMoreCuisine"] = "Explore More Cuisine | ";
    // For Miffy Gamification
    AdobeValueByTagName["CAppAccountMiffyGameEnterQuest"] = "Logged in: Miffy & Friends | Enter Quest";
    AdobeValueByTagName["CAppAccountMiffyGameGiftFriend"] = "Logged in: Miffy & Friends | Gift A Friend";
    AdobeValueByTagName["CAppAccountMiffyGameLoginToJoinQuest"] = "Not logged in: Miffy & Friends | Login to Join Quest";
    // For CAppRedeem
    AdobeValueByTagName["CAppRedeemBack"] = "Back";
    AdobeValueByTagName["CAppRedeemSeeAllExpiry"] = "See All Expiry";
    AdobeValueByTagName["CAppRedeemPoints"] = "Points";
    AdobeValueByTagName["CAppRedeemPerks"] = "Perks";
    AdobeValueByTagName["CAppRedeemSubmitAClaim"] = "Submit a claim";
    // AccountYourPoints
    AdobeValueByTagName["AccountYourPointsTabs"] = "Tab | ";
    AdobeValueByTagName["AccountYourPointsTransaction"] = "Points Transaction";
    AdobeValueByTagName["AccountYourPendingPoints"] = "Pending Points";
    AdobeValueByTagName["AccountYourPointsExpiry"] = "Points Expiry";
    // For CApp Banner
    AdobeValueByTagName["CAppBannerAccountLanding"] = "Account Landing";
    AdobeValueByTagName["CAppBannerVPR"] = "Vouchers, Prizes & Redemptions";
    // For CApp VPR
    AdobeValueByTagName["CAppVPRBack"] = "Back";
    AdobeValueByTagName["CAppLoginPage"] = "Login Page | ";
    AdobeValueByTagName["CAppSignUpPage"] = "Sign Up Page | ";
    AdobeValueByTagName["CAppVerifyEmailPage"] = "Verify Email Page | ";
    AdobeValueByTagName["CAppUserDetailsPage"] = "User Details Page | ";
    // For CApp Settings
    AdobeValueByTagName["CAppSettingsManageLogin"] = "Manage Login | ";
    AdobeValueByTagName["CAppSettingsUnlink"] = " | Unlink";
    AdobeValueByTagName["CAppSettingsEnableFaceID"] = "Enable FaceID";
    AdobeValueByTagName["CAppSettingsEnableFingerprintID"] = "Enable FingerprintID";
    AdobeValueByTagName["CAppSettingsToggleOn"] = " | Toggle On";
    AdobeValueByTagName["CAppSettingsToggleOff"] = " | Toggle Off";
    AdobeValueByTagName["CAppSettingsPasswordSuccess"] = " Password Success";
    // For Flight Details
    AdobeValueByTagName["FlightDetailsPageTravelling"] = "Travelling";
    AdobeValueByTagName["FlightDetailsPagePickingSomeone"] = "Picking Someone";
    // For CApp ATOMs Map
    AdobeValueByTagName["CAppATOMSMapSearchKeyword"] = "Search Keyword | ";
    AdobeValueByTagName["CAppATOMSMapSearchResult"] = "Search Result | ";
    AdobeValueByTagName["CAppATOMSMapLevelSelector"] = "Level Selector | ";
    AdobeValueByTagName["CAppATOMSMapMapClicks"] = "Map Clicks | ";
    AdobeValueByTagName["CAppPopularSearchesClearAll"] = "Clear All";
    AdobeValueByTagName["CAppSearchResultFlySaveFlight"] = "Save";
    AdobeValueByTagName["CAppSearchResultFlyRemoveFlight"] = "Remove";
    AdobeValueByTagName["CAppSearchResultIncompleteScan"] = "Incomplete Scan";
    // For Search V2 all results
    AdobeValueByTagName["iShopChangiLogoEntry"] = "iShopChangi Logo Entry";
    AdobeValueByTagName["iShopChangiSearch"] = "iShopChangi Search | ";
    AdobeValueByTagName["iShopChangiProduct"] = "iShopChangi Product | ";
    // For CApp Rewards
    AdobeValueByTagName["CAppRewardsNonLoggedIn"] = "Non-logged in";
    AdobeValueByTagName["RewardCatalogueClearAll"] = "Clear All";
    AdobeValueByTagName["RewardCatalogueApplyFilters"] = "Apply Filters | ";
    return AdobeValueByTagName;
  }({});
  var NotificationSettingToggleValue = exports.NotificationSettingToggleValue = /*#__PURE__*/function (NotificationSettingToggleValue) {
    NotificationSettingToggleValue["On"] = "On";
    NotificationSettingToggleValue["Off"] = "Off";
    return NotificationSettingToggleValue;
  }({});
  var GAMIFICATION_TRACKING_VALUES = exports.GAMIFICATION_TRACKING_VALUES = /*#__PURE__*/function (GAMIFICATION_TRACKING_VALUES) {
    GAMIFICATION_TRACKING_VALUES["LOGGED_IN_PREFIX"] = "Logged in:";
    GAMIFICATION_TRACKING_VALUES["NOT_LOGGED_IN_PREFIX"] = "Not logged in:";
    GAMIFICATION_TRACKING_VALUES["DAILY_COMET_STREAK"] = "Daily Comet Streak";
    GAMIFICATION_TRACKING_VALUES["CLAIM_TODAY_COMETS"] = "Claim Today's Comets";
    GAMIFICATION_TRACKING_VALUES["LOGIN_TO_CLAIM_TODAY_COMETS"] = "Login to Claim Today's Comets";
    GAMIFICATION_TRACKING_VALUES["MISSION_PASS"] = "Mission Pass";
    GAMIFICATION_TRACKING_VALUES["ASTEROID_CHALLENGE"] = "Asteroid Challenge";
    GAMIFICATION_TRACKING_VALUES["PLAY_NOW"] = "Play Now";
    GAMIFICATION_TRACKING_VALUES["LAUNCH_NOW"] = "Launch Now";
    GAMIFICATION_TRACKING_VALUES["LOGIN_TO_PLAY"] = "Login to Play";
    return GAMIFICATION_TRACKING_VALUES;
  }({}); // for action
  var trackAction = exports.trackAction = function trackAction(action, contextData) {
    _reactNativeAcpcore.ACPCore.trackAction(action, contextData);
  };

  // for pages
  var trackState = exports.trackState = function trackState(state, contextData) {
    _reactNativeAcpcore.ACPCore.trackState(state, contextData);
  };
  var getExperienceCloudId = exports.getExperienceCloudId = function getExperienceCloudId() {
    return _reactNativeAcpcore.ACPIdentity.getExperienceCloudId();
  };
  var commonTrackingScreen = exports.commonTrackingScreen = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (currentScreen, previousScreen, isLoggedIn) {
      var ecid = yield getExperienceCloudId();
      trackAction(AdobeTagName.common, (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, AdobeTagName.cagPagename, currentScreen), AdobeTagName.cagSiteSection, ""), AdobeTagName.cagLanguage, "EN"), AdobeTagName.cagSite, `Mobile App (${_reactNative.Platform.OS})`), AdobeTagName.cagPreviousPage, (0, _lodash.isEmpty)(previousScreen) ? "Explore" : previousScreen), AdobeTagName.cagDayOfWeek, (0, _dateTime.getWeekDay)()), AdobeTagName.cagEcid, ecid), AdobeTagName.cagBusinessUnit, "ChangiApp"), AdobeTagName.cagPageview, "1"), AdobeTagName.cappNewLogin, isLoggedIn ? "yes" : "no"));
    });
    return function commonTrackingScreen(_x, _x2, _x3) {
      return _ref.apply(this, arguments);
    };
  }();

  /**
   * @param obj {{}}
   */
  var adobeCampaignSetLinkageFields = exports.adobeCampaignSetLinkageFields = function adobeCampaignSetLinkageFields() {
    var obj = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    if (!Object.entries(obj).length) return null;
    _reactNativeAcpcampaign.ACPCampaign.setLinkageFields(obj);
  };
  var adobeCampaignResetLinkageFields = exports.adobeCampaignResetLinkageFields = function adobeCampaignResetLinkageFields() {
    _reactNativeAcpcampaign.ACPCampaign.resetLinkageFields();
  };
  var adobeUpdateConfiguration = exports.adobeUpdateConfiguration = function adobeUpdateConfiguration(obj) {
    // console.log("adobe__adobeUpdateConfiguration", obj)
    _reactNativeAcpcore.ACPCore.updateConfiguration(obj);
  };
  var adobePrefetchContent = exports.adobePrefetchContent = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      var profile = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      try {
        var ECID = yield getExperienceCloudId();
        // environment - dev - uat - preprod - prod
        var mBoxParameters1 = {
          environment: "dev",
          channel: 'changiapp'
        };
        var profileParameters1 = Object.assign({}, profile, {
          ECID: ECID
        });
        // console.log('adobe__PrefetchContent_mBoxParameters1', mBoxParameters1)
        // console.log('adobe__PrefetchContent_profileParameters1', profileParameters1)
        var targetParameters1 = new _reactNativeAcptarget.ACPTargetParameters(mBoxParameters1, profileParameters1, null, null);
        var prefetch1 = new _reactNativeAcptarget.ACPTargetPrefetchObject("JustForYouLanding", targetParameters1);
        var prefetchList = [prefetch1];
        var success = yield _reactNativeAcptarget.ACPTarget.prefetchContent(prefetchList, null);
        // console.log('adobe__PrefetchContent_success', success)
        return success;
      } catch (error) {
        // console.log("adobe__PrefetchContent_error", error)
      }
    });
    return function adobePrefetchContent() {
      return _ref2.apply(this, arguments);
    };
  }();
  var DEFAULT_LOCATION_CONTENT = exports.DEFAULT_LOCATION_CONTENT = 'null';
  var mBoxNameDefault = "changi-category-recs-mbox";
  var adobeRetrieveLocationContent = exports.adobeRetrieveLocationContent = /*#__PURE__*/function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* () {
      var profileParams = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var boxName = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '';
      var ECID = yield getExperienceCloudId();
      var mBoxParameters1 = Object.assign({
        environment: EnvSettings.ENV,
        channel: 'changiapp'
      }, params);
      var profileParameters1 = Object.assign({}, profileParams, {
        ECID: ECID
      });
      var mBoxName = boxName || mBoxNameDefault;
      // console.log('adobe__RetrieveLocationContent_mBoxName', mBoxName)
      // console.log('adobe__RetrieveLocationContent_mBoxParameters1', mBoxParameters1)
      // console.log('adobe__RetrieveLocationContent_profileParameters1', profileParameters1)
      var params1 = new _reactNativeAcptarget.ACPTargetParameters(mBoxParameters1, profileParameters1, null, null);
      // console.log('adobe__RetrieveLocationContent_ACPTargetParameters_params', params1)
      return new Promise(function (resolve, reject) {
        var request1 = new _reactNativeAcptarget.ACPTargetRequestObject(mBoxName, params1, DEFAULT_LOCATION_CONTENT, function (error, content) {
          if (error) {
            // console.error('adobe__RetrieveLocationContent_ACPTargetRequestObject_error', error)
            resolve(JSON.stringify({
              code: 'adobeError',
              message: error == null ? undefined : error.message
            }));
          } else {
            // console.log('adobe__RetrieveLocationContent_ACPTargetRequestObject_content:' + content)
            resolve(content);
          }
        });
        return _reactNativeAcptarget.ACPTarget.retrieveLocationContent([request1], null);
      });
    });
    return function adobeRetrieveLocationContent() {
      return _ref3.apply(this, arguments);
    };
  }();
  var adobeClickedLocation = exports.adobeClickedLocation = /*#__PURE__*/function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* () {
      var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var boxName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
      var ECID = yield getExperienceCloudId();
      var mBoxParameters1 = {
        environment: "dev",
        channel: 'changiapp'
      };
      var profileParameters1 = Object.assign({}, params, {
        ECID: ECID
      });
      var mBoxName = boxName || mBoxNameDefault;
      var params1 = new _reactNativeAcptarget.ACPTargetParameters(mBoxParameters1, profileParameters1, null, null);
      return _reactNativeAcptarget.ACPTarget.locationClickedWithName(mBoxName, params1);
    });
    return function adobeClickedLocation() {
      return _ref4.apply(this, arguments);
    };
  }();
  var adobeSyncIdentifier = exports.adobeSyncIdentifier = function adobeSyncIdentifier(_ref5) {
    var identifierType = _ref5.identifierType,
      identifierValue = _ref5.identifierValue,
      authenticationState = _ref5.authenticationState;
    var authenticationStates = {
      unknown: _reactNativeAcpcore.ACPMobileVisitorAuthenticationState.UNKNOWN,
      loggedOut: _reactNativeAcpcore.ACPMobileVisitorAuthenticationState.LOGGED_OUT,
      authenticated: _reactNativeAcpcore.ACPMobileVisitorAuthenticationState.AUTHENTICATED
    };
    _reactNativeAcpcore.ACPIdentity.syncIdentifier(identifierType || "", identifierValue || "", authenticationStates[authenticationState]);
  };
