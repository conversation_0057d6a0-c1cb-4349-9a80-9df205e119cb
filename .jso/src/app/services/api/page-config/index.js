  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.requestLandingPageAemData = exports.requestExploreLandingPage = exports.requestAccountLandingPage = exports.requestAEMActiveTooltip = exports.getPageStructureConfiguration = exports.getConsolidatedAemGroupTwo = exports.getConsolidatedAemGroupOne = exports.Tier = undefined;
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _isEmpty = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _get2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _envParams = _$$_REQUIRE(_dependencyMap[7]);
  var _pageConfigRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _aemRedux = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[9]));
  var _systemRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[10]));
  var _store = _$$_REQUIRE(_dependencyMap[11]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[12]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[13]);
  var _analytics = _$$_REQUIRE(_dependencyMap[14]);
  var _exploreRedux = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[15]));
  var _aemGroupTwo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _utils = _$$_REQUIRE(_dependencyMap[17]);
  var _aemHelper = _$$_REQUIRE(_dependencyMap[18]);
  var _storage = _$$_REQUIRE(_dependencyMap[19]);
  var _persist = _$$_REQUIRE(_dependencyMap[20]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var Tier = exports.Tier = /*#__PURE__*/function (Tier) {
    Tier["Member"] = "Member";
    Tier["StaffMember"] = "StaffMember";
    Tier["Gold"] = "Gold";
    Tier["StaffGold"] = "StaffGold";
    Tier["Platinum"] = "Platinum";
    Tier["StaffPlatinum"] = "StaffPlatinum";
    Tier["Classic"] = "Classic";
    Tier["Premium"] = "Premium";
    Tier["Elite"] = "Elite";
    Tier["Monarch"] = "Monarch";
    Tier["StaffMonarch"] = "StaffMonarch";
    return Tier;
  }({});
  var getPageStructureConfiguration = exports.getPageStructureConfiguration = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      var _env;
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_CONSOLIDATED_STRUCTURES);
      var paramsArray = _apis.default.getConsolidatedStructures.split(" ");
      var method = paramsArray[0] || "POST";
      var url = ((_env = (0, _envParams.env)()) == null ? undefined : _env.API_GATEWAY_URL) + paramsArray[1];
      var data = {
        apiVersion: {
          shopPageConfig: "2",
          dinePageConfig: "2",
          accountPageLevel: "2",
          flyPageLevel: "2",
          airportPageLevel: "2"
        },
        responseValue: {
          shopPageConfig: true,
          dinePageConfig: true,
          accountPageLevel: true,
          flyPageLevel: true,
          airportPageLevel: true
        }
      };
      try {
        var _env2;
        dtAction.reportStringValue("request_data", (0, _analytics.convertStringValue)(JSON.stringify(data)));
        var response = yield (0, _request.default)({
          url: url,
          method: method,
          data: data,
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.X_API_KEY
          }
        });
        if (!response.success) {
          throw response;
        }
        dtAction.reportStringValue("status", "success");
        if (!(0, _isEmpty.default)(response == null ? undefined : response.data)) {
          var _response$data = response == null ? undefined : response.data,
            dinePageConfig = _response$data.dinePageConfig,
            shopPageConfig = _response$data.shopPageConfig,
            accountPageLevel = _response$data.accountPageLevel,
            flyPageLevel = _response$data.flyPageLevel,
            airportPageLevel = _response$data.airportPageLevel;
          if (dinePageConfig != null && dinePageConfig.length) {
            _store.store.dispatch(_pageConfigRedux.default.dinePageConfigurationSuccess({
              data: {
                getPageConfig: dinePageConfig
              }
            }));
          }
          if (shopPageConfig != null && shopPageConfig.length) {
            _store.store.dispatch(_pageConfigRedux.default.shopPageConfigurationSuccess({
              data: {
                getPageConfig: shopPageConfig
              }
            }));
          }
          if (accountPageLevel != null && accountPageLevel.length) {
            var accountPage = (0, _aemHelper.standardizeDataAccountLanding)({
              list: accountPageLevel
            });
            _store.store.dispatch(_pageConfigRedux.default.accountPageConfigurationSuccess(accountPage));
          }
          if (flyPageLevel != null && flyPageLevel.length) {
            _store.store.dispatch(_pageConfigRedux.default.flyPageConfigurationSuccess(flyPageLevel));
          }
          if (airportPageLevel != null && airportPageLevel.length) {
            _store.store.dispatch(_pageConfigRedux.default.airportPageConfigurationSuccess(airportPageLevel));
          }
        }
        return true;
      } catch (error) {
        dtAction.reportStringValue("status", "failed");
        return null;
      } finally {
        dtAction.leaveAction();
      }
    });
    return function getPageStructureConfiguration() {
      return _ref.apply(this, arguments);
    };
  }();
  var getConsolidatedAemGroupOne = exports.getConsolidatedAemGroupOne = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      var appSettingData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_CONSOLIDATED_AEM_GROUP_ONE);
      var paramsArray = _apis.default.getConsolidatedAemGroupOne.split(" ");
      var method = paramsArray[0] || "POST";
      var envData = (0, _envParams.env)() || appSettingData;
      var url = (envData == null ? undefined : envData.API_GATEWAY_URL) + paramsArray[1];
      var data = {
        apiVersion: {
          forceUpdate: "2",
          errorMaintenance: "1",
          appCommonData: "1"
        },
        responseValue: {
          forceUpdate: true,
          errorMaintenance: true,
          appCommonData: true
        }
      };
      try {
        dtAction.reportStringValue("request_data", (0, _analytics.convertStringValue)(JSON.stringify(data)));
        var response = yield (0, _request.default)({
          url: url,
          method: method,
          data: data,
          headers: {
            "x-api-key": envData == null ? undefined : envData.X_API_KEY
          }
        });
        if (!response.success) {
          throw response;
        }
        dtAction.reportStringValue("status", "success");
        var _ref3 = (response == null ? undefined : response.data) || {},
          forceUpdate = _ref3.forceUpdate,
          errorMaintenance = _ref3.errorMaintenance,
          appCommonData = _ref3.appCommonData;

        // appCommonData
        if (!(0, _isEmpty.default)(appCommonData)) {
          _store.store.dispatch(_aemRedux.default.getAemConfigDataSuccess({
            data: appCommonData,
            statusCode: 200,
            success: true,
            name: _aemRedux.AEM_PAGE_NAME.AEM_COMMON_DATA,
            expires: null
          }));
        }

        // errorMaintenance
        _store.store.dispatch(_aemRedux.default.getAemConfigDataSuccess({
          data: !(0, _isEmpty.default)(errorMaintenance) ? {
            list: errorMaintenance
          } : null,
          statusCode: 200,
          success: true,
          name: _aemRedux.AEM_PAGE_NAME.GET_ERROR_MAINTENANCE,
          expires: null
        }));

        // forceUpdate
        (0, _mmkvStorage.setForcedUpdateTempData)(forceUpdate);
        _store.store.dispatch(_systemRedux.default.forcedUpdateSuccess(forceUpdate));
        return response == null ? undefined : response.data;
      } catch (error) {
        dtAction.reportStringValue("status", "failed");
        return null;
      } finally {
        dtAction.leaveAction();
      }
    });
    return function getConsolidatedAemGroupOne() {
      return _ref2.apply(this, arguments);
    };
  }();
  var getConsolidatedAemGroupTwo = exports.getConsolidatedAemGroupTwo = /*#__PURE__*/function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* () {
      var _env3;
      _store.store.dispatch(_aemGroupTwo.default.aemGroupTwoFetching());
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_CONSOLIDATED_AEM_GROUP_TWO);
      var paramsArray = _apis.default.getConsolidatedAemGroupTwo.split(" ");
      var method = paramsArray[0] || "POST";
      var url = ((_env3 = (0, _envParams.env)()) == null ? undefined : _env3.API_GATEWAY_URL) + paramsArray[1];
      var data = {
        apiVersion: {
          cardDetail: "1",
          attractionsFilter: "1",
          baggagePredictorTiming: "1",
          monarchOnboardingOverlay: "1",
          scrollBuddy: "1",
          changiRewardsMember: "2",
          cardDetailMonarch: "2",
          dineShopEpicLandingPage: "1",
          accountBannerList: "1"
        },
        responseValue: {
          cardDetail: true,
          attractionsFilter: true,
          baggagePredictorTiming: true,
          monarchOnboardingOverlay: true,
          scrollBuddy: true,
          changiRewardsMember: true,
          cardDetailMonarch: true,
          dineShopEpicLandingPage: true,
          accountBannerList: true
        }
      };
      try {
        var _env4;
        dtAction.reportStringValue("request_data", (0, _analytics.convertStringValue)(JSON.stringify(data)));
        var response = yield (0, _request.default)({
          url: url,
          method: method,
          data: data,
          headers: {
            "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.X_API_KEY
          }
        });
        if (!response.success) {
          throw response;
        }
        dtAction.reportStringValue("status", "success");
        var _ref5 = (response == null ? undefined : response.data) || {},
          cardDetail = _ref5.cardDetail,
          attractionsFilter = _ref5.attractionsFilter,
          baggagePredictorTiming = _ref5.baggagePredictorTiming,
          monarchOnboardingOverlay = _ref5.monarchOnboardingOverlay,
          scrollBuddy = _ref5.scrollBuddy,
          changiRewardsMember = _ref5.changiRewardsMember,
          cardDetailMonarch = _ref5.cardDetailMonarch,
          dineShopEpicLandingPage = _ref5.dineShopEpicLandingPage,
          accountBannerList = _ref5.accountBannerList;

        // bin/ichangi/foryou/horizontalcontentcard/detail
        _store.store.dispatch(_aemGroupTwo.default.horizontalContentCardDetailSuccess(cardDetail));

        // bin/ichangi/attractions/filter
        var locationFilterList = attractionsFilter == null ? undefined : attractionsFilter.find(function (item) {
          return item.tagName === "locations";
        });
        if (!(0, _isEmpty.default)(locationFilterList)) {
          dtAction.reportStringValue("response", "locationFilterList_success-with-data");
          _store.store.dispatch(_exploreRedux.default.getExploreChangiLocationSuccess((0, _get2.default)(locationFilterList, "childTags")));
        } else {
          dtAction.reportStringValue("response", "locationFilterList_success-but-no-data");
          _store.store.dispatch(_exploreRedux.default.getExploreChangiLocationSuccess([]));
        }

        // bin/ichangi/fly/baggage-predictor-timing-l3-page-link
        _store.store.dispatch(_aemGroupTwo.default.baggagePredictionSuccess(baggagePredictorTiming));

        // bin/ichangi/foryou/monarch-onboarding-overlay
        _store.store.dispatch(_aemGroupTwo.default.monarchOnboardingOverlaySuccess(monarchOnboardingOverlay));

        // bin/ichangi/explore/scroll-buddy
        _store.store.dispatch(_aemGroupTwo.default.scrollBuddySuccess(scrollBuddy));

        // bin/ichangi/foryou/changirewardsmember/v2
        var dataList = changiRewardsMember == null ? undefined : changiRewardsMember.reduce(function (iconsMemberInfo, element) {
          return Object.assign({}, iconsMemberInfo, (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, element.tierInfo, (0, _utils.mappingUrlAem)(element.icon)), "StaffMember", iconsMemberInfo == null ? undefined : iconsMemberInfo.Member), "StaffGold", iconsMemberInfo == null ? undefined : iconsMemberInfo.Gold), "StaffPlatinum", iconsMemberInfo == null ? undefined : iconsMemberInfo.Platinum), "StaffMonarch", iconsMemberInfo == null ? undefined : iconsMemberInfo.Monarch));
        }, {});
        dataList[Tier.StaffMember] = dataList[Tier.Member];
        dataList[Tier.StaffGold] = dataList[Tier.Gold];
        dataList[Tier.StaffPlatinum] = dataList[Tier.Platinum];
        dataList[Tier.StaffMonarch] = dataList[Tier.Monarch];
        _store.store.dispatch(_aemGroupTwo.default.rewardsMemberTierSuccess(dataList));

        // bin/ichangi/foryou/horizontalcontentcard/monarch-tier/v2
        _store.store.dispatch(_aemGroupTwo.default.horizontalContentCardDetailMonarchSuccess(cardDetailMonarch));

        // bin/ichangi/dineandshop/marketplaces/epic-landing-page
        _store.store.dispatch(_aemGroupTwo.default.dineShopEpicLandingPageHandle(dineShopEpicLandingPage));

        // /bin/ichangi/banners
        _store.store.dispatch(_aemGroupTwo.default.accountBannerListSuccess(accountBannerList));
        _store.store.dispatch(_aemGroupTwo.default.aemGroupTwoSuccess());
        return true;
      } catch (error) {
        _store.store.dispatch(_aemGroupTwo.default.aemGroupTwoFailure());
        dtAction.reportStringValue("status", "failed");
        return null;
      } finally {
        dtAction.leaveAction();
      }
    });
    return function getConsolidatedAemGroupTwo() {
      return _ref4.apply(this, arguments);
    };
  }();
  var requestLandingPageAemData = exports.requestLandingPageAemData = /*#__PURE__*/function () {
    var _ref6 = (0, _asyncToGenerator2.default)(function* () {
      try {
        var _path$getDineShopEpic = _apis.default.getDineShopEpicLandingPageData.split(" "),
          _path$getDineShopEpic2 = (0, _slicedToArray2.default)(_path$getDineShopEpic, 2),
          method = _path$getDineShopEpic2[0],
          url = _path$getDineShopEpic2[1];
        var response = yield (0, _request.default)({
          method: method || "GET",
          url: (0, _utils.mappingUrlAem)(url)
        });
        if ((response == null ? undefined : response.statusCode) === 200 && response != null && response.data) {
          _store.store.dispatch(_aemGroupTwo.default.dineShopEpicLandingPageHandle(response == null ? undefined : response.data));
        } else {
          _store.store.dispatch(_aemGroupTwo.default.dineShopEpicLandingPageFailure());
        }
      } catch (_unused) {
        _store.store.dispatch(_aemGroupTwo.default.dineShopEpicLandingPageFailure());
      }
    });
    return function requestLandingPageAemData() {
      return _ref6.apply(this, arguments);
    };
  }();
  var requestExploreLandingPage = exports.requestExploreLandingPage = /*#__PURE__*/function () {
    var _ref7 = (0, _asyncToGenerator2.default)(function* () {
      try {
        var _path$getPageLevelCon = _apis.default.getPageLevelConfig.split(" "),
          _path$getPageLevelCon2 = (0, _slicedToArray2.default)(_path$getPageLevelCon, 2),
          method = _path$getPageLevelCon2[0],
          url = _path$getPageLevelCon2[1];
        var response = yield (0, _request.default)({
          method: method || "GET",
          url: (0, _utils.mappingUrlAem)(url),
          parameters: {
            type: "exploreLanding"
          }
        });
        if ((response == null ? undefined : response.statusCode) === 200 && response != null && response.data) {
          _store.store.dispatch(_pageConfigRedux.default.explorePageConfigurationSuccess(response == null ? undefined : response.data));
        } else {
          _store.store.dispatch(_pageConfigRedux.default.explorePageConfigurationFailure());
        }
      } catch (_unused2) {
        _store.store.dispatch(_pageConfigRedux.default.explorePageConfigurationFailure());
      }
    });
    return function requestExploreLandingPage() {
      return _ref7.apply(this, arguments);
    };
  }();
  var requestAccountLandingPage = exports.requestAccountLandingPage = /*#__PURE__*/function () {
    var _ref8 = (0, _asyncToGenerator2.default)(function* () {
      try {
        _store.store.dispatch(_pageConfigRedux.default.accountPageConfigurationRequest());
        var _path$getPageLevelCon3 = _apis.default.getPageLevelConfig.split(" "),
          _path$getPageLevelCon4 = (0, _slicedToArray2.default)(_path$getPageLevelCon3, 2),
          method = _path$getPageLevelCon4[0],
          url = _path$getPageLevelCon4[1];
        var response = yield (0, _request.default)({
          method: method || "GET",
          url: (0, _utils.mappingUrlAem)(url),
          parameters: {
            type: "accountLanding"
          }
        });
        if ((response == null ? undefined : response.statusCode) === 200 && response != null && response.data) {
          var accountPage = (0, _aemHelper.standardizeDataAccountLanding)(response == null ? undefined : response.data);
          _store.store.dispatch(_pageConfigRedux.default.accountPageConfigurationSuccess(accountPage));
        } else {
          _store.store.dispatch(_pageConfigRedux.default.accountPageConfigurationFailure());
        }
      } catch (_unused3) {
        _store.store.dispatch(_pageConfigRedux.default.accountPageConfigurationFailure());
      }
    });
    return function requestAccountLandingPage() {
      return _ref8.apply(this, arguments);
    };
  }();
  var requestAEMActiveTooltip = exports.requestAEMActiveTooltip = /*#__PURE__*/function () {
    var _ref9 = (0, _asyncToGenerator2.default)(function* () {
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_AEM_GET_ACTIVE_TOOLTIP);
      try {
        var openFlyScreenBefore = yield (0, _storage.load)(_storage.StorageKey.needDisableToolTipFlyScreen);
        var activeToolTipAemData = _persist.usePersistStore.getState().activeToolTipAemData;
        if (openFlyScreenBefore && activeToolTipAemData) {
          dtAction.reportStringValue("status", "not-call-api");
          return;
        }
        var _path$getDataToolTip$ = _apis.default.getDataToolTip.split(" "),
          _path$getDataToolTip$2 = (0, _slicedToArray2.default)(_path$getDataToolTip$, 2),
          method = _path$getDataToolTip$2[0],
          url = _path$getDataToolTip$2[1];
        var response = yield (0, _request.default)({
          method: method || "GET",
          url: (0, _utils.mappingUrlAem)(url)
        });
        if ((response == null ? undefined : response.statusCode) === 200 && response != null && response.data) {
          dtAction.reportStringValue("status", "success");
          _persist.usePersistStore.setState({
            activeToolTipAemData: response == null ? undefined : response.data
          });
        } else {
          dtAction.reportStringValue("status", "throw-error");
          throw response;
        }
      } catch (error) {
        dtAction.reportStringValue("catch", "error");
      } finally {
        dtAction.leaveAction();
      }
    });
    return function requestAEMActiveTooltip() {
      return _ref9.apply(this, arguments);
    };
  }();
