  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.updateLocationPreference = exports.putUserDeviceTokenRequest = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[3]);
  var _storage = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[4]));
  var storage = _storage;
  var _mutations = _$$_REQUIRE(_dependencyMap[5]);
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[6]));
  var _envParams = _$$_REQUIRE(_dependencyMap[7]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[8]));
  var _analytics = _$$_REQUIRE(_dependencyMap[9]);
  var _messaging = _$$_REQUIRE(_dependencyMap[10]);
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[11]));
  var _reactNativePermissions = _$$_REQUIRE(_dependencyMap[12]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[13]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var deviceId = _reactNativeDeviceInfo.default.getUniqueIdSync();
  var deviceName = _reactNativeDeviceInfo.default.getDeviceNameSync();
  var putUserDeviceTokenRequest = exports.putUserDeviceTokenRequest = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_PUT_USER_DEVICE_TOKEN);
      try {
        var _env, _env2, _response$data;
        var deviceToken = yield (0, _messaging.getDeviceToken)();
        var variables = {
          device_platform: _reactNative.Platform.OS.toUpperCase(),
          // IOS , ANDROID
          device_id: deviceId,
          device_name: deviceName,
          device_token: deviceToken
        };
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_mutations.putUserDeviceTokenMutation, variables),
          parameters: {},
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        if ((response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null || (_response$data = _response$data.putUserDeviceToken) == null ? undefined : _response$data.status) === "success") {
          dtAction.reportStringValue('status', 'success');
          storage.save(storage.StorageKey.deviceInformation, variables);
        } else dtAction.reportStringValue('status', 'no_saving');
      } catch (error) {
        dtAction.reportStringValue('status', 'error');
      } finally {
        dtAction.leaveAction();
      }
    });
    return function putUserDeviceTokenRequest() {
      return _ref.apply(this, arguments);
    };
  }();
  var updateLocationPreference = exports.updateLocationPreference = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (isForcedUpdate, isLocation) {
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_POST_UPDATE_LOCATION_PREFERENCE);
      try {
        var _env3, _response$data2;
        var subscribed;
        if (isLocation === undefined || isLocation === null) {
          var permission = _reactNative.Platform.select({
            ios: _reactNativePermissions.PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
            android: _reactNativePermissions.PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
          });
          var result = yield (0, _reactNativePermissions.check)(permission);
          dtAction.reportStringValue('checked_location', `${result}`);
          if (result === _reactNativePermissions.RESULTS.GRANTED) {
            subscribed = true;
          } else {
            subscribed = false;
          }
        } else {
          subscribed = !!isLocation;
          dtAction.reportStringValue('passed_location', `${isLocation}`);
        }
        dtAction.reportStringValue('subscribed', `${subscribed}`);
        var LAST_LOGGED_IN_USER = yield (0, _storage.loadFromEncryptedStorage)(_storage.StorageKey.lastLoggedInUser);
        dtAction.reportStringValue('last_login_id', `${LAST_LOGGED_IN_USER == null ? undefined : LAST_LOGGED_IN_USER.uid}`);
        var locationPermissionPreferenceState = (0, _mmkvStorage.getLocationPermissionPreferenceState)();

        // check previous state if it equal to current state or not
        if (locationPermissionPreferenceState && !isForcedUpdate) {
          var prevSubscribed = locationPermissionPreferenceState.subscribed,
            last_login_id = locationPermissionPreferenceState.last_login_id;
          if (prevSubscribed === subscribed && last_login_id === ((LAST_LOGGED_IN_USER == null ? undefined : LAST_LOGGED_IN_USER.uid) || "")) {
            dtAction.reportStringValue('status', 'no_need_send_request');
            return false;
          }
        }
        var data = {
          device_id: deviceId,
          subscribed: subscribed,
          last_login_id: (LAST_LOGGED_IN_USER == null ? undefined : LAST_LOGGED_IN_USER.uid) || ""
        };
        var paramsArray = _apis.default.updateLocationPreference.split(" ");
        var method = paramsArray[0] || "GET";
        var url = ((_env3 = (0, _envParams.env)()) == null ? undefined : _env3.API_GATEWAY_URL) + paramsArray[1];
        var response = yield (0, _request.default)({
          url: url,
          method: method,
          data: data
        });
        if (response != null && (_response$data2 = response.data) != null && _response$data2.success) {
          dtAction.reportStringValue('status', 'success');
          (0, _mmkvStorage.setLocationPermissionPreferenceState)(data);
        } else {
          dtAction.reportStringValue('status', 'failed');
        }
        return true;
      } catch (error) {
        dtAction.reportStringValue("errorStatusCode", `${error == null ? undefined : error.statusCode}`);
        dtAction.reportStringValue('errorMessage', (0, _analytics.convertStringValue)(error == null ? undefined : error.message));
        return false;
      } finally {
        dtAction.leaveAction();
      }
    });
    return function updateLocationPreference(_x, _x2) {
      return _ref2.apply(this, arguments);
    };
  }();
