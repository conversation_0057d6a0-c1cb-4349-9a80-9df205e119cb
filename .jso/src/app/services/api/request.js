  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.default = undefined;
  var _objectWithoutProperties2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[3]);
  var _axios = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var _qs = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[5]));
  var _pathToRegexp = _$$_REQUIRE(_dependencyMap[6]);
  var _envParams = _$$_REQUIRE(_dependencyMap[7]);
  var _reactNativeDeviceInfo = _$$_REQUIRE(_dependencyMap[8]);
  var _reactNativeVersionInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[9]));
  var _constants = _$$_REQUIRE(_dependencyMap[10]);
  var _store = _$$_REQUIRE(_dependencyMap[11]);
  var _mmkvEncryptionStorage = _$$_REQUIRE(_dependencyMap[12]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[13]);
  var _excluded = ["url", "headers", "query", "data", "parameters", "method", "timeout"];
  /* eslint-disable prefer-promise-reject-errors */
  var appVersion = _reactNativeVersionInfo.default.appVersion;
  var buildVersion = _reactNativeVersionInfo.default.buildVersion;
  var TIME_OUT = 20000;
  var buildUrl = function buildUrl(url, params, queryString) {
    var domain = "";
    var fullPath = url;
    try {
      var urlMatch = fullPath.match(/[a-zA-Z]+:\/\/[^/]*/);
      if (urlMatch) {
        ;
        var _urlMatch = (0, _slicedToArray2.default)(urlMatch, 1);
        domain = _urlMatch[0];
        fullPath = fullPath.slice(domain.length);
      }
      fullPath = (0, _pathToRegexp.compile)(fullPath)(params);
      fullPath = domain + fullPath;
    } catch (error) {}
    if (queryString) {
      if (fullPath.indexOf("?") >= 0) {
        fullPath = `${fullPath}&${queryString}`;
      } else {
        fullPath = `${fullPath}?${queryString}`;
      }
    }
    return fullPath;
  };
  var buildQuery = function buildQuery(method, query) {
    var data = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    var dataQuery = Object.assign({}, query);
    if (method.toLocaleLowerCase() === "get") {
      Object.assign(dataQuery, data);
    }
    return _qs.default.stringify(dataQuery);
  };
  var buildOptions = function buildOptions(options) {
    var _options$url = options.url,
      _url = _options$url === undefined ? "" : _options$url,
      _options$headers = options.headers,
      _headers = _options$headers === undefined ? {} : _options$headers,
      _options$query = options.query,
      _query = _options$query === undefined ? {} : _options$query,
      _options$data = options.data,
      _data = _options$data === undefined ? undefined : _options$data,
      _options$parameters = options.parameters,
      params = _options$parameters === undefined ? {} : _options$parameters,
      _options$method = options.method,
      _method = _options$method === undefined ? "get" : _options$method,
      _options$timeout = options.timeout,
      timeout = _options$timeout === undefined ? TIME_OUT : _options$timeout,
      rest = (0, _objectWithoutProperties2.default)(options, _excluded);
    var queryString = buildQuery(_method, _query, _data);
    var url = buildUrl(_url, params, queryString);
    var axiosOptions = Object.assign({
      url: url,
      headers: _headers,
      data: _data,
      params: params,
      method: _method,
      timeout: timeout
    }, rest);
    return axiosOptions;
  };
  var setAuth = function setAuth(config) {
    var _authTokenPayload$pos, _authTokenPayload$pos2, _authTokenPayload$pos3;
    var newConfig = config;
    var authTokenPayload = (0, _mmkvEncryptionStorage.getAuthTokenPayload)() || {};
    var userAgent = (0, _mmkvStorage.getUserAgent)();
    if (userAgent) {
      newConfig.headers["user-agent"] = userAgent;
      newConfig.headers["x-amz-user-agent"] = userAgent;
    }
    newConfig.headers.ocid_login_token = (authTokenPayload == null || (_authTokenPayload$pos = authTokenPayload.postMethod) == null ? undefined : _authTokenPayload$pos.access_token) || "";
    newConfig.headers["ocid-login-token"] = (authTokenPayload == null || (_authTokenPayload$pos2 = authTokenPayload.postMethod) == null ? undefined : _authTokenPayload$pos2.access_token) || "";
    newConfig.headers.uid = (authTokenPayload == null || (_authTokenPayload$pos3 = authTokenPayload.postMethod) == null ? undefined : _authTokenPayload$pos3.member_id_cr) || (0, _reactNativeDeviceInfo.getUniqueIdSync)();
    newConfig.headers.deviceId = (0, _reactNativeDeviceInfo.getUniqueIdSync)();
    newConfig.headers.app_version = `${appVersion}_${buildVersion}`;
    newConfig.headers.native = true;
    return newConfig;
  };
  _axios.default.interceptors.request.use(setAuth);
  _axios.default.interceptors.response.use(function (res) {
    return res;
  }, function (error) {
    var _error$response, _error$response2;
    if ((error == null || (_error$response = error.response) == null ? undefined : _error$response.status) === _constants.HTTP_CODE.UNAUTHORIZED || (error == null || (_error$response2 = error.response) == null || (_error$response2 = _error$response2.data) == null ? undefined : _error$response2.errorCode) === _constants.HTTP_ERROR_CODE.UNAUTHORIZED) {
      var _env, _getAppSettingsData, _error$config;
      var apiGatewayURL = ((_env = (0, _envParams.env)()) == null ? undefined : _env.API_GATEWAY_URL) || ((_getAppSettingsData = (0, _mmkvEncryptionStorage.getAppSettingsData)()) == null || (_getAppSettingsData = _getAppSettingsData.settings) == null ? undefined : _getAppSettingsData.API_GATEWAY_URL);
      if (error != null && (_error$config = error.config) != null && (_error$config = _error$config.url) != null && _error$config.includes(apiGatewayURL)) {
        // case login fail will return 403
        _store.store.dispatch({
          type: "NATIVE_AUTH_TOKEN_VERIFY_FAILURE",
          kind: _constants.API_FAILURE_KIND.UNAUTHORIZED
        });
      }
    }
    return Promise.reject(error);
  });
  var request = function request(options) {
    var axiosOptions = buildOptions(options);
    var cancelToken;
    if (_reactNative.Platform.OS === 'android' && options != null && options.timeout) {
      var source = _axios.default.CancelToken.source();
      setTimeout(function () {
        source.cancel(`Timeout of ${options == null ? undefined : options.timeout}ms exceeded`);
      }, options == null ? undefined : options.timeout);
      cancelToken = source.token;
    }
    if (options != null && options.headers && options != null && options.headers["x-api-key"]) {
      axiosOptions.headers["x-api-key"] = options == null ? undefined : options.headers["x-api-key"];
    } else {
      var _env2, _env3;
      axiosOptions.headers["x-api-key"] = options.url.includes("graphql") ? (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY : (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.X_API_KEY;
    }
    return (0, _axios.default)(Object.assign({}, axiosOptions, {
      cancelToken: cancelToken
    })).then(function (response) {
      var statusText = response.statusText,
        status = response.status,
        data = response.data;
      var result = {};
      if (typeof data === "object") {
        if (Array.isArray(data)) {
          result.list = data;
        } else {
          result = data;
        }
      } else {
        result.data = data;
      }
      return Promise.resolve({
        success: true,
        message: statusText,
        statusCode: status,
        data: result
      });
    }).catch(function (error) {
      var response = error.response;
      var msg;
      var statusCode;
      var errorCode;
      if (response && response instanceof Object) {
        var _data$error, _data$error2;
        var data = response.data,
          statusText = response.statusText;
        var error_details = data.error_details,
          error_code = data.error_code;
        statusCode = response.status;
        msg = (data == null ? undefined : data.message) || error_details || statusText || (data == null || (_data$error = data.error) == null ? undefined : _data$error.message) || "Network Error";
        errorCode = error_code || (data == null || (_data$error2 = data.error) == null ? undefined : _data$error2.code) || null;
      } else {
        statusCode = 600;
        msg = (error == null ? undefined : error.message) || "Network Error";
        errorCode = null;
      }
      return Promise.reject({
        success: false,
        statusCode: statusCode,
        message: msg,
        errorCode: errorCode
      });
    });
  };
  var _default = exports.default = request;
