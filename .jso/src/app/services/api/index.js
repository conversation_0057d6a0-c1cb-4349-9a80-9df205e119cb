  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _exportNames = {};
  exports.default = undefined;
  var _apis = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var env = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[3]));
  var _api = _$$_REQUIRE(_dependencyMap[4]);
  Object.keys(_api).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
    if (key in exports && exports[key] === _api[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _api[key];
      }
    });
  });
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var generateApi = function generateApi(params) {
    var url = "";
    var method = "GET";
    var ENDPOINT = env == null ? undefined : env.API_GATEWAY_URL;
    var paramsArray = params.split(" ");
    if (paramsArray.length === 2) {
      method = paramsArray[0];
      url = ENDPOINT + paramsArray[1];
    }
    return function (data) {
      var parameters = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var query = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      return (0, _request.default)({
        url: url,
        data: method === "GET" ? undefined : data,
        parameters: method === "GET" ? Object.assign({}, data, parameters) : parameters,
        method: method,
        query: query
      });
    };
  };
  var APIFunction = {};
  for (var key in _apis.default) {
    APIFunction[key] = generateApi(_apis.default[key]);
  }
  var _default = exports.default = APIFunction;
