  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.pageConfigurationRender = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _dataSyncHandler = _$$_REQUIRE(_dependencyMap[2]);
  var _pageConfig = _$$_REQUIRE(_dependencyMap[3]);
  var _flightApi = _$$_REQUIRE(_dependencyMap[4]);
  var _playpass = _$$_REQUIRE(_dependencyMap[5]);
  var _profile = _$$_REQUIRE(_dependencyMap[6]);
  var postRender = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      setTimeout(/*#__PURE__*/(0, _asyncToGenerator2.default)(function* () {
        if ((0, _dataSyncHandler.syncDineAndShop)()) {
          yield (0, _pageConfig.getPageStructureConfiguration)();
        }
        yield (0, _flightApi.getFlightFilterOptions)();
        yield (0, _pageConfig.requestAEMActiveTooltip)();
        (0, _profile.updateLocationPreference)();
      }), 2000);
    });
    return function postRender() {
      return _ref.apply(this, arguments);
    };
  }();
  var playPassRender = /*#__PURE__*/function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* () {
      yield (0, _flightApi.flyMyTravelFlightsRequest)();
      yield (0, _playpass.getPlayPassBookingActiveRequest)();
    });
    return function playPassRender() {
      return _ref3.apply(this, arguments);
    };
  }();
  var pageConfigurationRender = exports.pageConfigurationRender = /*#__PURE__*/function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* (appSettingData) {
      yield (0, _pageConfig.getConsolidatedAemGroupOne)(appSettingData);
      yield (0, _pageConfig.requestExploreLandingPage)();
      yield playPassRender();
      yield (0, _pageConfig.getConsolidatedAemGroupTwo)();
      yield postRender();
    });
    return function pageConfigurationRender(_x) {
      return _ref4.apply(this, arguments);
    };
  }();
