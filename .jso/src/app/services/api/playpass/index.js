  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.getUpcomingEventRequest = exports.getPlayPassBookingActiveRequest = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _queries = _$$_REQUIRE(_dependencyMap[2]);
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[4]);
  var _envParams = _$$_REQUIRE(_dependencyMap[5]);
  var _store = _$$_REQUIRE(_dependencyMap[6]);
  var _playpass = _$$_REQUIRE(_dependencyMap[7]);
  var _authentication = _$$_REQUIRE(_dependencyMap[8]);
  var _exploreRedux = _$$_REQUIRE(_dependencyMap[9]);
  var _upcomingEvent = _$$_REQUIRE(_dependencyMap[10]);
  var _analytics = _$$_REQUIRE(_dependencyMap[11]);
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[12]);
  var BookingStatus = /*#__PURE__*/function (BookingStatus) {
    BookingStatus["CONFIRMED"] = "confirmed";
    BookingStatus["PENDING"] = "pending";
    return BookingStatus;
  }(BookingStatus || {});
  var getPlayPassBookingActiveRequest = exports.getPlayPassBookingActiveRequest = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      var upComingEvents;
      var stickyCart;
      try {
        var _store$getState, _env, _env2;
        var isLoggedIn = (0, _authentication.checkLoginState)();
        var email = (_store$getState = _store.store.getState()) == null || (_store$getState = _store$getState.profileReducer) == null || (_store$getState = _store$getState.profilePayload) == null ? undefined : _store$getState.email;
        if (!isLoggedIn || !email) return null;
        _playpass.usePlayPassStore.setState({
          playPassStickyCartLoading: true
        });
        _store.store.dispatch(_exploreRedux.ExploreActions.Creators.getUpcomingEventRequest());
        var query = {
          input: {
            userName: email,
            scope: "active",
            isUpcomingEvent: true
          }
        };
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getPlaypassBookingActive, query),
          parameters: {},
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        if ((response == null ? undefined : response.statusCode) === 200 && response != null && response.data) {
          var _response$data, _store$getState2;
          var playPassBookingData = (response == null || (_response$data = response.data) == null || (_response$data = _response$data.data) == null || (_response$data = _response$data.getPlaypassBookings_v2) == null ? undefined : _response$data.data) || [];

          // first load upcoming event
          upComingEvents = playPassBookingData == null ? undefined : playPassBookingData.filter(function (item) {
            return (item == null ? undefined : item.bookingStatus) === BookingStatus.CONFIRMED;
          });
          var getSavedFlights = (_store$getState2 = _store.store.getState()) == null || (_store$getState2 = _store$getState2.mytravelReducer) == null || (_store$getState2 = _store$getState2.myTravelFlightsPayload) == null ? undefined : _store$getState2.getMyTravelFlightDetails;
          var data = Object.assign({}, {
            getPlaypassBookings_v2: {
              data: upComingEvents
            }
          }, {
            getSavedFlights: getSavedFlights
          });
          var dataHandled = new _upcomingEvent.UpcomingEvent().handleData(data);
          _store.store.dispatch(_exploreRedux.ExploreActions.Creators.getUpcomingEventSuccess(dataHandled));

          // second load sticky cart
          stickyCart = playPassBookingData == null ? undefined : playPassBookingData.find(function (item) {
            return (item == null ? undefined : item.bookingStatus) === BookingStatus.PENDING;
          });
          _playpass.usePlayPassStore.setState({
            playPassStickyCart: stickyCart,
            playPassStickyCartLoading: false
          });
          return true;
        } else {
          throw response;
        }
      } catch (error) {
        _store.store.dispatch(_exploreRedux.ExploreActions.Creators.getUpcomingEventFailure(error));
        return null;
      } finally {
        (0, _mmkvStorage.setPlayPassBookingFinished)(true);
      }
    });
    return function getPlayPassBookingActiveRequest() {
      return _ref.apply(this, arguments);
    };
  }();
  var getUpcomingEventRequest = exports.getUpcomingEventRequest = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      var _store$getState3;
      var isLoggedIn = (0, _authentication.checkLoginState)();
      var email = (_store$getState3 = _store.store.getState()) == null || (_store$getState3 = _store$getState3.profileReducer) == null || (_store$getState3 = _store$getState3.profilePayload) == null ? undefined : _store$getState3.email;
      if (!isLoggedIn || !email) return null;
      var query = {
        input: {
          userName: email,
          scope: "all",
          isUpcomingEvent: true
        }
      };
      _store.store.dispatch(_exploreRedux.ExploreActions.Creators.getUpcomingEventRequest());
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_UPCOMING_EVENT);
      try {
        var _env3, _env4, _response$data2;
        var response = yield (0, _request.default)({
          url: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getUpcomingEventV2, query),
          parameters: {},
          headers: {
            "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.APPSYNC_GRAPHQL_API_KEY
          }
        });
        if (response != null && (_response$data2 = response.data) != null && _response$data2.errors) {
          var _response$data3;
          throw response == null || (_response$data3 = response.data) == null ? undefined : _response$data3.errors;
        }
        if (response.statusCode === 200 && response.success) {
          var _store$getState4, _response$data4;
          dtAction.reportStringValue("status", "success");
          var getSavedFlights = (_store$getState4 = _store.store.getState()) == null || (_store$getState4 = _store$getState4.mytravelReducer) == null || (_store$getState4 = _store$getState4.myTravelFlightsPayload) == null ? undefined : _store$getState4.getMyTravelFlightDetails;
          var data = Object.assign({}, response == null || (_response$data4 = response.data) == null ? undefined : _response$data4.data, {
            getSavedFlights: getSavedFlights
          });
          var dataHandled = new _upcomingEvent.UpcomingEvent().handleData(data);
          _store.store.dispatch(_exploreRedux.ExploreActions.Creators.getUpcomingEventSuccess(dataHandled));
        }
      } catch (err) {
        dtAction.reportStringValue("status", "failed");
        _store.store.dispatch(_exploreRedux.ExploreActions.Creators.getUpcomingEventFailure(err));
      } finally {
        dtAction.leaveAction();
      }
    });
    return function getUpcomingEventRequest() {
      return _ref2.apply(this, arguments);
    };
  }();
