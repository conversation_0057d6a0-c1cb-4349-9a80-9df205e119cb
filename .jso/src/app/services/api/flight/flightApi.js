  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.getFlightFilterOptions = exports.flyMyTravelFlightsRequest = exports.fetchIntoCityOrAirportV2 = exports.fetchIntoCityOrAirport = exports.fetchFlightJourney = exports.fetchFlightDetails = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _request = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _envParams = _$$_REQUIRE(_dependencyMap[3]);
  var _awsAmplify = _$$_REQUIRE(_dependencyMap[4]);
  var _lodash = _$$_REQUIRE(_dependencyMap[5]);
  var _queries = _$$_REQUIRE(_dependencyMap[6]);
  var _remoteConfig = _$$_REQUIRE(_dependencyMap[7]);
  var _utils = _$$_REQUIRE(_dependencyMap[8]);
  var _fly = _$$_REQUIRE(_dependencyMap[9]);
  var _flightDetailsCard = _$$_REQUIRE(_dependencyMap[10]);
  var _flightHeroImage = _$$_REQUIRE(_dependencyMap[11]);
  var _flightProps = _$$_REQUIRE(_dependencyMap[12]);
  var _analytics = _$$_REQUIRE(_dependencyMap[13]);
  var _store = _$$_REQUIRE(_dependencyMap[14]);
  var _flyRedux = _$$_REQUIRE(_dependencyMap[15]);
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[16]));
  var _dateTime = _$$_REQUIRE(_dependencyMap[17]);
  var _flySaga = _$$_REQUIRE(_dependencyMap[18]);
  var _mytravelRedux = _$$_REQUIRE(_dependencyMap[19]);
  var _authentication = _$$_REQUIRE(_dependencyMap[20]);
  var formatResponseGetDetailFlight = function formatResponseGetDetailFlight(response, isFromScanBoardingPass, enableGTTD) {
    var _response$getFlightDe, _response$getFlightDe2, _response$getFlightDe3, _response$getFlightDe4, _response$getFlightDe5, _response$getFlightDe6, _response$getFlightDe7, _response$getFlightDe8, _response$getFlightDe9, _response$getFlightDe0;
    var flightData = (0, _utils.handleCondition)(isFromScanBoardingPass, [Object.assign({}, response == null || (_response$getFlightDe = response.getFlightDetailsForScanBoardingPass) == null ? undefined : _response$getFlightDe.flightInfo, {
      isSaved: response == null || (_response$getFlightDe2 = response.getFlightDetailsForScanBoardingPass) == null || (_response$getFlightDe2 = _response$getFlightDe2.myTravelInfo) == null ? undefined : _response$getFlightDe2.is_saved,
      isPassenger: response == null || (_response$getFlightDe3 = response.getFlightDetailsForScanBoardingPass) == null || (_response$getFlightDe3 = _response$getFlightDe3.myTravelInfo) == null ? undefined : _response$getFlightDe3.is_passenger,
      baggageTracking: response == null || (_response$getFlightDe4 = response.getFlightDetailsForScanBoardingPass) == null || (_response$getFlightDe4 = _response$getFlightDe4.myTravelInfo) == null ? undefined : _response$getFlightDe4.baggage_tracking,
      groundTransport: enableGTTD ? response == null || (_response$getFlightDe5 = response.getFlightDetailsForScanBoardingPass) == null ? undefined : _response$getFlightDe5.groundTransport : null
    })], [Object.assign({}, response == null || (_response$getFlightDe6 = response.getFlightDetails) == null ? undefined : _response$getFlightDe6.flightInfo, {
      isSaved: response == null || (_response$getFlightDe7 = response.getFlightDetails) == null || (_response$getFlightDe7 = _response$getFlightDe7.myTravelInfo) == null ? undefined : _response$getFlightDe7.is_saved,
      isPassenger: response == null || (_response$getFlightDe8 = response.getFlightDetails) == null || (_response$getFlightDe8 = _response$getFlightDe8.myTravelInfo) == null ? undefined : _response$getFlightDe8.is_passenger,
      baggageTracking: response == null || (_response$getFlightDe9 = response.getFlightDetails) == null || (_response$getFlightDe9 = _response$getFlightDe9.myTravelInfo) == null ? undefined : _response$getFlightDe9.baggage_tracking,
      groundTransport: enableGTTD ? response == null || (_response$getFlightDe0 = response.getFlightDetails) == null ? undefined : _response$getFlightDe0.groundTransport : null
    })]);
    return {
      getEarlyCheckin: response == null ? undefined : response.getEarlyCheckin,
      getFlights: {
        next_token: null,
        flights: flightData
      },
      getOnlineCheckin: response == null ? undefined : response.getOnlineCheckin
    };
  };
  var fetchFlightDetails = exports.fetchFlightDetails = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* (_ref) {
      var direction = _ref.direction,
        flightNumber = _ref.flightNumber,
        scheduledDate = _ref.scheduledDate,
        flightStatus = _ref.flightStatus,
        airlineCode = _ref.airlineCode,
        isFromScanBoardingPass = _ref.isFromScanBoardingPass,
        flyCodes = _ref.flyCodes;
      try {
        var _env, _env2, _response$data, _resData$getFlightDet, _resData$getFlightDet2;
        var myTravelUidEnabled = true;
        var enableECIV2 = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ECI_DYNAMIC_DISPLAY);
        var enableGTTD = (0, _remoteConfig.isFlagON)(_remoteConfig.REMOTE_CONFIG_FLAGS.ECI_DYNAMIC_DISPLAY);
        var query = enableECIV2 ? isFromScanBoardingPass ? _queries.getDetailFlightForScanBoardingPassV2 : _queries.getDetailFlightV2 : isFromScanBoardingPass ? _queries.getDetailFlightForScanBoardingPass : _queries.getDetailFlight;
        var variables = {
          direction: direction,
          flightNumber: flightNumber,
          scheduledDate: scheduledDate,
          flightStatus: flightStatus,
          airlineCode: airlineCode,
          myTravelUidEnabled: myTravelUidEnabled,
          includeTravelInfo: true
        };
        var response = yield (0, _request.default)({
          url: (_env = (0, _envParams.env)()) == null ? undefined : _env.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(query, variables),
          headers: {
            "x-api-key": (_env2 = (0, _envParams.env)()) == null ? undefined : _env2.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var resData = !(0, _lodash.isEmpty)(response == null || (_response$data = response.data) == null ? undefined : _response$data.data) ? response.data.data : [];
        if (!isFromScanBoardingPass && resData != null && (_resData$getFlightDet = resData.getFlightDetails) != null && _resData$getFlightDet.flightInfo || isFromScanBoardingPass && resData != null && (_resData$getFlightDet2 = resData.getFlightDetailsForScanBoardingPass) != null && _resData$getFlightDet2.flightInfo) {
          var _formatData$getFlight, _dataHandled$getFligh, _dataHandled$getFligh2;
          var formatData = formatResponseGetDetailFlight(resData, isFromScanBoardingPass, enableGTTD);
          var dataHandled = _fly.FlightDetails.formatResponseFlightDetails(!(0, _lodash.isEmpty)(formatData == null || (_formatData$getFlight = formatData.getFlights) == null ? undefined : _formatData$getFlight.flights) ? formatData.getFlights.flights[0] : {}, flyCodes, formatData == null ? undefined : formatData.getEarlyCheckin, formatData == null ? undefined : formatData.getOnlineCheckin);
          var data = {
            flightDetailsData: _fly.FlightDetails.convertFlightDetails(dataHandled, _flightDetailsCard.FlightDetailsCardType.default),
            heroImageData: _fly.FlightDetails.convertHeroImage(Object.assign({}, dataHandled == null || (_dataHandled$getFligh = dataHandled.getFlightDetail) == null ? undefined : _dataHandled$getFligh.flightMoreDetail, dataHandled == null || (_dataHandled$getFligh2 = dataHandled.getFlightDetail) == null ? undefined : _dataHandled$getFligh2.flightMainInfo), _flightHeroImage.FlightHeroImageType.default, flyCodes),
            errorFlag: false,
            errorPayload: [],
            flightRequestType: _flightProps.FlightRequestType.FlightDefault
          };
          return {
            success: true,
            data: data
          };
        } else {
          var _data = {
            flightDetailsData: _fly.FlightDetails.convertFlightDetails({}, _flightDetailsCard.FlightDetailsCardType.loading),
            heroImageData: _fly.FlightDetails.convertHeroImage({}, _flightHeroImage.FlightHeroImageType.loading),
            errorFlag: true,
            errorPayload: [true, "Error fetching flight details"],
            flightRequestType: _flightProps.FlightRequestType.FlightDefault
          };
          return {
            success: false,
            error: "Error fetching flight details",
            data: _data
          };
        }
      } catch (errors) {
        var extractErrors = _fly.FlightDetails.checkErrors("getFlightDetail", errors);
        var _data2 = {
          flightDetailsData: _fly.FlightDetails.convertFlightDetails({}, _flightDetailsCard.FlightDetailsCardType.loading),
          heroImageData: _fly.FlightDetails.convertHeroImage({}, _flightHeroImage.FlightHeroImageType.loading),
          errorFlag: true,
          errorPayload: extractErrors,
          flightRequestType: _flightProps.FlightRequestType.FlightDefault
        };
        return {
          success: false,
          error: errors.message || "Error fetching flight details",
          data: _data2
        };
      }
    });
    return function fetchFlightDetails(_x) {
      return _ref2.apply(this, arguments);
    };
  }();
  var fetchIntoCityOrAirport = exports.fetchIntoCityOrAirport = /*#__PURE__*/function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* (_ref3) {
      var direction = _ref3.direction,
        flightUniqueId = _ref3.flightUniqueId;
      try {
        var _env3, _env4, _response$data2;
        var response = yield (0, _request.default)({
          url: (_env3 = (0, _envParams.env)()) == null ? undefined : _env3.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getIntoCityOrAirport, {
            direction: direction,
            flightUniqueId: flightUniqueId
          }),
          headers: {
            "x-api-key": (_env4 = (0, _envParams.env)()) == null ? undefined : _env4.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var resData = !(0, _lodash.isEmpty)(response == null || (_response$data2 = response.data) == null || (_response$data2 = _response$data2.data) == null ? undefined : _response$data2.getIntoCityOrAirport) ? response.data.data.getIntoCityOrAirport : [];
        return {
          success: true,
          data: resData
        };
      } catch (errors) {
        return {
          success: false,
          error: errors.message || "Error fetching flight details"
        };
      }
    });
    return function fetchIntoCityOrAirport(_x2) {
      return _ref4.apply(this, arguments);
    };
  }();
  var fetchIntoCityOrAirportV2 = exports.fetchIntoCityOrAirportV2 = /*#__PURE__*/function () {
    var _ref6 = (0, _asyncToGenerator2.default)(function* (_ref5) {
      var direction = _ref5.direction,
        flightUniqueId = _ref5.flightUniqueId;
      try {
        var _env5, _env6, _response$data3;
        var response = yield (0, _request.default)({
          url: (_env5 = (0, _envParams.env)()) == null ? undefined : _env5.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getIntoCityOrAirportV2, {
            direction: direction,
            flightUniqueId: flightUniqueId
          }),
          headers: {
            "x-api-key": (_env6 = (0, _envParams.env)()) == null ? undefined : _env6.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var resData = !(0, _lodash.isEmpty)(response == null || (_response$data3 = response.data) == null || (_response$data3 = _response$data3.data) == null ? undefined : _response$data3.getIntoCityOrAirport_v2) ? response.data.data.getIntoCityOrAirport_v2 : [];
        return {
          success: true,
          data: resData
        };
      } catch (errors) {
        return {
          success: false,
          error: errors.message || "Error fetching flight details"
        };
      }
    });
    return function fetchIntoCityOrAirportV2(_x3) {
      return _ref6.apply(this, arguments);
    };
  }();
  var fetchFlightJourney = exports.fetchFlightJourney = /*#__PURE__*/function () {
    var _ref8 = (0, _asyncToGenerator2.default)(function* (_ref7) {
      var flight_number = _ref7.flight_number,
        scheduled_date = _ref7.scheduled_date,
        direction = _ref7.direction;
      try {
        var _env7, _env8, _response$data4;
        var response = yield (0, _request.default)({
          url: (_env7 = (0, _envParams.env)()) == null ? undefined : _env7.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getFlightJourney, {
            input: {
              flight_number: flight_number,
              scheduled_date: scheduled_date,
              direction: direction
            }
          }),
          headers: {
            "x-api-key": (_env8 = (0, _envParams.env)()) == null ? undefined : _env8.APPSYNC_GRAPHQL_API_KEY
          }
        });
        if (!(0, _lodash.isEmpty)(response == null || (_response$data4 = response.data) == null || (_response$data4 = _response$data4.data) == null || (_response$data4 = _response$data4.getFlightJourney) == null ? undefined : _response$data4.data)) {
          return {
            success: true,
            data: response.data.data.getFlightJourney.data
          };
        }
        return {
          success: false,
          error: "Error fetching flight details"
        };
      } catch (errors) {
        return {
          success: false,
          error: errors.message || "Error fetching flight details"
        };
      }
    });
    return function fetchFlightJourney(_x4) {
      return _ref8.apply(this, arguments);
    };
  }();
  var getFlightFilterOptions = exports.getFlightFilterOptions = /*#__PURE__*/function () {
    var _ref9 = (0, _asyncToGenerator2.default)(function* () {
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_FLIGHT_FILTER_OPTIONS);
      var query = {
        input: {}
      };
      try {
        var _env9, _env0, _response$data5;
        var response = yield (0, _request.default)({
          url: (_env9 = (0, _envParams.env)()) == null ? undefined : _env9.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getFlightFilterOptionsQuery, query),
          parameters: {},
          headers: {
            "x-api-key": (_env0 = (0, _envParams.env)()) == null ? undefined : _env0.APPSYNC_GRAPHQL_API_KEY
          }
        });
        var _getFlightFilterOptions = (response == null || (_response$data5 = response.data) == null || (_response$data5 = _response$data5.data) == null ? undefined : _response$data5.getFlightFilterOptions) || {};
        if (!(0, _lodash.isEmpty)(_getFlightFilterOptions)) {
          dtAction.reportStringValue("status", "success");
          var flightFilterOptions = {
            airline: _getFlightFilterOptions == null ? undefined : _getFlightFilterOptions.airline,
            cityAirport: _getFlightFilterOptions == null ? undefined : _getFlightFilterOptions.city_airport,
            terminal: _getFlightFilterOptions == null ? undefined : _getFlightFilterOptions.terminal
          };
          _store.store.dispatch(_flyRedux.FlyCreators.getFlightFilterOptionsSuccess(flightFilterOptions));
        } else {
          dtAction.reportStringValue("status", "no-data");
          _store.store.dispatch(_flyRedux.FlyCreators.getFlightFilterOptionsFailure());
        }
      } catch (err) {
        dtAction.reportStringValue("status", "failed");
        _store.store.dispatch(_flyRedux.FlyCreators.getFlightFilterOptionsFailure());
      } finally {
        dtAction.leaveAction();
      }
    });
    return function getFlightFilterOptions() {
      return _ref9.apply(this, arguments);
    };
  }();
  var flyMyTravelFlightsRequest = exports.flyMyTravelFlightsRequest = /*#__PURE__*/function () {
    var _ref0 = (0, _asyncToGenerator2.default)(function* () {
      var _store$getState;
      var email = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      var isLoggedIn = yield (0, _authentication.checkLoginState)();
      var username = email || ((_store$getState = _store.store.getState()) == null || (_store$getState = _store$getState.profileReducer) == null || (_store$getState = _store$getState.profilePayload) == null ? undefined : _store$getState.email);
      if (!isLoggedIn || !username) {
        return null;
      }
      var dtAction = (0, _analytics.dtManualActionEvent)(_analytics.DT_ANALYTICS_LOG_EVENT_NAME.DT_GET_SAVED_FLIGHT);
      var myTravelUidEnabled = true;
      var startDate = (0, _moment.default)().format(_dateTime.DateFormats.YearMonthDay);
      var endDate = (0, _moment.default)().add(1, "year").format(_dateTime.DateFormats.YearMonthDay);
      var query = {
        startScheduledDate: startDate,
        endScheduledDate: endDate,
        username: username,
        myTravelUidEnabled: myTravelUidEnabled
      };
      _store.store.dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsPreRequest());
      try {
        var _env1, _env10;
        var response = yield (0, _request.default)({
          url: (_env1 = (0, _envParams.env)()) == null ? undefined : _env1.APPSYNC_GRAPHQL_URL,
          method: "post",
          data: (0, _awsAmplify.graphqlOperation)(_queries.getMyTravelFlightDetails, query),
          parameters: {},
          headers: {
            "x-api-key": (_env10 = (0, _envParams.env)()) == null ? undefined : _env10.APPSYNC_GRAPHQL_API_KEY
          }
        });
        dtAction.reportStringValue("status", "success");
        _store.store.dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsSuccess((0, _flySaga.formatResponseMyTravelFlight)(response == null ? undefined : response.data)));
        return true;
      } catch (err) {
        dtAction.reportStringValue("status", "failed");
        _store.store.dispatch(_mytravelRedux.MytravelCreators.flyMyTravelFlightsFailure(err.message));
        return null;
      } finally {
        dtAction.leaveAction();
      }
    });
    return function flyMyTravelFlightsRequest() {
      return _ref0.apply(this, arguments);
    };
  }();
