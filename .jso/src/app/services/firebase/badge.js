  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.updateBadgeCount = exports.increaseBadgeCount = exports.decreaseBadgeCount = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNativeAppBadge = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var updateBadgeCount = exports.updateBadgeCount = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (number) {
      _reactNativeAppBadge.default.setCount(number);
    });
    return function updateBadgeCount(_x) {
      return _ref.apply(this, arguments);
    };
  }();
  var increaseBadgeCount = exports.increaseBadgeCount = function increaseBadgeCount() {
    _reactNativeAppBadge.default.getCount().then(function (count) {
      _reactNativeAppBadge.default.setCount(count + 1);
    });
  };
  var decreaseBadgeCount = exports.decreaseBadgeCount = function decreaseBadgeCount() {
    _reactNativeAppBadge.default.getCount().then(function (count) {
      _reactNativeAppBadge.default.setCount(count - 1);
    });
  };
