  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  var _messaging = _$$_REQUIRE(_dependencyMap[0]);
  Object.keys(_messaging).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _messaging[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _messaging[key];
      }
    });
  });
  var _badge = _$$_REQUIRE(_dependencyMap[1]);
  Object.keys(_badge).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _badge[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _badge[key];
      }
    });
  });
  var _analytics = _$$_REQUIRE(_dependencyMap[2]);
  Object.keys(_analytics).forEach(function (key) {
    if (key === "default" || key === "__esModule") return;
    if (key in exports && exports[key] === _analytics[key]) return;
    Object.defineProperty(exports, key, {
      enumerable: true,
      get: function get() {
        return _analytics[key];
      }
    });
  });
