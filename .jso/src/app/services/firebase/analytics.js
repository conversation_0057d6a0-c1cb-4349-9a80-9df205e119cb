  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.dtManualActionEvent = exports.dtLeaveActionEvent = exports.dtBizEvent = exports.dtActionEvent = exports.dtACtionLogEvent = exports.convertStringValue = exports.convertBooleanValue = exports.analyticsLogEvent = exports.SECURITY_EVENT_KEYS = exports.FE_LOG_PREFIX = exports.DT_SECURITY_EVENT_NAME = exports.DT_SECURITY_EVENT = exports.DT_ANALYTICS_LOG_EVENT_NAME = exports.ANALYTICS_LOG_EVENT_NAME = exports.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME = undefined;
  var _reactNativePlugin = _$$_REQUIRE(_dependencyMap[1]);
  var _analytics = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _reactNativeDeviceInfo = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[4]);
  var _envSettings = _$$_REQUIRE(_dependencyMap[5]);
  var _Settings$ENV;
  var Settings = (0, _envSettings.getEnvSetting)();
  var defaultEventType = `ChangiApp-${Settings == null || (_Settings$ENV = Settings.ENV) == null ? undefined : _Settings$ENV.toUpperCase()}-${_reactNative.Platform.OS.toUpperCase()}`;
  var deviceId = _reactNativeDeviceInfo.default.getUniqueIdSync();
  var deviceName = _reactNativeDeviceInfo.default.getDeviceNameSync();
  var ANALYTICS_LOG_EVENT_NAME = exports.ANALYTICS_LOG_EVENT_NAME = /*#__PURE__*/function (ANALYTICS_LOG_EVENT_NAME) {
    ANALYTICS_LOG_EVENT_NAME["LANDING"] = "landing";
    ANALYTICS_LOG_EVENT_NAME["FLIGHT_LANDING"] = "flight_landing";
    ANALYTICS_LOG_EVENT_NAME["FLIGHT_DEP_LIST"] = "flight_dep_list";
    ANALYTICS_LOG_EVENT_NAME["FLIGHT_ARR_LIST"] = "flight_arr_list";
    ANALYTICS_LOG_EVENT_NAME["SAVED_FLIGHT"] = "saved_flight";
    ANALYTICS_LOG_EVENT_NAME["CHANGI_PAY_LANDING"] = "changi_pay_landing";
    ANALYTICS_LOG_EVENT_NAME["PLAYPASS_LANDING"] = "playpass_landing";
    ANALYTICS_LOG_EVENT_NAME["PBE_LANDING"] = "pbe_landing";
    ANALYTICS_LOG_EVENT_NAME["APPSCAPADE_LANDING"] = "appscapade_landing";
    ANALYTICS_LOG_EVENT_NAME["SAVEFLIGHT_DEPARTURE"] = "saveflight_departure";
    ANALYTICS_LOG_EVENT_NAME["SAVEFLIGHT_ARRIVAL"] = "saveflight_arrival";
    ANALYTICS_LOG_EVENT_NAME["STAFF_PERKS_LISTING_TITLE_CLICKING"] = "staff_perks_listing_tile_clicking";
    ANALYTICS_LOG_EVENT_NAME["STAFF_PERKS_LISTING_TITLE_OLD"] = "staff_perks_listing_tile_old";
    ANALYTICS_LOG_EVENT_NAME["STAFF_PERKS_LISTING_TITLE_NEW"] = "staff_perks_listing_tile_new";
    return ANALYTICS_LOG_EVENT_NAME;
  }({});
  var ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME = exports.ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME = /*#__PURE__*/function (ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME) {
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["FACILITIES_SERVICES"] = "facilities_services";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["FACILITIES_SERVICES_VIEW_ALL"] = "fs_view_all";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["FLIGHT_INFO_DETAILS"] = "flight_info_details";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["GETTING_AROUND_CHANGI_AIRPORT"] = "getting_around_changi";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["GETTING_TO_CHANGI_AIRPORT"] = "getting_to_changi";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["SHOP_TAB"] = "shop_tab";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["DINE_TAB"] = "dine_tab";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["FACILITIES_TAB"] = "facilities_tab";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["USER_PROFILE_SELECTOR"] = "profile_selector";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["SAVE_FLIGHT_BUTTON"] = "save_button";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["UNSAVE_FLIGHT_BUTTON"] = "unsave_button";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["ONLINE_CHECK_IN"] = "flight_check_in";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["EARLY_CHECK_IN"] = "flight_check_in";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["SHARE_FLIGHT_BUTTON"] = "share_button";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["ATOMS"] = "atoms";
    ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME["TRACK_BAGGAGE"] = "track_baggage";
    return ANALYTICS_FLIGHT_DETAIL_LOG_EVENT_NAME;
  }({});
  var DT_ANALYTICS_LOG_EVENT_NAME = exports.DT_ANALYTICS_LOG_EVENT_NAME = /*#__PURE__*/function (DT_ANALYTICS_LOG_EVENT_NAME) {
    DT_ANALYTICS_LOG_EVENT_NAME["DT_FLY_SCREEN_FOCUS"] = "dt_fly_screen_focus";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_EXPLORE_DATA"] = "dt_get_explore_data";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_FLIGHT_LANDING_ARRIVAL"] = "dt_flight_landing_arrival";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_FLIGHT_LANDING_DEPARTURE"] = "dt_flight_landing_departure";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_LATEST_HAPPENING"] = "dt_get_latest_happening";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_EXPLORE_SHORTCUT_LINK"] = "dt_get_explore_shortcut_link";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_JUST_FOR_YOU"] = "dt_get_just_for_you";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_FLY_TICKER_BAND"] = "dt_get_fly_ticker_band";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_PAGE_CONFIG"] = "dt_get_page_config";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_FLIGHT_FILTER_OPTIONS"] = "dt_get_flight_filter_options";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_NOTIFICATION_COUNT"] = "dt_get_notification_count";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_SAVED_FLIGHT"] = "dt_get_saved_flight";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_CITY_CODES"] = "dt_get_city_codes";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_CONFIGURATIONS"] = "dt_get_configurations";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_RECOMMENDED_PRODUCTS"] = "dt_get_recommended_products";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_PUT_USER_DEVICE_TOKEN"] = "dt_put_user_device_token";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_USER_NOTIFICATIONS"] = "dt_get_user_notifications";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_REWARD_DETAIL"] = "dt_get_reward_detail";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_PLAYPASS_BOOKINGS"] = "dt_get_playpass_bookings";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_PENDING_PLAYPASS_BOOKING"] = "dt_get_pending_playpass_bookings";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_POST_VALIDATE_TOKEN"] = "dt_post_validate_token";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_UPCOMING_EVENT"] = "dt_get_upcoming_event";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_UPCOMING_FLIGHTS"] = "dt_get_upcoming_flights";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_ACCOUNT_PROFILE"] = "dt_get_account_profile";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_CONSOLIDATED_STRUCTURES"] = "dt_get_consolidated_Structures";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_CONSOLIDATED_AEM_GROUP_ONE"] = "dt_get_consolidated_aem_group_one";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_GET_CONSOLIDATED_AEM_GROUP_TWO"] = "dt_get_consolidated_aem_group_two";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_POST_REGISTER"] = "dt_post_register";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_POST_UPDATE_LOCATION_PREFERENCE"] = "dt_post_update_location_preference";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_AEM_GET_FORCED_UPDATE"] = "dt_aem_get_forced_update";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_AEM_GET_ATTRACTIONS_FILTER"] = "dt_aem_get_attractions_filter";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_AEM_GET_ACTIVE_TOOLTIP"] = "dt_aem_get_active_tooltip";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_RETRO_CLAIM_GET_CONFIGS"] = "dt_retro_claim_get_configs";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_RETRO_CLAIM_SUBMIT_IMAGE"] = "dt_retro_claim_submit_image";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_RETRO_CLAIM_TAKE_PHOTO"] = "dt_retro_claim_take_photo";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_RETRO_CLAIM_OPEN_GALLERY"] = "dt_retro_claim_open_gallery";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_BAGGAGE_TRACKER_GET_CONFIGS"] = "dt_baggage_tracker_get_configs";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_BAGGAGE_TRACKER_TAKE_PHOTO"] = "dt_baggage_tracker_take_photo";
    DT_ANALYTICS_LOG_EVENT_NAME["DT_BAGGAGE_TRACKER_OPEN_GALLERY"] = "dt_baggage_tracker_open_gallery";
    return DT_ANALYTICS_LOG_EVENT_NAME;
  }({});
  var SECURITY_EVENT_KEYS = exports.SECURITY_EVENT_KEYS = /*#__PURE__*/function (SECURITY_EVENT_KEYS) {
    SECURITY_EVENT_KEYS["STATUS"] = "event.status";
    SECURITY_EVENT_KEYS["ERROR_CODE"] = "event.errorCode";
    SECURITY_EVENT_KEYS["LAST_LOGGED_IN_EMAIL"] = "lastLoggedInemail";
    SECURITY_EVENT_KEYS["LAST_LOGGED_IN_UID"] = "lastLoggedInUid";
    SECURITY_EVENT_KEYS["LAST_LOGGED_IN_TIMESTAMP"] = "lastLoggedIntimestamp";
    SECURITY_EVENT_KEYS["UID"] = "currentUid";
    SECURITY_EVENT_KEYS["CURRENT_EMAIL"] = "currentEmail";
    SECURITY_EVENT_KEYS["DETAIL"] = "detail";
    return SECURITY_EVENT_KEYS;
  }({});
  var DT_SECURITY_EVENT_NAME = exports.DT_SECURITY_EVENT_NAME = /*#__PURE__*/function (DT_SECURITY_EVENT_NAME) {
    DT_SECURITY_EVENT_NAME["DT_EMULATOR_DETECTED_FOR_EMAIL_LOGIN_SUCCESS"] = "dt_emulator_detected_for_email_login_success";
    DT_SECURITY_EVENT_NAME["DT_EMULATOR_DETECTED_FOR_EMAIL_SIGNUP_EMAIL_VERIFICATION"] = "dt_emulator_detected_for_email_signup_email_verification";
    DT_SECURITY_EVENT_NAME["DT_SIGN_UP_ACCOUNT_AND_PROFILE_CREATED"] = "dt_sign_up_account_and_profile_created";
    DT_SECURITY_EVENT_NAME["DT_ROOTED_DEVICE_DETECTED"] = "rooted_device_detected";
    DT_SECURITY_EVENT_NAME["DT_EMAIL_LOGIN_SUCCESS"] = "dt_email_login_success";
    return DT_SECURITY_EVENT_NAME;
  }({});
  var DT_SECURITY_EVENT = exports.DT_SECURITY_EVENT = "dt_security_event";
  var FE_LOG_PREFIX = exports.FE_LOG_PREFIX = "FElog__";
  var convertStringValue = exports.convertStringValue = function convertStringValue(value) {
    if (value) {
      return value == null ? undefined : value.toString().replace(/[^a-zA-Z0-9]/g, '_');
    } else if (value === 0) {
      return "0";
    }
    return "";
  };
  var convertBooleanValue = exports.convertBooleanValue = function convertBooleanValue(currentNeedToRefreshStatus) {
    if (typeof currentNeedToRefreshStatus !== "boolean") {
      return "null";
    } else {
      if (currentNeedToRefreshStatus) {
        return "true";
      } else {
        return "false";
      }
    }
  };
  var analyticsLogEvent = exports.analyticsLogEvent = function analyticsLogEvent(name) {
    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    if (params) {
      return (0, _analytics.default)().logEvent(name, params);
    }
    return (0, _analytics.default)().logEvent(name);
  };
  var dtACtionLogEvent = exports.dtACtionLogEvent = function dtACtionLogEvent(name) {
    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    if (params) {
      var myAction = _reactNativePlugin.Dynatrace.enterAutoAction(name);
      for (var key in params) {
        if (params.hasOwnProperty(key)) {
          myAction.reportStringValue(key, params[key]);
        }
      }
      myAction.leaveAction();
    }
    if (name) {
      var _myAction = _reactNativePlugin.Dynatrace.enterAutoAction(name);
      _myAction.leaveAction();
    }
  };
  var dtActionEvent = exports.dtActionEvent = function dtActionEvent(name) {
    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    // ìf using this function, you should use leaveAction to cancel it
    var myAction = _reactNativePlugin.Dynatrace.enterAutoAction(name);
    if (params) {
      for (var key in params) {
        if (params.hasOwnProperty(key)) {
          myAction.reportStringValue(key, params[key]);
        }
      }
    }
    return myAction;
  };
  var dtManualActionEvent = exports.dtManualActionEvent = function dtManualActionEvent(name) {
    var action = _reactNativePlugin.Dynatrace.enterManualAction(name);
    action.reportStringValue("device_name", convertStringValue(deviceName));
    action.reportStringValue("device_id", deviceId);
    return action;
  };
  var dtLeaveActionEvent = exports.dtLeaveActionEvent = function dtLeaveActionEvent(myAction) {
    myAction == null || myAction.leaveAction();
  };
  var dtBizEvent = exports.dtBizEvent = function dtBizEvent(event_provider, event_name, event_category) {
    var event_details = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;
    _reactNativePlugin.Dynatrace.sendBizEvent(defaultEventType, Object.assign({
      "event.category": event_category,
      "event.name": event_name,
      "screen": event_provider,
      // "uid": uid,
      "device.name": deviceName,
      "device.id": deviceId
    }, event_details));
  };
