  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.rmFetchAndActive = exports.remoteConfigGetValue = exports.remoteConfigGetAllValue = exports.isFlagOnCondition = exports.isFlagON = exports.getFeatureFlagInit = exports.REMOTE_FLAG_VALUE = exports.REMOTE_CONFIG_FLAGS = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _slicedToArray2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[2]));
  var _defineProperty2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _remoteConfig = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  // the caching time - 5 minutes - milliseconds
  var FREQUENCY = 300000;

  // connection timeout  - 5 seconds - milliseconds
  var TIME_OUT = 5000;
  var REMOTE_FLAG_VALUE = exports.REMOTE_FLAG_VALUE = /*#__PURE__*/function (REMOTE_FLAG_VALUE) {
    REMOTE_FLAG_VALUE["ON"] = "ON";
    REMOTE_FLAG_VALUE["OFF"] = "OFF";
    return REMOTE_FLAG_VALUE;
  }({});
  var REMOTE_CONFIG_FLAGS = exports.REMOTE_CONFIG_FLAGS = /*#__PURE__*/function (REMOTE_CONFIG_FLAGS) {
    REMOTE_CONFIG_FLAGS["APPSCAPADE_FLIGHTDETAILS"] = "APPSCAPADE_FLIGHTDETAILS";
    REMOTE_CONFIG_FLAGS["APPSCAPADE_FLIGHTLANDING"] = "APPSCAPADE_FLIGHTLANDING";
    REMOTE_CONFIG_FLAGS["ATOMS_MAP"] = "ATOMS_MAP";
    REMOTE_CONFIG_FLAGS["EXPLORE_SCROLLBUDDY"] = "EXPLORE_SCROLLBUDDY";
    REMOTE_CONFIG_FLAGS["COLLECT_CLICKSTREAM"] = "COLLECT_CLICKSTREAM";
    REMOTE_CONFIG_FLAGS["EXPLORE_JUSTFORYOU"] = "EXPLORE_JUSTFORYOU";
    REMOTE_CONFIG_FLAGS["EXPLORE_STAFFPERKS"] = "EXPLORE_STAFFPERKS";
    REMOTE_CONFIG_FLAGS["GROUND_TRANSPORT_TIMINGS_DISPLAY"] = "GROUND_TRANSPORT_TIMINGS_DISPLAY";
    REMOTE_CONFIG_FLAGS["SHOP_JUSTFORYOU"] = "SHOP_JUSTFORYOU";
    REMOTE_CONFIG_FLAGS["TENANTDETAILS_STAFFPERKS"] = "TENANTDETAILS_STAFFPERKS";
    REMOTE_CONFIG_FLAGS["ECI_DYNAMIC_DISPLAY"] = "ECI_DYNAMIC_DISPLAY";
    REMOTE_CONFIG_FLAGS["BAGGAGE_PREDICTION_TIMING"] = "BAGGAGE_PREDICTION_TIMING";
    REMOTE_CONFIG_FLAGS["CSM_CM24"] = "CSM_CM24";
    REMOTE_CONFIG_FLAGS["ACCOUNT_V2_CM24"] = "ACCOUNT_V2_CM24";
    REMOTE_CONFIG_FLAGS["APPRATING_SAVEFLIGHT"] = "APPRATING_SAVEFLIGHT";
    REMOTE_CONFIG_FLAGS["CIAM_UNLINKSOCIAL"] = "CIAM_UNLINKSOCIAL";
    REMOTE_CONFIG_FLAGS["TXNHISTORY_RETROCLAIMS"] = "TXNHISTORY_RETROCLAIMS";
    REMOTE_CONFIG_FLAGS["TXNHISTORY_PENDINGCLAIMS"] = "TXNHISTORY_PENDINGCLAIMS";
    REMOTE_CONFIG_FLAGS["L2_ANNOUNCEMENT"] = "L2_ANNOUNCEMENT";
    REMOTE_CONFIG_FLAGS["NOTIFICATION_INBOX_V2"] = "NOTIFICATION_INBOX_V2";
    REMOTE_CONFIG_FLAGS["STAFFPERKS_TILESV2"] = "STAFFPERKS_TILESV2";
    REMOTE_CONFIG_FLAGS["CIAM_SOCIALLINKING"] = "CIAM_SOCIALLINKING";
    REMOTE_CONFIG_FLAGS["ACCOUNT_CONTACTUS"] = "ACCOUNT_CONTACTUS";
    REMOTE_CONFIG_FLAGS["VCEA_LIVECHAT"] = "VCEA_LIVECHAT";
    REMOTE_CONFIG_FLAGS["NOTIFICATION_PREFERENCES_V2"] = "NOTIFICATION_PREFERENCES_V2";
    REMOTE_CONFIG_FLAGS["CSM_GROUPBUY"] = "CSM_GROUPBUY";
    REMOTE_CONFIG_FLAGS["CIAM_BIOMETRICS"] = "CIAM_BIOMETRICS";
    REMOTE_CONFIG_FLAGS["L1_ANNOUNCEMENT"] = "L1_ANNOUNCEMENT";
    REMOTE_CONFIG_FLAGS["FLIGHT_DETAILS_P1"] = "FLIGHT_DETAILS_P1";
    REMOTE_CONFIG_FLAGS["ATOMS_ROUTING"] = "ATOMS_ROUTING";
    REMOTE_CONFIG_FLAGS["SHOPDINE_EPIC"] = "SHOPDINE_EPIC";
    REMOTE_CONFIG_FLAGS["SEARCHV2_EPIC"] = "SEARCHV2_EPIC";
    REMOTE_CONFIG_FLAGS["PROJECTFIRST_FLIGHTDETAILS"] = "PROJECTFIRST_FLIGHTDETAILS";
    REMOTE_CONFIG_FLAGS["MIFFYGAME_VPR"] = "MIFFYGAME_VPR";
    REMOTE_CONFIG_FLAGS["PROJECTFIRST_FLIGHTJOURNEY"] = "PROJECTFIRST_FLIGHTJOURNEY";
    REMOTE_CONFIG_FLAGS["MIFFYGAME_ACCOUNT"] = "MIFFYGAME_ACCOUNT";
    REMOTE_CONFIG_FLAGS["DRIVE_PARKING"] = "DRIVE_PARKING";
    REMOTE_CONFIG_FLAGS["CSM_CARPARSS"] = "CSM_CARPASS";
    REMOTE_CONFIG_FLAGS["NATIVE_ONBOARDING_SCREEN"] = "NATIVE_ONBOARDING_SCREEN";
    REMOTE_CONFIG_FLAGS["ACCOUNT_PROMOCODES"] = "ACCOUNT_PROMOCODES";
    REMOTE_CONFIG_FLAGS["DRIVE_CSAT"] = "DRIVE_CSAT";
    REMOTE_CONFIG_FLAGS["FLIGHT_SAVEPROMPT"] = "FLIGHT_SAVEPROMPT";
    REMOTE_CONFIG_FLAGS["SHOPDINE_V2"] = "SHOPDINE_V2";
    REMOTE_CONFIG_FLAGS["SHOPDINE_V2_JUSTFORYOU"] = "SHOPDINE_V2_JUSTFORYOU";
    REMOTE_CONFIG_FLAGS["FLY_LANDING"] = "FLY_LANDING";
    REMOTE_CONFIG_FLAGS["EXPLORE_V2"] = "EXPLORE_V2";
    return REMOTE_CONFIG_FLAGS;
  }({});
  var DEFAULT_VALUE = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, REMOTE_CONFIG_FLAGS.EXPLORE_SCROLLBUDDY, ""), REMOTE_CONFIG_FLAGS.ATOMS_MAP, ""), REMOTE_CONFIG_FLAGS.SHOP_JUSTFORYOU, ""), REMOTE_CONFIG_FLAGS.EXPLORE_JUSTFORYOU, ""), REMOTE_CONFIG_FLAGS.COLLECT_CLICKSTREAM, "");
  (0, _remoteConfig.default)().setConfigSettings({
    minimumFetchIntervalMillis: FREQUENCY,
    fetchTimeMillis: TIME_OUT
  });
  (0, _remoteConfig.default)().setDefaults(DEFAULT_VALUE);
  var remoteConfigGetAllValue = exports.remoteConfigGetAllValue = function remoteConfigGetAllValue() {
    // if the system don't `fetch` data, the cache will always be there 
    var newObj = {};
    var parameters = (0, _remoteConfig.default)().getAll();
    Object.entries(parameters).forEach(function (item) {
      var _item = (0, _slicedToArray2.default)(item, 2),
        key = _item[0],
        entry = _item[1];
      newObj[key] = {
        value: entry.asString(),
        source: entry.getSource()
      };
    });
    return newObj;
  };
  var remoteConfigGetValue = exports.remoteConfigGetValue = function remoteConfigGetValue(key) {
    // if the system don't `fetch` data, the cache will always be there
    return (0, _remoteConfig.default)().getValue(key).asString();
  };
  var rmFetchAndActive = exports.rmFetchAndActive = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      try {
        // fetchedRemotely - true - Configs were retrieved from the backend and activated
        // fetchedRemotely - false - No configs were fetched from the backend, and the local configs were already activated
        var fetchedRemotely = yield (0, _remoteConfig.default)().fetchAndActivate();
        var parameters = (0, _remoteConfig.default)().getAll();
        var remoteAllData = {};
        Object.entries(parameters).forEach(function (item) {
          var _item2 = (0, _slicedToArray2.default)(item, 2),
            key = _item2[0],
            entry = _item2[1];
          remoteAllData[key] = {
            value: entry.asString(),
            source: entry.getSource()
          };
        });
        return {
          status: true,
          fetchedRemotely: fetchedRemotely,
          remoteData: remoteAllData
        };
      } catch (error) {
        return {
          status: false,
          fetchedRemotely: null,
          remoteData: null
        };
      }
    });
    return function rmFetchAndActive() {
      return _ref.apply(this, arguments);
    };
  }();
  var isFlagON = exports.isFlagON = function isFlagON(flagName) {
    var getRemoteConfig = remoteConfigGetValue(flagName);
    return getRemoteConfig && typeof getRemoteConfig === "string" && getRemoteConfig.trim() === REMOTE_FLAG_VALUE.ON;
  };
  var isFlagOnCondition = exports.isFlagOnCondition = function isFlagOnCondition(flag) {
    return flag && typeof flag === "string" && flag.trim() === REMOTE_FLAG_VALUE.ON;
  };
  var getFeatureFlagInit = exports.getFeatureFlagInit = function getFeatureFlagInit(flagName) {
    var value = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    try {
      // #1 from context
      if (value !== null) {
        return Boolean(isFlagOnCondition(value));
      }
      // #2 get from local storage or remote
      var result = Boolean(isFlagON(flagName));
      return result;
    } catch (error) {
      return false; // Default fallback value
    }
  };
