  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.requestUserPermission = exports.onMessageReceivedForeground = exports.onForegroundEventLocalNotification = exports.onBackgroundEventLocalNotification = exports.notificationQuiteState = exports.notificationBackgroundState = exports.handleMessageForegroundState = exports.handleMessageBackgroundAndQuitState = exports.getInitialLocalNotification = exports.getDeviceToken = exports.NOTIFICATION_CHANEL = exports.NOTIFEE_EVENT_TYPE = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _messaging = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var getDeviceToken = exports.getDeviceToken = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* () {
      yield (0, _messaging.default)().registerDeviceForRemoteMessages();
      var token = yield (0, _messaging.default)().getToken();
      return token;
    });
    return function getDeviceToken() {
      return _ref.apply(this, arguments);
    };
  }();
  var NOTIFEE_EVENT_TYPE = exports.NOTIFEE_EVENT_TYPE = _reactNative.EventType;
  var NOTIFICATION_CHANEL = exports.NOTIFICATION_CHANEL = /*#__PURE__*/function (NOTIFICATION_CHANEL) {
    NOTIFICATION_CHANEL["FOREGROUND"] = "foreground";
    NOTIFICATION_CHANEL["FOREGROUND_CLICKED"] = "foreground_clicked";
    return NOTIFICATION_CHANEL;
  }({});
  var requestUserPermission = exports.requestUserPermission = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      // FOR IOS ONLY
      var authStatus = yield (0, _messaging.default)().requestPermission();
      var enabled = authStatus === _messaging.default.AuthorizationStatus.AUTHORIZED || authStatus === _messaging.default.AuthorizationStatus.PROVISIONAL;
      if (enabled) {
        // console.log("Authorization status:", authStatus)
      }
      return {
        enabled: enabled,
        authStatus: authStatus
      };
    });
    return function requestUserPermission() {
      return _ref2.apply(this, arguments);
    };
  }();
  var handleMessageForegroundState = exports.handleMessageForegroundState = function handleMessageForegroundState(callback) {
    return (0, _messaging.default)().onMessage(callback);
  };
  var handleMessageBackgroundAndQuitState = exports.handleMessageBackgroundAndQuitState = function handleMessageBackgroundAndQuitState(callback) {
    return (0, _messaging.default)().setBackgroundMessageHandler(callback);
  };
  var notificationBackgroundState = exports.notificationBackgroundState = function notificationBackgroundState(callback) {
    return (0, _messaging.default)().onNotificationOpenedApp(callback);
  };
  var notificationQuiteState = exports.notificationQuiteState = function notificationQuiteState(callback) {
    return (0, _messaging.default)().getInitialNotification().then(callback);
  };
  var onForegroundEventLocalNotification = exports.onForegroundEventLocalNotification = function onForegroundEventLocalNotification(callback) {
    return _reactNative.default.onForegroundEvent(callback);
  };
  var onBackgroundEventLocalNotification = exports.onBackgroundEventLocalNotification = function onBackgroundEventLocalNotification(callback) {
    return _reactNative.default.onBackgroundEvent(callback);
  };

  // Check Data from notification that it has caused your app to open from local notification
  var getInitialLocalNotification = exports.getInitialLocalNotification = /*#__PURE__*/function () {
    var _ref3 = (0, _asyncToGenerator2.default)(function* () {
      var _initialLocalNotifica;
      var initialLocalNotification = yield _reactNative.default.getInitialNotification();
      if (initialLocalNotification != null && (_initialLocalNotifica = initialLocalNotification.notification) != null && (_initialLocalNotifica = _initialLocalNotifica.data) != null && _initialLocalNotifica.ab) {
        return null;
      }
      return initialLocalNotification;
    });
    return function getInitialLocalNotification() {
      return _ref3.apply(this, arguments);
    };
  }();
  var onMessageReceivedForeground = exports.onMessageReceivedForeground = /*#__PURE__*/function () {
    var _ref4 = (0, _asyncToGenerator2.default)(function* (message) {
      // console.log("onMessageReceivedForeground", message)
      var channelId = yield _reactNative.default.createChannel({
        id: NOTIFICATION_CHANEL.FOREGROUND,
        name: "Foreground Channel"
      });
      if (message.data && message.notification) {
        var _message$notification, _message$notification2;
        yield _reactNative.default.displayNotification({
          id: message == null ? undefined : message.messageId,
          title: message == null || (_message$notification = message.notification) == null ? undefined : _message$notification.title,
          body: message == null || (_message$notification2 = message.notification) == null ? undefined : _message$notification2.body,
          data: message == null ? undefined : message.data,
          android: {
            channelId: channelId,
            smallIcon: "ic_launcher_round",
            pressAction: {
              id: NOTIFICATION_CHANEL.FOREGROUND_CLICKED,
              launchActivity: "default"
            },
            circularLargeIcon: true
          }
        });
      }
    });
    return function onMessageReceivedForeground(_x) {
      return _ref4.apply(this, arguments);
    };
  }();
