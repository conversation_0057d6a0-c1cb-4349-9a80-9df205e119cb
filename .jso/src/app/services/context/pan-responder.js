  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.PanResponderContext = undefined;
  var _react = _$$_REQUIRE(_dependencyMap[0]);
  var defaultContextValue = {
    handleSetCapturePanResponseRef: null,
    idleTimeRef: null,
    conditionTimeRef: null,
    isPageLoadingRef: null,
    triggerShowAppRatingRef: null,
    currentForceUpdateFlow: null,
    forceUpdateHandling: null,
    isPendingCpayUpdateCheck: null,
    setPendingCpayUpdate: null
  };
  var PanResponderContext = exports.PanResponderContext = (0, _react.createContext)(defaultContextValue);
