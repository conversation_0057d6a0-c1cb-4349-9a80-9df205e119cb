  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.convertTimeFrom24to12Hrs = exports.convertGMTToSingapore = exports.convertDateTimeToTimeStampSingapore = exports.convertDateTimeToSingapore = exports.TimeFormats = exports.Locales = exports.DateVariations = exports.DateFormats = undefined;
  exports.convertTimestampToDate = convertTimestampToDate;
  exports.dateRangeFormatting = dateRangeFormatting;
  exports.dateToFromNow = dateToFromNow;
  exports.dayIdentifier = dayIdentifier;
  exports.eventCardDateFormatting = undefined;
  exports.flyGetRetimedDate = flyGetRetimedDate;
  exports.flyModuleDateFormatting = flyModuleDateFormatting;
  exports.flyModuleUpdatedTime = flyModuleUpdatedTime;
  exports.getWeekDay = exports.getScheduledTime = exports.getDateSingapore = exports.getCurrentTimeStampSingapore = exports.getCurrentTimeSingapore = exports.getCurrentDateSingapore = exports.getCurrentDate = exports.formatMonthYear = exports.formatCampaignDateRange = undefined;
  exports.toDate = toDate;
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var MomentTimeZone = _interopRequireWildcard(_$$_REQUIRE(_dependencyMap[2]));
  var _ = _$$_REQUIRE(_dependencyMap[3]);
  var _i18n = _$$_REQUIRE(_dependencyMap[4]);
  function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) "default" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }
  var DateVariations = exports.DateVariations = /*#__PURE__*/function (DateVariations) {
    DateVariations["specificDate"] = "specificDate";
    DateVariations["dateRange"] = "dateRange";
    DateVariations["eventDate"] = "eventDate";
    return DateVariations;
  }({});
  var TimeFormats = exports.TimeFormats = /*#__PURE__*/function (TimeFormats) {
    TimeFormats["24Hours"] = "HH:mm";
    TimeFormats["12Hours"] = "hh:mm A";
    return TimeFormats;
  }({});
  var Locales = exports.Locales = /*#__PURE__*/function (Locales) {
    Locales["en"] = "en";
    Locales["zh"] = "zh-cn";
    return Locales;
  }({});
  var DateFormats = exports.DateFormats = /*#__PURE__*/function (DateFormats) {
    DateFormats["MMMyyy"] = "MMM yyyy";
    DateFormats["DayDateMonth"] = "ddd, DD MMM";
    DateFormats["DayDateMonthYear"] = "ddd, DD MMM YYYY";
    DateFormats["YearMonthDay"] = "YYYY-MM-DD";
    DateFormats["DayMonthYear"] = "DD MMM YYYY";
    DateFormats["DateWithDayMonthYear"] = "ddd DD MMMM YYYY";
    DateFormats["DayMonthYearWithSlash"] = "DD/MM/YYYY";
    DateFormats["MonthYear"] = "MMMM YYYY";
    DateFormats["DateTime"] = "YYYY-MM-DD[T]HH:mm:ss.SSSSS";
    DateFormats["flyModuleUpdatedTime"] = "HH:mm, DD MMM yyyy";
    DateFormats["Year"] = "YYYY";
    DateFormats["DayOfWeek"] = "dddd";
    DateFormats["DayMonth"] = "(DD MMM)";
    DateFormats["DateMonth"] = "DD MMM";
    DateFormats["YearMonthDayTime"] = "YYYY-MM-DD HH:mm";
    DateFormats["HoursMinutesSeconds"] = "HH:mm:ss.SSSSS";
    DateFormats["HoursMinutes"] = "HH:mm";
    DateFormats["DateTimeSeconds"] = "YYYY-MM-DD HH:mm:ss";
    DateFormats["CalendarEventUTC"] = "YYYY-MM-DDTHH:mm:ss.SSS[Z]";
    return DateFormats;
  }({});
  function convertTimestampToDate(inputDate) {
    var dateVariation = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DateVariations.specificDate;
    var timeFormat = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : TimeFormats["24Hours"];
    var isOnlyDateRequired = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
    var isOnlyTimeRequired = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;
    var dateFormat = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : DateFormats.MMMyyy;
    var locale = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : Locales.en;
    inputDate = (0, _moment.default)(inputDate).utc();
    var today = (0, _moment.default)().format("YYYY-MM-DD");
    var tomorrow = (0, _moment.default)().add(1, "day").toISOString();
    var time = (0, _moment.default)(inputDate).locale(locale).utc().format(timeFormat);
    var date = (0, _moment.default)(inputDate).locale(locale).format(dateFormat);
    if (isOnlyTimeRequired) {
      return time;
    }
    if (isOnlyDateRequired) {
      return date;
    }
    if (dateVariation === DateVariations.eventDate) {
      if ((0, _moment.default)(today).isSame((0, _moment.default)(inputDate).format(DateFormats.YearMonthDay), "day")) {
        return `${(0, _i18n.translate)("upcomingEvent.today")} · ${time}`;
      } else if ((0, _moment.default)(tomorrow).isSame((0, _moment.default)(inputDate).format(DateFormats.YearMonthDay), "day")) {
        return `${(0, _i18n.translate)("upcomingEvent.tomorrow")} · ${time}`;
      } else {
        return `${date} · ${time}`;
      }
    }
    return false;
  }
  function toDate(inputDate) {
    var dateFormat = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DateFormats.DayDateMonthYear;
    var locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Locales.en;
    inputDate = (0, _moment.default)(inputDate);
    var date = (0, _moment.default)(inputDate).locale(locale).format(dateFormat);
    return date;
  }
  var formatMonthYear = exports.formatMonthYear = function formatMonthYear(inputDate) {
    var dateFormat = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DateFormats.MonthYear;
    var locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Locales.en;
    var date = (0, _moment.default)(inputDate, DateFormats.DayMonthYear, locale).format(dateFormat);
    return date;
  };
  function dateRangeFormatting(startDate, endDate) {
    var locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Locales.en;
    startDate = (0, _moment.default)(startDate).utc();
    endDate = (0, _moment.default)(endDate).utc();
    var today = (0, _moment.default)().utc().format("YYYY-MM-DD");
    var tomorrow = (0, _moment.default)().utc().add(1, "day").toISOString();
    var isStartDateToday = (0, _moment.default)(today).isSame(startDate.format(DateFormats.YearMonthDay), "day");
    var isStartDateTomorrow = (0, _moment.default)(tomorrow).isSame(startDate.format(DateFormats.YearMonthDay), "day");
    var isEndDateToday = (0, _moment.default)(today).isSame(endDate.format(DateFormats.YearMonthDay), "day");
    var isEndDateTomorrow = (0, _moment.default)(tomorrow).isSame(endDate.format(DateFormats.YearMonthDay), "day");
    var isStartDateValid = (0, _moment.default)(startDate).isValid();
    var isEndDateValid = (0, _moment.default)(endDate).isValid();
    if (!isStartDateValid) return "";
    if (isStartDateToday && !isEndDateValid || isStartDateToday && isEndDateToday) {
      // if startdate == Today && end date is null then return "Today"
      return `${(0, _i18n.translate)("upcomingEvent.today")}`;
    } else if (isStartDateTomorrow && !isEndDateValid || isStartDateTomorrow && isEndDateTomorrow) {
      // if startdate == Tomorrow && end date is null then return "Tomorrow"
      return `${(0, _i18n.translate)("upcomingEvent.tomorrow")}`;
    } else if (!(isStartDateToday || isStartDateTomorrow) && (!isEndDateValid || startDate.isSame(endDate))) {
      // if startdate !== (today or tomorrow) and end date is null then return specific
      return `${startDate.locale(locale).format(DateFormats.DayDateMonthYear)}`;
    } else if (isStartDateValid && isEndDateValid) {
      // if startdate !== (today or tomorrow) and end date is not null then return date range
      return `${startDate.locale(locale).format(DateFormats.DayMonthYear)} - ${endDate.locale(locale).format(DateFormats.DayMonthYear)}`;
    } else {
      return "";
    }
  }
  function flyModuleDateFormatting(date) {
    var dateFormat = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : DateFormats.DayMonthYear;
    var locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Locales.en;
    var dateCheck = (0, _moment.default)(date, DateFormats.YearMonthDay);
    if (dateCheck.isValid()) {
      var getIdentifiedDay = dayIdentifier(date);
      var constructDate = (0, _moment.default)(date).locale(locale).format(dateFormat);
      if (getIdentifiedDay) {
        constructDate = (0, _moment.default)(date).locale(locale).format(DateFormats.DayMonthYear);
        return `${getIdentifiedDay}, ${constructDate}`;
      } else {
        constructDate = (0, _moment.default)(date).locale(locale).format(DateFormats.DayMonthYear);
        return constructDate;
      }
    } else {
      return "";
    }
  }
  function dayIdentifier(date) {
    var yesterday = (0, _moment.default)().subtract(1, "day");
    var today = (0, _moment.default)();
    var tomorrow = (0, _moment.default)().add(1, "day");
    var flightDate = (0, _moment.default)(date);
    if ((0, _moment.default)(tomorrow).isSame(flightDate.format(DateFormats.YearMonthDay), "day")) {
      return `${(0, _i18n.translate)("upcomingEvent.tomorrow")}`;
    } else if ((0, _moment.default)(today).isSame(flightDate.format(DateFormats.YearMonthDay), "day")) {
      return `${(0, _i18n.translate)("upcomingEvent.today")}`;
    } else if ((0, _moment.default)(yesterday).isSame(flightDate.format(DateFormats.YearMonthDay), "day")) {
      return `${(0, _i18n.translate)("upcomingEvent.yesterday")}`;
    } else {
      return false;
    }
  }
  function flyModuleUpdatedTime() {
    var locale = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : Locales.en;
    return (0, _moment.default)(new Date()).locale(locale).format(DateFormats.flyModuleUpdatedTime);
  }
  var eventCardDateFormatting = exports.eventCardDateFormatting = function eventCardDateFormatting(eventStart, eventEnd) {
    var locale = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Locales.en;
    var startDateCheck = (0, _moment.default)(eventStart, DateFormats.YearMonthDay);
    var endDateCheck = (0, _moment.default)(eventEnd, DateFormats.YearMonthDay);
    if (startDateCheck.isValid() && endDateCheck.isValid()) {
      var currentDate = (0, _moment.default)().format(DateFormats.YearMonthDay);
      var nextDayDate = (0, _moment.default)().add(1, "days").format(DateFormats.YearMonthDay);
      var formattedDateString = "";
      if (eventStart === eventEnd) {
        var dayOfWeek = (0, _.handleCondition)(eventStart === currentDate, (0, _i18n.translate)("upcomingEvent.today"), (0, _.handleCondition)(eventStart === nextDayDate, (0, _i18n.translate)("upcomingEvent.tomorrow"), (0, _moment.default)(eventStart).locale(locale).format(DateFormats.DayOfWeek)));
        formattedDateString = `${dayOfWeek}, ${(0, _moment.default)(eventStart).locale(locale).format(DateFormats.DayMonthYear)}`;
      } else {
        formattedDateString = (0, _moment.default)(eventStart).locale(locale).format(DateFormats.Year) === (0, _moment.default)(eventEnd).locale(locale).format(DateFormats.Year) ? `${(0, _moment.default)(eventStart).locale(locale).format(DateFormats.DateMonth)} - ${(0, _moment.default)(eventEnd).locale(locale).format(DateFormats.DayMonthYear)}` : `${(0, _moment.default)(eventStart).locale(locale).format(DateFormats.DayMonthYear)} - ${(0, _moment.default)(eventEnd).locale(locale).format(DateFormats.DayMonthYear)}`;
      }
      return formattedDateString;
    } else {
      return "";
    }
  };
  function flyGetRetimedDate(date) {
    var locale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Locales.en;
    return (0, _moment.default)(date).locale(locale).format(DateFormats.DayMonth);
  }
  var convertTimeFrom24to12Hrs = exports.convertTimeFrom24to12Hrs = function convertTimeFrom24to12Hrs(timeString) {
    var locale = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Locales.en;
    var formattedTime = (0, _moment.default)(timeString, TimeFormats["24Hours"]).locale(locale).format(TimeFormats["12Hours"]);
    return formattedTime;
  };
  var getCurrentTimeSingapore = exports.getCurrentTimeSingapore = function getCurrentTimeSingapore() {
    return MomentTimeZone.tz((0, _moment.default)(), "Asia/Singapore").format("YYYY-MM-DD HH:mm");
  };
  var getCurrentDateSingapore = exports.getCurrentDateSingapore = function getCurrentDateSingapore() {
    return MomentTimeZone.tz((0, _moment.default)(), "Asia/Singapore").format("YYYY-MM-DD");
  };
  var getDateSingapore = exports.getDateSingapore = function getDateSingapore(datetime) {
    return MomentTimeZone.tz(datetime, "Asia/Singapore").format("YYYY-MM-DD");
  };
  var getCurrentTimeStampSingapore = exports.getCurrentTimeStampSingapore = function getCurrentTimeStampSingapore() {
    return MomentTimeZone.tz("Asia/Singapore").valueOf();
  };
  var convertDateTimeToTimeStampSingapore = exports.convertDateTimeToTimeStampSingapore = function convertDateTimeToTimeStampSingapore(datetime) {
    var _MomentTimeZone$tz;
    var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "YYYY-MM-DD HH:mm";
    return (_MomentTimeZone$tz = MomentTimeZone.tz((0, _moment.default)(datetime, format), "Asia/Singapore")) == null ? undefined : _MomentTimeZone$tz.valueOf();
  };
  var convertDateTimeToSingapore = exports.convertDateTimeToSingapore = function convertDateTimeToSingapore(datetime, format, toFormat) {
    var _MomentTimeZone$tz2;
    return (_MomentTimeZone$tz2 = MomentTimeZone.tz((0, _moment.default)(datetime, format || "YYYY-MM-DD HH:mm"), "Asia/Singapore")) == null ? undefined : _MomentTimeZone$tz2.format(toFormat);
  };
  function dateToFromNow(date) {
    return (0, _moment.default)(date).calendar(null, {
      lastWeek: `ddd, ${DateFormats.DayMonthYear}`,
      lastDay: `[Yesterday], ${DateFormats.DayMonthYear}`,
      sameDay: `[Today], ${DateFormats.DayMonthYear}`,
      nextDay: `[Tomorrow], ${DateFormats.DayMonthYear}`,
      nextWeek: `ddd, ${DateFormats.DayMonthYear}`,
      sameElse: `ddd, ${DateFormats.DayMonthYear}`
    });
  }
  var convertGMTToSingapore = exports.convertGMTToSingapore = function convertGMTToSingapore(timeStamp) {
    var currentTimeToSingapore = MomentTimeZone.utc(timeStamp).tz("Asia/Singapore").format(DateFormats.YearMonthDayTime);
    return currentTimeToSingapore;
  };
  var getCurrentDate = exports.getCurrentDate = function getCurrentDate() {
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; // January is 0!

    var yyyy = today.getFullYear();
    if (dd < 10) {
      dd = "0" + dd;
    }
    if (mm < 10) {
      mm = "0" + mm;
    }
    return yyyy + "-" + mm + "-" + dd;
  };
  var getWeekDay = exports.getWeekDay = function getWeekDay() {
    var today = new Date();
    var day = today.getDay();
    if (day === 0 || day === 6) {
      return "WeekEnd";
    }
    return "WeekDay";
  };
  var getScheduledTime = exports.getScheduledTime = function getScheduledTime(days, hours, minutes) {
    var now = (0, _moment.default)();
    return now.clone().startOf('day').add(days, 'day').add(hours, 'hour').add(minutes, 'minute').toISOString();
  };

  /**
   * Format campaign date range for display
   * @param start Campaign start date
   * @param end Campaign end date
   * @returns Formatted date string or undefined if both dates are empty
   */
  var formatCampaignDateRange = exports.formatCampaignDateRange = function formatCampaignDateRange(start, end) {
    if (!start && !end) {
      return;
    } else if (!start) {
      return (0, _moment.default)(end).format(DateFormats.DayMonthYear);
    } else if (!end) {
      return (0, _moment.default)(start).format(DateFormats.DayMonthYear);
    }
    var startYear = (0, _moment.default)(start).format(DateFormats.Year);
    var endYear = (0, _moment.default)(end).format(DateFormats.Year);
    var startTime = "";
    var endTime = "";
    if (startYear !== endYear) {
      startTime = (0, _moment.default)(start).format(DateFormats.DayMonthYear);
      endTime = (0, _moment.default)(end).format(DateFormats.DayMonthYear);
    } else {
      startTime = (0, _moment.default)(start).format(DateFormats.DateMonth);
      endTime = (0, _moment.default)(end).format(DateFormats.DayMonthYear);
    }
    return `${startTime} - ${endTime}`;
  };
