  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.removeCookiesByListName = exports.removeCookie = exports.removeAllCookies = undefined;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _reactNative = _$$_REQUIRE(_dependencyMap[2]);
  var _cookies = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[4]));
  var RCTNetworking = _$$_REQUIRE(_dependencyMap[5]);
  var removeCookie = exports.removeCookie = function removeCookie(url, name) {
    var dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ";
    var expiredDate = (0, _moment.default)().subtract(1, "days").format(dateFormat).toString();
    if (_reactNative.Platform.OS === "ios") {
      _cookies.default.clearByName(url, name);
    } else {
      _cookies.default.set(url, {
        name: name,
        value: "",
        version: "1",
        expires: expiredDate
      });
    }
  };
  var removeCookiesByListName = exports.removeCookiesByListName = /*#__PURE__*/function () {
    var _ref = (0, _asyncToGenerator2.default)(function* (url, array) {
      if (!array.length) return false;
      var dateFormat = "YYYY-MM-DDTHH:mm:ss.sssZ";
      var expiredDate = (0, _moment.default)().subtract(1, "days").format(dateFormat).toString();
      if (_reactNative.Platform.OS === "ios") {
        for (var i = 0; i < array.length; i++) {
          yield _cookies.default.clearByName(url, array[i], true);
        }
      } else {
        for (var _i = 0; _i < array.length; _i++) {
          yield _cookies.default.set(url, {
            name: array[_i],
            value: "",
            version: "1",
            expires: expiredDate
          });
        }
      }
    });
    return function removeCookiesByListName(_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }();
  var removeAllCookies = exports.removeAllCookies = /*#__PURE__*/function () {
    var _ref2 = (0, _asyncToGenerator2.default)(function* () {
      _cookies.default.clearAll();
      if (_reactNative.Platform.OS === "ios") {
        yield _reactNative.NativeModules.ClearWebviewCache.clearWebviewIOS();
      } else {
        try {
          RCTNetworking == null || RCTNetworking.clearCookies(function () {
            return null;
          });
        } catch (error) {}
      }
    });
    return function removeAllCookies() {
      return _ref2.apply(this, arguments);
    };
  }();
