  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.syncDineAndShop = exports.isSyncUpRequired = exports.SYNCUP_FREQUENCY = undefined;
  exports.syncGetFlyCityCode = syncGetFlyCityCode;
  var _moment = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _mmkvStorage = _$$_REQUIRE(_dependencyMap[2]);
  var _dateTime = _$$_REQUIRE(_dependencyMap[3]);
  var _flySaga = _$$_REQUIRE(_dependencyMap[4]);
  var SYNCUP_FREQUENCY = exports.SYNCUP_FREQUENCY = /*#__PURE__*/function (SYNCUP_FREQUENCY) {
    SYNCUP_FREQUENCY[SYNCUP_FREQUENCY["DAILY"] = 1] = "DAILY";
    SYNCUP_FREQUENCY[SYNCUP_FREQUENCY["WEEKLY"] = 7] = "WEEKLY";
    SYNCUP_FREQUENCY[SYNCUP_FREQUENCY["MONTHLY"] = 30] = "MONTHLY";
    return SYNCUP_FREQUENCY;
  }({});
  /**
   * To sync up non-saga api call - data if the last update is older than 24 hours against to daily @ 3.10 AM.
   * @returns 
   */
  var isSyncUpRequired = exports.isSyncUpRequired = function isSyncUpRequired(taskKey, SYNCUP_FREQUENCY, hrs, minutes) {
    var lastUpdate = (0, _mmkvStorage.getSyncTaskInfo)(taskKey);
    var now = (0, _moment.default)();

    // First-time setup: schedule for tomorrow at 3:10 AM
    if (!lastUpdate) {
      var initialUpdateTime = (0, _dateTime.getScheduledTime)(SYNCUP_FREQUENCY, hrs, minutes);
      //const initialUpdateTime = now.clone().startOf('day').add(1, 'day').add(3, 'hours').add(10, 'minutes');
      (0, _mmkvStorage.setSyncTaskInfo)(taskKey, initialUpdateTime);
      return true;
    }
    var lastUpdateMoment = (0, _moment.default)(lastUpdate);
    // subsequent app nav / calls - If current time is equal to or past last update, schedule next update and return true
    if (now.isSameOrAfter(lastUpdateMoment)) {
      var nextUpdate = lastUpdateMoment.clone().add(SYNCUP_FREQUENCY, 'day');
      (0, _mmkvStorage.setSyncTaskInfo)(taskKey, nextUpdate.toISOString());
      return true;
    }
    // No update needed yet
    return false;
  };
  /** 
   * dine and shop sync task that runs daily at 3:10 AM
   * */
  var syncDineAndShop = exports.syncDineAndShop = function syncDineAndShop() {
    return isSyncUpRequired("DINE_SHOP_SYNC_TASK", SYNCUP_FREQUENCY.DAILY, 3, 10);
  };

  /** 
   * get fly city code sync task that runs daily at 3:10 AM
   * */
  function* syncGetFlyCityCode(action) {
    if (isSyncUpRequired("GET_FLY_CITY_CODE_SYNC_TASK", SYNCUP_FREQUENCY.DAILY, 3, 10)) {
      yield* (0, _flySaga.getFlyCityCode)();
    }
  }
