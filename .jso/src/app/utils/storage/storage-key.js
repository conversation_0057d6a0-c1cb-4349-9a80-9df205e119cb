  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.StorageKey = undefined;
  var StorageKey = exports.StorageKey = {
    authTokenPayload: "authTokenPayload",
    needDisableToolTipExploreScreen: "needDisableToolTipExploreScreen",
    needDisableToolTipFlyScreen: "needDisableToolTipFlyScreen",
    useBiometricForLoginAndSecurity: "useBiometricForLoginAndSecurity",
    tempSaveEmailAndPassword: "tempSaveEmailAndPassword",
    userSectionIdAndCsrftoken: "userSectionIdAndCsrftoken",
    userDismissedCardPrivileges: "userDismissedCardPrivileges",
    userInstallNewApp: "userInstallNewApp",
    tickerBandShowed: "tickerBandShowed",
    currentVersionInstalled: "currentVersionInstalled",
    eventAndPromotionNotifications: "eventAndPromotionNotifications",
    isFirstOpenApp: "isFirstOpenApp",
    deviceInformation: "deviceInformation",
    dateStartCycleCacheImage: "dateStartCycleCacheImage",
    lastLoggedInUser: "lastLoggedInUser",
    aemAppVersionUpdating: "AemAppVersionUpdating",
    aemAppVersionUpdatingCPay: "aemAppVersionUpdatingCPay",
    keywordSearchMissingByAppState: "keywordSearchMissingByAppState",
    keywordAtomSearchMissingByAppState: "keywordAtomSearchMissingByAppState",
    isFirstRequestPermissionCalendar: "isFirstRequestPermissionCalendar",
    lastLoggedInTimeStamp: "lastLoggedInTimeStamp",
    lastRatingTimeStamp: "lastRatingTimeStamp",
    isParticipatedCmWebview: "isParticipatedCmWebview",
    isParticipatedInAppFolio: "isParticipatedInAppFolio",
    conciergeWAEndpoint: "conciergeWAEndpoint",
    monarchOveylayUserID: "monarchOveylayUserID",
    checkSurveys: "checkSurveys",
    authCookies: "authCookies",
    accountOverlayV2: "accountOverlayV2",
    lastNPSPromptTimestamp: "lastNPSPromptTimestamp",
    isSaveFlightTriggered: "isSaveFlightTriggered",
    redirectedFromLaunchingScreen: "redirectedFromLaunchingScreen",
    isFirstLogin: "isFirstLogin",
    newCiam: "newCiam",
    lockedEmails: "lockedEmails",
    retroClaimsSkipGuideUsers: "retroClaimsSkipGuideUsers",
    lasTimeOpenedECard: "lastTimeOpenedECard",
    biometricLoggedIn: "biometricLoggedIn",
    isRequestedMicroPermission: "isRequestedMicroPermission",
    isRequestedCameraPermission: "isRequestedCameraPermission",
    nonLoginEpicTransactionSummary: "nonLoginEpicTransactionSummary",
    lastNotificationPreferenceAccount: "lastNotificationPreferenceAccount"
  };
