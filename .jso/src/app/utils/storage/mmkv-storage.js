  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.setUserPassword = exports.setUserAgent = exports.setThrottleRegisterAttempt = exports.setSyncTaskInfo = exports.setPreventNPSPrompt = exports.setPlayPassBookingFinished = exports.setMMKVdata = exports.setLocationPermissionPreferenceState = exports.setLastSavedFlightTime = exports.setIsShowingSessionPopup = exports.setIsShowingLogin = exports.setIsShowModalCheckRatingPopup = exports.setHasShowedNPS = exports.setForcedUpdateTempData = exports.setFirstSetDeviceIDForBraze = exports.setFirstFetchRemoteConfig = exports.setDateSaveDineDirectoryInday = exports.setDataDineDirectoryInday = exports.setAppSettingVersion = exports.setAppSettingNeedToRefreshStatus = exports.setAnimationInDayParkingRight = exports.setAnimationInDayParkingLeft = exports.setAnimationInDay = exports.setAllInitialPromptsAreDone = exports.removeMMKVStorage = exports.getUserPassword = exports.getUserAgent = exports.getThrottleRegisterAttempt = exports.getSyncTaskInfo = exports.getPreventNPSPrompt = exports.getPlayPassBookingFinished = exports.getMMKVdata = exports.getLocationPermissionPreferenceState = exports.getLastSavedFlightTime = exports.getIsShowingSessionPopup = exports.getIsShowingLogin = exports.getIsShowModalCheckRatingPopup = exports.getHasShowedNPS = exports.getForcedUpdateTempData = exports.getFirstSetDeviceIDForBraze = exports.getFirstFetchRemoteConfig = exports.getDateSaveDineDirectoryInday = exports.getDataDineDirectoryInday = exports.getAppSettingVersion = exports.getAppSettingNeedToRefreshStatus = exports.getAnimationInDayParkingRight = exports.getAnimationInDayParkingLeft = exports.getAnimationInDay = exports.getAllInitialPromptsAreDone = exports.ENUM_STORAGE_TYPE = exports.ENUM_STORAGE_MMKV = undefined;
  var _reactNativeMmkv = _$$_REQUIRE(_dependencyMap[0]);
  var mmkvStorage = new _reactNativeMmkv.MMKV();
  var ENUM_STORAGE_MMKV = exports.ENUM_STORAGE_MMKV = /*#__PURE__*/function (ENUM_STORAGE_MMKV) {
    ENUM_STORAGE_MMKV["CURRENT_SCREEN_ACTIVE"] = "current_screen_active";
    ENUM_STORAGE_MMKV["PREVIOUS_SCREEN"] = "previous_screen";
    ENUM_STORAGE_MMKV["IS_FIRST_APP"] = "isFirstApp";
    ENUM_STORAGE_MMKV["IS_SHOW_MODAL_CHECK_RATING_POPUP"] = "IsShowModalCheckRatingPopup";
    ENUM_STORAGE_MMKV["APP_SETTINGS_VERSION"] = "appSettingsVersion";
    ENUM_STORAGE_MMKV["APP_SETTINGS_REFRESH_STATUS"] = "appSettingsRefreshStatus";
    ENUM_STORAGE_MMKV["IS_SHOWING_LOGIN"] = "isShowingLogin";
    ENUM_STORAGE_MMKV["IS_SHOWING_SESSION_POPUP"] = "isShowingSessionPopup";
    ENUM_STORAGE_MMKV["HAS_SHOWED_NPS_POPUP"] = "hasShowedNpsPopup";
    ENUM_STORAGE_MMKV["NEW_CIAM"] = "newCiam";
    ENUM_STORAGE_MMKV["USER_PASSWORD"] = "userPassword";
    ENUM_STORAGE_MMKV["IS_NAVIGATING_TO_SCREEN"] = "isNavigatingToScreen";
    ENUM_STORAGE_MMKV["IS_ALL_INITIAL_PROMPTS_ARE_DONE"] = "isAllInitialPromptsAreDone";
    ENUM_STORAGE_MMKV["IS_PREVENT_NPS_PROMPT"] = "isPreventNPSPrompt";
    ENUM_STORAGE_MMKV["ANIMATION_IN_DAY"] = "animationInDay";
    ENUM_STORAGE_MMKV["ANIMATION_IN_DAY_PARKING_LEFT"] = "animationInDayParkingLeft";
    ENUM_STORAGE_MMKV["ANIMATION_IN_DAY_PARKING_RIGHT"] = "animationInDayParkingRight";
    ENUM_STORAGE_MMKV["IS_FINISHED_JOURNEY_ONBOARDING"] = "isFinishJourneyOnboarding";
    ENUM_STORAGE_MMKV["IS_ANDROID_BIOMETRICS_PROMPT_SHOWN"] = "isAndroidBiometricPromptShown";
    ENUM_STORAGE_MMKV["FORCED_UPDATE_TEMP_DATA"] = "forcedUpdateTempData";
    ENUM_STORAGE_MMKV["LAST_SAVED_FLIGHT_TIMESTAMP"] = "lastSavedFlightTimestamp";
    ENUM_STORAGE_MMKV["PAGE_CONFIG_LAST_UPDATE"] = "pageConfigLastUpdate";
    ENUM_STORAGE_MMKV["IS_BOT_SIGNUP"] = "isBotSignUp";
    ENUM_STORAGE_MMKV["APP_USER_AGENT"] = "appUserAgent";
    ENUM_STORAGE_MMKV["THROTTLE_REGISTER_ATTEMPT"] = "throttleRegisterAttempt";
    ENUM_STORAGE_MMKV["IS_PLAYPASS_ACTIVE_FINISHED"] = "isPlayPassActiveFinished";
    ENUM_STORAGE_MMKV["SAVED_FLIGHT_PRIOR_ACTIONS"] = "savedFlightPriorActions";
    ENUM_STORAGE_MMKV["LOCATION_PERMISSION_PREFERENCE_STATE"] = "locationPermissionPreferenceState";
    ENUM_STORAGE_MMKV["FIRST_FETCH_REMOTE_CONFIG"] = "firstFetchRemoteConfig";
    ENUM_STORAGE_MMKV["DATA_DINE_DIRECTORY_IN_DAY"] = "dataDineDirectoryInDay";
    ENUM_STORAGE_MMKV["DATA_DATE_DIRECTORY"] = "dataDateDirectory";
    ENUM_STORAGE_MMKV["FIRST_SET_DEVICE_ID_FOR_BRAZE"] = "firstSetDeviceIdForBraze";
    return ENUM_STORAGE_MMKV;
  }({});
  var ENUM_STORAGE_TYPE = exports.ENUM_STORAGE_TYPE = /*#__PURE__*/function (ENUM_STORAGE_TYPE) {
    ENUM_STORAGE_TYPE["string"] = "string";
    ENUM_STORAGE_TYPE["boolean"] = "boolean";
    ENUM_STORAGE_TYPE["number"] = "number";
    return ENUM_STORAGE_TYPE;
  }({});
  var keysToRemove = [ENUM_STORAGE_MMKV.CURRENT_SCREEN_ACTIVE, ENUM_STORAGE_MMKV.PREVIOUS_SCREEN, ENUM_STORAGE_MMKV.IS_FIRST_APP, ENUM_STORAGE_MMKV.IS_SHOW_MODAL_CHECK_RATING_POPUP, ENUM_STORAGE_MMKV.IS_SHOWING_LOGIN, ENUM_STORAGE_MMKV.IS_SHOWING_SESSION_POPUP, ENUM_STORAGE_MMKV.HAS_SHOWED_NPS_POPUP, ENUM_STORAGE_MMKV.FORCED_UPDATE_TEMP_DATA, ENUM_STORAGE_MMKV.IS_PLAYPASS_ACTIVE_FINISHED];
  var removeMMKVStorage = exports.removeMMKVStorage = function removeMMKVStorage() {
    keysToRemove.forEach(function (key) {
      mmkvStorage.delete(key);
    });
  };
  var setSyncTaskInfo = exports.setSyncTaskInfo = function setSyncTaskInfo(key, data) {
    setMMKVdata(key, data);
  };
  var getSyncTaskInfo = exports.getSyncTaskInfo = function getSyncTaskInfo(key) {
    var data = getMMKVdata(key, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var setMMKVdata = exports.setMMKVdata = function setMMKVdata(key, data) {
    mmkvStorage.set(key, data);
  };
  var getMMKVdata = exports.getMMKVdata = function getMMKVdata(key, type) {
    try {
      if (type === ENUM_STORAGE_TYPE.string) {
        return mmkvStorage.getString(key);
      } else if (type === ENUM_STORAGE_TYPE.boolean) {
        return mmkvStorage.getBoolean(key);
      } else if (type === ENUM_STORAGE_TYPE.number) {
        return mmkvStorage.getNumber(key);
      }
      return null;
    } catch (error) {
      console.error(error.message || "Something went wrong with getMMKVdata");
      return null;
    }
  };
  var setIsShowModalCheckRatingPopup = exports.setIsShowModalCheckRatingPopup = function setIsShowModalCheckRatingPopup(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_SHOW_MODAL_CHECK_RATING_POPUP, data);
  };
  var getIsShowModalCheckRatingPopup = exports.getIsShowModalCheckRatingPopup = function getIsShowModalCheckRatingPopup() {
    return getMMKVdata(ENUM_STORAGE_MMKV.IS_SHOW_MODAL_CHECK_RATING_POPUP, ENUM_STORAGE_TYPE.boolean);
  };
  var setIsShowingLogin = exports.setIsShowingLogin = function setIsShowingLogin(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_LOGIN, data);
  };
  var getIsShowingLogin = exports.getIsShowingLogin = function getIsShowingLogin() {
    return getMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_LOGIN, ENUM_STORAGE_TYPE.boolean);
  };
  var setIsShowingSessionPopup = exports.setIsShowingSessionPopup = function setIsShowingSessionPopup(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_SESSION_POPUP, data);
  };
  var getIsShowingSessionPopup = exports.getIsShowingSessionPopup = function getIsShowingSessionPopup() {
    return getMMKVdata(ENUM_STORAGE_MMKV.IS_SHOWING_SESSION_POPUP, ENUM_STORAGE_TYPE.boolean);
  };
  var setHasShowedNPS = exports.setHasShowedNPS = function setHasShowedNPS(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.HAS_SHOWED_NPS_POPUP, data);
  };
  var getHasShowedNPS = exports.getHasShowedNPS = function getHasShowedNPS() {
    return getMMKVdata(ENUM_STORAGE_MMKV.HAS_SHOWED_NPS_POPUP, ENUM_STORAGE_TYPE.boolean);
  };
  var setUserPassword = exports.setUserPassword = function setUserPassword(password) {
    setMMKVdata(ENUM_STORAGE_MMKV.USER_PASSWORD, password);
  };
  var getUserPassword = exports.getUserPassword = function getUserPassword() {
    return getMMKVdata(ENUM_STORAGE_MMKV.USER_PASSWORD, ENUM_STORAGE_TYPE.string);
  };

  // APP SETTINGS
  var setAppSettingVersion = exports.setAppSettingVersion = function setAppSettingVersion() {
    var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : "";
    setMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_VERSION, data);
  };
  var getAppSettingVersion = exports.getAppSettingVersion = function getAppSettingVersion() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_VERSION, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var setAppSettingNeedToRefreshStatus = exports.setAppSettingNeedToRefreshStatus = function setAppSettingNeedToRefreshStatus(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_REFRESH_STATUS, data);
  };
  var getAppSettingNeedToRefreshStatus = exports.getAppSettingNeedToRefreshStatus = function getAppSettingNeedToRefreshStatus() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.APP_SETTINGS_REFRESH_STATUS, ENUM_STORAGE_TYPE.boolean);
    return data;
  };
  var setAnimationInDay = exports.setAnimationInDay = function setAnimationInDay(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY, data);
  };
  var getAnimationInDay = exports.getAnimationInDay = function getAnimationInDay() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var setAnimationInDayParkingLeft = exports.setAnimationInDayParkingLeft = function setAnimationInDayParkingLeft(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_LEFT, data);
  };
  var getAnimationInDayParkingLeft = exports.getAnimationInDayParkingLeft = function getAnimationInDayParkingLeft() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_LEFT, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var setAnimationInDayParkingRight = exports.setAnimationInDayParkingRight = function setAnimationInDayParkingRight(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_RIGHT, data);
  };
  var getAnimationInDayParkingRight = exports.getAnimationInDayParkingRight = function getAnimationInDayParkingRight() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.ANIMATION_IN_DAY_PARKING_RIGHT, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var getDataDineDirectoryInday = exports.getDataDineDirectoryInday = function getDataDineDirectoryInday() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.DATA_DINE_DIRECTORY_IN_DAY, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var setDataDineDirectoryInday = exports.setDataDineDirectoryInday = function setDataDineDirectoryInday(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.DATA_DINE_DIRECTORY_IN_DAY, data);
  };
  var getDateSaveDineDirectoryInday = exports.getDateSaveDineDirectoryInday = function getDateSaveDineDirectoryInday() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.DATA_DATE_DIRECTORY, ENUM_STORAGE_TYPE.string);
    return data;
  };
  var setDateSaveDineDirectoryInday = exports.setDateSaveDineDirectoryInday = function setDateSaveDineDirectoryInday(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.DATA_DATE_DIRECTORY, data);
  };

  // Check if all initial permission prompts are confirmed
  var setAllInitialPromptsAreDone = exports.setAllInitialPromptsAreDone = function setAllInitialPromptsAreDone(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_ALL_INITIAL_PROMPTS_ARE_DONE, data);
  };
  var getAllInitialPromptsAreDone = exports.getAllInitialPromptsAreDone = function getAllInitialPromptsAreDone() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.IS_ALL_INITIAL_PROMPTS_ARE_DONE, ENUM_STORAGE_TYPE.boolean);
    return data;
  };

  // Flag to prevent showing NPS prompt
  var setPreventNPSPrompt = exports.setPreventNPSPrompt = function setPreventNPSPrompt(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_PREVENT_NPS_PROMPT, data);
  };
  var getPreventNPSPrompt = exports.getPreventNPSPrompt = function getPreventNPSPrompt() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.IS_PREVENT_NPS_PROMPT, ENUM_STORAGE_TYPE.boolean);
    return data;
  };

  // temp data for forcedUpdate
  var setForcedUpdateTempData = exports.setForcedUpdateTempData = function setForcedUpdateTempData(data) {
    var tempData = data ? JSON.stringify(data) : "";
    setMMKVdata(ENUM_STORAGE_MMKV.FORCED_UPDATE_TEMP_DATA, tempData);
  };
  var getForcedUpdateTempData = exports.getForcedUpdateTempData = function getForcedUpdateTempData() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.FORCED_UPDATE_TEMP_DATA, ENUM_STORAGE_TYPE.string);
    if (data) return JSON.parse(data);
    return null;
  };
  var setLastSavedFlightTime = exports.setLastSavedFlightTime = function setLastSavedFlightTime(time) {
    setMMKVdata(ENUM_STORAGE_MMKV.LAST_SAVED_FLIGHT_TIMESTAMP, time);
  };
  var getLastSavedFlightTime = exports.getLastSavedFlightTime = function getLastSavedFlightTime() {
    return getMMKVdata(ENUM_STORAGE_MMKV.LAST_SAVED_FLIGHT_TIMESTAMP, ENUM_STORAGE_TYPE.number) || 0;
  };
  var setUserAgent = exports.setUserAgent = function setUserAgent(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.APP_USER_AGENT, data);
  };
  var getUserAgent = exports.getUserAgent = function getUserAgent() {
    return getMMKVdata(ENUM_STORAGE_MMKV.APP_USER_AGENT, ENUM_STORAGE_TYPE.string) || "";
  };
  var setThrottleRegisterAttempt = exports.setThrottleRegisterAttempt = function setThrottleRegisterAttempt(data) {
    var tempData = data ? JSON.stringify(data) : "";
    setMMKVdata(ENUM_STORAGE_MMKV.THROTTLE_REGISTER_ATTEMPT, tempData);
  };
  var getThrottleRegisterAttempt = exports.getThrottleRegisterAttempt = function getThrottleRegisterAttempt() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.THROTTLE_REGISTER_ATTEMPT, ENUM_STORAGE_TYPE.string);
    if (data) return JSON.parse(data);
    return null;
  };
  var setPlayPassBookingFinished = exports.setPlayPassBookingFinished = function setPlayPassBookingFinished(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.IS_PLAYPASS_ACTIVE_FINISHED, data);
  };
  var getPlayPassBookingFinished = exports.getPlayPassBookingFinished = function getPlayPassBookingFinished() {
    return getMMKVdata(ENUM_STORAGE_MMKV.IS_PLAYPASS_ACTIVE_FINISHED, ENUM_STORAGE_TYPE.boolean) || false;
  };
  var setLocationPermissionPreferenceState = exports.setLocationPermissionPreferenceState = function setLocationPermissionPreferenceState(data) {
    var tempData = data ? JSON.stringify(data) : "";
    setMMKVdata(ENUM_STORAGE_MMKV.LOCATION_PERMISSION_PREFERENCE_STATE, tempData);
  };
  var getLocationPermissionPreferenceState = exports.getLocationPermissionPreferenceState = function getLocationPermissionPreferenceState() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.LOCATION_PERMISSION_PREFERENCE_STATE, ENUM_STORAGE_TYPE.string);
    if (data) return JSON.parse(data);
    return null;
  };
  var getFirstFetchRemoteConfig = exports.getFirstFetchRemoteConfig = function getFirstFetchRemoteConfig() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.FIRST_FETCH_REMOTE_CONFIG, ENUM_STORAGE_TYPE.boolean);
    return data;
  };
  var setFirstFetchRemoteConfig = exports.setFirstFetchRemoteConfig = function setFirstFetchRemoteConfig(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.FIRST_FETCH_REMOTE_CONFIG, data);
  };
  var getFirstSetDeviceIDForBraze = exports.getFirstSetDeviceIDForBraze = function getFirstSetDeviceIDForBraze() {
    var data = getMMKVdata(ENUM_STORAGE_MMKV.FIRST_SET_DEVICE_ID_FOR_BRAZE, ENUM_STORAGE_TYPE.boolean);
    return data;
  };
  var setFirstSetDeviceIDForBraze = exports.setFirstSetDeviceIDForBraze = function setFirstSetDeviceIDForBraze(data) {
    setMMKVdata(ENUM_STORAGE_MMKV.FIRST_SET_DEVICE_ID_FOR_BRAZE, data);
  };
