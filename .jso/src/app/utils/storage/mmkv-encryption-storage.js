  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.setMMKVEncryptionData = exports.setAuthTokenPayload = exports.setAppSettingsData = exports.removeAuthTokenPayload = exports.getMMKVEncryptionData = exports.getAuthTokenPayload = exports.getAppSettingsData = exports.ENUM_ENCRYPTION_STORAGE_TYPE = exports.ENUM_ENCRYPTION_STORAGE_KEY = undefined;
  var _reactNativeMmkv = _$$_REQUIRE(_dependencyMap[0]);
  var mmkvStorage = new _reactNativeMmkv.MMKV({
    id: "mmkv.encryptiondata",
    encryptionKey: "changiapp-encryptiondata"
  });
  var ENUM_ENCRYPTION_STORAGE_KEY = exports.ENUM_ENCRYPTION_STORAGE_KEY = /*#__PURE__*/function (ENUM_ENCRYPTION_STORAGE_KEY) {
    ENUM_ENCRYPTION_STORAGE_KEY["APP_SETTINGS_DATA"] = "appSettingsData";
    ENUM_ENCRYPTION_STORAGE_KEY["AUTH_TOKEN_PAYLOAD"] = "authTokenPayload";
    return ENUM_ENCRYPTION_STORAGE_KEY;
  }({});
  var ENUM_ENCRYPTION_STORAGE_TYPE = exports.ENUM_ENCRYPTION_STORAGE_TYPE = /*#__PURE__*/function (ENUM_ENCRYPTION_STORAGE_TYPE) {
    ENUM_ENCRYPTION_STORAGE_TYPE["string"] = "string";
    ENUM_ENCRYPTION_STORAGE_TYPE["boolean"] = "boolean";
    ENUM_ENCRYPTION_STORAGE_TYPE["number"] = "number";
    return ENUM_ENCRYPTION_STORAGE_TYPE;
  }({});
  var setMMKVEncryptionData = exports.setMMKVEncryptionData = function setMMKVEncryptionData(key, data) {
    mmkvStorage.set(key, data);
  };
  var getMMKVEncryptionData = exports.getMMKVEncryptionData = function getMMKVEncryptionData(key, type) {
    try {
      if (type === ENUM_ENCRYPTION_STORAGE_TYPE.string) {
        return mmkvStorage.getString(key);
      } else if (type === ENUM_ENCRYPTION_STORAGE_TYPE.boolean) {
        return mmkvStorage.getBoolean(key);
      } else if (type === ENUM_ENCRYPTION_STORAGE_TYPE.number) {
        return mmkvStorage.getNumber(key);
      }
      return null;
    } catch (error) {
      console.error(error.message || "Something went wrong with getMMKVEncryptionData");
      return null;
    }
  };
  var setAppSettingsData = exports.setAppSettingsData = function setAppSettingsData() {
    var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    setMMKVEncryptionData(ENUM_ENCRYPTION_STORAGE_KEY.APP_SETTINGS_DATA, JSON.stringify(data));
  };
  var getAppSettingsData = exports.getAppSettingsData = function getAppSettingsData() {
    try {
      var dataJSON = getMMKVEncryptionData(ENUM_ENCRYPTION_STORAGE_KEY.APP_SETTINGS_DATA, ENUM_ENCRYPTION_STORAGE_TYPE.string);
      if (dataJSON) {
        return JSON.parse(dataJSON);
      }
      return null;
    } catch (_unused) {
      return null;
    }
  };
  var setAuthTokenPayload = exports.setAuthTokenPayload = function setAuthTokenPayload() {
    var data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    setMMKVEncryptionData(ENUM_ENCRYPTION_STORAGE_KEY.AUTH_TOKEN_PAYLOAD, JSON.stringify(data));
  };
  var getAuthTokenPayload = exports.getAuthTokenPayload = function getAuthTokenPayload() {
    try {
      var dataJSON = getMMKVEncryptionData(ENUM_ENCRYPTION_STORAGE_KEY.AUTH_TOKEN_PAYLOAD, ENUM_ENCRYPTION_STORAGE_TYPE.string);
      if (dataJSON) {
        return JSON.parse(dataJSON);
      }
      return null;
    } catch (_unused2) {
      return null;
    }
  };
  var removeAuthTokenPayload = exports.removeAuthTokenPayload = function removeAuthTokenPayload() {
    mmkvStorage.delete(ENUM_ENCRYPTION_STORAGE_KEY.AUTH_TOKEN_PAYLOAD);
  };
