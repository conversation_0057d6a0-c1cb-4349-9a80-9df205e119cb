  var _interopRequireDefault = _$$_REQUIRE(_dependencyMap[0]);
  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.clear = clear;
  exports.clearAllFromEncryptedStorage = clearAllFromEncryptedStorage;
  exports.load = load;
  exports.loadFromEncryptedStorage = loadFromEncryptedStorage;
  exports.loadString = loadString;
  exports.remove = remove;
  exports.removeFromEncryptedStorage = removeFromEncryptedStorage;
  exports.save = save;
  exports.saveString = saveString;
  exports.saveToEncryptedStorage = saveToEncryptedStorage;
  var _asyncToGenerator2 = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[1]));
  var _asyncStorage = _$$_REQUIRE(_dependencyMap[2]);
  var _reactNativeEncryptedStorage = _interopRequireDefault(_$$_REQUIRE(_dependencyMap[3]));
  /**
   * Loads a string from storage.
   *
   * @param key The key to fetch.
   */
  function loadString(_x) {
    return _loadString.apply(this, arguments);
  }
  /**
   * Saves a string to storage.
   *
   * @param key The key to fetch.
   * @param value The value to store.
   */
  function _loadString() {
    _loadString = (0, _asyncToGenerator2.default)(function* (key) {
      try {
        return yield _asyncStorage.AsyncStorage.getItem(key);
      } catch (_unused) {
        // not sure why this would fail... even reading the RN docs I'm unclear
        return null;
      }
    });
    return _loadString.apply(this, arguments);
  }
  function saveString(_x2, _x3) {
    return _saveString.apply(this, arguments);
  }
  /**
   * Loads something from storage and runs it thru JSON.parse.
   *
   * @param key The key to fetch.
   */
  function _saveString() {
    _saveString = (0, _asyncToGenerator2.default)(function* (key, value) {
      try {
        yield _asyncStorage.AsyncStorage.setItem(key, value);
        return true;
      } catch (_unused2) {
        return false;
      }
    });
    return _saveString.apply(this, arguments);
  }
  function load(_x4) {
    return _load.apply(this, arguments);
  }
  /**
   * Saves an object to storage.
   *
   * @param key The key to fetch.
   * @param value The value to store.
   */
  function _load() {
    _load = (0, _asyncToGenerator2.default)(function* (key) {
      try {
        var data = yield _asyncStorage.AsyncStorage.getItem(key);
        return JSON.parse(data);
      } catch (_unused3) {
        return null;
      }
    });
    return _load.apply(this, arguments);
  }
  function save(_x5, _x6) {
    return _save.apply(this, arguments);
  }
  /**
   * Removes something from storage.
   *
   * @param key The key to kill.
   */
  function _save() {
    _save = (0, _asyncToGenerator2.default)(function* (key, value) {
      try {
        yield _asyncStorage.AsyncStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch (_unused4) {
        return false;
      }
    });
    return _save.apply(this, arguments);
  }
  function remove(_x7) {
    return _remove.apply(this, arguments);
  }
  /**
   * Burn it all to the ground.
   */
  function _remove() {
    _remove = (0, _asyncToGenerator2.default)(function* (key) {
      try {
        yield _asyncStorage.AsyncStorage.removeItem(key);
      } catch (_unused5) {}
    });
    return _remove.apply(this, arguments);
  }
  function clear() {
    return _clear.apply(this, arguments);
  }
  /**
   * Save data to encrypted storage
   */
  function _clear() {
    _clear = (0, _asyncToGenerator2.default)(function* () {
      try {
        yield _asyncStorage.AsyncStorage.clear();
      } catch (_unused6) {}
    });
    return _clear.apply(this, arguments);
  }
  function saveToEncryptedStorage(_x8, _x9) {
    return _saveToEncryptedStorage.apply(this, arguments);
  }
  /**
   * Loads something from encrypted storage and runs it through JSON.parse.
   *
   * @param key The key to fetch.
   */
  function _saveToEncryptedStorage() {
    _saveToEncryptedStorage = (0, _asyncToGenerator2.default)(function* (key, value) {
      try {
        yield _reactNativeEncryptedStorage.default.setItem(key, JSON.stringify(value));
        return true;
      } catch (_unused7) {
        return false;
      }
    });
    return _saveToEncryptedStorage.apply(this, arguments);
  }
  function loadFromEncryptedStorage(_x0) {
    return _loadFromEncryptedStorage.apply(this, arguments);
  }
  /**
   * Remove data from encrypted storage
   *
   * @param key The key to fetch.
   */
  function _loadFromEncryptedStorage() {
    _loadFromEncryptedStorage = (0, _asyncToGenerator2.default)(function* (key) {
      try {
        var dataJSON = yield _reactNativeEncryptedStorage.default.getItem(key);
        if (dataJSON) {
          return JSON.parse(dataJSON);
        }
        return null;
      } catch (_unused8) {
        return null;
      }
    });
    return _loadFromEncryptedStorage.apply(this, arguments);
  }
  function removeFromEncryptedStorage(_x1) {
    return _removeFromEncryptedStorage.apply(this, arguments);
  }
  /**
   * Clear all data from encrypted storage
   */
  function _removeFromEncryptedStorage() {
    _removeFromEncryptedStorage = (0, _asyncToGenerator2.default)(function* (key) {
      try {
        yield _reactNativeEncryptedStorage.default.setItem(key, "");
        yield _reactNativeEncryptedStorage.default.removeItem(key);
      } catch (_unused9) {}
    });
    return _removeFromEncryptedStorage.apply(this, arguments);
  }
  function clearAllFromEncryptedStorage() {
    return _clearAllFromEncryptedStorage.apply(this, arguments);
  }
  function _clearAllFromEncryptedStorage() {
    _clearAllFromEncryptedStorage = (0, _asyncToGenerator2.default)(function* () {
      try {
        yield _reactNativeEncryptedStorage.default.clear();
      } catch (_unused0) {}
    });
    return _clearAllFromEncryptedStorage.apply(this, arguments);
  }
