  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.MMKVPersistStorage = undefined;
  var _reactNativeMmkv = _$$_REQUIRE(_dependencyMap[0]);
  var storage = new _reactNativeMmkv.MMKV();
  var MMKVPersistStorage = exports.MMKVPersistStorage = {
    setItem: function setItem(key, value) {
      storage.set(key, value);
      return Promise.resolve(true);
    },
    getItem: function getItem(key) {
      var value = storage.getString(key);
      return Promise.resolve(value || null);
    },
    removeItem: function removeItem(key) {
      storage.delete(key);
      return Promise.resolve();
    }
  };
