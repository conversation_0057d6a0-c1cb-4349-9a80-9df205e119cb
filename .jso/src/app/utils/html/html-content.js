  Object.defineProperty(exports, "__esModule", {
    value: true
  });
  exports.isHTML = exports.formatHtmlContent = undefined;
  var _lodash = _$$_REQUIRE(_dependencyMap[0]);
  var isHTML = exports.isHTML = function isHTML(text) {
    var converted = text.replace(/<br\/>|<br>|<br \/>/g, "\n");
    var htmlRegex = /<[a-z][\s\S]*>/i;
    return htmlRegex.test(converted);
  };
  var formatHtmlContent = exports.formatHtmlContent = function formatHtmlContent(content, replacePTag) {
    if ((0, _lodash.isEmpty)(content)) return;
    var isHTMLContent = isHTML(content);
    if (isHTMLContent) {
      return replacePTag ? content.replace(/<p>/g, "<div>").replace(/<\/p>/g, "</div>") : content;
    }
    try {
      return `<html><head><meta name="viewport" content="width=device-width, initial-scale=0.5"/>
      </head><body>
      <p>${content.replaceAll("\n", "<br/>")}</p>
      </body>
      </html>`;
    } catch (_e) {
      return "";
    }
  };
