const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const path = require('path');

const defaultConfig = getDefaultConfig(__dirname);
const { assetExts, sourceExts } = defaultConfig.resolver;

const jsoMetroPlugin = require("obfuscator-io-metro-plugin")(
  {
    // for these option look javascript-obfuscator library options from  above url
    compact: false,
    sourceMap: false, // source Map generated after obfuscation is not useful right now so use default value i.e. false
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    numbersToExpressions: true,
    simplify: true,
    stringArrayShuffle: true,
    splitStrings: true,
    stringArrayThreshold: 1,
  },
  {
    runInDev: false /* optional */,
    logObfuscatedFiles: false /* optional generated files will be located at ./.jso */,
  }
);

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  transformer: {
    //babelTransformerPath: require.resolve("react-native-svg-transformer/react-native"),
    babelTransformerPath: require.resolve('@dynatrace/react-native-plugin/lib/dynatrace-transformer'),
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  reporter: require('@dynatrace/react-native-plugin/lib/dynatrace-reporter'),
  resolver: {
    assetExts: [...assetExts, 'lottie'].filter((ext) => ext !== "svg"),
    sourceExts: [...sourceExts, "svg", "ts", "tsx"],
    extraNodeModules: {
      app: path.resolve(__dirname, 'app'),
      assets: path.resolve(__dirname, 'assets'),
      'ichangi-fe': path.resolve(__dirname),
    }
  },
  ...jsoMetroPlugin,
};

module.exports = mergeConfig(defaultConfig, config);
